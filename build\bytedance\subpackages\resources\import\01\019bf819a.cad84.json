[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "7dDqJ1SwpHsaJqh4wH/E+7", "07uTi5pw5INIAU5mtPfWfY", "67qzoauzRD14EfoKrVR0SA", "aej5KCfVhFI6ZBtIoyh/zN", "b76uHOgvlBK44IpR7/IIXg", "2fyKStw/BIZJo+ld7ER3CQ", "cdvDZRLPZKb70zilA0TpQ8", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr"], ["node", "_spriteFrame", "root", "_N$file", "_textureSetter", "<PERSON><PERSON><PERSON><PERSON>", "boximg", "shine", "box", "tips", "boxname", "_N$target", "data", "asset", "_N$skeletonData"], [["cc.Node", ["_name", "_opacity", "_active", "_groupIndex", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 4, 9, 5, 1, 2, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_enableWrapText", "node", "_materials"], -3, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["ebf41wd1adB66URywdyAPkb", ["UItype", "node", "boxname", "tips", "box", "shine", "boximg", "<PERSON><PERSON><PERSON><PERSON>"], 2, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.<PERSON><PERSON>", ["node", "clickEvents", "_N$target"], 3, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "node"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -3, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6]], [[3, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 2], [1, 2, 3, 4, 1], [0, 0, 7, 5, 4, 6, 2], [2, 0, 1, 2, 4, 4], [2, 0, 4, 2], [12, 0, 1, 2, 3], [14, 0, 1, 2, 2], [6, 0, 2], [0, 0, 3, 8, 5, 4, 6, 3], [0, 0, 7, 8, 5, 4, 6, 2], [0, 0, 1, 7, 5, 4, 6, 3], [0, 0, 1, 7, 8, 5, 4, 10, 6, 3], [0, 0, 2, 7, 8, 5, 4, 6, 3], [0, 0, 2, 7, 5, 4, 6, 11, 9, 3], [0, 0, 7, 8, 5, 4, 6, 9, 2], [0, 0, 7, 5, 4, 6, 9, 2], [0, 0, 7, 4, 9, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 2], [3, 1, 2, 1], [9, 0, 1, 2, 3, 3], [2, 0, 3, 4, 3], [1, 0, 2, 3, 2], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 4, 3], [1, 2, 3, 1], [10, 0, 1, 2, 1], [11, 0, 1, 2, 3], [13, 0, 1, 2, 3, 4, 5, 6, 7, 7], [15, 0, 1, 2, 3, 4, 5, 6, 5], [4, 0, 4, 5, 1, 2, 3, 6, 7, 7], [4, 0, 1, 2, 3, 6, 7, 5]], [[[{"name": "4", "rect": [0, 0, 10, 10], "offset": [0, 0], "originalSize": [10, 10], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [4], [2]], [[[8, "Item_GetBox"], [9, "Item_GetBox", 1, [-9, -10, -11], [[18, 0, -8, -7, -6, -5, -4, -3, -2]], [19, -1, 0], [5, 750, 1334]], [10, "bg", 1, [-13, -14, -15, -16, -17, -18, -19], [[4, 45, 750, 1334, -12]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [11, "bg copy", 0, 2, [[22, 0, -20, [14]], [4, 45, 100, 100, -21], [26, -23, [[27, "ebf41wd1adB66URywdyAPkb", "getReward", 1]], -22]], [0, "aaWu4OFWZLSZnPFrw7xeL+", 1, 0], [5, 750, 1334]], [12, "maskbg", 200, 1, [-26], [[5, 45, -24], [23, 0, -25, [2], 3]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [3, "faguang_big", 2, [[2, -27, [9], 10], [6, 6, 2, -28]], [0, "6aK9L7QI5AFI59Uf8a3L7i", 1, 0], [5, 511, 505]], [13, "rewardsnode", false, 2, [-30], [[28, 1, 3, 30, 30, 20, 20, -29, [5, 604, 120]]], [0, "f8w1wvegRAmoOwYpKGY3ox", 1, 0], [5, 604, 120]], [1, "treasurebox_01", 2, [[-31, [6, 3, 0.5, -32]], 1, 4], [0, "32Du5x94NCBbJ2TWMy+xz1", 1, 0], [5, 146, 142], [0, -5.184, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [14, "img_dian", false, 4, [[24, 2, 0, -33, [0], 1], [5, 4, -34]], [0, "3879NmTE5EyJNxSo2YeK8O", 1, 0], [5, 750, 320], [0, 0.5, 0], [0, -667, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "title_huode", 2, [-36], [[2, -35, [5], 6]], [0, "2enky3bh5Os7VJuJ86mkgS", 1, 0], [5, 531, 47], [-12, 362.578, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 9, [[-37, [7, 3, -38, [4, 4278190080]]], 1, 4], [0, "a5T4nexxFC2reBYZzoKujm", 1, 0], [5, 150, 56.4], [12, 7.793, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 2, [[-39, [7, 2, -40, [4, 4278190080]]], 1, 4], [0, "54BvaOn3BFKprgoRL9VibH", 1, 0], [5, 164, 54.4], [0, -376.903, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "effect_ribbons", 1, [[29, "default", "animation", 0, "animation", -41, [15], 16], [21, 1, 3.9951904144287482, -42]], [0, "35F05GRbFJFo4x061sQxqy", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 646.266, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "精致宝箱", 36, false, false, 1, 1, 10, [4]], [3, "pic_jl_00", 2, [[2, -43, [7], 8]], [0, "4c8jIUW5JA/4GGT3HIaN/f", 1, 0], [5, 608, 587]], [17, "RewardItem", 6, [20, "c34ylKNstN6bOAkf/eL4OO", true, -44, 11], [-214, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 7, [12]], [31, "点击打开", false, 1, 1, 11, [13]]], 0, [0, 2, 1, 0, 5, 6, 0, 6, 16, 0, 7, 5, 0, 8, 7, 0, 9, 17, 0, 10, 13, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 12, 0, 0, 2, 0, -1, 9, 0, -2, 14, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 11, 0, -7, 3, 0, 0, 3, 0, 0, 3, 0, 11, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 15, 0, -1, 16, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, -1, 13, 0, 0, 10, 0, -1, 17, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 14, 0, 2, 15, 0, 12, 1, 44], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 17], [-1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, 13, -1, -1, -1, -1, 14, 3, 3], [0, 3, 0, 4, 0, 0, 5, 0, 6, 0, 7, 8, 0, 0, 0, 9, 10, 1, 1]]]]