[1, ["ecpdLyjvZBwrvm+cedCcQy", "89YqH0KIZJ26WKtSNfrXcv", "04y3cprWxFQrha4WRCk4AY"], ["node", "_spriteFrame", "root", "<PERSON><PERSON><PERSON><PERSON>", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color"], 1, 9, 4, 5, 1, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_size"], 3, 1, 5], ["cc.RigidBody", ["_type", "node"], 2, 1], ["cc.PhysicsCircleCollider", ["_friction", "node"], 2, 1], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node"], 2, 1], ["5a55e3TjupL/I9+DxL+YRF8", ["miniGameLv", "node"], 2, 1], ["cc.PhysicsBoxCollider", ["_friction", "node", "_offset", "_size"], 2, 1, 5, 5], ["5688fiOKklDfL+crWJqzZ67", ["node", "<PERSON><PERSON><PERSON><PERSON>"], 3, 1, 1]], [[1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 3], [0, 0, 5, 2, 3, 4, 6, 2], [8, 0, 1, 2], [12, 0, 1, 2, 3, 2], [3, 0, 2], [0, 0, 1, 7, 2, 3, 4, 3], [0, 0, 1, 5, 7, 2, 3, 8, 4, 6, 3], [0, 0, 1, 5, 2, 3, 4, 6, 3], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 6], [6, 0, 1, 2, 2], [1, 0, 1, 2], [1, 1, 2, 1], [2, 2, 3, 1], [7, 0, 1, 1], [9, 0, 1, 2], [10, 0, 1, 2], [11, 0, 1, 2], [13, 0, 1, 1]], [[5, "MPUBEatWallWeapon"], [6, "MPUBEatWall", 8, [-7, -8, -9, -10], [[3, 0, -2], [4, 0, -3, [0, -125, 0], [5, 50, 120]], [4, 0, -4, [0, 125, 0], [5, 50, 120]], [19, -6, -5]], [13, -1, 0], [5, 300, 120]], [8, "MPUBWeapon", 7, 1, [[14, -12, [7]], [3, 0, -13], [16, 0.05, -14], [17, 50, -15], [18, 1007, -16]], [12, "49bKh3lAZAp5+Yjfgns5s+", -11], [5, 92, 87], [0, 114.722, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Eat", 5, 1, [-19], [[1, 1, 0, -17, [1], 2], [15, -18, [5, 200, 60]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 1, 0], [4, 4283453695], [5, 280, 80], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "label_multiple", 3, [[-20, [11, 3, -21, [4, 4278190080]]], 1, 4], [0, "06/2jfsSBICI4jM9YCpnfz", 1, 0], [5, 106.07, 56.4], [2.905, 2.905, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "-150", 50, false, 1, 1, 4, [0]], [2, "wall", 1, [[1, 1, 0, -22, [3], 4]], [0, "24c+KvVkdL4bvegRYe3/1M", 1, 0], [5, 40, 100], [-125, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall", 1, [[1, 1, 0, -23, [5], 6]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 1, 0], [5, 40, 100], [125, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 3, 5, 0, 0, 1, 0, -1, 3, 0, -2, 6, 0, -3, 7, 0, -4, 2, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, -1, 5, 0, 0, 4, 0, 0, 6, 0, 0, 7, 0, 4, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, 1, -1, 1, -1, 1, -1], [0, 0, 2, 0, 1, 0, 1, 0]]