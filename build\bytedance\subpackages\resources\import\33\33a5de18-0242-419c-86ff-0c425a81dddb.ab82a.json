[1, ["ecpdLyjvZBwrvm+cedCcQy", "22cjIamqZH/Lbdvp80pFLv", "a2MjXRFdtLlYQ5ouAFv/+R", "65OUoZ25pGHqftEDT5VTS4", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "f6NcoTBPNJ4qSyD40PNpRP", "18fU8MiRhAvacPMzvkRWSp"], ["node", "_spriteFrame", "root", "_parent", "_N$normalSprite", "_N$disabledSprite", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 5, 1, 9, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_N$cacheMode", "_lineHeight", "_spacingX", "_enableWrapText", "node", "_materials"], -7, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingBottom", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["349afQnI5BO8LA+vJl/psZA", ["node"], 3, 1]], [[3, 0, 1, 2, 2], [3, 0, 1, 2], [0, 0, 5, 6, 3, 4, 7, 2], [8, 0, 1, 2, 2], [0, 0, 5, 8, 6, 3, 4, 7, 2], [1, 1, 0, 2, 3, 4, 3], [0, 0, 8, 6, 3, 4, 2], [1, 2, 3, 4, 1], [2, 0, 1, 2, 3, 5, 10, 11, 6], [9, 0, 1, 2, 3], [6, 0, 2], [0, 0, 1, 5, 6, 3, 9, 4, 3], [0, 0, 5, 8, 3, 4, 2], [0, 0, 2, 5, 8, 6, 3, 4, 7, 3], [0, 0, 5, 6, 3, 9, 4, 7, 2], [0, 0, 8, 3, 4, 2], [0, 0, 5, 6, 3, 4, 2], [0, 0, 5, 6, 3, 4, 10, 7, 2], [0, 0, 5, 6, 3, 9, 4, 10, 7, 2], [0, 0, 5, 8, 3, 7, 2], [1, 0, 2, 3, 4, 2], [1, 0, 2, 3, 2], [1, 1, 0, 2, 3, 3], [7, 0, 1, 2, 3, 4], [3, 1, 2, 1], [2, 0, 1, 7, 2, 8, 4, 3, 5, 6, 10, 11, 10], [2, 0, 1, 9, 2, 4, 3, 6, 10, 11, 8], [2, 0, 1, 2, 4, 3, 10, 11, 6], [2, 0, 1, 7, 2, 4, 3, 5, 6, 10, 11, 9], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [4, 0, 1, 2, 3, 3], [5, 0, 1, 2, 4, 5, 4], [5, 0, 1, 3, 2, 4, 5, 5], [10, 0, 1], [11, 0, 1]], [[10, "PlatformGiftsView"], [15, "item", [-2, -3, -4, -5, -6, -7, -8], [1, "6bI0Jw1tJKxIIlV45NLm9s", -1], [5, 630, 160]], [6, "PlatformGiftsView", [-11, -12], [[34, -10]], [24, -9, 0], [5, 750, 1334]], [6, "imgbg", [-16, -17, -18], [[5, 1, 0, -13, [20], 21], [33, -14], [32, 1, 2, 40, 20, -15, [5, 680, 322]]], [0, "f6ri3/00pMCrhxsYSHRV+N", 2, 0], [5, 680, 322]], [13, "titlebg", 512, 3, [-20, -21], [[5, 1, 0, -19, [7], 8]], [0, "f5ACCUqWZNpamBG1ar/RjP", 2, 0], [5, 680, 82], [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn", 1, [-24], [[22, 1, 0, -22, [17]], [30, 0.9, 3, -23, [[9, "65c107D+EdDHr71wNqPczVr", "onClickOpenGame", 1]]]], [1, "79u0tIgcpIyqeiaRwj0UJr", 1], [5, 185, 87], [192.926, 22.595, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "maskbg", 190, 2, [[20, 0, -25, [0], 1], [23, 45, 750, 1334, -26]], [0, "d43JuPMF5Oo6DkR4RaHTW8", 2, 0], [4, 4281542699], [5, 750, 1334]], [14, "title", 4, [[25, "礼包", 38, 50, false, 1, 1, 1, 2, 1, -27, [2]], [3, 2, -28, [4, 4278190080]]], [0, "5a00o02lFAXIaEpmt6nksW", 2, 0], [4, 4292279039], [5, 311.24, 67], [0, 2.417, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnClose", 4, [-31], [[29, 0.9, 3, -30, [[9, "349afQnI5BO8LA+vJl/psZA", "close", 2]], [4, 4293322470], [4, 3363338360], -29, 5, 6]], [0, "f26adaM2NN3IxTDLC9rJTs", 2, 0], [5, 62, 65], [287.866, -0.54, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Background", 8, [[7, -32, [3], 4]], [0, "c5AhxIb5tBi6zCcyN6G9rW", 2, 0], [5, 52, 56], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "layer", 3, [1], [[31, 1, 2, 20, -33, [5, 497, 160]]], [0, "baXpRGFtZB2qklxUne8+7X", 2, 0], [5, 497, 160], [0, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "title", 1, [[8, "礼包名称", 30, false, 1, 2, -34, [14]], [3, 3, -35, [4, 4278190080]]], [1, "95UKSMyRdN4KQ/DsefUpVm", 1], [5, 279, 56.4], [0, 0, 0.5], [-157.795, 34.173, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 5, [[26, "领取", 32, false, false, 1, 1, 1, -36, [16]], [3, 3, -37, [4, 4278190080]]], [1, "1e77MXPHFC9IgYd8O0ajst", 1], [5, 70, 56.4], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lastTime", 1, [[27, "距离过期还有", 20, false, 2, 1, -38, [18]], [3, 3, -39, [4, 4278190080]]], [1, "4a1QW5zfJAX4E6BfAvczBb", 1], [5, 126, 56.4], [194.54, -40.979, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg", 2, [3], [0, "f2Cua3yhlI6ZrQKo0+cZ/i", 2, 0], [5, 750, 1334]], [16, "bg", 1, [[5, 1, 0, -40, [9], 10]], [1, "3djLRR99VOZoFW/RkqTpsR", 1], [5, 630, 160]], [2, "tagbg", 1, [[7, -41, [11], 12]], [1, "8025YC8oRDUKQtAE7Q8b8T", 1], [5, 375, 48], [-90.54, 33.077, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 1, [[21, 2, -42, [13]]], [1, "2d0ABMc1VOiKgD1rEitrYn", 1], [5, 116, 116], [-232.324, 1.102, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lockTips", 1, [[8, "2025/2/15", 26, false, 1, 2, -43, [15]]], [1, "42B9cMzDlOJ4grdzQaiuy+", 1], [4, 4281940021], [5, 283.27, 50.4], [0, 0, 0.5], [-156.636, -13.802, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "New Node", 3, [-44], [0, "45bidDN1pHqbRLfEHuzsUP", 2, 0], [0, -121, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tips", 19, [[28, "点击空白区域可关闭", 25, 25, false, 1, 1, 2, 1, -45, [19]]], [0, "d0+ustH9xKfqM9buh/UJTh", 2, 0], [5, 485, 80], [0, -74.977, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -4, 11, 0, -5, 18, 0, -6, 5, 0, -7, 13, 0, 2, 2, 0, 0, 2, 0, -1, 6, 0, -2, 14, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, -2, 10, 0, -3, 19, 0, 0, 4, 0, -1, 7, 0, -2, 8, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 6, 9, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, -1, 20, 0, 0, 20, 0, 7, 2, 1, 3, 10, 3, 3, 14, 45], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, 4, 5, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, 1], [0, 2, 0, 0, 3, 4, 5, 0, 6, 0, 1, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 1]]