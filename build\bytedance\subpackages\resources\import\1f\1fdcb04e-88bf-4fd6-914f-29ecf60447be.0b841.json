[1, ["ecpdLyjvZBwrvm+cedCcQy", "cdvDZRLPZKb70zilA0TpQ8", "f7293wEF9JhIuMEsrLuqng", "39GqxMcF1KM7g8lk0ZpI4V", "f8npR6F8ZIZoCp3cKXjJQz", "4dMZmkcjlJEbhEI0rAtegJ", "e40u3avyZHWYWu7f8ivsQQ", "a2MjXRFdtLlYQ5ouAFv/+R"], ["node", "root", "_spriteFrame", "asset", "_N$file", "_normalMaterial", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 1, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.RichText", ["_enabled", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_normalMaterial"], 2, 1, 9, 5, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["bba63Aq939DuIrx5vwI3Ohy", ["node"], 3, 1]], [[2, 0, 1, 2, 2], [0, 0, 5, 2, 6, 2], [5, 0, 1, 2, 3, 3], [0, 0, 5, 3, 2, 8, 4, 9, 6, 2], [0, 0, 5, 3, 2, 4, 6, 2], [1, 1, 0, 2, 3, 4, 3], [3, 0, 1, 2, 3, 6, 4, 5, 7, 8, 9, 8], [4, 0, 2], [0, 0, 7, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 2], [0, 0, 5, 7, 3, 2, 4, 6, 2], [0, 0, 5, 7, 3, 2, 4, 9, 6, 2], [0, 0, 1, 5, 7, 3, 2, 8, 4, 3], [1, 2, 3, 4, 1], [1, 0, 2, 3, 4, 2], [2, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 7, 8, 9, 7], [6, 0, 1, 2, 3, 4, 5], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 2], [9, 0, 1, 2, 3], [10, 0, 1, 2, 3, 4, 4], [11, 0, 1], [12, 0, 1]], [[7, "SevenTaskItem"], [8, "SevenTaskItem", [-3, -4, -5, -6, -7, -8], [[23, -2]], [15, -1, 0], [5, 500, 154]], [11, "rewardNode", 1, [-10, -11, -12, -13], [[21, 1, 1, 5, -9, [5, 479, 128]]], [0, "912HcuixFOdJKp3XPPn5NN", 1, 0], [5, 479, 128], [0, 0, 0.5], [-233, -23.337, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [10, "button", 1, [-16], [[5, 1, 0, -14, [8], 9], [19, 3, -15, [[20, "bba63Aq939DuIrx5vwI3Ohy", "onClickItem", 1]], [4, 4286348412], 10]], [0, "a8dz6tYGxGk6GOFu76im4B", 1, 0], [5, 130, 58], [170.469, -29.95, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "dagouNode", 156, 1, [-19], [[14, 0, -17, [17], 18], [22, -18]], [0, "525SMmZmFJU4Dp6RIkv/WU", 1, 0], [4, 4278190080], [5, 500, 154]], [3, "lbCn", 1, [[6, "完成主线第46关", 24, 31, false, 1, 1, 1, -20, [2], 3], [17, false, "", 26, 26, -21]], [0, "faw9K1zJJAW5/1K2cCwiV2", 1, 0], [4, 4280562001], [5, 170.64, 39.06], [0, 0, 0.5], [-231.524, 43.071, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnLb", 3, [[16, "领取", 28, 28, false, 1, 1, -22, [6], 7], [18, 2, -23, [4, 4278190080]]], [0, "9dttdUcDVFRJpc06KIARfO", 1, 0], [5, 60, 39.28], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_jj_txz", 1, [[5, 1, 0, -24, [0], 1]], [0, "50aw/yollE8beRovLrrAzp", 1, 0], [5, 500, 154]], [3, "lbPro", 1, [[6, "(0/1)", 24, 31, false, 1, 1, 1, -25, [4], 5]], [0, "51ogEHsiFFN4IuyOAMmEEu", 1, 0], [4, 4280562001], [5, 52.27, 39.06], [0, 1, 0.5], [225, 42.429, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "RewardItem", 2, [2, "2bASLwK1NDL6pZvMKugC5l", true, -26, 11], [58, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "RewardItem", 2, [2, "03f05kNalMO6UJBfs8i9hV", true, -27, 12], [179, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "RewardItem", 2, [2, "2e+lztNSdNd7X2S+ieyUj2", true, -28, 13], [300, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "RewardItem", 2, [2, "c8Ct8GHvtHfZKZk2YElbs5", true, -29, 14], [421, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "icon_dagou", 4, [[13, -30, [15], 16]], [0, "81CnHGlvtJ2KN+RYq1zDPS", 1, 0], [5, 62, 49], [173.008, -28.965, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 1, 1, 0, 0, 1, 0, -1, 7, 0, -2, 5, 0, -3, 8, 0, -4, 3, 0, -5, 2, 0, -6, 4, 0, 0, 2, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, 0, 3, 0, 0, 3, 0, -1, 6, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 1, 9, 0, 1, 10, 0, 1, 11, 0, 1, 12, 0, 0, 13, 0, 6, 1, 30], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 4, -1, 4, -1, 4, -1, 2, 5, 3, 3, 3, 3, -1, 2, -1, 2], [0, 3, 0, 2, 0, 2, 0, 4, 0, 5, 0, 1, 1, 1, 1, 0, 6, 0, 7]]