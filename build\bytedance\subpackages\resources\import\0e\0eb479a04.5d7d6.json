[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "4a/DGKeh9MaLr8XamRVTAa", "92F+28yBpPZp8ppmnvDXHT", "c7XpkY91dIVoPhWa7BeKmN", "24MsuYyLlFpLMExvgKnqNw", "0c9nyjaDlFGJNgfnyEq+KV", "239PMBbZJIjKuX2Zp2kY+T", "a2MjXRFdtLlYQ5ouAFv/+R", "03HXNFoQ1K7rfy9/eeY5Wm", "dal4o32KpO75yexm9hALiM", "a0Ioi5dGVDnaiAbjhk6uIp", "65OUoZ25pGHqftEDT5VTS4", "ddFhwuPitMK6rcBkC3ddyJ", "4613FnL15GjKE6Ci5QeOnx", "deFEOle59AvpQNSLtdxntU", "8fH7ll0XxMb7PGxIFbOp/6"], ["node", "_spriteFrame", "_parent", "_textureSetter", "root", "checkMark", "_N$target", "_N$disabledSprite", "my<PERSON>rid<PERSON>iew", "lbPrice", "sanjao", "btnBg", "goodNode", "bg", "goodimg", "goodsbg", "Count", "data", "templete"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 9, 4, 5, 1, 2, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 0, 1, 2, 4, 5, 7, 2], "cc.SpriteFrame", ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_N$overflow", "_fontSize", "_styleFlags", "_lineHeight", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_left", "_right", "_top", "_originalHeight", "node"], -4, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$spacingY", "node", "_layoutSize"], -4, 1, 5], ["cc.Toggle", ["_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["968b2knTLlMPaOgGMGsQrEY", ["node", "nodeArr", "my<PERSON>rid<PERSON>iew"], 3, 1, 12, 1], ["95da9P4jEtNuoaRyFibMcnC", ["node", "bg", "goodNode", "btnBg", "sanjao", "lbPrice"], 3, 1, 1, 1, 1, 1, 1], ["588e9YJTR5JAoebMnGix+fw", ["node", "Count", "goodsbg", "goodimg"], 3, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "scrollview", "content"], 3, 1, 1, 1]], [[6, 0, 1, 2, 2], [6, 0, 1, 2], [19, 0, 1, 2, 2], [0, 0, 7, 8, 4, 5, 6, 9, 2], [0, 0, 7, 4, 5, 6, 9, 2], [1, 2, 0, 3, 4, 3], [7, 0, 1, 6, 7, 4], [0, 0, 8, 4, 5, 6, 9, 2], [0, 0, 7, 4, 5, 6, 2], [1, 3, 4, 5, 1], [10, 0, 1, 2, 3, 4], [4, 0, 5, 7, 1, 2, 10, 11, 6], [0, 0, 7, 8, 4, 5, 6, 2], [0, 0, 1, 7, 4, 5, 6, 9, 3], [5, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 2], [1, 0, 3, 4, 5, 2], [1, 3, 4, 1], [18, 0, 1, 2, 3, 4, 5, 2], [11, 0, 2], [0, 0, 2, 8, 4, 5, 6, 3], [0, 0, 8, 4, 5, 6, 2], [0, 0, 8, 4, 5, 6, 11, 9, 2], [0, 0, 1, 3, 7, 4, 5, 10, 6, 4], [0, 0, 7, 4, 5, 10, 6, 9, 2], [2, 0, 2, 3, 8, 4, 5, 6, 7, 3], [2, 0, 3, 4, 5, 6, 2], [2, 0, 1, 3, 4, 5, 6, 3], [2, 0, 3, 4, 5, 6, 7, 2], [2, 0, 1, 3, 4, 5, 6, 7, 3], [5, 0, 1, 6, 2, 3, 4, 5, 2], [12, 0, 1, 2, 1], [6, 1, 2, 1], [13, 0, 1, 2, 3, 4, 5, 1], [14, 0, 1, 2, 3, 1], [1, 1, 3, 4, 5, 2], [1, 0, 1, 3, 4, 5, 3], [1, 2, 0, 3, 4, 5, 3], [1, 1, 3, 4, 2], [1, 0, 1, 3, 4, 3], [1, 2, 0, 1, 3, 4, 4], [1, 0, 3, 4, 2], [7, 2, 0, 3, 4, 5, 1, 7, 7], [7, 0, 7, 2], [15, 0, 1], [16, 0, 1], [8, 0, 1, 2, 7, 8, 4], [8, 0, 1, 3, 4, 5, 2, 6, 7, 8, 8], [8, 0, 1, 7, 8, 3], [9, 1, 2, 3, 4, 5, 1], [9, 0, 1, 2, 3, 4, 5, 2], [10, 0, 1, 3, 3], [17, 0, 1, 2, 2], [4, 0, 3, 6, 1, 2, 4, 10, 11, 7], [4, 0, 5, 3, 1, 2, 4, 10, 11, 7], [4, 0, 8, 3, 1, 2, 4, 9, 10, 11, 8], [20, 0, 1, 2, 3, 4, 5, 6, 6], [21, 0, 1, 2, 1]], [[[{"name": "img_djsd", "rect": [0, 0, 66, 50], "offset": [0, 0], "originalSize": [66, 50], "capInsets": [16, 25, 16, 21]}], [3], 0, [0], [3], [4]], [[{"name": "img_fy02", "rect": [0, 0, 28, 28], "offset": [0, 0], "originalSize": [28, 28], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [5]], [[{"name": "img_fy01", "rect": [0, 0, 28, 28], "offset": [0, 0], "originalSize": [28, 28], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [6]], [[{"name": "img_qipao", "rect": [0, 0, 226, 119], "offset": [0, 0], "originalSize": [226, 119], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [7]], [[[19, "SuperRewardView"], [20, "SuperRewardView", 1, [-6, -7], [[31, -5, [[-3, -4, null, null, null], 1, 1, 0, 0, 0], -2]], [32, -1, 0], [5, 750, 1334]], [7, "SuperRewardItem", [-15, -16, -17, -18], [[33, -14, -13, -12, -11, -10, -9]], [1, "13r02FC4RF+Yolp+vL+r/g", -8], [5, 547, 229], [0, -199.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "goodItem", [-24, -25, -26, -27], [[34, -23, -22, -21, -20]], [1, "6eq/A/rfdIHoMWX0MM17+b", -19], [5, 116, 120], [0, 0, 0, 0, 0, 0, 1, 0.75, 0.75, 0.75]], [7, "title_zhua<PERSON><PERSON>", [-30, -31, -32, -33, -34, -35], [[5, 1, 0, -28, [15]], [42, 0, 41, 20, 20, -290.375, 664, -29]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 582, 328], [0, 570.875, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "content", [4, -38, -39, -40], [[5, 1, 0, -36, [34]], [44, -37]], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 622, 889]], [3, "New ToggleContainer", 5, [-43, -44], [[45, -41], [46, 1, 1, 30, -42, [5, 86, 61]]], [0, "20z0RzyJxFP6pPBMvDHMwd", 1, 0], [5, 86, 61], [0, -485.547, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "content", [2], [[47, 1, 2, 10, 10, 85, 18, 10, -45, [5, 590, 314]]], [0, "92xA5QO05D1ZM+JWAH7QBw", 1, 0], [5, 590, 314], [0, 0.5, 1], [0, 435, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg", 1, [5], [[6, 45, 750, 1334, -46]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [3, "img_qipao", 4, [-48, -49], [[9, -47, [10], 11]], [0, "bc9qOFNylBfJOxo2j09yeu", 1, 0], [5, 226, 119], [196.343, 109.149, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "toggle1", 6, [-53, -54], [[49, -52, [4, 4292269782], -51, -50, [[10, "968b2knTLlMPaOgGMGsQrEY", "onToggle", "1", 1]]]], [0, "80HEGm5cZLyoXavmkvi3Sm", 1, 0], [5, 28, 28], [-29, 12.371, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [3, "toggle2", 6, [-58, -59], [[50, false, -57, [4, 4292269782], -56, -55, [[10, "968b2knTLlMPaOgGMGsQrEY", "onToggle", "2", 1]]]], [0, "ef9mA/3JRJ2rQR/z9QFwSj", 1, 0], [5, 28, 28], [29, 16.471, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [25, "New ScrollView", 512, 5, [-62], [-60, -61], [0, "c9CVsCyK1IlKTrQHxnuzI1", 1, 0], [5, 640, 700], [0, -36.657, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 12, [7], [[52, 0, -63, [33]], [6, 45, 240, 250, -64]], [0, "7cOdfaa0hNSpG21Oio1dm4", 1, 0], [5, 640, 700]], [3, "goodList", 2, [3], [[48, 1, 1, -65, [5, 116, 100]]], [1, "22XCiXuGFHiZyH5SttuWSe", 2], [5, 116, 100], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btn_next", 2, [-68], [[[18, 3, -66, [[10, "95da9P4jEtNuoaRyFibMcnC", "onClickItem", "1", 2]], [4, 4293322470], [4, 3363338360], 31], -67], 4, 1], [1, "e6ElpFd0pFvbH5h3hbhHb0", 2], [5, 248, 100], [0, -59.219, 0, 0, 0, 0, 1, 0.75, 0.75, 0]], [23, "maskbg", false, 20, 1, [[43, 45, -69], [16, 0, -70, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [8, "Label_title", 4, [[53, "玩法合集", false, 1, 1, 1, 2, -71, [2]], [2, 3, -72, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 203, 56.4]], [4, "time", 4, [[11, "17:00:30", 30, 30, 1, 1, -73, [5]], [2, 3, -74, [4, 4279966491]]], [0, "c2KZey+rJOrLNVgoWg8a8e", 1, 0], [5, 122.78, 43.8], [0, -196.352, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "precent", 9, [[11, "1500%", 66, 66, 1, 1, -75, [8]], [2, 5, -76, [4, 4280358590]]], [0, "2eJy7unW1MsqlZkYG4mfnU", 1, 0], [4, 4284547071], [5, 215.51, 93.16], [0, 56.62, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 9, [[11, "回馈", 34, 34, 1, 1, -77, [9]], [2, 3, -78, [4, 4278190080]]], [0, "1eChZfpAdDCJIVNZ+4FVLm", 1, 0], [5, 74, 48.84], [0, 7.163, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnclose", 4, [[18, 3, -79, [[51, "968b2knTLlMPaOgGMGsQrEY", "close", 1]], [4, 4293322470], [4, 3363338360], 12], [16, 2, -80, [13], 14]], [0, "e7EyHwTS1Acpf3U1W6GzKg", 1, 0], [5, 52, 56], [264.467, -193.742, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "Background", 10, [[35, false, -81, [16], 17]], [0, "36PttaRelI54v/2HFzBjLU", 1, 0], [5, 28, 28]], [8, "Background", 11, [[36, 2, false, -82, [19], 20]], [0, "4ckyrVFLxBj5B1gVd7/m5J", 1, 0], [5, 28, 28]], [14, "bg", 2, [[-83, [6, 45, 116, 120, -84]], 1, 4], [1, "a3lph4JQ9Kc7Wv2gjJpFZT", 2], [5, 547, 229]], [14, "bg", 3, [[-85, [6, 45, 116, 120, -86]], 1, 4], [1, "a3lph4JQ9Kc7Wv2gjJpFZT", 3], [5, 116, 120]], [15, "New Label", 3, [[-87, [2, 2, -88, [4, 4278190080]]], 1, 4], [1, "168nscGDFOFLCeNsfJoSlR", 3], [5, 70.53, 56.4], [22.161, -32.235, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Label", 15, [[-89, [2, 3, -90, [4, 3573547008]]], 1, 4], [1, "8fMkoT+2xPc4llMHvZcEUv", 2], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_djsd", 4, [[37, 1, 0, -91, [3], 4]], [0, "70eg6Q2xVJW5DptTgI+fUm", 1, 0], [5, 239, 50], [0, -196.352, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "icon_clock", 4, [[9, -92, [6], 7]], [0, "63kGfha79MkY0+z6s8N5pi", 1, 0], [5, 43, 59], [-122.713, -193.217, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "checkmark", 10, [-93], [0, "68jK3IiTFF2ZnlNrJPLAbr", 1, 0], [5, 28, 28]], [38, false, 30, [18]], [27, "checkmark", false, 11, [-94], [0, "702EAdVg9HNa3m2rhZSdva", 1, 0], [5, 28, 28]], [39, 2, false, 32, [21]], [13, "bg", false, 5, [[5, 1, 0, -95, [22]]], [0, "5coDMvVoBIipw1rHzKjXNZ", 1, 0], [5, 590, 870], [0, -34.095, 0, 0, 0, 0, 1, 1, 1, 1]], [40, 1, 0, false, 24, [23]], [41, 0, 25, [24]], [28, "img", 3, [-96], [1, "c8TQPIne1PvJf+EhL/y/tz", 3], [5, 90, 90], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [17, 37, [25]], [54, "x40", 30, false, 2, 1, 2, 26, [26]], [13, "icon_pintu", false, 3, [[9, -97, [27], 28]], [1, "81jE12cbtAJaj7tyX8R5Ym", 3], [5, 35, 35], [51.182, 48.061, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "img_jiant", false, 2, [-98], [1, "99plbyRFZFD7b5/eyB6d2L", 2], [5, 61, 40], [0, 120.133, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 41, [29]], [55, "18元", false, false, 1, 1, 2, 1, 27, [30]], [5, 1, 0, 15, [32]], [56, false, 0.75, 0.23, null, null, 12, 7], [57, 12, 45, 7]], 0, [0, 4, 1, 0, 8, 46, 0, -1, 8, 0, -2, 4, 0, 0, 1, 0, -1, 16, 0, -2, 8, 0, 4, 2, 0, 9, 43, 0, 10, 42, 0, 11, 44, 0, 12, 14, 0, 13, 35, 0, 0, 2, 0, -1, 24, 0, -2, 14, 0, -3, 41, 0, -4, 15, 0, 4, 3, 0, 14, 38, 0, 15, 36, 0, 16, 39, 0, 0, 3, 0, -1, 25, 0, -2, 37, 0, -3, 26, 0, -4, 40, 0, 0, 4, 0, 0, 4, 0, -1, 17, 0, -2, 28, 0, -3, 18, 0, -4, 29, 0, -5, 9, 0, -6, 21, 0, 0, 5, 0, 0, 5, 0, -2, 6, 0, -3, 34, 0, -4, 12, 0, 0, 6, 0, 0, 6, 0, -1, 10, 0, -2, 11, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, -1, 19, 0, -2, 20, 0, 5, 31, 0, 6, 22, 0, 0, 10, 0, -1, 22, 0, -2, 30, 0, 5, 33, 0, 6, 23, 0, 0, 11, 0, -1, 23, 0, -2, 32, 0, -1, 45, 0, -2, 46, 0, -1, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, -2, 44, 0, -1, 27, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, -1, 35, 0, 0, 24, 0, -1, 36, 0, 0, 25, 0, -1, 39, 0, 0, 26, 0, -1, 43, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 31, 0, -1, 33, 0, 0, 34, 0, -1, 38, 0, 0, 40, 0, -1, 42, 0, 17, 1, 2, 2, 7, 3, 2, 14, 4, 2, 5, 5, 2, 8, 7, 2, 13, 98], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 33, 36, 42, 46], [-1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, 7, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 7, -1, -1, -1, 1, 1, 1, 1, 18], [0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 0, 11, 1, 0, 12, 0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 13, 0, 0, 1, 0, 0, 0, 3, 3, 14, 15, 16]]]]