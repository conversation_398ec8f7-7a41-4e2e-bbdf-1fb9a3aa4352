let moduleMap = {
'src/assets/start/plugins/cc_extension.js' () { return require('src/assets/start/plugins/cc_extension.js') },
'assets/internal/index.js' () { return require('assets/internal/index.js') },
// tail
};

window.__cocos_require__ = function (moduleName) {
    let func = moduleMap[moduleName];
    if (!func) {
        throw new Error(`cannot find module ${moduleName}`);
    }
    return func();
};