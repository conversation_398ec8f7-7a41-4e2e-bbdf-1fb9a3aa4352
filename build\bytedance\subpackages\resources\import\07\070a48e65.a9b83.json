[1, ["ecpdLyjvZBwrvm+cedCcQy", "b9bHY0AXBPqJng2OM01wMf", "e9x6woHBVGvqLoo4y1tACJ", "41FA0uKdpIHrvZ83VfstiM", "4deokoIzxIn47ebcFPV9Z2", "b2FDPfCDVHFrkdOxQ3fLd+", "b5vFUZKKtLnbIw24gk7Pwr", "70EuzbQhJCm6WO2Suxm270", "de5PM4iGxBbJ6lT1viTvpD", "38ZJVXhOVCgZC28hIXUY9h", "4bqGhwk55LGqOFi61xHow+", "55pYI/QypGe6xoPJ0nlwaW", "09qnIXHcxBK5EhUvUD7Syl"], ["node", "_spriteFrame", "_textureSetter", "root", "_parent", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint", "_eulerAngles"], 0, 4, 5, 1, 9, 7, 12, 5, 5, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_prefab"], 2, 1, 2, 4]], [[2, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 3], [0, 0, 1, 5, 6, 3, 4, 10, 7, 3], [0, 0, 1, 5, 6, 3, 4, 7, 3], [0, 0, 5, 6, 3, 4, 7, 11, 2], [4, 0, 2], [0, 0, 1, 8, 3, 4, 3], [0, 0, 5, 3, 7, 2], [0, 0, 5, 6, 3, 9, 4, 2], [0, 0, 2, 5, 6, 3, 4, 7, 3], [0, 0, 2, 5, 6, 3, 4, 3], [0, 0, 5, 6, 3, 4, 2], [0, 0, 5, 6, 3, 4, 7, 2], [5, 0, 1, 2, 3, 2], [2, 1, 2, 1], [3, 2, 3, 4, 1]], [[[{"name": "base2", "rect": [0, 0, 166, 166], "offset": [0, 0], "originalSize": [166, 166], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[{"name": "greywall01", "rect": [0, 0, 183, 199], "offset": [0, 0], "originalSize": [183, 199], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [4]], [[{"name": "greywall02", "rect": [0, 0, 183, 199], "offset": [0, 0], "originalSize": [183, 199], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "greywall04", "rect": [0, 0, 397, 397], "offset": [0, 0], "originalSize": [397, 397], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[{"name": "wood", "rect": [0, 0, 140, 89], "offset": [0, 0], "originalSize": [140, 89], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[{"name": "greywall05", "rect": [0, 0, 122, 44], "offset": [0, 0], "originalSize": [122, 44], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[[5, "map4"], [6, "map1", 2, [[-2, -3, -4, -5, -6, -7, [7, "pet", -9, [0, "877i+RI+JORL4Ag3QxOIMI", -8, 0], [375.224, 1117.602, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 1, 1, 1, 1, 1, 4], [14, -1, 0], [5, 1153, 2052]], [13, "New Node", 1, [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22], [0, "397yQf5AtNRazjmmHOvbsW", 1, 0]], [8, "wall_bg", 1, [[1, 2, 0, -23, [0], 1]], [0, "bakdNpLnBKNJhwQmwGSA3B", 1, 0], [4, 4290690750], [5, 3000, 3000]], [9, "wall_bg", false, 1, [[1, 2, 0, -24, [2], 3]], [0, "7cAZMVL/BL0ZzaLHnUBIDu", 1, 0], [5, 500, 500], [0, 450, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "wall_middle", false, 2, [[1, 1, 0, -25, [4], 5]], [0, "f0juwFfvhM8a/iqO+/mU/1", 1, 0], [5, 535, 350]], [11, "base", 2, [[15, -26, [6], 7]], [0, "5d0DqmHtBL8LpYIs1HAM7O", 1, 0], [5, 166, 166]], [3, "wall copy", 8, 2, [[1, 2, 0, -27, [8], 9]], [0, "80J+4MlBdIyrF/yv5RfTba", 1, 0], [5, 1330, 89], [0, 882.425, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -28, [10], 11]], [0, "e24LwtIiZFBYYJQNn++mFU", 1, 0], [5, 420, 89], [0, 0.5, 1], [-462.167, 363.274, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -29, [12], 13]], [0, "2az1rfTaJJkrahQAmZNNpn", 1, 0], [5, 420, 89], [0, 0.5, 1], [443.828, 363.274, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -30, [14], 15]], [0, "c9TPPE85JNW4IlpjBAUpHf", 1, 0], [5, 350, 89], [0, 0.5, 1], [-250, 49.865, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -31, [16], 17]], [0, "fe0UyBhRJNpo3OUC89Dxcm", 1, 0], [5, 840, 89], [0, 0.5, 1], [0, -580.788, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -32, [18], 19]], [0, "93sdxeFZ1IO7OJ8TY+jhc3", 1, 0], [5, 490, 89], [0, 0.5, 1], [-410.883, -883.295, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -33, [20], 21]], [0, "30cneDishO/6SMrf40Jd8F", 1, 0], [5, 490, 89], [0, 0.5, 1], [410.86, -883.295, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -34, [22], 23]], [0, "a9SeBNdhhJ6KGESodCsFnU", 1, 0], [5, 490, 89], [0, 0.5, 1], [-417.69, -250.544, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -35, [24], 25]], [0, "62hpfkteBHC4y3dCfPcMgk", 1, 0], [5, 490, 89], [0, 0.5, 1], [418.186, -250.544, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall copy", 8, 2, [[1, 2, 0, -36, [26], 27]], [0, "194MCpfTBOspkutaOwMo6m", 1, 0], [5, 350, 89], [0, 0.5, 1], [250, 49.865, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "wall copy", 8, 2, [[1, 2, 0, -37, [28], 29]], [0, "49PeeiSjxKBI81kQvYq0na", 1, 0], [5, 840, 89], [0, 585.713, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "wall_top", 1, [[1, 2, 0, -38, [30], 31]], [0, "ddun0UnwRA14bCyiEvZNUl", 1, 0], [5, 1500, 44], [0, 1200, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "wall_left", 1, [[1, 2, 0, -39, [32], 33]], [0, "ef3We0ot9MOoZ53OaN8OJ8", 1, 0], [5, 3000, 44], [-680, 500, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [4, "wall_right", 1, [[1, 2, 0, -40, [34], 35]], [0, "fedo8kkzlPKLI0+e3aKbUH", 1, 0], [5, 3000, 44], [680, 500, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]]], 0, [0, 3, 1, 0, -1, 3, 0, -2, 4, 0, -3, 2, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, 3, 1, 0, 4, 1, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, -10, 14, 0, -11, 15, 0, -12, 16, 0, -13, 17, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 5, 1, 40], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 9, 0, 10, 0, 11, 0, 12, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2]]]]