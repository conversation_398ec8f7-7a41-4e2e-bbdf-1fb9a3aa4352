[1, ["ecpdLyjvZBwrvm+cedCcQy", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO"], ["node", "_spriteFrame", "_N$normalSprite", "_N$disabledSprite", "root", "data", "_parent"], [["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], -2, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["9d48by4cnRBB4TmNeg5RTAE", ["UItype", "node"], 2, 1]], [[2, 0, 1, 2, 2], [0, 0, 6, 8, 3, 4, 5, 7, 2], [0, 0, 6, 3, 4, 5, 7, 2], [0, 0, 6, 3, 4, 5, 9, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6], [3, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 6, 3, 4, 5, 2], [0, 0, 8, 3, 4, 5, 9, 7, 2], [0, 0, 2, 6, 3, 4, 5, 7, 3], [1, 0, 4, 2], [1, 0, 1, 4, 3], [1, 0, 2, 4, 3], [1, 0, 1, 2, 3, 4, 5], [2, 1, 2, 1], [4, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 6, 2], [6, 0, 1, 2, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 7], [9, 0, 1, 2, 2], [10, 0, 1, 2]], [[5, "M31_FightUIView"], [6, "M30_FightUIView", 1, [-3, -4], [[20, 0, -2]], [14, -1, 0], [5, 750, 1334]], [8, "topUI", [-6, -7, -8, -9], [[11, 41, -47.60900000000004, -5]], [0, "8bkbxxodpNcpnBOLn5m5Hf", 1, 0], [5, 750, 100], [0, 0.5, 1], [0, 654.609, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 1, [2, -11], [[13, 45, 120, 750, 1334, -10]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1214], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnpause", 2, [-13], [[16, 3, -12, [[17, "9d48by4cnRBB4TmNeg5RTAE", "pauseGame", 1]], [4, 4293322470], [4, 3363338360], 2, 3]], [0, "dfUReHEtdNp6p7DWejBiVU", 1, 0], [5, 88, 93], [-297.339, -61.426, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "level", 2, [[18, "当前第%d关", 23, false, 1, 1, 1, -14, [4]], [19, 3, -15, [4, 4278190080]]], [0, "56j9ZBSM5MY5ixk3smhCgR", 1, 0], [5, 132.5, 56.4], [0, -34.736, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "maskbg", 1, [[10, 45, -16]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [2, "tpdg_icon_zanting", 4, [[15, -17, [0], 1]], [0, "41NQUbmGdJHJr6qSEvZucS", 1, 0], [5, 66, 65], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [9, "New RichText", false, 2, [[4, false, "<b><outline color=black width=2>通关第2关,从主界面的玩法合集\n进入<color=#00ff00>让子弹飞</c>玩法</outline></b>", 1, 23, 30, -18]], [0, "0f7lHm/95Kd5sT02HRuUYl", 1, 0], [5, 321.15, 67.8], [0, -74.472, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "diff", 2, [[4, false, "<b><outline color=black width=3>当前难度:<color=#00ff00>普通</c>", 1, 23, 32, -19]], [0, "74Vk+hMpVFIqJn4fPqZ8vW", 1, 0], [5, 157.66, 40.32], [0, 0.5, 1], [0, -51.182, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bottonUI", 3, [[12, 44, 600, -20]], [0, "f3xMePUptLpI+sbMIURS5n", 1, 0], [5, 750, 140], [0, 0.5, 0], [0, -607, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 6, 0, -2, 3, 0, 0, 2, 0, -1, 4, 0, -2, 8, 0, -3, 5, 0, -4, 9, 0, 0, 3, 0, -2, 10, 0, 0, 4, 0, -1, 7, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 5, 1, 2, 6, 3, 20], [0, 0, 0, 0, 0], [-1, 1, 2, 3, -1], [0, 1, 1, 2, 0]]