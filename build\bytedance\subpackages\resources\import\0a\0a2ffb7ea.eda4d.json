[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "7bzjAO6HtC94AIMh6mNkgH", "cdvDZRLPZKb70zilA0TpQ8", "dal4o32KpO75yexm9hALiM", "f3p84uzd5EVLXvLuhlyoRY", "9fUicDpb9ESYDEBfH7jjiH", "40OtPssnZP9antMAWsDtDT", "bfG2tqeBlJ740LsHarUTor", "8avhroiOFC9YVheq5S2kAK", "e2VHprIDlCQre6wlyQ42yl", "11gir1rxlPS5YZ5oCdPbOq", "f8npR6F8ZIZoCp3cKXjJQz", "f1DqLz8+JJb4uEhMRxUAE9", "01I8hzLhpGcrckZtrknAF8", "dc6RQNNqJDV57aX8ufoXJ8", "64X0E40SpNHpDAsB/BcRaH", "d6UnZhBp1O1K26Zd7td8Z+", "a8lPnHTihBmrRgij+r2Olk", "dbJA515+ZKrLUEcWj/2yVW", "e5QzRziBlI9oFwgvWR8QvT", "1a6+41TA1BDp5iYZdqHdCI", "91T0E3hMBDf6CLxbYbDoII"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$file", "asset", "_parent", "buyButton", "data", "_N$font", "_normalMaterial"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_anchorPoint", "_color"], 1, 4, 5, 1, 9, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$verticalAlign", "_isSystemFontUsed", "_N$horizontalAlign", "_N$fontFamily", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$horizontalDirection", "node", "_layoutSize"], -1, 1, 5], ["cc.RichText", ["_N$string", "_N$fontSize", "_N$lineHeight", "_enabled", "_isSystemFontUsed", "node", "_N$font"], -2, 1, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_normalMaterial"], 2, 1, 9, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["660eclSZ/FIvKS0WqemRakP", ["node", "buyButton"], 3, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["48d8djdjTJBH4k8s5IfoHgR", ["node"], 3, 1]], [[4, 0, 1, 2, 2], [0, 0, 4, 5, 2, 3, 6, 2], [2, 2, 3, 4, 1], [5, 0, 1, 2, 4, 5, 4], [13, 0, 1, 2, 2], [4, 0, 1, 2], [0, 0, 4, 7, 5, 2, 3, 8, 6, 2], [2, 0, 1, 2, 3, 4, 3], [6, 0, 1, 2, 5, 4], [0, 0, 4, 7, 5, 2, 3, 6, 2], [0, 0, 4, 5, 2, 3, 2], [0, 0, 4, 2, 6, 2], [11, 0, 1, 2, 3, 3], [3, 0, 1, 2, 3, 6, 4, 8, 9, 7], [0, 0, 7, 5, 2, 3, 2], [5, 0, 1, 2, 3, 4, 5, 5], [2, 1, 2, 3, 4, 2], [0, 0, 1, 4, 7, 5, 2, 3, 6, 3], [0, 0, 4, 5, 2, 3, 8, 6, 2], [0, 0, 4, 7, 2, 3, 6, 2], [0, 0, 4, 5, 2, 9, 3, 6, 2], [12, 0, 1, 2, 3], [6, 3, 0, 1, 2, 5, 5], [3, 0, 1, 2, 5, 3, 8, 9, 10, 6], [3, 0, 1, 2, 5, 3, 6, 4, 8, 9, 10, 8], [8, 0, 2], [0, 0, 7, 5, 2, 3, 8, 6, 2], [0, 0, 1, 4, 7, 5, 2, 3, 3], [0, 0, 4, 7, 5, 2, 3, 2], [0, 0, 4, 7, 2, 6, 2], [0, 0, 1, 4, 5, 2, 3, 6, 3], [9, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 1], [4, 1, 2, 1], [5, 0, 1, 4, 5, 3], [2, 0, 1, 2, 3, 3], [2, 0, 2, 3, 4, 2], [7, 1, 2, 1], [7, 0, 1, 2, 3, 2], [6, 4, 0, 1, 2, 5, 6, 5], [3, 0, 1, 2, 5, 3, 4, 8, 9, 10, 7], [3, 0, 1, 2, 3, 6, 4, 7, 8, 9, 8], [14, 0, 1]], [[[{"name": "vip", "rect": [0, 0, 192, 213], "offset": [0, 0], "originalSize": [192, 213], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[{"name": "bg_,main_stage", "rect": [0, 0, 498, 274], "offset": [0, 0], "originalSize": [498, 274], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [9]], [[[25, "MonthCardBigItem"], [14, "MonthCardBigItem", [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[32, -3, -2]], [33, -1, 0], [5, 678, 398]], [31, "BuyButtonByType", 1, [-18, -19], [-17], [5, "a2cYJHliJNb4QjnmV7ZHs6", -16], [5, 215, 96], [180, -142, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "desNode", 1, [-21, -22, -23, -24, -25], [[34, 1, 2, -20, [5, 0, 150]]], [0, "a0IuDebjpM4ZlOF5eUrbRB", 1, 0], [5, 0, 150], [0, 0.5, 1], [-307.058, 63.012, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "rewardNode", [-27, -28, -29, -30], [[3, 1, 1, 5, -26, [5, 479, 128]]], [0, "2bnk7B+xlPAZKj5spZbOy0", 1, 0], [5, 479, 128], [0, 0, 0.5], [110, 5, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [9, "img_qpl", 1, [-33, -34], [[2, -31, [39], 40], [37, -32, [[21, "660eclSZ/FIvKS0WqemRakP", "onClickGet", 1]]]], [0, "e4fZjn6OBC06/H5qdVNfpc", 1, 0], [5, 249, 125], [124.676, 163.562, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "layout", [-36, -37, -38], [[3, 1, 1, 8, -35, [5, 173, 40]]], [5, "d2xm1ADOJAlYmFk6uhm73x", 2], [5, 173, 40]], [14, "tlNode", [-40, -41], [[3, 1, 1, 2, -39, [5, 256, 200]]], [0, "c3DVpPAoNMbZqDb/cH+AF5", 1, 0], [5, 256, 200]], [6, "lbNode", 3, [-43, -44], [[3, 1, 1, 5, -42, [5, 186.56, 30]]], [0, "96wwlSMHZHzrrJJXvumk/0", 1, 0], [5, 186.56, 30], [0, 0, 0.5], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbNode", 3, [-46, -47], [[3, 1, 1, 5, -45, [5, 186.56, 30]]], [0, "7ffnL0KQ1OLIpzqAClp4l/", 1, 0], [5, 186.56, 30], [0, 0, 0.5], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbNode", 3, [-49, -50], [[3, 1, 1, 5, -48, [5, 186.56, 30]]], [0, "48ayqIXL9P+b6KLqed9wAI", 1, 0], [5, 186.56, 30], [0, 0, 0.5], [0, -75, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbNode", 3, [-52, -53], [[3, 1, 1, 5, -51, [5, 186.56, 30]]], [0, "86ew1Y+AZDCpFg0QmZ/ZX5", 1, 0], [5, 186.56, 30], [0, 0, 0.5], [0, -105, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbNode", 3, [-55, -56], [[3, 1, 1, 5, -54, [5, 186.56, 30]]], [0, "a0iP60J8FFwZqbzlI2VITE", 1, 0], [5, 186.56, 30], [0, 0, 0.5], [0, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "nodeRe", 1, [-58, 4], [[3, 1, 1, 10, -57, [5, 589, 36.24]]], [0, "7aBm+xsO5HBoveGHvOHpV9", 1, 0], [5, 589, 36.24], [0, 0, 0.5], [-304.898, -144.736, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDayGet", 13, [[22, false, "<outline color=black width=4><color=#ffffff><b>立即领取</b></c></outline>", 24, 24, -59], [23, "每日领取", 24, 24, false, 1, -60, [21], 22], [4, 2, -61, [4, 4278190080]]], [0, "43mZ4QxFJOopN7+GDWxj6k", 1, 0], [5, 100, 34.239999999999995], [50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbGet", 5, [[22, false, "<outline color=black width=4><color=#ffffff><b>立即领取</b></c></outline>", 24, 24, -62], [23, "立即领取", 24, 24, false, 1, -63, [31], 32], [4, 2, -64, [4, 4278190080]]], [0, "0fF/zp4oRMwoblk2bNizDt", 1, 0], [5, 100, 34.239999999999995], [0, 36.365, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "getNode", 5, [-66, -67], [[3, 1, 1, 30, -65, [5, 110, 0]]], [0, "cbbDHH8qdD7rkJ0y0PLYJn", 1, 0], [5, 110, 0], [0, 0.5, 0.52], [0, -8.571, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "countTimeNode", false, 1, [-69, -70], [[15, 1, 1, 5, 1, -68, [5, 155.07999999999998, 50]]], [0, "01ToNWl8NAU5lH2/HKoah6", 1, 0], [5, 155.07999999999998, 50], [197, -138.278, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btn", 2, [6], [[7, 1, 0, -71, [54], 55], [38, 3, -72, [[21, "660eclSZ/FIvKS0WqemRakP", "onClickBuy", 1]], 56]], [5, "c3UP7LGPZPFpR5z+aREDlU", 2], [5, 215, 86], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [27, "countTimeNode", false, 2, [-74, -75], [[15, 1, 1, 5, 1, -73, [5, 155.07999999999998, 50]]], [5, "d5tmMyJYxLu6B1hfEwQQqb", 2], [5, 155.07999999999998, 50]], [17, "btnHaveGet", false, 1, [-77, -78], [[35, 1, 0, -76, [65]]], [0, "e02GlEqRpEfKEpLiRUEyts", 1, 0], [5, 215, 86], [180, -142, 0, 0, 0, 0, 1, 1, 1, 0]], [28, "countTimeNode", 20, [-80, -81], [[15, 1, 1, 5, 1, -79, [5, 171.6, 86]]], [0, "50Paq8PCJMEoH83F+K2zZc", 1, 0], [5, 171.6, 86]], [9, "img_bq03", 1, [7], [[7, 1, 0, -82, [9], 10]], [0, "73czH1U31ImI722ohxpZ6x", 1, 0], [5, 334, 47], [-180, 87, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbLv", 7, [[40, "激活后解锁以下特权", 28, 28, false, 1, 1, -83, [7], 8], [4, 2, -84, [4, 4278190080]]], [0, "609Q8maZlEs5Q4SyeE00bJ", 1, 0], [5, 256, 39.28], [0, 0, 0.5], [-128, -0.58, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "card_icon", 1, [-85, -86], [0, "4fNVAyN1NAbLbdI8ik4GND", 1, 0], [186, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "iconNode", 16, [-87, -88], [0, "f1b0ZZfbVJKblYSuerZDX+", 1, 0], [5, 40, 40], [-35, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "lbNum", 25, [[13, "9000", 25, 20, 1, 1, 1, -89, [35]], [4, 2, -90, [4, 4280361249]]], [0, "771LYbuktMo5eSZqsVq31W", 1, 0], [4, 4279304191], [5, 59.62, 29.2], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "iconNode", 16, [-91, -92], [0, "9eoMlWSxtFoL+QMl9CM1IP", 1, 0], [5, 40, 40], [35, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "lbNum", 27, [[13, "9000", 25, 20, 1, 1, 1, -93, [38]], [4, 2, -94, [4, 4280361249]]], [0, "bdPY9DuFhMTpWaMlw5exlk", 1, 0], [4, 4279304191], [5, 59.62, 29.2], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_wsxhd", 1, [-96], [[7, 1, 0, -95, [43], 44]], [0, "f3/cmEndRP6YsrP6ybiZSA", 1, 0], [5, 144, 38], [180, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "New Label", 29, [[24, "未生效", 28, 28, false, 1, 1, 1, -97, [41], 42], [4, 2, -98, [4, 4280427042]]], [0, "e6AyEqGBlCt5DUEf0ie66P", 1, 0], [5, 88, 39.28]], [1, "lbTime", 17, [[13, "00:00:00", 28, 31, 1, 1, 1, -99, [45]], [4, 3, -100, [4, 4278190080]]], [0, "65yLRcA2tGYrEwgu+urz9i", 1, 0], [5, 118.08, 45.06], [18.499999999999993, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbPrice", 6, [[24, "免费", 28, 28, false, 1, 1, 1, -101, [52], 53], [4, 2, -102, [4, 4280427042]]], [5, "bbAl/jK5BODYmKbvlhguNV", 2], [5, 60, 39.28], [56.5, 0.902, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 19, [[13, "00:00:00", 28, 31, 1, 1, 1, -103, [57]], [4, 3, -104, [4, 4278190080]]], [5, "81VXyEtIBJ1ZbZwlasw6uH", 2], [5, 118.08, 45.06], [18.499999999999993, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 21, [[41, "00:00:00", 28, 28, 1, 1, 1, "Consola", -105, [62]], [4, 2, -106, [4, 4278190080]]], [0, "79QpU3QiVF2qXK/G2TX4N/", 1, 0], [5, 123.6, 39.28], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "img_bg", 1, [[7, 1, 0, -107, [0], 1]], [0, "6euhnfvGNOfLxpt2gS7N9O", 1, 0], [5, 678, 398]], [1, "bg_,main_stage", 1, [[16, 0, -108, [2], 3]], [0, "00iBUhpxRNW45BlZ5kyG4p", 1, 0], [5, 282, 172], [-246.216, 148.82, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbTitle", 1, [[39, false, "<outline color=black width=3><color=#ffffff><b>月卡</b></color></outline>", 48, 48, -109, 4]], [0, "ccST5oV2pKQJce5WZPmW3r", 1, 0], [5, 102, 60.480000000000004], [0, 0, 0.5], [-300, 146.698, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "icon_mggtb", false, 7, [[2, -110, [5], 6]], [0, "d86DNFl0VGFozG9Rep0cVb", 1, 0], [5, 50, 52], [-129, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_lx", 8, [[2, -111, [11], 12]], [0, "2daS6R4bJGG7bsnvCq2maG", 1, 0], [5, 12, 12], [6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 8, [[8, "<outline color=black width=2><color=#ffffff><b>立即领取 立即领取</b></c></outline>", 20, 30, -112]], [0, "eau0vv6y5D8a/W+J1A4CEo", 1, 0], [5, 169.56, 37.8], [101.78, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_lx", 9, [[2, -113, [13], 14]], [0, "b163oQ65VC5plDLAH9iBeK", 1, 0], [5, 12, 12], [6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 9, [[8, "<outline color=black width=2><color=#ffffff><b>立即领取 立即领取</b></c></outline>", 20, 30, -114]], [0, "cfrYXx7LdHw7ShiIyeC7mr", 1, 0], [5, 169.56, 37.8], [101.78, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_lx", 10, [[2, -115, [15], 16]], [0, "dehSx0ZWRCmY1N1JL4MdFZ", 1, 0], [5, 12, 12], [6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 10, [[8, "<outline color=black width=2><color=#ffffff><b>立即领取 立即领取</b></c></outline>", 20, 30, -116]], [0, "bc0xrKr9lLYp+AiifBKsjJ", 1, 0], [5, 169.56, 37.8], [101.78, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_lx", 11, [[2, -117, [17], 18]], [0, "59AcT0hWRCWK5KJBXTLs5t", 1, 0], [5, 12, 12], [6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 11, [[8, "<outline color=black width=2><color=#ffffff><b>立即领取 立即领取</b></c></outline>", 20, 30, -118]], [0, "78V6dZjbJE5JGcKFbCuFuQ", 1, 0], [5, 169.56, 37.8], [101.78, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_lx", 12, [[2, -119, [19], 20]], [0, "43ZNqG4gFClrZr7fbhXdRl", 1, 0], [5, 12, 12], [6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 12, [[8, "<outline color=black width=2><color=#ffffff><b>立即领取 立即领取</b></c></outline>", 20, 30, -120]], [0, "a4dEMyZHJLsZuI17mHS30V", 1, 0], [5, 169.56, 37.8], [101.78, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "RewardItem", 4, [12, "2bASLwK1NDL6pZvMKugC5l", true, -121, 23], [58, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "RewardItem", 4, [12, "03f05kNalMO6UJBfs8i9hV", true, -122, 24], [179, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "RewardItem", 4, [12, "2e+lztNSdNd7X2S+ieyUj2", true, -123, 25], [300, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "RewardItem", 4, [12, "c8Ct8GHvtHfZKZk2YElbs5", true, -124, 26], [421, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "vip", 24, [[2, -125, [27], 28]], [0, "8f7JpnrlFMEJU/nvVjytj/", 1, 0], [5, 192, 213]], [10, "svip", 24, [[2, -126, [29], 30]], [0, "0c4ADa2+tGw54Xp24L9fKX", 1, 0], [5, 222, 246]], [1, "ImgIcon", 25, [[2, -127, [33], 34]], [0, "99qaBeLE9B+p6VyOkNkfgD", 1, 0], [5, 48, 49], [0, 4.942, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "ImgIcon", 27, [[2, -128, [36], 37]], [0, "b2nxtDiX5AWYz3D0FQGiR/", 1, 0], [5, 48, 49], [0, 4.942, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "icon_clock", 17, [[16, 0, -129, [46], 47]], [0, "e2lDLBMUdJBLVKV4JKCnSL", 1, 0], [5, 32, 38], [-61.540000000000006, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ads", 6, [[2, -130, [48], 49]], [5, "22G6hi1OxJdqDdFtHZYCoA", 2], [5, 50, 52], [-61.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "zuan", 6, [[36, 1, -131, [50], 51]], [5, "56w8D4cE5HT6J1PZvlaULq", 2], [5, 47, 55], [-5, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "icon_clock", 19, [[16, 0, -132, [58], 59]], [5, "72dxqfbOVOJpFxt+On9cme", 2], [5, 32, 38], [-61.540000000000006, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [42, 2], [1, "button02", 20, [[7, 1, 0, -133, [60], 61]], [0, "2d8WmYyFJBX7SCdyy0u2RP", 1, 0], [5, 215, 86], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "icon_clock", 21, [[2, -134, [63], 64]], [0, "5eCL9YFXBIIb6QG9Naf/fx", 1, 0], [5, 43, 59], [-64.3, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]]], 0, [0, 3, 1, 0, 7, 61, 0, 0, 1, 0, -1, 35, 0, -2, 36, 0, -3, 37, 0, -4, 22, 0, -5, 3, 0, -6, 13, 0, -7, 24, 0, -8, 5, 0, -9, 29, 0, -10, 17, 0, -11, 2, 0, -12, 20, 0, 3, 2, 0, -1, 61, 0, -1, 18, 0, -2, 19, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, 0, 4, 0, -1, 49, 0, -2, 50, 0, -3, 51, 0, -4, 52, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, -2, 16, 0, 0, 6, 0, -1, 58, 0, -2, 59, 0, -3, 32, 0, 0, 7, 0, -1, 38, 0, -2, 23, 0, 0, 8, 0, -1, 39, 0, -2, 40, 0, 0, 9, 0, -1, 41, 0, -2, 42, 0, 0, 10, 0, -1, 43, 0, -2, 44, 0, 0, 11, 0, -1, 45, 0, -2, 46, 0, 0, 12, 0, -1, 47, 0, -2, 48, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 25, 0, -2, 27, 0, 0, 17, 0, -1, 31, 0, -2, 57, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -1, 33, 0, -2, 60, 0, 0, 20, 0, -1, 62, 0, -2, 21, 0, 0, 21, 0, -1, 34, 0, -2, 63, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, -1, 53, 0, -2, 54, 0, -1, 55, 0, -2, 26, 0, 0, 26, 0, 0, 26, 0, -1, 56, 0, -2, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, -1, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 3, 49, 0, 3, 50, 0, 3, 51, 0, 3, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 62, 0, 0, 63, 0, 8, 1, 4, 6, 13, 6, 6, 18, 7, 6, 22, 134], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, 9, -1, 1, -1, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 4, 5, 5, 5, 5, -1, 1, -1, 1, -1, 4, -1, 1, -1, -1, 1, -1, -1, 1, -1, 4, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 4, -1, 1, 10, -1, -1, 1, -1, 1, -1, -1, 1, -1], [0, 10, 0, 11, 12, 0, 5, 0, 1, 0, 13, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 1, 3, 3, 3, 3, 0, 14, 0, 15, 0, 1, 0, 6, 0, 0, 6, 0, 0, 16, 0, 1, 0, 17, 0, 0, 4, 0, 5, 0, 18, 0, 1, 0, 7, 0, 0, 0, 4, 0, 7, 0, 0, 4, 0]], [[{"name": "img_lx", "rect": [0, 0, 12, 12], "offset": [0, 0], "originalSize": [12, 12], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [19]], [[{"name": "img_wsxhd", "rect": [0, 0, 38, 38], "offset": [0, 0], "originalSize": [38, 38], "capInsets": [8, 12, 8, 12]}], [1], 0, [0], [2], [20]], [[{"name": "svip", "rect": [1, 0, 222, 246], "offset": [-4, 0.5], "originalSize": [232, 247], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [21]], [[{"name": "bg_ds_03", "rect": [0, 0, 125, 39], "offset": [-0.5, 0.5], "originalSize": [126, 40], "capInsets": [54.5, 17.5, 54.5, 16.5]}], [1], 0, [0], [2], [22]]]]