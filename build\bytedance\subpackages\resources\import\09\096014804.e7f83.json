[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "37mJZnBfBIEIq3cGWlwyIM", "b6ZSZ7J4xNsa/rtzDcpZR1"], ["node", "_file", "_spriteFrame", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_prefab", "_children", "_parent", "_components", "_trs", "_eulerAngles"], 2, 4, 2, 1, 9, 7, 5], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "angle", "angleVar", "startSize", "endSize", "emitterMode", "speed", "speedVar", "tangentialAccel", "tangentialAccelVar", "radialAccel", "radialAccelVar", "startRadius", "life", "autoRemoveOnFinish", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -15, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2]], [[2, 0, 1, 2, 2], [0, 0, 3, 2, 4, 1, 5, 6, 2], [0, 0, 3, 4, 1, 2], [1, 0, 1, 2, 3, 16, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 18], [4, 0, 2], [0, 0, 2, 1, 2], [2, 1, 2, 1], [1, 0, 1, 17, 2, 3, 16, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 19], [1, 0, 1, 17, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 18]], [[[{"name": "fx_icicle1", "rect": [6, 14, 46, 99], "offset": [1.5, -3.5], "originalSize": [55, 120], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [3]], [[[4, "ice_burst"], [5, "ice_burst", [-2, -3], [6, -1, 0]], [1, "bing", 1, [-5], [[3, 1, true, 100, 1, 1.2, 0, 0, 16, 12, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 70, -4, [2], [4, 4294967295], [4, 0], [4, 3388997631], [4, 0], [0, 7, 7], [0, 0.25, 0.8600000143051147], 3, 4]], [0, "14EzThVDVFWb64WxhHC6Tv", 1, 0], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [1, "bing copy", 1, [-7], [[3, 1, true, 100, 1, 1.5, 0, 0, 16, 12, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 70, -6, [7], [4, 4294967295], [4, 0], [4, 3388997631], [4, 0], [0, 7, 7], [0, 0.25, 0.8600000143051147], 8, 9]], [0, "60ytmWc4hO7qxo9XY3LdSe", 1, 0], [22, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "lizi", 2, [[7, 1, true, true, 100, 1, 1.7, 0, 0, 11, 5, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 70, -8, [0], [4, 4294958423], [4, 0], [4, 4294948405], [4, 0], [0, 7, 7], [0, 0.25, 0.8600000143051147], 1]], [0, "f87BrBGHtI65prLqGpH9aN", 1, 0]], [2, "lizi", 3, [[8, 1, true, true, 100, 1, 0, 0, 11, 5, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 70, -9, [5], [4, 4294958423], [4, 0], [4, 4294948405], [4, 0], [0, 7, 7], [0, 0.25, 0.8600000143051147], 6]], [0, "23yWLhl0dHmIVjMpe5znCq", 1, 0]]], 0, [0, 4, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, 0, 5, 0, 5, 1, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, 2, -1, 1, -1, 1, 2], [0, 1, 0, 1, 2, 0, 1, 0, 1, 2]]]]