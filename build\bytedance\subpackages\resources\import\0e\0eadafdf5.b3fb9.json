[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "d7oYPcJg1BAZMxrtImFfrT", "5eS0Ng3+xKv4mrQX+5grLO", "cdvDZRLPZKb70zilA0TpQ8", "deYeqBbrtAM7ABF8+EGpZQ", "dcvk9LIURKo5urNIi0OkLV", "f8npR6F8ZIZoCp3cKXjJQz", "c9+TJfKLNE/IRJRRBI8im6", "44T6IcpztBvY5wneOUE6Pp"], ["node", "_spriteFrame", "_N$file", "root", "asset", "_textureSetter", "tag", "bg", "val", "itemName", "target", "data"], [["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_children", "_trs", "_anchorPoint", "_color"], 1, 1, 9, 4, 5, 2, 7, 5, 5], ["cc.Node", ["_name", "_prefab", "_trs", "_components", "_contentSize", "_parent", "_children"], 2, 4, 7, 12, 5, 1, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_styleFlags", "node", "_materials", "_N$file"], -4, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["87c41E785FLq54IayUaS2Pc", ["node", "itemName", "val", "bg", "tag"], 3, 1, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "node", "_layoutSize"], -1, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [13, 0, 1, 2, 2], [0, 0, 2, 3, 4, 5, 7, 2], [1, 0, 5, 3, 1, 4, 2, 2], [1, 0, 5, 1, 2, 2], [10, 0, 1, 2, 3, 3], [2, 1, 0, 2, 3, 4, 3], [3, 0, 1, 2, 6, 3, 4, 7, 8, 7], [6, 0, 2], [1, 0, 6, 3, 1, 4, 2, 2], [0, 0, 2, 6, 3, 4, 5, 8, 7, 2], [0, 0, 2, 6, 3, 4, 2], [0, 0, 1, 2, 3, 4, 9, 5, 3], [7, 0, 1, 2, 3, 4, 1], [8, 0, 1, 2, 3, 3], [9, 0, 1, 2, 3], [5, 1, 2, 1], [2, 0, 2, 3, 4, 2], [2, 1, 0, 2, 3, 3], [11, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1], [3, 0, 1, 5, 2, 3, 4, 7, 8, 9, 7], [3, 0, 1, 2, 3, 4, 7, 8, 9, 6]], [[[[8, "Shop_GoodsItem"], [9, "Shop_GoodsItem", [-10, -11, -12, -13, -14, -15, -16], [[-2, [13, -7, -6, -5, -4, -3], [14, 0.9, 3, -9, [[15, "87c41E785FLq54IayUaS2Pc", "onClickBuy", -8]]]], 1, 4, 4], [16, -1, 0], [5, 222, 312], [-234, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "tap", 1, [-19], [[6, 1, 0, -17, [7], 8], [19, 1, 1, 10, 6, -18, [5, 49.55, 58]]], [0, "47VSzOCldN+I64gEWyzoFK", 1, 0], [5, 49.55, 58], [0, 1, 0.5], [124.416, 148.235, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lock", 1, [-21, -22], [[20, -20]], [0, "15PT1b0JVCnrDTmnwAuG8P", 1, 0]], [3, "val", 1, [[-23, [1, 2, -24, [4, 4278190080]]], 1, 4], [0, "f88kVtGTtOIaz5OBSv4l+u", 1, 0], [5, 67.9, 54.4], [0, -51.466, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 2, [[21, "7折", 20, 20, false, 1, 1, -25, [5], 6], [1, 2, -26, [4, 4278387633]]], [0, "f3Jepe9MdI+JWcN6BQCxIU", 1, 0], [5, 33.55, 29.2], [-22.775, 2.822, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "name", 1, [[-27, [1, 2, -28, [4, 4278190080]]], 1, 4], [0, "83KWNl8jZHU5AIyVi0J2X1", 1, 0], [5, 52, 54.4], [0, 114.464, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "name", 3, [[22, "已售罄", 36, false, 1, 1, -29, [12], 13], [1, 2, -30, [4, 4278190080]]], [0, "98nJoaNMxJ6bhACrFh1Sr1", 1, 0], [5, 112, 54.4], [0, -110.549, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sp_shop_goldbg", 1, [[17, 0, -31, [0], 1]], [0, "89ZCXArgFDu4mbOT0gwmcN", 1, 0], [5, 160, 36], [0, 113.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "RewardItem", 1, [5, "e1sUeEBO1EEJUhat4/PQxy", true, -32, 2], [0, 31.54, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Item_NeedDisplay", 1, [5, "11tBdHo7xIR6QANQuYysYF", true, -33, 3], [0, -113.839, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "x680", 28, false, 1, 1, 1, 4, [4]], [7, "灵币", 24, false, 1, 1, 1, 6, [9]], [12, "bg_goods_02", 200, 3, [[6, 1, 0, -34, [10], 11]], [0, "ealvrMkVdNp5FX0/ifWRXm", 1, 0], [4, 4278190080], [5, 222, 312]], [18, 1, 0, 1, [14]]], 0, [0, 3, 1, 0, -1, 14, 0, 6, 2, 0, 7, 14, 0, 8, 11, 0, 9, 12, 0, 0, 1, 0, 10, 1, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 4, 0, -5, 2, 0, -6, 6, 0, -7, 3, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, 0, 3, 0, -1, 13, 0, -2, 7, 0, -1, 11, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 3, 9, 0, 3, 10, 0, 0, 13, 0, 11, 1, 34], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 12, 14], [-1, 1, 4, 4, -1, -1, 2, -1, 1, -1, -1, 1, -1, 2, -1, 2, 2, 1], [0, 3, 4, 5, 0, 0, 1, 0, 6, 0, 0, 2, 0, 7, 0, 1, 1, 2]], [[{"name": "sp_shop_item", "rect": [0, 0, 161, 245], "offset": [0, 0], "originalSize": [161, 245], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [5], [8]], [[{"name": "sale_01", "rect": [0, 0, 58, 58], "offset": [0, 0], "originalSize": [58, 58], "capInsets": [0, 17, 0, 16]}], [4], 0, [0], [5], [9]]]]