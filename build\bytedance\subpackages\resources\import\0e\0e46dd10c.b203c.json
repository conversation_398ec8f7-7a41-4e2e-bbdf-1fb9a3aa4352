[1, ["ecpdLyjvZBwrvm+cedCcQy", "denPJe+T5CRr/SBHc0T1VF", "bffmYw4n9ApJlVfnHriS0Y", "25WZOgGqhJSZL4Rhfbodq3", "ddGPcSaUJOc7TvrBI6xoA6"], ["node", "_spriteFrame", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 1, 9, 4, 5, 2, 1, 7], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "node", "_layoutSize"], -2, 1, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[0, 0, 1, 6, 2, 3, 4, 7, 3], [2, 0, 1, 2, 2], [1, 2, 3, 4, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [7, 0, 1, 2, 2], [4, 0, 2], [0, 0, 1, 5, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 3], [5, 0, 1, 2, 3, 4, 5, 6, 6], [2, 1, 2, 1]], [[[{"name": "icon_jtx", "rect": [0, 0, 33, 37], "offset": [0, 0], "originalSize": [33, 37], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [1]], [[[5, "FightCapaTips"], [6, "FightCapaTips", 1, [-4, -5, -6, -7], [[7, 1, 0, -2, [6], 7], [8, 1, 1, 100, 100, 10, -3, [5, 415.76, 90]]], [9, -1, 0], [5, 415.76, 90]], [0, "val", 1, 1, [[3, "100", 39, 50, false, false, 1, 1, 1, -8, [2]], [4, 3, -9, [4, 4278190080]]], [1, "a2M1EVVDVHlpp5CFyOL255", 1, 0], [5, 71.07, 69], [-8.344999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "add", 1, 1, [[3, "0", 39, 50, false, false, 1, 1, 1, -10, [5]], [4, 3, -11, [4, 4278190080]]], [1, "37qYGXoc5NzacwiQ1sPFV3", 1, 0], [5, 27.69, 69], [94.035, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "icon_cp", 1, 1, [[2, -12, [0], 1]], [1, "b3AniQlLRLlpXePPrv4RPd", 1, 0], [5, 54, 65], [-80.88, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "<PERSON><PERSON><PERSON>", 1, 1, [[2, -13, [3], 4]], [1, "20L4aVbpdAvb9M6a0C/B9X", 1, 0], [5, 33, 37], [53.69, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 5, 0, -4, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 4, 1, 13], [0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, 1], [0, 2, 0, 0, 3, 0, 0, 4]]]]