[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "71cvTz3uFKs7p/yq4iHHDe", "ebJXGx5cNJ+b1BpmgufQFT", "61fKVktIJNzo+Z2akxoqzh", "dbw0kvReBJm6ScF5/Z2Ufe", "10C9gN0zdKfZMmtgc+gmrT", "29FYIk+N1GYaeWH/q1NxQO", "3ae7efMv1CLq2ilvUY/tQi", "cb1VFt7lZM5bK3HPiU1WUY", "83I+RIbNRGN7aFDgqVq6Vv", "7dxKocDYhNTpmtkXcfsRmE", "80IwBAffhNXZvHIXzOTcJu", "0fFi2ro3JI0rNxz8dumUx8", "5aRxHu6UJHt6UITJ2mChjv", "47DZQx15FOd6zOVnTVSOD4", "d6MVzQ4l1ArYI+uvV+LJph", "77chVQQp1NIKaZHQ1IEtV3", "c8WhliGcZELZVBHW+yoq4l", "07UhZbbVZGB4VrSAk3CrbQ", "7eYK3f5BVIZIsTbwz1sKFE", "c6823ZZu5DO5uHXx3BV9h0", "485eGEfEJLOqZGExd6w/kd", "9alhDeZttPnIjWWf28gp1y", "b2gseIigJABZr/xG0zwT9Y", "b2AxAf3ZJHXYpmZKl+3U37", "5fAZZ3arREPYEwzIiF58qc", "550UlFYhVFGbiSB8EyHYyb", "99uatUG6xNhYLp1GOHpjHY", "e6dXuN43hFoZ7Vsv70PRcn", "27RTpTPjZN9KOAJJnUPPk1", "67qpQ4UQRJPpgLpkY7tGmN", "40fxfp6ipFNLgAYnPfcBxt", "d7PwcvW5lG0oTHpHqVs2TL", "2cnw/5vOBE557efh8bKmEV", "3bZ4JsnvVBp4I+rzsU2pPn", "39PPi3hrhF8ogxwEUv0MC+", "8fyv+GLmZBqqA6xFvb5SaT", "60+PYQJC1NOY9pKnhsrzrZ", "29u1aCRQxDtJZ1YBMLV01f"], ["node", "_spriteFrame", "_textureSetter", "_N$target", "checkMark", "_N$file", "_N$normalSprite", "_N$disabledSprite", "_normalMaterial", "_grayMaterial", "_parent", "root", "toggleC<PERSON><PERSON>", "viewnode", "tweenbg", "timeStr", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children"], 0, 9, 4, 5, 1, 7, 2], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_originalWidth", "_originalHeight", "_bottom", "alignMode", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "_N$paddingLeft", "_enabled", "_N$spacingY", "_N$paddingRight", "node", "_layoutSize"], -4, 1, 5], ["cc.Node", ["_name", "_active", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 0, 1, 12, 4, 5, 7, 2], ["cc.Toggle", ["zoomScale", "_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents", "_normalMaterial", "_grayMaterial"], 0, 1, 5, 1, 1, 9, 6, 6], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_styleFlags", "_N$fontFamily", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6, 6], ["cc.Prefab", ["_name"], 2], ["74d27XR4shG97qUPGx7qvw9", ["node", "tweenbg", "viewnode", "toggleC<PERSON><PERSON>"], 3, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["1563dBtNsFCp7Et7MyFQ3Sd", ["SwitchID", "node"], 2, 1], ["1fcc9wvG+JJhqAR64eBU2by", ["<PERSON><PERSON>", "KnapsackName", "node", "timeStr"], 1, 1, 1], ["061b8/OBgdEfI0ajbX8IA//", ["<PERSON><PERSON>", "KnapsackName", "node"], 1, 1], ["00798YhOSNDMq8AyX+/Wp5o", ["my<PERSON>ey", "node"], 2, 1], ["cc.ToggleContainer", ["node"], 3, 1]], [[8, 0, 1, 2, 2], [3, 2, 3, 4, 1], [1, 0, 2, 6, 3, 4, 5, 7, 3], [13, 0, 1, 2, 3, 4], [9, 0, 1, 2, 2], [1, 0, 1, 6, 3, 4, 5, 7, 3], [1, 0, 1, 6, 8, 3, 4, 5, 7, 3], [1, 0, 6, 3, 4, 5, 2], [1, 0, 6, 8, 3, 4, 5, 7, 2], [1, 0, 6, 8, 3, 4, 5, 2], [2, 0, 4, 5, 8, 4], [3, 1, 0, 2, 3, 4, 3], [2, 0, 1, 2, 3, 6, 4, 5, 8, 8], [3, 2, 3, 1], [7, 0, 1, 4, 5, 2, 3, 7, 8, 9, 7], [7, 0, 1, 4, 2, 3, 7, 8, 6], [1, 0, 6, 3, 4, 5, 7, 2], [5, 0, 1, 3, 8, 4, 5, 6, 7, 3], [16, 0, 1, 2, 3], [17, 0, 1, 2], [2, 7, 0, 1, 2, 3, 6, 4, 5, 8, 9], [4, 2, 0, 3, 1, 7, 8, 5], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 2], [10, 1, 2, 1], [1, 0, 1, 3, 4, 5, 7, 3], [5, 0, 3, 8, 4, 5, 6, 7, 2], [2, 0, 8, 2], [4, 2, 0, 3, 6, 1, 7, 8, 6], [11, 0, 2], [1, 0, 1, 8, 3, 4, 5, 3], [1, 0, 2, 1, 6, 8, 3, 4, 5, 7, 4], [1, 0, 2, 1, 6, 3, 4, 5, 7, 4], [5, 0, 2, 3, 4, 5, 6, 7, 3], [12, 0, 1, 2, 3, 1], [8, 1, 2, 1], [2, 0, 1, 2, 3, 4, 5, 8, 7], [2, 0, 1, 2, 3, 8, 5], [4, 4, 2, 0, 1, 7, 8, 5], [4, 0, 3, 1, 5, 7, 8, 5], [3, 0, 2, 3, 2], [3, 2, 4, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 4], [6, 0, 1, 3, 4, 5, 6, 7, 3], [14, 0, 1, 2], [15, 0, 1, 2, 3, 3], [7, 0, 1, 5, 2, 3, 6, 7, 8, 7], [9, 1, 1], [18, 0, 1]], [[[{"name": "module_2", "rect": [0, 3, 70, 80], "offset": [0, 0], "originalSize": [70, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [10]], [[{"name": "module_4", "rect": [1, 7, 61, 75], "offset": [-3.5, -1.5], "originalSize": [70, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [11]], [[{"name": "icon_huodong", "rect": [0, 0, 79, 75], "offset": [0, 0], "originalSize": [79, 75], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [12]], [[{"name": "module_5", "rect": [6, 5, 60, 77], "offset": [1, -0.5], "originalSize": [70, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [13]], [[{"name": "module_1", "rect": [8, 7, 57, 76], "offset": [1.5, -2], "originalSize": [70, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [14]], [[{"name": "icon_store", "rect": [0, 0, 77, 74], "offset": [0, 0], "originalSize": [77, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [15]], [[{"name": "sp_choice", "rect": [0, 0, 147, 149], "offset": [0, 0], "originalSize": [147, 149], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [16]], [[{"name": "tpdg_zjm_icon_jiahao", "rect": [0, 0, 24, 24], "offset": [0, 0], "originalSize": [24, 24], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [17]], [[{"name": "bg_main_foot", "rect": [0, 3, 720, 162], "offset": [0, -1.5], "originalSize": [720, 165], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [18]], [[{"name": "icon_mxz", "rect": [0, 0, 67, 65], "offset": [0, 0], "originalSize": [67, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [19]], [[[29, "M20_PrePare_MenuView"], [30, "M20_PrePare_MenuView", 1, [-6, -7, -8, -9], [[34, -5, -4, -3, -2]], [35, -1, 0], [5, 750, 1334]], [26, "ToggleMenu", 1, [-14, -15, -16, -17, -18], [[-10, [36, 4, 264.5, 264.5, 636.5, 221, 61, -11], [38, false, 1, 1, 30, -12, [5, 870, 145]], [11, 1, 0, -13, [97], 98]], 1, 4, 4, 4], [0, "abwpPmkNNEw7S4st/VTeHD", 1, 0], [5, 752, 165], [0, -584.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "toggle1", 2, [-23, -24, -25, -26, -27, -28], [[22, 1.1, 3, false, -21, [4, 4292269782], -20, -19, [[3, "74d27XR4shG97qUPGx7qvw9", "menuCheck", "ui/role/Role_Select", 1]], 65, 66], [44, 10, -22]], [0, "e9+ftx27JCtq0yK4X4RB2K", 1, 0], [5, 137, 128], [-151, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "top", 1, [-31, -32, -33, -34, -35], [[37, 1, 375, 375, 100, -29], [39, 3, 30, 30, 30, -30, [5, 750, 100]]], [0, "cbrkPi+N5GIo4ZoJH/PbbT", 1, 0], [5, 750, 100], [0, 517, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "energy", 1, 4, [-40, -41, -42], [[11, 1, 0, -36, [12], 13], [21, 1, 1, -25, 5, -37, [5, 178.68, 43]], [45, "1", "MCatGame", -39, -38]], [0, "245DMN+8hAmI2sSNzugG3P", 1, 0], [5, 178.68, 43], [-255.65999999999997, 28.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "toggle4", 2, [-46, -47, -48, -49, -50], [[22, 1.1, 3, false, -45, [4, 4292269782], -44, -43, [[3, "74d27XR4shG97qUPGx7qvw9", "menuCheck", "Activity", 1]], 95, 96]], [0, "d5dtjnY0lDiZILF3wBSso2", 1, 0], [5, 137, 128], [302, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "coin", 1, 4, [-54, -55, -56], [[18, "2", "MCatGame", -51], [11, 1, 0, -52, [22], 23], [21, 1, 1, -25, 5, -53, [5, 99.53999999999999, 43]]], [0, "bcAKL9C75LvaEk8eVTKaDL", 1, 0], [5, 99.53999999999999, 43], [-86.54999999999997, 28.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "diamond", 1, 4, [-60, -61, -62], [[18, "11", "MCatGame", -57], [11, 1, 0, -58, [32], 33], [21, 1, 1, -25, 5, -59, [5, 98.53999999999999, 43]]], [0, "48XBBuJmpFyKLhYzkB33LH", 1, 0], [5, 98.53999999999999, 43], [42.49000000000002, 28.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "toggle0", 2, [-66, -67, -68, -69], [[42, 1.1, 3, false, -65, [4, 4292269782], -64, -63, [[3, "74d27XR4shG97qUPGx7qvw9", "menuCheck", "ui/shop/Shop_View", 1]]]], [0, "89GAFBL9BKt4uNvpRr4y+R", 1, 0], [5, 137, 128], [-302, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "toggle2", 2, [-73, -74, -75, -76], [[43, 1.1, 3, -72, [4, 4292269782], -71, -70, [[3, "74d27XR4shG97qUPGx7qvw9", "menuCheck", "Fight", 1]]]], [0, "c52/R4/SBP8YMTb4dkN364", 1, 0], [5, 150, 128], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "toggle3", 2, [-80, -81, -82, -83], [[22, 1.1, 3, false, -79, [4, 4292269782], -78, -77, [[3, "74d27XR4shG97qUPGx7qvw9", "menuCheck", "Equip", 1]], 83, 84]], [0, "06WuK94qNMDKtJovl7gUCm", 1, 0], [5, 137, 128], [151, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [31, "adcoupons", false, 1, 4, [-87, -88], [[18, "17", "MCatGame", -84], [11, 1, 0, -85, [38], 39], [28, 1, 1, -25, 25, 5, -86, [5, 101.78999999999999, 43]]], [0, "a3LqebAo9PFJKRqClBLXeh", 1, 0], [5, 101.78999999999999, 43], [180.515, 28.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "adcoin", 1, 4, [-92, -93], [[18, "53", "MCatGame", -89], [11, 1, 0, -90, [44], 45], [28, 1, 1, -25, 25, 5, -91, [5, 78.53999999999999, 43]]], [0, "51M4O4Gj1HnJmBXRFyk1kL", 1, 0], [5, 78.53999999999999, 43], [161.03000000000003, 28.5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 1, [-95, -96], [[10, 45, 750, 1334, -94]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [6, "head", 1, 5, [-98, -99], [[1, -97, [3], 4]], [0, "a7qulGS6VOi5+4gIAh1G2m", 1, 0], [5, 54, 55], [-87.34, -2.541, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "val", 1, 5, [[14, "100/30", 28, false, 1, 1, 1, -100, [5], 6], [4, 2, -101, [4, 4278190080]], [47, -102]], [0, "0cp7D87JlAT6//s8TjApNQ", 1, 0], [5, 92.68, 54.4], [-9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Button", 1, 5, [-105, -106], [[23, 3, -104, [[3, "1fcc9wvG+JJhqAR64eBU2by", "openView", "ui/shop/Shop_GetEnergy", 5]], [4, 4293322470], [4, 3363338360], -103, 10, 11]], [0, "50prCK1JJEBZVcE6Ukkhi9", 1, 0], [5, 47, 51], [65.84, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 1, 17, [[1, -107, [7], 8], [20, 0, 45, 6, 6, 8, 8, 100, 40, -108]], [0, "abZbjsEP1GxKqOu5GmPzPg", 1, 0], [5, 35, 35], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [25, "Background", 1, [[1, -109, [18], 19], [20, 0, 45, 6, 6, 8, 8, 100, 40, -110]], [0, "3fV+atEEhBcIGMWItFQfSZ", 1, 0], [5, 35, 35], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [25, "Background", 1, [[1, -111, [28], 29], [20, 0, 45, 6, 6, 8, 8, 100, 40, -112]], [0, "3bvcdBNJVIi7wkLCegYjsR", 1, 0], [5, 35, 35], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "checkmark", false, 9, [-115], [[-113, [12, 45, -5, -5, -3.5, -17.5, 40, 40, -114]], 1, 4], [0, "d1BqJwCspO1o3MU4cj7Ynn", 1, 0], [5, 147, 149], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "checkmark", false, 3, [-118], [[-116, [12, 45, -5, -5, -3.5, -17.5, 40, 40, -117]], 1, 4], [0, "92X4K35ZZOp6CVSDswLbax", 1, 0], [5, 147, 149], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "checkmark", 10, [-121], [[-119, [12, 45, 1.5, 1.5, -3.5000000000000178, -17.499999999999993, 40, 40, -120]], 1, 4], [0, "9c17iz/yBDtYir+ttL7CrX", 1, 0], [5, 147, 149], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "checkmark", false, 11, [-124], [[-122, [12, 45, -5, -5, -3.5, -17.5, 40, 40, -123]], 1, 4], [0, "913GED/5pAgrBYVHLgrvmT", 1, 0], [5, 147, 149], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "checkmark", false, 6, [-127], [[-125, [12, 45, -5, -5, -3.5, -17.5, 40, 40, -126]], 1, 4], [0, "b8FwecpwBCWKgeXVYUwSkA", 1, 0], [5, 147, 149], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "content", 14, [[27, 45, -128]], [0, "9asGDh9HRIQqtI2l7HfWNC", 1, 0], [5, 750, 1334]], [7, "bg", 14, [[40, 0, -129, [0]]], [0, "c5RufuXnNJcZefrrH532lI", 1, 0], [5, 750, 1334]], [33, "cd", 1, 17, [[-130, [4, 2, -131, [4, 4278190080]]], 1, 4], [0, "e2daLjRb5GApcgUrYLYKlk", 1, 0], [5, 80.97, 54.4], [-70.831, -35.199, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "head", 1, 7, [[1, -132, [14], 15], [24, -133, [[3, "74d27XR4shG97qUPGx7qvw9", "onBtn", "TTTTTTTTTTs", 1]]]], [0, "falfSd3mlEK7Wn+jkihXwT", 1, 0], [5, 48, 49], [-50.769999999999996, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "val", 1, 7, [[14, "0", 28, false, 1, 1, 1, -134, [16], 17], [4, 2, -135, [4, 4278190080]]], [0, "d17/+Slt1FdocUl/dLRHD0", 1, 0], [5, 19.54, 54.4], [-11.999999999999996, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Button", 1, 7, [19], [[23, 3, -136, [[3, "74d27XR4shG97qUPGx7qvw9", "onBtn", "getCoin", 1]], [4, 4293322470], [4, 3363338360], 19, 20, 21]], [0, "524sY4GCpOv5uU7m/5QTTU", 1, 0], [5, 47, 51], [26.270000000000003, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "head", 1, 8, [[1, -137, [24], 25], [24, -138, [[3, "74d27XR4shG97qUPGx7qvw9", "onBtn", "gm_add_resources", 1]]]], [0, "32CvD1OvRPvaczNcSGrw2K", 1, 0], [5, 47, 55], [-50.769999999999996, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "val", 1, 8, [[14, "0", 28, false, 1, 1, 1, -139, [26], 27], [4, 2, -140, [4, 4278190080]]], [0, "0alcQqhk9KfbFQQaPD4XHm", 1, 0], [5, 19.54, 54.4], [-12.499999999999996, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Button", 1, 8, [20], [[23, 3, -141, [[3, "74d27XR4shG97qUPGx7qvw9", "onBtn", "getDiamond", 1]], [4, 4293322470], [4, 3363338360], 20, 30, 31]], [0, "ffrq0pQ8NFib2Pb2EnE/KY", 1, 0], [5, 47, 51], [25.770000000000003, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "val", 1, 12, [[14, "0", 28, false, 1, 1, 1, -142, [36], 37], [4, 2, -143, [4, 4278190080]]], [0, "28tnc0DK5JE6pP3s5tKWbR", 1, 0], [5, 20.79, 54.4], [15.500000000000004, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "val", 1, 13, [[14, "0", 28, false, 1, 1, 1, -144, [42], 43], [4, 2, -145, [4, 4278190080]]], [0, "c53s5jof1M/6Ju5H2vewPV", 1, 0], [5, 19.54, 54.4], [4.5000000000000036, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 9, [-147], [[10, 45, 40, 40, -146]], [0, "99YrvBQHlB2oyfsB74I5IW", 1, 0], [5, 137, 128]], [16, "ggtipicon", 9, [[1, -148, [51], 52], [19, 1000, -149]], [0, "4aU4mIugdGo4dn1L4Ej1+n", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [2, "New Label", false, 9, [[15, "商店", 26, false, 1, 1, -150, [53]], [4, 3, -151, [4, 4281545529]]], [0, "01bj3G8nRNqZtqlDYBS6/a", 1, 0], [5, 58, 56.4], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 3, [-153], [[10, 45, 40, 40, -152]], [0, "6a4jRUtR5DbL7yTpFvX7RZ", 1, 0], [5, 137, 128]], [16, "ggtipicon", 3, [[1, -154, [59], 60], [19, 1001, -155]], [0, "72GCiedztDT4eNVSM/Vtjo", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [2, "New Label", false, 3, [[15, "装备", 26, false, 1, 1, -156, [61]], [4, 3, -157, [4, 4281545529]]], [0, "b3n7c8VvpPkJVp/dVB5YWw", 1, 0], [5, 58, 56.4], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 10, [-159], [[10, 45, 40, 40, -158]], [0, "83IQp72GlIsb6ePTTxxMjh", 1, 0], [5, 150, 128]], [2, "New Label", false, 10, [[15, "战斗", 26, false, 1, 1, -160, [74]], [4, 3, -161, [4, 4281545529]]], [0, "12mqlmN8FIbbgxB6vcAA3z", 1, 0], [5, 58, 56.4], [0, -45.721, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 11, [-163], [[10, 45, 40, 40, -162]], [0, "a4KUeEKthKVol8e8W+9h9B", 1, 0], [5, 137, 128]], [16, "ggtipicon", 11, [[1, -164, [80], 81], [19, 1003, -165]], [0, "aeHyHVwI5DYIAuGVGI9WuU", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [2, "New Label", false, 11, [[15, "技能", 26, false, 1, 1, -166, [82]], [4, 3, -167, [4, 4281545529]]], [0, "cbb0jKbDdIToD86NWFt/+v", 1, 0], [5, 58, 56.4], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 6, [-169], [[10, 45, 40, 40, -168]], [0, "2bBJWuzutOqrd43rycIvNj", 1, 0], [5, 137, 128]], [2, "ggtipicon", false, 6, [[1, -170, [90], 91], [19, 1004, -171]], [0, "e1Lfn0p3BAkapc1Xo9xPJO", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [2, "New Label", false, 6, [[15, "挑战", 26, false, 1, 1, -172, [92]], [4, 3, -173, [4, 4281545529]]], [0, "e2lvmKbupCW59fJvYj+Qp9", 1, 0], [5, 58, 56.4], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "maskbg", 1, [[27, 45, -174]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [32, "tpdg_zjm_icon_jiahao", false, 1, 15, [[1, -175, [1], 2]], [0, "572jMTuLdK1Y2QZd00XNPX", 1, 0], [5, 24, 24], [17.29, -10.221, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "headClick", 15, [[24, -176, [[3, "74d27XR4shG97qUPGx7qvw9", "onBtn", "gm_clean_data", 1]]]], [0, "6bNzwIJllNsIjR5ywJGAwd", 1, 0], [5, 120, 80], [27.517, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "00:00", 28, 1, 1, 1, "Consolas", 28, [9]], [5, "head", 1, 12, [[1, -177, [34], 35]], [0, "5f/SLweQ9APLHlXCpNNFnE", 1, 0], [5, 76, 64], [-37.894999999999996, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [5, "head", 1, 13, [[1, -178, [40], 41]], [0, "22VU3pOUNGiKUMpvuwYyVK", 1, 0], [5, 54, 56], [-37.269999999999996, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "icon_store", false, 21, [[1, -179, [46], 47]], [0, "a7hriCALJKCKzs6JM2ISZG", 1, 0], [5, 77, 74], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [13, 21, [48]], [7, "icon_store", 37, [[1, -180, [49], 50]], [0, "2fWRQs/ClC54BOumofI3DY", 1, 0], [5, 57, 76]], [2, "icon_store", false, 22, [[1, -181, [54], 55]], [0, "e6zNbp4odB9b+lGX+tYLd+", 1, 0], [5, 67, 65], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [13, 22, [56]], [7, "icon_store", 40, [[1, -182, [57], 58]], [0, "6d7PW31xZEUYgH5p7SqaFo", 1, 0], [5, 65, 79]], [2, "hand", false, 3, [[41, -183, 62]], [0, "b1XD1qbMpOeJU6MA/liGwa", 1, 0], [5, 85, 83], [37.802, -25.201, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lock", false, 3, [[1, -184, [63], 64]], [0, "1f2ogKwDJBqo+LUPRWngKq", 1, 0], [5, 34, 41], [29.848, 36.48, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_store", false, 23, [[1, -185, [67], 68]], [0, "89VM4DjeZGF6SryJJHHE8b", 1, 0], [5, 172, 99], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [13, 23, [69]], [7, "icon_store", 43, [[1, -186, [70], 71]], [0, "f8bI9wpChLAbe7kBQT8C+r", 1, 0], [5, 60, 77]], [2, "ggtipicon", false, 10, [[1, -187, [72], 73]], [0, "18GrzRIvRLDIk3mFKe7lOS", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [2, "icon_store", false, 24, [[1, -188, [75], 76]], [0, "22s2iH4CtH4o0woFxvjjW1", 1, 0], [5, 80, 77], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [13, 24, [77]], [7, "icon_store", 45, [[1, -189, [78], 79]], [0, "c3ygfpNnxGBa+CZzBmoHCh", 1, 0], [5, 70, 80]], [2, "icon_store", false, 25, [[1, -190, [85], 86]], [0, "bbSvItGJhKoY/FFACsSj0O", 1, 0], [5, 79, 75], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [13, 25, [87]], [7, "icon_store", 48, [[1, -191, [88], 89]], [0, "davoDpIM1H/oT3ljqEtpBX", 1, 0], [5, 61, 75]], [2, "lock", false, 6, [[1, -192, [93], 94]], [0, "5c4TwUx5BGf62T6lMErxLY", 1, 0], [5, 34, 41], [29.848, 36.48, 0, 0, 0, 0, 1, 1, 1, 1]], [48, 2]], 0, [0, 11, 1, 0, 12, 76, 0, 13, 26, 0, 14, 27, 0, 0, 1, 0, -1, 51, 0, -2, 14, 0, -3, 4, 0, -4, 2, 0, -1, 76, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 3, 0, -3, 10, 0, -4, 11, 0, -5, 6, 0, 4, 61, 0, 3, 3, 0, 0, 3, 0, 0, 3, 0, -1, 22, 0, -2, 40, 0, -3, 41, 0, -4, 42, 0, -5, 63, 0, -6, 64, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, -2, 7, 0, -3, 8, 0, -4, 12, 0, -5, 13, 0, 0, 5, 0, 0, 5, 0, 15, 54, 0, 0, 5, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, 4, 73, 0, 3, 6, 0, 0, 6, 0, -1, 25, 0, -2, 48, 0, -3, 49, 0, -4, 50, 0, -5, 75, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 29, 0, -2, 30, 0, -3, 31, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, 4, 58, 0, 3, 9, 0, 0, 9, 0, -1, 21, 0, -2, 37, 0, -3, 38, 0, -4, 39, 0, 4, 66, 0, 3, 10, 0, 0, 10, 0, -1, 23, 0, -2, 43, 0, -3, 68, 0, -4, 44, 0, 4, 70, 0, 3, 11, 0, 0, 11, 0, -1, 24, 0, -2, 45, 0, -3, 46, 0, -4, 47, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 55, 0, -2, 35, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 56, 0, -2, 36, 0, 0, 14, 0, -1, 26, 0, -2, 27, 0, 0, 15, 0, -1, 52, 0, -2, 53, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 3, 18, 0, 0, 17, 0, -1, 18, 0, -2, 28, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 58, 0, 0, 21, 0, -1, 57, 0, -1, 61, 0, 0, 22, 0, -1, 60, 0, -1, 66, 0, 0, 23, 0, -1, 65, 0, -1, 70, 0, 0, 24, 0, -1, 69, 0, -1, 73, 0, 0, 25, 0, -1, 72, 0, 0, 26, 0, 0, 27, 0, -1, 54, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, -1, 59, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, -1, 62, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, -1, 67, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, -1, 71, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, -1, 74, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 59, 0, 0, 60, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 71, 0, 0, 72, 0, 0, 74, 0, 0, 75, 0, 16, 1, 19, 10, 31, 20, 10, 34, 192], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 61, 66, 70, 73], [-1, -1, 1, -1, 1, -1, 5, -1, 1, -1, 6, 7, -1, 1, -1, 1, -1, 5, -1, 1, 6, 7, -1, 1, -1, 1, -1, 5, -1, 1, 6, 7, -1, 1, -1, 1, -1, 5, -1, 1, -1, 1, -1, 5, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, 8, 9, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 8, 9, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, 8, 9, -1, 1, 1, 1, 1, 1, 1], [0, 0, 20, 0, 21, 0, 1, 0, 5, 0, 6, 7, 0, 2, 0, 22, 0, 1, 0, 5, 6, 7, 0, 2, 0, 23, 0, 1, 0, 5, 6, 7, 0, 2, 0, 24, 0, 1, 0, 2, 0, 25, 0, 1, 0, 2, 0, 26, 0, 0, 27, 0, 3, 0, 0, 28, 0, 0, 29, 0, 3, 0, 30, 0, 9, 0, 8, 0, 31, 0, 0, 32, 0, 3, 0, 0, 33, 0, 0, 34, 0, 3, 0, 0, 8, 0, 35, 0, 0, 36, 0, 3, 0, 0, 9, 0, 8, 0, 37, 4, 4, 4, 4, 4]], [[{"name": "icon_jinneg", "rect": [0, 0, 80, 77], "offset": [0, 0], "originalSize": [80, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[{"name": "module_3", "rect": [5, 4, 65, 79], "offset": [2.5, -0.5], "originalSize": [70, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]]]]