import fs from 'fs';
import path from 'path';

// 设置输入和输出目录
const jsonDir = './json';
const csvDir = './csv';

// 确保输出目录存在
if (!fs.existsSync(csvDir)) {
    fs.mkdirSync(csvDir, { recursive: true });
}

// 获取所有JSON文件（排除.meta文件）
function getJsonFiles() {
    try {
        const files = fs.readdirSync(jsonDir);
        return files.filter(file => 
            file.endsWith('.json') && !file.endsWith('.meta.json')
        );
    } catch (err) {
        console.error(`Error reading json directory: ${err.message}`);
        return [];
    }
}

// 处理单个JSON文件
function processJsonFile(jsonFile) {
    const jsonPath = path.join(jsonDir, jsonFile);
    console.log(`Processing file: ${jsonFile}`);
    
    try {
        // 读取JSON内容
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const jsonData = JSON.parse(jsonContent);
        
        // 创建CSV文件名
        const csvFileName = path.basename(jsonFile, '.json') + '.csv';
        const csvPath = path.join(csvDir, csvFileName);
        
        // 获取所有属性名称
        const properties = Object.keys(jsonData);
        
        if (properties.length > 0) {
            // 获取所有可能的列（合并所有对象的属性）
            const allColumns = new Set();
            properties.forEach(prop => {
                const item = jsonData[prop];
                if (typeof item === 'object' && item !== null) {
                    Object.keys(item).forEach(key => allColumns.add(key));
                }
            });
            const uniqueColumns = Array.from(allColumns);
            
            // 创建CSV内容
            let csvContent = '';
            
            // 添加ID列和其他列
            csvContent += 'ID,' + uniqueColumns.join(',') + '\n';
            
            // 添加数据行
            properties.forEach(prop => {
                const item = jsonData[prop];
                let row = prop + ',';
                
                uniqueColumns.forEach(column => {
                    if (item && typeof item === 'object' && item.hasOwnProperty(column)) {
                        let value = item[column];
                        if (value !== null && value !== undefined) {
                            // 处理数组类型
                            if (Array.isArray(value)) {
                                // 所有数组统一使用JSON格式，保持一致性
                                value = JSON.stringify(value);
                            } else {
                                value = value.toString();
                            }
                            
                            // 处理可能包含逗号的值
                            value = value.replace(/"/g, '""');
                            if (value.includes(',') || value.includes('\n') || value.includes('"')) {
                                value = `"${value}"`;
                            }
                            row += value;
                        }
                    }
                    row += ',';
                });
                
                // 移除最后一个逗号
                row = row.slice(0, -1) + '\n';
                csvContent += row;
            });
            
            // 写入CSV文件
            fs.writeFileSync(csvPath, csvContent, 'utf8');
            console.log(`Created CSV file: ${csvFileName}`);
        } else {
            console.log(`Warning: ${jsonFile} does not contain valid JSON data or is empty`);
        }
    } catch (err) {
        console.error(`Error: Failed to process ${jsonFile}: ${err.message}`);
    }
}

// 主函数
function main() {
    const jsonFiles = getJsonFiles();
    
    if (jsonFiles.length === 0) {
        console.log('No JSON files found in the json directory');
        return;
    }
    
    jsonFiles.forEach(processJsonFile);
    console.log('All JSON files have been converted!');
}

// 执行主函数
main();