#!/usr/bin/env node

/**
 * 配置文件解压工具
 * 
 * 这个脚本用于解压使用LZ-string压缩的配置文件，并将它们转换为可读的JSON格式。
 * 
 * 使用方法:
 * node decompress-configs.js [options]
 * 
 * 选项:
 *   -f, --file <path>       指定要解压的单个文件路径
 *   -o, --output <path>     指定输出目录，默认为"json"
 *   -h, --help              显示帮助信息
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 引入项目中的LZ-string库
const lzstringPath = path.resolve(__dirname, '../assets/start/scripts/lzstring.js');
const lzstringContent = fs.readFileSync(lzstringPath, 'utf8');

// 创建一个模块环境来执行LZ-string库
const lzModule = { exports: {} };
const lzFunction = new Function('module', 'exports', lzstringContent);
lzFunction(lzModule, lzModule.exports);
const $lzstring = lzModule.exports;

// 解析命令行参数
const args = process.argv.slice(2);
let singleFile = null;
let outputDir = './json';

for (let i = 0; i < args.length; i++) {
  if (args[i] === '-f' || args[i] === '--file') {
    singleFile = args[i + 1];
    i++;
  } else if (args[i] === '-o' || args[i] === '--output') {
    outputDir = args[i + 1];
    i++;
  } else if (args[i] === '-h' || args[i] === '--help') {
    showHelp();
    process.exit(0);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
配置文件解压工具

使用方法:
  node decompress-configs.js [options]

选项:
  -f, --file <path>       指定要解压的单个文件路径
  -o, --output <path>     指定输出目录，默认为"json"
  -h, --help              显示帮助信息
  `);
}

// 确保输出目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 解压单个文件
function decompressFile(filePath, outputPath) {
  try {
    console.log(`正在处理: ${filePath}`);
    
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 使用LZ-string解压内容
    const decompressed = $lzstring.decompressFromBase64(content);
    
    if (!decompressed) {
      console.error(`  错误: 无法解压文件 ${filePath}`);
      return false;
    }
    
    try {
      // 尝试解析为JSON
      const jsonData = JSON.parse(decompressed);
      
      // 格式化JSON并写入文件
      fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2), 'utf8');
      console.log(`  成功: 已解压并保存到 ${outputPath}`);
      return true;
    } catch (e) {
      console.error(`  错误: 解压后的内容不是有效的JSON (${filePath})`);
      console.error(`  错误详情: ${e.message}`);
      
      // 尝试保存原始解压内容
      const rawOutputPath = outputPath.replace('.json', '.raw.txt');
      fs.writeFileSync(rawOutputPath, decompressed, 'utf8');
      console.log(`  已保存原始解压内容到: ${rawOutputPath}`);
      return false;
    }
  } catch (e) {
    console.error(`  错误: 处理文件 ${filePath} 时出错`);
    console.error(`  错误详情: ${e.message}`);
    return false;
  }
}

// 主函数
async function main() {
  // 配置文件目录
  const configDir = path.resolve(__dirname, './config');
  
  // 输出目录
  const fullOutputDir = path.resolve(__dirname, outputDir);
  ensureDirectoryExists(fullOutputDir);
  
  console.log(`配置文件解压工具`);
  console.log(`输出目录: ${fullOutputDir}`);
  
  let files = [];
  
  if (singleFile) {
    // 处理单个文件
    const fullPath = path.resolve(process.cwd(), singleFile);
    if (!fs.existsSync(fullPath)) {
      console.error(`错误: 文件不存在 ${fullPath}`);
      process.exit(1);
    }
    files.push(fullPath);
  } else {
    // 处理所有配置文件
    if (!fs.existsSync(configDir)) {
      console.error(`错误: 配置目录不存在 ${configDir}`);
      process.exit(1);
    }
    
    const allFiles = fs.readdirSync(configDir);
    files = allFiles
      .filter(file => file.endsWith('.txt') && !file.endsWith('.meta'))
      .map(file => path.join(configDir, file));
  }
  
  console.log(`找到 ${files.length} 个文件需要处理\n`);
  
  let successCount = 0;
  let failCount = 0;
  
  // 处理所有文件
  for (const file of files) {
    const fileName = path.basename(file, '.txt');
    const outputPath = path.join(fullOutputDir, `${fileName}.json`);
    
    const success = decompressFile(file, outputPath);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log(`\n处理完成!`);
  console.log(`成功: ${successCount} 个文件`);
  console.log(`失败: ${failCount} 个文件`);
}

// 运行主函数
main().catch(err => {
  console.error('发生错误:', err);
  process.exit(1);
});