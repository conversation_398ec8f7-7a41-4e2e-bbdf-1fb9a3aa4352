[1, ["ecpdLyjvZBwrvm+cedCcQy", "deYeqBbrtAM7ABF8+EGpZQ", "e4eMCobZhBA4/5VVBqe50h", "7a/QZLET9IDreTiBfRn2PD", "59Olpc2FdPj4eJWrfow3PE", "61A1GvNsNLBL1jGs2Mkf2a", "92HrO05xhOdLiCyZDU9NzS", "244AJ1itVD8IoTp2eOvsAT"], ["node", "root", "_spriteFrame", "asset", "ske", "btnLayout", "data", "_parent", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_prefab", "_contentSize", "_parent", "_trs", "_components", "_children", "_anchorPoint"], 1, 4, 5, 1, 7, 9, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["53df7PO4oJABpcJH9ZbPqVh", ["node", "btnLayout", "ske"], 3, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Label", ["_string", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3]], [[1, 0, 1, 2, 2], [0, 0, 4, 6, 2, 3, 5, 2], [0, 0, 4, 7, 6, 2, 3, 5, 2], [3, 2, 3, 4, 1], [0, 0, 4, 2, 5, 2], [8, 0, 1, 2, 3, 3], [3, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [11, 0, 1, 2, 2], [0, 0, 4, 6, 2, 3, 2], [5, 0, 2], [0, 0, 1, 7, 6, 2, 3, 3], [0, 0, 7, 2, 3, 5, 2], [0, 0, 4, 6, 2, 3, 8, 5, 2], [0, 0, 4, 7, 2, 5, 2], [6, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 2, 1], [1, 1, 2, 1], [9, 0, 1, 2, 3, 4, 4], [2, 0, 1, 4, 3], [2, 0, 2, 3, 4, 4], [12, 0, 1, 2, 3, 4, 5], [13, 0, 1, 2, 3, 4, 5, 6, 6]], [[[[10, "Shop_DrawCards_Equip"], [11, "Shop_DrawCards_Equip", 1, [-5, -6, -7, -8, -9, -10], [[16, -4, -3, -2]], [17, -1, 0], [5, 750, 1334]], [2, "btnLayer", 1, [-13, -14, -15], [[18, 1, 1, 18, -11, [5, 669, 100]], [19, 4, 139.60800000000006, -12]], [0, "8374C8kdJHEKh/ApMchH4T", 1, 0], [5, 669, 100], [0, -477.39199999999994, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "upgrade_btn", 2, [-17, -18], [[6, 1, 0, -16, [5]]], [0, "47TdskmQtNpaR4/7b3luMy", 1, 0], [5, 211, 104], [-229, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "upgrade_btn", 2, [-20, -21], [[6, 1, 0, -19, [8]]], [0, "61c8gCJQhJqKq2hJjTn9ji", 1, 0], [5, 211, 104], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "upgrade_btn", 2, [-23, -24], [[6, 1, 0, -22, [11]]], [0, "96x1jU4D1JlKZeO6VcWp1/", 1, 0], [5, 211, 104], [229, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "item", [-25, -26, -27], [0, "c2EdYVgzVCU4B/gdDAkOfI", 1, 0], [5, 160, 160], [-221.378, 70.148, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 3, [[7, "召唤10次", 30, false, false, 1, 1, 1, 1, -28, [3]], [8, 3, -29, [4, 4278190080]]], [0, "catt1b0khOz5qXw6d3JD6/", 1, 0], [5, 129.37, 56.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 4, [[7, "召唤10次", 30, false, false, 1, 1, 1, 1, -30, [6]], [8, 3, -31, [4, 4278190080]]], [0, "eawcEuMkRJR6pkeEnl0gFJ", 1, 0], [5, 129.37, 56.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 5, [[7, "召唤10次", 30, false, false, 1, 1, 1, 1, -32, [9]], [8, 3, -33, [4, 4278190080]]], [0, "5adu/RUmdLVIjKVavuAY62", 1, 0], [5, 129.37, 56.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 1, [[20, 45, 750, 1334, -34]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [1, "wz_cwzh", 1, [[3, -35, [0], 1]], [0, "48juG/VLFM54MJwJExAjdl", 1, 0], [5, 326, 84], [0, 375.437, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "gMsg", 1, [[21, false, "<b><outline color=black width=4><color=#f8a64e>8</c>次内必出<color=#cd47ff>史诗</c>装备</outline>", 32, 50, -36]], [0, "40iZrpPk1EkbFmm05qyjKt", 1, 0], [5, 305.8, 63], [0, 0.5, 1], [0, 327.532, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "jian", 1, [-37], [0, "01FZG+X7ZIGpctVoLVmsEE", 1, 0], [5, 875.0719604492188, 1334], [0, -358.69, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "default", "idle", 0, false, "idle", 13, [2]], [4, "Item_NeedDisplay", 3, [5, "e5rCo8ubBNu5uQz9xgrXVj", true, -38, 4], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Item_NeedDisplay", 4, [5, "52knsJNQ9GQYWqD51oqhZd", true, -39, 7], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Item_NeedDisplay", 5, [5, "3aQkZQyx1EirnmblT/9hBF", true, -40, 10], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "popList", 1, [6], [0, "d82ktpN+1H07v5TSI3SC21", 1, 0], [0, -133.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "zb_yc_104", 6, [[3, -41, [12], 13]], [0, "2c1iAFMgBLZZqIhIf9b8N8", 1, 0], [5, 103, 105], [5.897, 9.829, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "s", 6, [[3, -42, [14], 15]], [0, "5ef9vqFgFC/L4JOWuZFq+Z", 1, 0], [5, 34, 45], [-35.273, -22.227, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_qp", 6, [[3, -43, [16], 17]], [0, "08fMJLCaFBxZRNo3uB/dOr", 1, 0], [5, 160, 160]]], 0, [0, 1, 1, 0, 4, 14, 0, 5, 2, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -5, 2, 0, -6, 18, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, 0, 3, 0, -1, 7, 0, -2, 15, 0, 0, 4, 0, -1, 8, 0, -2, 16, 0, 0, 5, 0, -1, 9, 0, -2, 17, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, -1, 14, 0, 1, 15, 0, 1, 16, 0, 1, 17, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 6, 1, 6, 7, 18, 43], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, -1, 2, -1, 2, -1, 2], [0, 2, 3, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 4, 0, 5, 0, 6]], [[{"name": "wz_sqzh", "rect": [0, 0, 326, 84], "offset": [0, 0], "originalSize": [326, 84], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [8], [7]]]]