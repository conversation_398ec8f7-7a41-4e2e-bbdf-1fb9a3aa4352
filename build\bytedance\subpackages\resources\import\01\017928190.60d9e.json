[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "c3+ruR7+FEnKfu8yo+WDeT", "edJUUtcvJF/biLJ+V/T0gf", "4613FnL15GjKE6Ci5QeOnx", "9alhDeZttPnIjWWf28gp1y", "e3q84hOUtFEo1aiRNVD8I7", "5brP7NB4dAvJguM5IzElrj", "22cjIamqZH/Lbdvp80pFLv", "f6NcoTBPNJ4qSyD40PNpRP", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "65OUoZ25pGHqftEDT5VTS4", "b02+mE2zFDDI9LFDVxZtWB"], ["node", "_spriteFrame", "root", "data", "_parent", "_N$normalSprite", "_N$disabledSprite", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "_styleFlags", "_lineHeight", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["6b3a0+oGFdFjJeL2/WiG0xH", ["node", "labelArr"], 3, 1, 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["1cb88LbIMdCF67rE0bwqZML", ["node", "clickEvents"], 3, 1, 9], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6, 6]], [[3, 0, 1, 2, 2], [2, 1, 0, 2, 3, 4, 3], [13, 0, 1, 2, 2], [0, 0, 6, 3, 4, 5, 7, 2], [2, 2, 3, 4, 1], [0, 0, 6, 8, 3, 4, 5, 7, 2], [0, 0, 6, 8, 3, 4, 5, 2], [0, 0, 6, 3, 4, 5, 10, 7, 2], [0, 0, 1, 6, 3, 4, 5, 7, 3], [1, 0, 5, 7, 1, 2, 3, 4, 10, 11, 8], [7, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 8, 3, 4, 5, 7, 2], [0, 0, 1, 6, 8, 3, 4, 5, 7, 3], [0, 0, 2, 6, 3, 4, 9, 5, 3], [8, 0, 1, 2, 3, 4, 5, 6, 3], [9, 0, 1, 1], [3, 1, 2, 1], [2, 0, 2, 3, 4, 2], [10, 0, 1], [4, 0, 1, 2, 3, 4], [4, 0, 3, 2], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1, 1], [5, 0, 1, 2, 3, 4], [5, 0, 1, 3, 3], [1, 0, 5, 8, 1, 6, 2, 3, 4, 9, 10, 11, 10], [1, 0, 1, 6, 2, 3, 4, 10, 11, 7], [1, 0, 5, 7, 8, 1, 6, 2, 3, 4, 9, 10, 11, 11], [14, 0, 1, 2, 3, 4, 5, 6, 7, 3]], [[[[10, "TT_ShareGiftView"], [11, "TT_ShareGiftView", 1, [-4, -5], [[16, -3, [-2]]], [17, -1, 0], [5, 750, 1334]], [12, "item", [-8, -9, -10, -11], [[1, 1, 0, -6, [18], 19], [19, -7]], [0, "d6e0/HRQxEOIhq3vexkb1/", 1, 0], [5, 540, 500], [0, -34.195, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "bg", 1, [2, -13, -14], [[20, 45, 750, 1334, -12]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [6, "New Node", 2, [-16, -17], [[22, 1, 1, 50, -15, [5, 250, 48]]], [0, "974831gpxPQKuUVGlRYiMc", 1, 0], [5, 250, 48]], [5, "coin_bg", 4, [-19, -20], [[4, -18, [7], 8]], [0, "80Q/N3ExBKGaKByUi0DNTU", 1, 0], [5, 100, 100], [-75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "dm_bg", 4, [-22, -23], [[4, -21, [12], 13]], [0, "0cSU1GvRJAOYplm6TSfktC", 1, 0], [5, 100, 100], [75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "btn", 1, 2, [-26], [[1, 1, 0, -24, [16], 17], [23, -25, [[24, "6b3a0+oGFdFjJeL2/WiG0xH", "onBtn", "Get", 1]]]], [0, "6a+IAWGbNGjKQ1ySLTTZD/", 1, 0], [5, 250, 110], [0, -243.078, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "maskbg", 140, 1, [[21, 45, -27], [18, 0, -28, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [7, "count", 5, [[9, "x200", 28, 25, false, 2, 1, 2, -29, [6]], [2, 2, -30, [4, 4278190080]]], [0, "4fbcGgpJZN4Z4T1v8YonmX", 1, 0], [5, 100, 38], [0, 1, 0.5], [52.858, -37.946, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "count", 6, [[9, "x100", 28, 25, false, 2, 1, 2, -31, [11]], [2, 2, -32, [4, 4278190080]]], [0, "a8uXVC9QBN8Lr9jKRq55h9", 1, 0], [5, 70, 38], [0, 1, 0.5], [52.858, -37.946, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "tips", 1, 2, [[26, "每天可分享2次", 30, false, false, 1, 1, 1, 2, 1, -33, [14]], [2, 3, -34, [4, 4278190080]]], [0, "13Gfd5RLpAIrY7aGsWhdI2", 1, 0], [5, 433, 80], [0, -143.725, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Label", 1, 7, [[-35, [2, 3, -36, [4, 4278190080]]], 1, 4], [0, "0dMqiCXVVK5qk1vMw/OAUX", 1, 0], [5, 161, 66.6], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "title", 3, [-38], [[1, 1, 0, -37, [21], 22]], [0, "844ujGr4ZJzaFrTaMTfGjY", 1, 0], [5, 535, 103], [0, 165.59, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Label_title", 13, [[27, "分享有礼", false, 1, 1, 1, 2, -39, [20]], [2, 3, -40, [4, 4278190080]]], [0, "32Xj38mPRK9LMt/dxQ5dfk", 1, 0], [5, 280, 56.4], [0, -0.946, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn_close", 1, 3, [[29, 0.9, 3, -41, [[25, "6b3a0+oGFdFjJeL2/WiG0xH", "close", 1]], [4, 4293322470], [4, 3363338360], 23, 24], [1, 1, 2, -42, [25], 26]], [0, "32TgrMoVhJS4tIY1R6QjQF", 1, 0], [5, 52, 56], [224.474, 161.229, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "bg_shuxing", 2, [[1, 1, 0, -43, [2], 3]], [0, "2dlVi71YtFVoeFTwgbOraH", 1, 0], [5, 456, 203], [0, 2.145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_gold", 5, [[4, -44, [4], 5]], [0, "81kbAsIvJMa7yYa+MNNUfL", 1, 0], [5, 64, 59], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "shuijing", 6, [[4, -45, [9], 10]], [0, "05NYJe0VZG2Lw1XahJNeT3", 1, 0], [5, 47, 55], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "分享(2)", 35, 60, false, false, 1, 1, 1, 2, 1, 12, [15]]], 0, [0, 2, 1, 0, -1, 19, 0, 0, 1, 0, -1, 8, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -2, 4, 0, -3, 11, 0, -4, 7, 0, 0, 3, 0, -2, 13, 0, -3, 15, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, 0, 5, 0, -1, 17, 0, -2, 9, 0, 0, 6, 0, -1, 18, 0, -2, 10, 0, 0, 7, 0, 0, 7, 0, -1, 12, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 19, 0, 0, 12, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 3, 1, 2, 4, 3, 45], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, 5, 6, -1, 1], [0, 1, 0, 2, 0, 3, 0, 0, 4, 0, 5, 0, 0, 6, 0, 0, 0, 7, 0, 8, 0, 0, 9, 10, 11, 0, 12]], [[{"name": "btn_home_gleen", "rect": [0, 0, 82, 124], "offset": [0, 0], "originalSize": [82, 124], "capInsets": [38, 0, 38, 0]}], [6], 0, [0], [7], [13]]]]