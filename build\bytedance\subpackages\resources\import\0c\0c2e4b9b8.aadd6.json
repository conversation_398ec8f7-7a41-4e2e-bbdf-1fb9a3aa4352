[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "edDwn5bQdASY5v9e0bCAhi", "ddFhwuPitMK6rcBkC3ddyJ", "2b4vWm6xxFHJg6KVm92Xlr", "eer4QofNNLOqZtK9df0rqC", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "0aOU9JK+NMX4RiRCGelLON", "6eFZ9xCBFLRYlecnzVr/1r", "f9qnuSq3NFwbgvCvXIMiTP"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "root", "tag", "puzzleNode", "upgradeNode", "equipLv", "equipFrame", "equipCellIcon", "equipBg", "equipImg", "equipName", "_N$barSprite", "data", "_parent", "_N$normalSprite", "_N$disabledSprite"], [["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_prefab", "_components", "_contentSize", "_children", "_trs", "_parent"], 1, 4, 9, 5, 2, 7, 1], ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 0, 1, 2, 4, 5, 7, 2, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_N$overflow", "_enableWrapText", "node", "_materials"], -6, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["1e389XcopBF+7hXswEiyaOy", ["node", "equipName", "equipImg", "equipBg", "equipCellIcon", "equipFrame", "equipLv", "upgradeNode", "puzzleNode", "tag"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Widget", ["_alignFlags", "node"], 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[4, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 2], [0, 3, 4, 1], [13, 0, 1, 2, 2], [1, 0, 7, 5, 2, 6, 2], [1, 0, 1, 7, 3, 2, 4, 6, 3], [2, 0, 3, 4, 5, 6, 7, 2], [12, 0, 1, 2], [6, 0, 2], [1, 0, 5, 3, 2, 4, 6, 2], [1, 0, 5, 3, 2, 4, 2], [1, 0, 7, 5, 3, 2, 4, 6, 2], [1, 0, 7, 3, 2, 4, 2], [2, 0, 3, 8, 4, 5, 6, 2], [2, 0, 1, 3, 4, 5, 6, 7, 3], [2, 0, 2, 3, 4, 5, 6, 9, 7, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [4, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [10, 0, 1, 2, 3], [11, 0, 1, 2, 3, 3], [0, 1, 0, 3, 4, 5, 3], [0, 3, 4, 5, 1], [0, 0, 3, 4, 5, 2], [0, 0, 2, 3, 4, 3], [0, 1, 0, 3, 4, 3], [0, 1, 3, 4, 2], [3, 0, 1, 2, 3, 6, 4, 5, 7, 9, 10, 9], [3, 0, 1, 2, 8, 3, 4, 5, 7, 9, 10, 9], [3, 0, 1, 2, 3, 6, 4, 5, 9, 10, 8]], [[[{"name": "item_20034", "rect": [4, 0, 113, 120], "offset": [0.5, 0], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [3], [2]], [[[8, "equiptem"], [9, "equipGridItem", [-12], [[16, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [17, -1, 0], [5, 165, 221], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "alwaysin", [-14, -15, -16, -17, -18, -19, -20], [[18, 0.9, 3, -13, [[19, "1e389XcopBF+7hXswEiyaOy", "onClick", 1]], [4, 4293322470], [4, 3363338360], 1, 14, 15]], [0, "a6ZUxzc9pAM77+hpj8vvnQ", 1, 0], [5, 154, 230]], [11, "New ProgressBar", 2, [-24, -25, -26], [[20, 132, 0, -22, -21], [21, 1, 0, -23, [10], 11]], [0, "b0DijN+I9IAImM6IGkoEzh", 1, 0], [5, 132, 24], [0, -56.753, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [1, "name", 2, [[-27, [7, 16, -28], [3, 2, -29, [4, 4278190080]]], 1, 4, 4], [0, "ecLiIqzStPH4eaKpr9PdNv", 1, 0], [5, 124.55, 31.5], [0, -25.689, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lv", 2, [[-30, [7, 16, -31], [3, 2, -32, [4, 4278190080]]], 1, 4, 4], [0, "baqhDqZI1MmovmuSzboe+U", 1, 0], [5, 135.55, 35.5], [0, 87.003, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "content", 1, [2], [-33], [0, "6aRoZzy4hEd7NFrfqTWruy", 1, 0], [5, 166, 213]], [1, "progress", 3, [[-34, [3, 2, -35, [4, 4278190080]]], 1, 4], [0, "b5HrMiNE5DpKRnR1R0+jmi", 1, 0], [5, 44.13, 29.2], [0, 0.301, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "state", 3, [-36, -37], [0, "e3Yk3WeK1Pb5s8+FZyk3zP", 1, 0], [-54.58, 1.16, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "icon_pintu", 8, [[22, -38, [6], 7]], [0, "aeGXhoMR1Bxa0yMRdlXa0a", 1, 0], [5, 33, 33]], [5, "icon_shengji", false, 8, [[23, 0, -39, [8], 9]], [0, "bbdC4G0RZIwqPks1jktxvp", 1, 0], [5, 35, 35], [113.333, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "tag", false, 2, [-40], [0, "b6gmVVbrlCeLdB+7lGNBng", 1, 0], [5, 43, 43], [56, 83, 0, 0, 0, 0, 1, 0.55, 0.55, 1]], [2, 11, [0]], [27, "Label", 20, 25, false, 1, 1, 1, 2, 4, [1]], [28, "等级1", 18, 25, false, false, 1, 1, 2, 5, [2]], [4, "goodnode", 2, [-41], [0, "e1h4rPz+dPG6ehmjte6bHk", 1, 0], [0, 34.296, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "goodimg", 15, [-42], [0, "58vtE0dfdGYoHyD15mpSOX", 1, 0], [5, 80, 80], [0, 2.191, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 0, false, 16, [3]], [15, "bar", 512, 3, [-43], [0, "4bhKv+V/VE9oW63F4vgGAn", 1, 0], [5, 0, 24], [0, 0, 0.5], [-66, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 1, 0, 18, [4]], [29, "0/25", 18, 20, false, 1, 1, 1, 7, [5]], [6, "icon_gezi_1", 2, [-44], [0, "95alIwcuJJipHDsNNZ9dbe", 1, 0], [5, 26, 26], [48.209, -32.613, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 21, [12]], [5, "mergeTips", false, 2, [[2, -45, [13]]], [0, "b0dSzxvJ1JHqHsxxvUnn2n", 1, 0], [5, 43, 43], [61.317, 98.35, 0, 0, 0, 0, 1, 1, 1, 1]], [26, 1, 6, [16]]], 0, [0, 4, 1, 0, 5, 12, 0, 6, 9, 0, 7, 10, 0, 8, 14, 0, 9, 20, 0, 10, 22, 0, 11, 24, 0, 12, 17, 0, 13, 13, 0, 0, 1, 0, -1, 6, 0, 0, 2, 0, -1, 11, 0, -2, 4, 0, -3, 5, 0, -4, 15, 0, -5, 3, 0, -6, 21, 0, -7, 23, 0, 14, 19, 0, 0, 3, 0, 0, 3, 0, -1, 18, 0, -2, 7, 0, -3, 8, 0, -1, 13, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, 0, 5, 0, 0, 5, 0, -1, 24, 0, -1, 20, 0, 0, 7, 0, -1, 9, 0, -2, 10, 0, 0, 9, 0, 0, 10, 0, -1, 12, 0, -1, 16, 0, -1, 17, 0, -1, 19, 0, -1, 22, 0, 0, 23, 0, 15, 1, 2, 16, 6, 45], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 14, 17, 19, 20, 24], [-1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 17, 18, -1, 2, 2, 1, 1, 2, 1], [0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 0, 5, 0, 0, 6, 7, 0, 1, 1, 8, 9, 1, 10]]]]