[1, ["ecpdLyjvZBwrvm+cedCcQy", "deMqh8egtOepwb0Zm04kMD", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "4dMZmkcjlJEbhEI0rAtegJ", "33vkt8nwtOUoHfX7rfVnHT", "40OtPssnZP9antMAWsDtDT", "7eokxwza1PXqlvbsavbhNH", "3aI6ktDIVIpbCHi5DMjtsj", "81af+oCaNAM5RXWzNa6lUL", "0a9d3cb35MYoMtDKfQo3FL", "a2MjXRFdtLlYQ5ouAFv/+R", "c71MhpO79OyJRuYeSPWAdr", "f5OTueD8lP9ogbWq5R2hYc", "01LxTfW1FLG7PbJZSszHaw", "38vRTLUYxDTqyqVAXhQTxE", "2a/b9BeYdFcZh3j62fkCp3", "09xJQ/x3xKmbwe9rWIGtds", "b2AxAf3ZJHXYpmZKl+3U37", "c0VITW/OJML7691KcQocPY", "97hxDilpZK4aBw6c9tlCZ+", "38KNCU8eNEnpUQjfxBoZHC", "3bEV0n32NGp7XLsXIyxeJg"], ["node", "_spriteFrame", "root", "_N$file", "asset", "_textureSetter", "checkMark", "_N$target", "toggleC<PERSON><PERSON>", "myGiftIcon", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color"], -1, 4, 9, 5, 1, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], "cc.SpriteFrame", ["cc.Label", ["_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_isSystemFontUsed", "_styleFlags", "_lineHeight", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_children", "_trs"], 1, 1, 2, 4, 5, 2, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "node", "_layoutSize"], 0, 1, 5], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 1, 1, 5, 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["29754hYulZN0pxOmQa0A+9d", ["node", "myGiftIcon", "toggleC<PERSON><PERSON>"], 3, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ToggleContainer", ["node"], 3, 1]], [[5, 0, 1, 2, 2], [0, 0, 7, 9, 5, 4, 6, 8, 2], [0, 0, 7, 5, 4, 6, 8, 2], [15, 0, 1, 2, 2], [1, 4, 5, 6, 1], [0, 0, 7, 4, 8, 2], [14, 0, 1, 2, 3, 3], [7, 2, 0, 1, 3, 4, 4], [3, 3, 0, 6, 4, 1, 2, 7, 8, 9, 7], [0, 0, 7, 9, 5, 4, 6, 2], [0, 0, 3, 7, 5, 4, 6, 8, 3], [9, 0, 1, 2, 3, 4], [9, 0, 1, 3, 3], [1, 1, 0, 2, 4, 5, 6, 4], [1, 1, 0, 2, 4, 5, 4], [4, 0, 1, 2, 6, 3, 4, 5, 3], [6, 0, 3, 2], [8, 0, 1, 2, 3, 4, 5, 6, 3], [10, 0, 1, 2, 2], [3, 3, 0, 6, 4, 5, 1, 2, 7, 8, 9, 8], [11, 0, 2], [0, 0, 1, 9, 5, 4, 6, 3], [0, 0, 2, 7, 5, 4, 10, 6, 3], [0, 0, 7, 5, 4, 6, 2], [0, 0, 7, 5, 4, 10, 6, 8, 2], [12, 0, 1, 2, 3, 4, 5, 6, 2], [4, 0, 2, 6, 3, 4, 5, 2], [4, 0, 2, 3, 4, 5, 7, 2], [13, 0, 1, 2, 1], [5, 1, 2, 1], [6, 0, 1, 2, 3, 4], [7, 0, 1, 3, 4, 3], [8, 0, 2, 3, 4, 5, 6, 2], [1, 3, 1, 0, 4, 5, 6, 4], [1, 1, 0, 4, 5, 6, 3], [1, 0, 4, 5, 6, 2], [1, 4, 5, 1], [10, 1, 2, 1], [3, 3, 0, 4, 5, 1, 2, 7, 8, 9, 7], [3, 0, 6, 5, 1, 2, 7, 8, 6], [16, 0, 1]], [[[{"name": "cb", "rect": [0, 0, 70, 71], "offset": [0, 0], "originalSize": [70, 71], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [5], [7]], [[{"name": "sp_red", "rect": [0, 0, 65, 66], "offset": [0, 0], "originalSize": [65, 66], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [5], [8]], [[{"name": "title-b", "rect": [0, 0, 223, 50], "offset": [0, 0], "originalSize": [223, 50], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [5], [9]], [[{"name": "icon_bsj", "rect": [7, 5, 445, 449], "offset": [-0.5, 0.5], "originalSize": [460, 460], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [5], [10]], [[[20, "NewGiftView"], [21, "NewGiftView", 1, [-5, -6], [[28, -4, -3, -2]], [29, -1, 0], [5, 750, 1334]], [9, "bg", 1, [-8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[30, 45, 750, 1334, -7]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [1, "dayNode", 2, [-19, -20, -21, -22, -23, -24, -25, -26], [[31, 3, 10, -18, [5, 497, 244]]], [0, "7a8HbY3BhGPaHTr8GDTi4L", 1, 0], [5, 497, 244], [0, -103.365, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [25, "toggle", 2, [-30, -31, -32], [[-27, [7, 1, 1, 20, -28, [5, 400, 61]], [16, 16, -29]], 1, 4, 4], [0, "756G4CHdhJr42QtJj1yIn4", 1, 0], [5, 400, 61], [0, -359.668, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "toggle1", 4, [-36, -37, -38], [[32, 3, -35, [4, 4292269782], -34, -33, [[11, "29754hYulZN0pxOmQa0A+9d", "onToggle", "0", 1]]]], [0, "9bUWUkMrxIHZPiu73EoT5J", 1, 0], [5, 120, 50], [-140, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "toggle2", 4, [-42, -43, -44], [[17, 3, false, -41, [4, 4292269782], -40, -39, [[11, "29754hYulZN0pxOmQa0A+9d", "onToggle", "1", 1]]]], [0, "b85VGEjKZMsJapEheqpvY4", 1, 0], [5, 120, 50], [0, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "toggle3", 4, [-48, -49, -50], [[17, 3, false, -47, [4, 4292269782], -46, -45, [[11, "29754hYulZN0pxOmQa0A+9d", "onToggle", "2", 1]]]], [0, "95Z0qo4tlIya63RMetU25P", 1, 0], [5, 120, 50], [140, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "img_txd", 2, [-53, -54], [[33, false, 1, 0, -51, [17], 18], [37, -52, [[12, "29754hYulZN0pxOmQa0A+9d", "openEquip", 1]]]], [0, "f3X1g7F4VH4pGIkzPxPo/Z", 1, 0], [5, 295, 54], [0, 38.559, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "button03", 2, [-57], [[34, 1, 0, -55, [31], 32], [18, 3, -56, [[12, "29754hYulZN0pxOmQa0A+9d", "buyIt", 1]]]], [0, "68nW/dHYNOQ7tM5GwJogXp", 1, 0], [5, 216, 72], [0, -264.635, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "priLayout", 9, [-59, -60], [[7, 1, 1, 20, -58, [5, 95.76, 80]]], [0, "4c3p3YOYNEmZllLhbGo7AC", 1, 0], [5, 95.76, 80]], [22, "maskbg", 230, 1, [[16, 45, -61], [35, 0, -62, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [2, "icon_wh", 2, [[4, -63, [7], 8], [18, 3, -64, [[12, "29754hYulZN0pxOmQa0A+9d", "close", 1]]]], [0, "58UcXqC3ZMUZL+Tr7SvLK0", 1, 0], [5, 70, 71], [172.395, 428.46, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "img_pzmcd", 2, [-66], [[4, -65, [11], 12]], [0, "18BTAQ/0BLBJzZQ05GYzLZ", 1, 0], [5, 223, 50], [5, 92.805, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbNe", 13, [[38, "冰霜剑", 32, false, 1, 1, 1, -67, [9], 10], [3, 3, -68, [4, 4279900698]]], [0, "2fQYuEe3ZGlY44rMW17lw+", 1, 0], [5, 102, 56.4], [-5, 1.214, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "lbDesc", 8, [[19, "超级无敌法杖", 28, 28, false, 1, 1, 1, -69, [13], 14], [3, 2, -70, [4, 4278190080]]], [0, "45Aj7MotlMW47hB5DMd2P6", 1, 0], [5, 172, 39.28]], [2, "lbPrice", 10, [[19, "6", 32, 32, false, 1, 1, 1, -71, [29], 30], [3, 2, -72, [4, 4279900698]]], [0, "8dUmyitv9MhIqpc3AgREmt", 1, 0], [5, 21.76, 44.32], [37.00000000000001, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "lbTime", 2, [[39, 22, 30, 1, 1, 1, -73, [33]], [3, 3, -74, [4, 4279966491]]], [0, "3f+P4G8n5EOLZm3SE5FGR7", 1, 0], [4, 4293322470], [5, 6, 43.8], [0, -416.437, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 5, [-76], [[13, 1, 0, false, -75, [36], 37]], [0, "f5e6s6RL1JjpeYkoa34+5n", 1, 0], [5, 120, 50]], [1, "priLayout", 18, [-78], [[7, 1, 1, 10, -77, [5, 82, 80]]], [0, "c1oXWUktxGLosXW4kqnMO1", 1, 0], [5, 82, 80], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 19, [[8, "第一天", 26, 26, false, 1, 1, -79, [34], 35], [3, 2, -80, [4, 4278190080]]], [0, "35gB0UxD1Kl4BPVCrI6tgT", 1, 0], [5, 82, 36.76], [0, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "checkmark", 5, [-82], [-81], [0, "c3u01Fr+RB1au6cckMf2e3", 1, 0], [5, 120, 50]], [1, "priLayout", 21, [-84], [[7, 1, 1, 10, -83, [5, 82, 80]]], [0, "f5GcgGSb5FE5G0v9nceQrA", 1, 0], [5, 82, 80], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 22, [[8, "第一天", 26, 26, false, 1, 1, -85, [38], 39], [3, 2, -86, [4, 4278190080]]], [0, "c4k2pAbhZDkYTGEr45EbwJ", 1, 0], [5, 82, 36.76], [0, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 6, [-88], [[13, 1, 0, false, -87, [45], 46]], [0, "94QLwQzMtK2paYvA+uFJEe", 1, 0], [5, 120, 50]], [1, "priLayout", 24, [-90], [[7, 1, 1, 10, -89, [5, 82, 80]]], [0, "9b7p+ltKhAmojp4u9/s9Fw", 1, 0], [5, 82, 80], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 25, [[8, "第二天", 26, 26, false, 1, 1, -91, [43], 44], [3, 2, -92, [4, 4278190080]]], [0, "65EeFm+/FFNLYFHKs6yDve", 1, 0], [5, 82, 36.76], [0, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "checkmark", false, 6, [-94], [-93], [0, "02R3gO8+JPPbIcHx3l2A26", 1, 0], [5, 120, 50]], [1, "priLayout", 27, [-96], [[7, 1, 1, 10, -95, [5, 100, 80]]], [0, "f7vHCHVIpO2YJ4SV69IYuP", 1, 0], [5, 100, 80], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 28, [[8, "第二天", 26, 26, false, 1, 1, -97, [47], 48], [3, 2, -98, [4, 4278190080]]], [0, "07G5dE/uVIorF3Bk6ggx/s", 1, 0], [5, 100, 54.4], [0, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", 7, [-100], [[13, 1, 0, false, -99, [54], 55]], [0, "0alOIWzjVBvILpA44Wo60J", 1, 0], [5, 120, 50]], [1, "priLayout", 30, [-102], [[7, 1, 1, 10, -101, [5, 82, 80]]], [0, "cfqxZGkShKioFbxA+uma4I", 1, 0], [5, 82, 80], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 31, [[8, "第三天", 26, 26, false, 1, 1, -103, [52], 53], [3, 2, -104, [4, 4278190080]]], [0, "0e6j7sFY9P3rjj4xDooWq4", 1, 0], [5, 82, 36.76], [0, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "checkmark", false, 7, [-106], [-105], [0, "e8FcwYzQhDeaeY26wIUMPV", 1, 0], [5, 120, 50]], [1, "priLayout", 33, [-108], [[7, 1, 1, 10, -107, [5, 100, 80]]], [0, "08MRu4Rt1PoqYZ5MZ0hia6", 1, 0], [5, 100, 80], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 34, [[8, "第三天", 26, 26, false, 1, 1, -109, [56], 57], [3, 2, -110, [4, 4278190080]]], [0, "e2tU3HM3tFH7/8OkgoAvrM", 1, 0], [5, 100, 54.4], [0, 4.724, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_hddb01", 2, [[4, -111, [2], 3]], [0, "df24XB4GRHxqB5j7MPRo01", 1, 0], [5, 601, 1005], [7.126, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wz_xslb", 2, [[4, -112, [4], 5]], [0, "9e0BDUEs9JhIL3SklGCBCP", 1, 0], [5, 275, 52], [0, 450.535, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "gift_icon", 2, [-113], [0, "c8mDg/xQtDW6x4gPvOqP1Q", 1, 0], [5, 445, 449], [0, 271.219, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [36, 38, [6]], [2, "icon_wh", 8, [[4, -114, [15], 16]], [0, "81MSRnrBlK+ptLqeV7UHU9", 1, 0], [5, 32, 32], [133.107, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "NewGiftGoodItem", 3, [6, "3cqje1IYxDh4AJYmJsFo65", true, -115, 19], [-190.5, 62, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "58JAd+Ie5PT53titj/vnMA", true, -116, 20], [-64.5, 62, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "15XFT4d1xLB53eSesNpI6w", true, -117, 21], [61.5, 62, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "fcwdpD6rNKT5jWCFeWTotM", true, -118, 22], [187.5, 62, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "63iv+JlP9C3bN9o9h5OW76", true, -119, 23], [-190.5, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "4aXDgo3O9Geb2nUiI8FrlM", true, -120, 24], [-64.5, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "18K0rjO8JKRoI91yWrpXak", true, -121, 25], [61.5, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "NewGiftGoodItem", 3, [6, "66f+DfF39BV4HdiQFfMpqs", true, -122, 26], [187.5, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "imgSp", 10, [[4, -123, [27], 28]], [0, "7cQYOgw4RDvI0WyssIknlZ", 1, 0], [5, 54, 56], [-20.879999999999995, 3, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [14, 1, 0, false, 21, [40]], [10, "ggtipicon", false, 5, [[4, -124, [41], 42]], [0, "45cC9KwQZHnJf1PxhQiaXa", 1, 0], [5, 65, 66], [75.874, 36.251, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [14, 1, 0, false, 27, [49]], [10, "ggtipicon", false, 6, [[4, -125, [50], 51]], [0, "317ZrnvphIh6o59+CCgDfv", 1, 0], [5, 65, 66], [75.874, 36.251, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [14, 1, 0, false, 33, [58]], [10, "ggtipicon", false, 7, [[4, -126, [59], 60]], [0, "60PiUWDCpEl7qiNv+5fGc/", 1, 0], [5, 65, 66], [75.874, 36.251, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [40, 4]], 0, [0, 2, 1, 0, 8, 56, 0, 9, 39, 0, 0, 1, 0, -1, 11, 0, -2, 2, 0, 0, 2, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 12, 0, -5, 13, 0, -6, 8, 0, -7, 3, 0, -8, 9, 0, -9, 17, 0, -10, 4, 0, 0, 3, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, -4, 44, 0, -5, 45, 0, -6, 46, 0, -7, 47, 0, -8, 48, 0, -1, 56, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, 6, 50, 0, 7, 5, 0, 0, 5, 0, -1, 18, 0, -2, 21, 0, -3, 51, 0, 6, 52, 0, 7, 6, 0, 0, 6, 0, -1, 24, 0, -2, 27, 0, -3, 53, 0, 6, 54, 0, 7, 7, 0, 0, 7, 0, -1, 30, 0, -2, 33, 0, -3, 55, 0, 0, 8, 0, 0, 8, 0, -1, 15, 0, -2, 40, 0, 0, 9, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, -1, 49, 0, -2, 16, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 20, 0, -1, 50, 0, -1, 22, 0, 0, 22, 0, -1, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, -1, 26, 0, 0, 26, 0, 0, 26, 0, -1, 52, 0, -1, 28, 0, 0, 28, 0, -1, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, -1, 31, 0, 0, 31, 0, -1, 32, 0, 0, 32, 0, 0, 32, 0, -1, 54, 0, -1, 34, 0, 0, 34, 0, -1, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, -1, 39, 0, 0, 40, 0, 2, 41, 0, 2, 42, 0, 2, 43, 0, 2, 44, 0, 2, 45, 0, 2, 46, 0, 2, 47, 0, 2, 48, 0, 0, 49, 0, 0, 51, 0, 0, 53, 0, 0, 55, 0, 10, 1, 126], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 50, 52, 54], [-1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, 4, 4, 4, 4, 4, 4, 4, 4, -1, 1, -1, 3, -1, 1, -1, -1, 3, -1, 1, -1, 3, -1, -1, 1, -1, 3, -1, 1, -1, 3, -1, -1, 1, -1, 3, -1, 1, -1, 3, -1, -1, 1, 1, 1, 1, 1], [0, 11, 0, 12, 0, 13, 0, 0, 14, 0, 3, 0, 15, 0, 3, 0, 16, 0, 17, 1, 1, 1, 1, 1, 1, 1, 1, 0, 18, 0, 3, 0, 19, 0, 0, 2, 0, 4, 0, 2, 0, 0, 5, 0, 2, 0, 4, 0, 2, 0, 0, 5, 0, 2, 0, 4, 0, 2, 0, 0, 5, 20, 6, 6, 6]], [[{"name": "bm", "rect": [1, 0, 599, 1005], "offset": [0, 0], "originalSize": [601, 1005], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [5], [21]], [[{"name": "title_xslb", "rect": [0, 0, 275, 52], "offset": [0, 0], "originalSize": [275, 52], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [5], [22]]]]