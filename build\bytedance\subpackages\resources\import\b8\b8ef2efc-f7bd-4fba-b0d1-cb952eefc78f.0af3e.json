[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "f8npR6F8ZIZoCp3cKXjJQz", "d3gQGt3U5ODrxK5EaBPUsd", "a2MjXRFdtLlYQ5ouAFv/+R", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "73jGpne/9JUI/171Zur5Pr", "9fUicDpb9ESYDEBfH7jjiH", "9alhDeZttPnIjWWf28gp1y", "deYeqBbrtAM7ABF8+EGpZQ", "c0VITW/OJML7691KcQocPY", "a8lPnHTihBmrRgij+r2Olk", "40OtPssnZP9antMAWsDtDT", "65hdeSelhOVq5sz7eNtoFd", "cdvDZRLPZKb70zilA0TpQ8"], ["node", "_spriteFrame", "_N$file", "root", "_parent", "asset", "_N$disabledSprite", "content", "_N$target", "data"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_enableWrapText", "_N$cacheMode", "_lineHeight", "_N$overflow", "_N$fontFamily", "node", "_materials", "_N$file"], -8, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -4, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$affectedByScale", "_N$paddingLeft", "_N$paddingRight", "_N$spacingY", "node", "_layoutSize"], -4, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["6467bRqlENIrawyykX3FMRo", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["bf1760LkBxFVaSNjXip45ah", ["node", "labelArr", "content"], 3, 1, 2, 1]], [[3, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 9, 2], [11, 0, 1, 2, 2], [0, 0, 7, 8, 6, 4, 5, 9, 2], [2, 1, 0, 2, 3, 4, 3], [2, 2, 3, 4, 1], [1, 0, 3, 8, 4, 5, 1, 2, 11, 12, 13, 8], [3, 0, 1, 2], [1, 0, 3, 4, 5, 1, 2, 11, 12, 13, 7], [1, 0, 3, 4, 1, 2, 11, 12, 13, 6], [0, 0, 7, 6, 4, 10, 5, 9, 2], [0, 0, 7, 4, 9, 2], [10, 0, 1, 2, 3, 3], [4, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4], [8, 0, 2], [0, 0, 2, 8, 6, 4, 5, 3], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 7, 8, 4, 5, 2], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 1, 7, 6, 4, 5, 9, 3], [0, 0, 8, 6, 4, 5, 9, 2], [0, 0, 8, 6, 4, 5, 2], [0, 0, 8, 6, 4, 5, 11, 9, 2], [9, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 7, 6], [5, 0, 5, 6, 7, 4], [2, 0, 2, 3, 4, 2], [3, 1, 2, 1], [1, 0, 6, 4, 1, 2, 9, 7, 11, 12, 8], [1, 0, 3, 8, 5, 1, 2, 10, 11, 12, 8], [1, 0, 3, 5, 1, 2, 11, 12, 6], [1, 0, 3, 6, 4, 5, 1, 2, 7, 11, 12, 13, 9], [4, 0, 1, 2, 3, 4, 5, 6, 2], [4, 1, 1], [6, 0, 1, 3, 3], [7, 0, 1, 2, 3, 7, 8, 5], [7, 0, 1, 4, 5, 2, 6, 7, 8, 7], [12, 0, 1], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 1]], [[15, "IdleAwardAdView"], [16, "IdleAwardAdView", 1, [-5, -6], [[42, -4, [-3], -2]], [29, -1, 0], [5, 642, 900]], [18, "bg", 1, [-7, -8, -9, -10, -11, -12, -13, -14, -15], [0, "d5iMdkpqZNhaS+GSBTrslt", 1, 0], [5, 655, 950]], [22, "Item_NeedDisplay", [-19, -20], [[37, 1, 1, 2, true, -17, [5, 112.94, 40]], [39, -18]], [7, "0a3Z87sHJFm7I+d7u5IFoo", -16], [5, 112.94, 40], [0, 2.473, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "title_bg", 2, [-23, -24, -25], [[4, 1, 0, -21, [10], 11], [35, -22]], [0, "63oHBAYQVMWLB1BdJu54K0", 1, 0], [5, 655, 750]], [3, "button03", 2, [-28, -29, -30], [[4, 1, 0, -26, [36], 37], [13, 3, -27, [[14, "bf1760LkBxFVaSNjXip45ah", "onBtn", "getAward", 1]]]], [0, "c6w8jIBU5Daoxa992Hqgji", 1, 0], [5, 274, 108], [165, -300, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "button04", 2, [-33, -34, 3], [[4, 1, 0, -31, [45], 46], [13, 3, -32, [[14, "bf1760LkBxFVaSNjXip45ah", "onBtn", "getfreeAward", 1]]]], [0, "98OJFzb0pBIqnbvPEBrsmg", 1, 0], [5, 274, 108], [-160, -300, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "Background", 4, [-38], [[5, -35, [5], 6], [34, 3, -37, [[36, "bf1760LkBxFVaSNjXip45ah", "close", 1]], [4, 4293322470], [4, 3363338360], -36, 7]], [0, "abxGRs4WJGjJuzEP/Sk48A", 1, 0], [5, 64, 65], [280, 335, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "img_jbd", 2, [-40, -41, -42], [[4, 1, 0, -39, [21], 22]], [0, "52+DDyQbBPirMgG8Iffucc", 1, 0], [5, 205, 45], [-154.729, 116.024, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_jbd1", 2, [-44, -45, -46], [[4, 1, 0, -43, [29], 30]], [0, "4b/hx8hA9KDImSVv0GPhR4", 1, 0], [5, 205, 45], [174.763, 116.024, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "content", [-48], [[38, 1, 3, 15, 10, 20, 15, -47, [5, 550, 120]]], [0, "25ZUKELnxKBYuJv6WBfZui", 1, 0], [5, 550, 120], [0, 0.5, 1], [0, 127.342, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "view", [10], [[40, 0, -49, [50]], [27, 45, 240, 250, -50]], [0, "9dbo7QRptI65vz2zRcniFY", 1, 0], [5, 585, 220]], [17, "maskbg", 200, 1, [[26, 45, -54, -54, -217, -217, -51], [28, 0, -52, [0], 1]], [0, "1cRiRztq9HK7NnkLOmpPd/", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "Label_title", 4, [[8, "快速游历", 48, false, 1, 1, 1, -53, [2], 3], [2, 4, -54, [4, 4278190080]]], [0, "a95L7sfc5IW4h1I5ISJcwK", 1, 0], [5, 200, 58.4], [0, 335, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label_title", 4, [[9, "可立即获得一定时间的累计奖励", 36, false, 1, 1, -55, [8], 9], [2, 2, -56, [4, 4278190080]]], [0, "418Xr+MoVDnpoLV3RGpAKf", 1, 0], [5, 508, 54.4], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 2, [[9, "点击任意位置关闭", 36, false, 1, 1, -57, [12], 13], [2, 2, -58, [4, 4278190080]]], [0, "50HEg+TpdGSo8+tTwG1Dse", 1, 0], [5, 292, 54.4], [0, -506.287, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lbTime", 2, [[-59, [2, 2, -60, [4, 4278190080]]], 1, 4], [0, "7f9JlrpztBCLbbvb9HHvi8", 1, 0], [5, 135.95, 41.8], [0, 172.787, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbVal", 8, [[6, "14.4k", 28, 22, false, 1, 1, 1, -61, [17], 18], [2, 2, -62, [4, 4278190080]]], [0, "298vPRnJJBnLgF107ovN80", 1, 0], [5, 73.86, 31.72], [13.988, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbTime", 8, [[6, "32/分钟", 20, 20, false, 1, 1, 1, -63, [19], 20], [2, 2, -64, [4, 4278190080]]], [0, "5ag+I8NsRCaK7PWO4EKg8z", 1, 0], [4, 4283168127], [5, 74.04, 29.2], [-87.035, -16.375, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbVal", 9, [[6, "14.4k", 28, 22, false, 1, 1, 1, -65, [25], 26], [2, 2, -66, [4, 4278190080]]], [0, "24u0+GjxpLdZzN6HOi5CZl", 1, 0], [5, 73.86, 31.72], [13.988, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbTime", 9, [[6, "32/分钟", 20, 20, false, 1, 1, 1, -67, [27], 28], [2, 2, -68, [4, 4278190080]]], [0, "71MQ/26JJOYrXLOIYOOUCo", 1, 0], [4, 4283168127], [5, 74.04, 29.2], [-87.035, -16.375, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label_title", 5, [[8, "可用次数：", 24, false, 1, 1, 1, -69, [31], 32], [2, 2, -70, [4, 4278190080]]], [0, "5eKcIo4KJEjJL5ThImYtSr", 1, 0], [5, 124, 54.4], [0, 74.019, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNe", 5, [[9, "领取奖励", 36, false, 1, 1, -71, [33], 34], [2, 2, -72, [4, 4279374353]]], [0, "ba1MwrKjVKUaSFNHWGd3rH", 1, 0], [5, 148, 54.4], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label_title", 6, [[8, "可用次数：", 24, false, 1, 1, 1, -73, [38], 39], [2, 2, -74, [4, 4278190080]]], [0, "e8LaNneUhA36K4KG9FZcEf", 1, 0], [5, 124, 54.4], [0, 74.019, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbNe", false, 6, [[32, "免费", 36, 1, 1, 1, -75, [40]], [2, 3, -76, [4, 4279374353]]], [0, "b3RciQZNtFGo3hrdF5ofDQ", 1, 0], [5, 78, 56.4], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 3, [[33, "100", 36, false, false, 1, 1, 1, 1, -77, [43], 44], [2, 2, -78, [4, 4278190080]]], [7, "c51Kf+QEpFP7uzo7Z29m6A", 3], [5, 63.94, 54.4], [24.5, 1.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New ScrollView", 2, [11], [[41, false, 0.75, 0.23, null, null, -79, 10]], [0, "c9eYB1H4FC7ruZ8X/siRJF", 1, 0], [5, 585, 220], [0, -63.101, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Label", false, 7, [[30, "返回", false, false, 1, 1, 1, 1, -80, [4]]], [0, "57w9m/PXFEfZ6PllirTlxG", 1, 0], [4, 4278209897], [5, 100, 62]], [31, "07:24:00", 30, 30, 1, 1, 1, "Consolas", 16, [14]], [1, "icon_gold", 8, [[5, -81, [15], 16]], [0, "53FlQ3op9DoIvZbkwVZaRz", 1, 0], [5, 48, 49], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_gold", 9, [[5, -82, [23], 24]], [0, "f9JXgnaLJHsZh0+dgxFX4Q", 1, 0], [5, 47, 55], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Item_NeedDisplay", 5, [12, "0a3Z87sHJFm7I+d7u5IFoo", true, -83, 35], [0, -21.006, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon", 3, [[5, -84, [41], 42]], [7, "37+oKdJX9Hr4prAI9kch+w", 3], [5, 47, 55], [-32.97, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_gmtxzjld", 2, [[4, 1, 0, -85, [47], 48]], [0, "04mXS5OxVJj6w/aw2LNr9Z", 1, 0], [5, 605, 280], [0, -62.227, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "RewardItem", 10, [12, "1c39X23AxDS5KHIWleBG8w", true, -86, 49], [-202, -60, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 7, 10, 0, -1, 28, 0, 0, 1, 0, -1, 12, 0, -2, 2, 0, -1, 4, 0, -2, 15, 0, -3, 16, 0, -4, 8, 0, -5, 9, 0, -6, 5, 0, -7, 6, 0, -8, 33, 0, -9, 26, 0, 3, 3, 0, 0, 3, 0, 0, 3, 0, -1, 32, 0, -2, 25, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, -2, 7, 0, -3, 14, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, -2, 22, 0, -3, 31, 0, 0, 6, 0, 0, 6, 0, -1, 23, 0, -2, 24, 0, 0, 7, 0, 8, 7, 0, 0, 7, 0, -1, 27, 0, 0, 8, 0, -1, 29, 0, -2, 17, 0, -3, 18, 0, 0, 9, 0, -1, 30, 0, -2, 19, 0, -3, 20, 0, 0, 10, 0, -1, 34, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -1, 28, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 29, 0, 0, 30, 0, 3, 31, 0, 0, 32, 0, 0, 33, 0, 3, 34, 0, 9, 1, 3, 4, 6, 10, 4, 11, 11, 4, 26, 86], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 2, -1, -1, 1, 6, -1, 2, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 2, 5, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, 1, 5, -1], [0, 4, 0, 2, 0, 0, 5, 6, 0, 2, 0, 7, 0, 2, 0, 0, 8, 0, 1, 0, 1, 0, 3, 0, 9, 0, 1, 0, 1, 0, 3, 0, 1, 0, 2, 10, 0, 11, 0, 1, 0, 0, 12, 0, 1, 0, 13, 0, 14, 15, 0]]