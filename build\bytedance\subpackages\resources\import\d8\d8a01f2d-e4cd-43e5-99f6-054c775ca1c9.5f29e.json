[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "03W+ZBfDNJwreQxWi3jfXh", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "97IAXRHnlLq7dNeJPB6SbL", "22cjIamqZH/Lbdvp80pFLv"], ["node", "_spriteFrame", "_N$disabledSprite", "root", "desc", "title", "target", "_N$target", "_N$content", "data", "_parent"], [["cc.Node", ["_name", "_active", "_opacity", "_groupIndex", "_components", "_prefab", "_contentSize", "_parent", "_children", "_color", "_trs"], -1, 9, 4, 5, 1, 2, 5, 7], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_bottom", "node"], -2, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_styleFlags", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_N$string", "_N$fontSize", "_N$maxWidth", "node"], 0, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["31060Wp8DNAh5ScH0KVBLqA", ["node", "title", "desc"], 3, 1, 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 7, 8, 4, 5, 6, 10, 2], [1, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 5, 3], [2, 0, 3, 4, 5, 4], [4, 1, 2, 1], [5, 0, 1, 2, 3], [7, 0, 2], [0, 0, 3, 8, 4, 5, 6, 3], [0, 0, 1, 2, 7, 4, 5, 9, 6, 4], [0, 0, 7, 8, 4, 5, 9, 6, 2], [0, 0, 2, 7, 4, 5, 9, 6, 3], [0, 0, 8, 4, 5, 6, 2], [0, 0, 1, 7, 4, 5, 9, 6, 10, 3], [0, 0, 1, 7, 4, 5, 6, 10, 3], [0, 0, 7, 8, 4, 5, 6, 2], [8, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [1, 1, 3, 4, 5, 2], [1, 1, 0, 3, 4, 3], [1, 2, 0, 3, 4, 5, 3], [2, 0, 1, 2, 5, 4], [3, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 2], [5, 0, 1, 3], [6, 0, 1, 5, 2, 3, 4, 8, 9, 7], [6, 0, 6, 1, 2, 3, 4, 7, 8, 9, 8], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 1]], [[7, "CommonExplain"], [8, "CommonExplain", 1, [-7, -8], [[31, -4, -3, -2], [5, -6, [[6, "31060Wp8DNAh5ScH0KVBLqA", "close", -5]]]], [22, -1, 0], [5, 750, 1335]], [12, "showNode", [-10, -11, -12, -13], [[3, 1, 0, -9, [15], 16]], [0, "a9hnsxLFFALI2B3biKtkPY", 1, 0], [5, 500, 600]], [10, "bg", 1, [-16, 2], [[4, 45, -2.842170943040401e-14, 2.842170943040401e-14, -14], [20, false, 0, -15, [17], 18]], [0, "a9eVDVVwRP65/QJaB+R1fD", 1, 0], [4, 4278190080], [5, 750, 1335]], [1, "Background", 2, [-20], [[18, 1, -17, [8], 9], [23, 3, -19, [[6, "31060Wp8DNAh5ScH0KVBLqA", "close", 1]], [4, 4293322470], [4, 3363338360], -18, 10]], [0, "3fMAxYxIRKwbgz1XxMloLB", 1, 0], [5, 52, 56], [199.507, 258.164, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "maskbg", 5, 3, [[4, 45, 5.329070518200751e-15, -5.329070518200751e-15, -21], [2, 0, -22, [2], 3], [5, -23, [[24, "d57a7d397FISp9LyX81u3JC", "onClickItem"]]]], [0, "femEAY3ZJOJrRwQw1l4dtX", 1, 0], [4, 4278190080], [5, 750, 1335]], [1, "New ScrollView", 2, [-27], [[19, 1, 0, -24, [14]], [30, false, 0.75, 0.23, null, null, -26, -25]], [0, "3ef3TpFF1OBYKBJuzX9qne", 1, 0], [5, 420, 480], [0, -34.993, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "mask", false, 177.98999999999998, 1, [[2, 0, -28, [0], 1], [21, 45, 750, 1334, -29]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4281542699], [5, 750, 1335]], [1, "title_zhua<PERSON><PERSON>", 2, [-31], [[3, 1, 0, -30, [5], 6]], [0, "7bVg4oZuVEp4FeHdyIWcCd", 1, 0], [5, 500, 600], [0, 1.4210854715202004e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Label_title", 8, [[-32, [27, 4, -33, [4, 4278190080]]], 1, 4], [0, "feYhcjccRIZ4akjhA8kzbZ", 1, 0], [5, 285, 56.4], [0, 259.171, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 6, [-35], [[29, 0, -34, [13]]], [0, "5ezIKKLxhPY7mgNsOW9nSs", 1, 0], [5, 420, 480]], [17, "New Label", 10, [-36], [0, "bfzj70TsdBlI0essDBqTc/", 1, 0], [5, 420, 50.4], [0, 0, 1], [-210, 240.00000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "规则说明", false, 1, 1, 1, 2, 9, [4]], [13, "Label", false, 4, [[26, "返回", false, false, 1, 1, 1, 1, -37, [7]]], [0, "8d0F4swmxFToxVZ9XX5M4A", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "img_fgx", false, 2, [[2, 0, -38, [11], 12]], [0, "6a+QJ9o3RGyJkavtzz5nyX", 1, 0], [5, 322, 3], [0, -94.843, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "", 30, 420, 11]], 0, [0, 3, 1, 0, 4, 15, 0, 5, 12, 0, 0, 1, 0, 6, 1, 0, 0, 1, 0, -1, 7, 0, -2, 3, 0, 0, 2, 0, -1, 8, 0, -2, 4, 0, -3, 14, 0, -4, 6, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, 7, 4, 0, 0, 4, 0, -1, 13, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 8, 11, 0, 0, 6, 0, -1, 10, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, -1, 12, 0, 0, 9, 0, 0, 10, 0, -1, 11, 0, -1, 15, 0, 0, 13, 0, 0, 14, 0, 9, 1, 2, 10, 3, 38], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, -1, 1, 2, -1, 1, -1, -1, -1, 1, -1, 1], [0, 1, 0, 1, 0, 0, 2, 0, 0, 3, 4, 0, 5, 0, 0, 0, 6, 0, 1]]