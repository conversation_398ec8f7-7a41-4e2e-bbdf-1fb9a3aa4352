[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "f8npR6F8ZIZoCp3cKXjJQz", "c7/kiNVi5MFoRxA17RAHul", "7a/QZLET9IDreTiBfRn2PD", "a8IbIbwcdHAa14HznBfher", "d3gQGt3U5ODrxK5EaBPUsd", "20r8bogShNt6jVa+2q6Rqg", "65fY7u3K9FzoRcN3oKN+OE", "69ngXaPnZD1oyANKHvPAUy", "78rhbwV59CPortOHTpVzuI", "d7n/3r9BlJgaMp0acZEmaL", "7fwy5i88xEuaJt6DFbR/zP", "99vt+kLtVKF5nKehG9Glxh", "a2MjXRFdtLlYQ5ouAFv/+R", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "73jGpne/9JUI/171Zur5Pr", "6eIHunmG5Msrk3dvFEvPBX", "2d05sqQMpKkpGuV3nqyLoI", "5deCV0t8hM6oJTwJJSmemw", "dal4o32KpO75yexm9hALiM", "18nOvW6CdKz6pIMkEx7p7R", "26g1t1f5xKGJVKiwc3x4hh", "9fUicDpb9ESYDEBfH7jjiH", "9alhDeZttPnIjWWf28gp1y", "deYeqBbrtAM7ABF8+EGpZQ", "c0VITW/OJML7691KcQocPY", "4dMZmkcjlJEbhEI0rAtegJ", "65hdeSelhOVq5sz7eNtoFd", "95YBzepGVJ0J588E1RFWXY", "cdvDZRLPZKb70zilA0TpQ8", "ceN7GkwF5MOqjTOvBJSlcS"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "root", "_parent", "asset", "bar", "content", "_N$target", "data", "_N$disabledSprite"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -2, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_lineHeight", "_N$fontFamily", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials", "_N$file"], -8, 1, 3, 6], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "_enabled", "node"], -5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_enabled", "_N$horizontalDirection", "_resize", "_N$paddingLeft", "_N$paddingRight", "_N$spacingY", "node", "_layoutSize"], -5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["3d029OEmWBK3JxJeMKWo0aq", ["node", "labelArr", "content", "bar"], 3, 1, 2, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1]], [[7, 0, 1, 2, 2], [0, 0, 8, 7, 5, 6, 9, 2], [15, 0, 1, 2, 2], [0, 0, 8, 10, 7, 5, 6, 9, 2], [3, 0, 2, 3, 4, 2], [3, 1, 0, 2, 3, 4, 3], [3, 2, 3, 4, 1], [1, 0, 3, 6, 5, 1, 2, 11, 12, 7], [8, 0, 1, 2, 3, 4], [4, 0, 1, 2, 2], [1, 0, 3, 6, 4, 5, 1, 2, 11, 12, 13, 8], [0, 0, 1, 8, 7, 5, 6, 9, 3], [17, 0, 1, 2, 3, 4, 5, 6, 6], [0, 0, 1, 8, 10, 7, 5, 6, 9, 3], [0, 0, 8, 10, 5, 9, 2], [0, 0, 8, 7, 5, 11, 6, 9, 2], [0, 0, 8, 7, 5, 6, 2], [0, 0, 8, 5, 9, 2], [14, 0, 1, 2, 3, 3], [1, 0, 3, 4, 1, 2, 11, 12, 13, 6], [11, 0, 2], [0, 0, 2, 10, 7, 5, 6, 3], [0, 0, 8, 10, 5, 6, 2], [0, 0, 8, 10, 7, 5, 6, 2], [0, 0, 10, 7, 5, 6, 12, 9, 2], [0, 0, 10, 7, 5, 6, 2], [0, 0, 3, 8, 7, 5, 11, 6, 3], [0, 0, 1, 8, 7, 5, 11, 6, 9, 3], [0, 0, 4, 8, 7, 5, 6, 3], [5, 0, 1, 6, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 3], [13, 0, 1, 2, 3, 1], [7, 1, 2, 1], [3, 1, 0, 2, 3, 3], [4, 1, 1], [4, 0, 1, 2, 3, 4, 5, 6, 2], [4, 1, 2, 1], [8, 0, 1, 3, 3], [9, 1, 2, 1], [9, 0, 1, 2, 2], [10, 2, 0, 1, 3, 8, 9, 5], [10, 4, 0, 5, 6, 1, 7, 8, 9, 7], [6, 7, 0, 1, 2, 3, 4, 5, 6, 8, 9], [6, 0, 5, 6, 8, 4], [6, 0, 1, 2, 3, 4, 8, 6], [1, 0, 3, 4, 5, 1, 2, 11, 12, 13, 7], [1, 0, 5, 1, 2, 11, 12, 5], [1, 0, 3, 6, 4, 1, 2, 11, 12, 13, 7], [1, 0, 3, 6, 4, 5, 1, 2, 7, 11, 12, 13, 9], [1, 0, 8, 4, 1, 2, 9, 10, 11, 12, 8], [16, 0, 1, 2, 3, 4, 5, 6, 6], [18, 0, 1, 2, 3, 3]], [[[{"name": "img_txzd", "rect": [0, 0, 536, 180], "offset": [0, 0], "originalSize": [536, 180], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [7]], [[{"name": "icon_gjjl", "rect": [0, 0, 118, 111], "offset": [0, 0], "originalSize": [118, 111], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [8]], [[{"name": "jdt01", "rect": [0, 0, 60, 24], "offset": [0, 0], "originalSize": [60, 24], "capInsets": [16, 9, 14, 8]}], [2], 0, [0], [3], [9]], [[{"name": "icon_tqk", "rect": [6, 12, 100, 87], "offset": [1, -0.5], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [10]], [[{"name": "icon_lwjb", "rect": [8, 7, 65, 66], "offset": [0.5, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [11]], [[{"name": "img_tbyd", "rect": [0, 0, 82, 82], "offset": [0, 0], "originalSize": [82, 82], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [12]], [[{"name": "jdt02", "rect": [3, 3, 55, 18], "offset": [0.5, 0], "originalSize": [60, 24], "capInsets": [10.5, 7, 14.5, 7]}], [2], 0, [0], [3], [13]], [[[20, "Idle<PERSON><PERSON><PERSON>ie<PERSON>"], [21, "Idle<PERSON><PERSON><PERSON>ie<PERSON>", 1, [-7, -8], [[33, -6, [-4, -5], -3, -2]], [34, -1, 0], [5, 642, 900]], [22, "bg", 1, [-9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20], [0, "d5iMdkpqZNhaS+GSBTrslt", 1, 0], [5, 655, 950]], [13, "img_txzd", false, 2, [-22, -23, -24, -25, -26, -27], [[6, -21, [48], 49]], [0, "d2AEOl+eNIGauOvMbCbDJO", 1, 0], [5, 536, 180], [0, 486.17, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "showMiddle", 2, [-28, -29, -30, -31, -32], [0, "f8tMBvcnFB0bUTATKv69GI", 1, 0], [0, 219.096, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "title_bg", 2, [-35, -36], [[5, 1, 0, -33, [8], 9], [36, -34]], [0, "63oHBAYQVMWLB1BdJu54K0", 1, 0], [5, 655, 950]], [3, "Background", 5, [-40], [[6, -37, [5], 6], [37, 3, -39, [[39, "3d029OEmWBK3JxJeMKWo0aq", "close", 1]], [4, 4293322470], [4, 3363338360], -38, 7]], [0, "abxGRs4WJGjJuzEP/Sk48A", 1, 0], [5, 64, 65], [273.919, 431.491, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "showNd", 2, [-42, -43, -44], [[40, -41, [14]]], [0, "76Y2z90WZHlLABSBSCcWbw", 1, 0], [5, 645, 355], [0, 216.535, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "New ProgressBar", 3, [-47, -48], [[[5, 1, 0, -45, [27], 28], -46], 4, 1], [0, "11ZU3taJ9Dtp4sm5rOd0zh", 1, 0], [5, 356, 24], [0, -17.539, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Node", 3, [-51, -52], [[42, false, 1, 5, 1, -49, [5, 130, 50]], [44, false, 21, 372.781, 159.001, 136.164, -6.163999999999991, 300, 50, -50]], [0, "345K5Z7DJDCpLJwaWpn1q+", 1, 0], [5, 130, 50], [-14.628, -65.35, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_jbd", 2, [-54, -55, -56], [[5, 1, 0, -53, [56], 57]], [0, "52+DDyQbBPirMgG8Iffucc", 1, 0], [5, 205, 45], [-154.729, -18.121, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_jbd1", 2, [-58, -59, -60], [[5, 1, 0, -57, [64], 65]], [0, "4b/hx8hA9KDImSVv0GPhR4", 1, 0], [5, 205, 45], [174.763, -18.121, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button03", 2, [-63, -64], [[5, 1, 0, -61, [69], 70], [9, 3, -62, [[8, "3d029OEmWBK3JxJeMKWo0aq", "onBtn", "getAward", 1]]]], [0, "c6w8jIBU5Daoxa992Hqgji", 1, 0], [5, 274, 108], [168, -401.348, 0, 0, 0, 0, 1, 1, 1, 0]], [24, "content", [-66], [[43, 1, 3, 15, 10, 20, 15, -65, [5, 550, 120]]], [0, "25ZUKELnxKBYuJv6WBfZui", 1, 0], [5, 550, 120], [0, 0.5, 1], [0, 127.342, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "rewardNd", 3, [-67, -68, -69], [0, "04LOlNWvJOHLwfvE+S3dUy", 1, 0], [-33.753, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_tbyd1", 14, [-71, -72], [[4, 0, -70, [39], 40]], [0, "64zB6+Hu5B5blGoQaUcBhJ", 1, 0], [5, 61, 61], [106.642, -17.596, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_tbyd2", 14, [-74, -75], [[4, 0, -73, [46], 47]], [0, "dcQzZcYkNNloWJuzfcCW/a", 1, 0], [5, 61, 61], [196.085, -17.596, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button04", 2, [-78], [[5, 1, 0, -76, [73], 74], [9, 3, -77, [[8, "3d029OEmWBK3JxJeMKWo0aq", "onBtn", "goNew", 1]]]], [0, "c33Abgw5dDXaB7ygYdpm3X", 1, 0], [5, 274, 108], [-165, -402.577, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "img_tbyd", false, 2, [-81], [[6, -79, [79], 80], [9, 3, -80, [[8, "3d029OEmWBK3JxJeMKWo0aq", "onClickBtn", "monthcard", 1]]]], [0, "a8Z6TIvzBOdrARSKHe2fBi", 1, 0], [5, 82, 82], [-249.943, 339.245, 0, 0, 0, 0, 1, 1, 1, 0]], [25, "view", [13], [[41, 0, -82, [82]], [45, 45, 240, 250, -83]], [0, "9dbo7QRptI65vz2zRcniFY", 1, 0], [5, 585, 220]], [26, "maskbg", 200, 1, [[46, 45, -54, -54, -217, -217, -84], [4, 0, -85, [0], 1]], [0, "1cRiRztq9HK7NnkLOmpPd/", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "Label_title", 5, [[47, "游历", 48, false, 1, 1, 1, -86, [2], 3], [2, 3, -87, [4, 4278190080]]], [0, "a95L7sfc5IW4h1I5ISJcwK", 1, 0], [5, 102, 56.4], [0, 430.544, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Label", false, 2, [[48, "点击任意位置关闭", 1, 1, 1, -88, [10]], [2, 3, -89, [4, 4278190080]]], [0, "54zmrgvF1OSo5sldltGTrc", 1, 0], [5, 326, 56.4], [0, -506.287, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 4, [[49, "正在游历中...", 24, 24, false, 1, 1, -90, [15], 16], [2, 2, -91, [4, 4278190080]]], [0, "26GMvhg+NAS4pA3z+1zfnH", 1, 0], [5, 141.33, 34.239999999999995], [0, -101.303, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "icon_gjjl", false, 4, [[6, -92, [19], 20], [9, 3, -93, [[8, "3d029OEmWBK3JxJeMKWo0aq", "onBtn", "explain", 1]]]], [0, "4ekjKvFPdAWK5QKhdEO98g", 1, 0], [5, 118, 111], [259.696, -105.172, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "New Label", false, 4, [[7, "挂机奖励", 24, 22, 1, 1, 1, -94, [21]], [2, 3, -95, [4, 4278190080]]], [0, "44ki7CFZZKiLiSkVy19Vms", 1, 0], [5, 102, 33.72], [252.674, -146.047, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[50, "07:24:10", 30, 30, false, 1, 1, 1, "Consolas", -96, [22], 23], [2, 2, -97, [4, 4278190080]]], [0, "f8PgAdi3dEEqG4zKNVGYOg", 1, 0], [5, 120.58, 41.8], [0, -155.259, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTitle", 3, [[7, "佣兵与海盗", 32, 32, 1, 1, 1, -98, [24]], [2, 3, -99, [4, 4278190080]]], [0, "a7N+6DNmVJbIpZ2jMLDbvX", 1, 0], [5, 166, 46.32], [0, 45.194, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "lbTime", 8, [[-100, [2, 3, -101, [4, 4278190080]]], 1, 4], [0, "26KdH733xLeon+Wij/XiIp", 1, 0], [5, 51.04, 31.2]], [3, "img_tbyd", 3, [-103], [[4, 0, -102, [31], 32]], [0, "e30Pf5GtlMj6XbSWMSFR3z", 1, 0], [5, 61, 61], [-180.176, -17.674, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "New Label", 9, [[-104, [2, 3, -105, [4, 4278190080]]], 1, 4], [0, "484gKdPotH349NJIsuyFpR", 1, 0], [5, 72.03, 45.06], [28.985, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNum", 15, [[7, "x100", 18, 20, 1, 1, 1, -106, [38]], [2, 3, -107, [4, 4278190080]]], [0, "02Ag8+G2REP7tZ+iCY1+yP", 1, 0], [5, 46.04, 31.2], [0, -22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNum", 16, [[7, "x100", 18, 20, 1, 1, 1, -108, [45]], [2, 3, -109, [4, 4278190080]]], [0, "9fJvfVTYNN7qoRy9oJtoZP", 1, 0], [5, 46.04, 31.2], [0, -22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbVal", 10, [[10, "14.4k", 28, 22, false, 1, 1, 1, -110, [52], 53], [2, 2, -111, [4, 4278190080]]], [0, "298vPRnJJBnLgF107ovN80", 1, 0], [5, 73.86, 31.72], [13.988, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbTime", 10, [[10, "32/分钟", 20, 20, false, 1, 1, 1, -112, [54], 55], [2, 2, -113, [4, 4278190080]]], [0, "5ag+I8NsRCaK7PWO4EKg8z", 1, 0], [4, 4283168127], [5, 74.04, 29.2], [-87.035, -16.375, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbVal", 11, [[10, "14.4k", 28, 22, false, 1, 1, 1, -114, [60], 61], [2, 2, -115, [4, 4278190080]]], [0, "24u0+GjxpLdZzN6HOi5CZl", 1, 0], [5, 73.86, 31.72], [13.988, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbTime", 11, [[10, "32/分钟", 20, 20, false, 1, 1, 1, -116, [62], 63], [2, 2, -117, [4, 4278190080]]], [0, "71MQ/26JJOYrXLOIYOOUCo", 1, 0], [4, 4283168127], [5, 74.04, 29.2], [-87.035, -16.375, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNe", 12, [[19, "领取奖励", 36, false, 1, 1, -118, [66], 67], [2, 2, -119, [4, 4279374353]]], [0, "ba1MwrKjVKUaSFNHWGd3rH", 1, 0], [5, 148, 54.4], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNe", 17, [[19, "快速游历", 36, false, 1, 1, -120, [71], 72], [2, 2, -121, [4, 4279374353]]], [0, "b5s69hmg9P2YqGXA6XwyCl", 1, 0], [5, 148, 54.4], [0, 3.263, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New ScrollView", 2, [19], [[52, false, 0.75, 0.23, null, null, -122, 13]], [0, "c9eYB1H4FC7ruZ8X/siRJF", 1, 0], [5, 585, 220], [0, -197.246, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "Label", false, 6, [[51, "返回", false, false, 1, 1, 1, 1, -123, [4]]], [0, "57w9m/PXFEfZ6PllirTlxG", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "cj", 512, 7, [[12, "day", "animation", 0, false, "animation", -124, [11]]], [0, "86Bx5rlWFKe5MVgYql9zla", 1, 0], [5, 1466.6396484375, 473]], [1, "bird2", 7, [[12, "default", "idle", 0, false, "idle", -125, [12]]], [0, "f9cPSWRrNGboDlWFyysROB", 1, 0], [5, 156.4189453125, 104.87898254394531], [-96.967, -43.337, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flower2", 7, [[12, "default", "attack2", 0, false, "attack2", -126, [13]]], [0, "14l7TWCppB2735U66X27So", 1, 0], [5, 86.71499633789062, 128.16497802734375], [-115.643, -116.527, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_cjhb", 4, [[5, 1, 0, -127, [17], 18]], [0, "0cojPyqkFAVbI826tBarDT", 1, 0], [5, 649, 76], [0, -162.774, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "bar", 512, 8, [-128], [0, "73Yzi6c21DY7LVU6Fngznd", 1, 0], [5, 249.2, 24], [0, 0, 0.5], [-178, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [35, 1, 0, 45, [25]], [7, "0/200", 18, 20, 1, 1, 1, 28, [26]], [53, 356, 0.7, 8, 46], [1, "icon_lwjb", 29, [[4, 0, -129, [29], 30]], [0, "d4EhgK9TtI+6daH4Bd591D", 1, 0], [5, 46, 46], [0, -2.842170943040401e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_clock", 9, [[4, 0, -130, [33], 34]], [0, "6bIa7r2YxM166LGt5l1ymF", 1, 0], [5, 32, 38], [-28.03, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [7, "8天21时", 18, 31, 1, 1, 1, 30, [35]], [16, "icon_lwjb", 15, [[4, 0, -131, [36], 37]], [0, "44NkVAMfJDx7+j33b528Yq", 1, 0], [5, 41, 41]], [1, "icon_jiahao", 14, [[4, 0, -132, [41], 42]], [0, "bcko9+JZJFv5uhCyq4uBDG", 1, 0], [5, 23, 23], [151.764, -17.506, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "icon_lwjb", 16, [[4, 0, -133, [43], 44]], [0, "d9N/7ieBdB6IJSKeh56nqQ", 1, 0], [5, 41, 41]], [1, "New Node", 3, [[38, -134, [[8, "3d029OEmWBK3JxJeMKWo0aq", "onBtn", "fund", 1]]]], [0, "e3E74IL75DmJ87dbn5kEHs", 1, 0], [5, 450, 120], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_gold", 10, [[6, -135, [50], 51]], [0, "53FlQ3op9DoIvZbkwVZaRz", 1, 0], [5, 48, 49], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_gold", 11, [[6, -136, [58], 59]], [0, "f9JXgnaLJHsZh0+dgxFX4Q", 1, 0], [5, 47, 55], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Item_NeedDisplay", 12, [18, "0a3Z87sHJFm7I+d7u5IFoo", true, -137, 68], [0, -21.006, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_gmtxzjld", 2, [[5, 1, 0, -138, [75], 76]], [0, "04mXS5OxVJj6w/aw2LNr9Z", 1, 0], [5, 605, 280], [0, -196.372, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_tqk", 18, [[6, -139, [77], 78]], [0, "45KjYpj9VO778E+sQ6BMnN", 1, 0], [5, 100, 87], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [17, "RewardItem", 13, [18, "1c39X23AxDS5KHIWleBG8w", true, -140, 81], [-202, -60, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 7, 48, 0, 8, 13, 0, -1, 47, 0, -2, 51, 0, 0, 1, 0, -1, 20, 0, -2, 2, 0, -1, 5, 0, -2, 22, 0, -3, 7, 0, -4, 4, 0, -5, 3, 0, -6, 10, 0, -7, 11, 0, -8, 12, 0, -9, 17, 0, -10, 59, 0, -11, 18, 0, -12, 39, 0, 0, 3, 0, -1, 27, 0, -2, 8, 0, -3, 29, 0, -4, 9, 0, -5, 14, 0, -6, 55, 0, -1, 23, 0, -2, 44, 0, -3, 24, 0, -4, 25, 0, -5, 26, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, -2, 6, 0, 0, 6, 0, 9, 6, 0, 0, 6, 0, -1, 40, 0, 0, 7, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, 0, 8, 0, -2, 48, 0, -1, 45, 0, -2, 28, 0, 0, 9, 0, 0, 9, 0, -1, 50, 0, -2, 30, 0, 0, 10, 0, -1, 56, 0, -2, 33, 0, -3, 34, 0, 0, 11, 0, -1, 57, 0, -2, 35, 0, -3, 36, 0, 0, 12, 0, 0, 12, 0, -1, 37, 0, -2, 58, 0, 0, 13, 0, -1, 61, 0, -1, 15, 0, -2, 53, 0, -3, 16, 0, 0, 15, 0, -1, 52, 0, -2, 31, 0, 0, 16, 0, -1, 54, 0, -2, 32, 0, 0, 17, 0, 0, 17, 0, -1, 38, 0, 0, 18, 0, 0, 18, 0, -1, 60, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 47, 0, 0, 28, 0, 0, 29, 0, -1, 49, 0, -1, 51, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, -1, 46, 0, 0, 49, 0, 0, 50, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 4, 58, 0, 0, 59, 0, 0, 60, 0, 4, 61, 0, 10, 1, 13, 5, 19, 19, 5, 39, 140], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46], [-1, 1, -1, 2, -1, -1, 1, 11, -1, 1, -1, -1, -1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, 2, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 2, 6, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, 6, -1, 1], [0, 14, 0, 2, 0, 0, 15, 16, 0, 17, 0, 4, 4, 4, 0, 0, 2, 0, 18, 0, 19, 0, 0, 1, 0, 0, 0, 0, 20, 0, 5, 0, 3, 0, 21, 0, 0, 5, 0, 0, 3, 0, 22, 0, 5, 0, 0, 3, 0, 23, 0, 24, 0, 1, 0, 1, 0, 6, 0, 25, 0, 1, 0, 1, 0, 6, 0, 2, 26, 0, 27, 0, 2, 0, 28, 0, 29, 0, 30, 0, 3, 31, 0, 32]]]]