[1, ["ecpdLyjvZBwrvm+cedCcQy", "ddFhwuPitMK6rcBkC3ddyJ", "02TdF+VAtPyaV2VX4yLDch", "00Q6C6u21GNKUTvX7PxgOJ", "04KZEi0D5MYpPZuNK/G1v4", "72ee4bhaZLvp46zzFG6yMv", "8ayJdoqGpKU4u2CNIMZElR", "06BVABEDhOh7GVxOQInJYs", "delmanOUtNoq5cogWguiIE", "e2J1T2O3ZDToHhJKtZerCY", "3foiog7YBPuI1XLP+iOaQD", "d7z7YKUt9PtIddu+2SC1rc", "b7bL+PLNVI8odeZADPOqqG", "6eBWFz0oVHPLIGQKf/9Thu", "d0hHjyhNpLVrBKRk5gjxcJ", "c987EgAQpCX66l90sG4Vlh", "5aogFhpWxHFa6gFcOrtmi/", "053ZnVx2NKH60FnBUzAeoB", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "ceXDBhTeJJ/pE+Ue9lv8Wm", "1ad7qxgghMRaeZGgCO++9i", "1fevf19ExMW4Dg3zbkgLzt", "8ay52ofStAy7/iq8NwAoZM", "f3p84uzd5EVLXvLuhlyoRY", "93wuX02PJC7IGmQj/DuILZ", "e5ZNTZSoZCRqDA2Mt0UOli", "8c20Sso/ZEn7NUfNSM+EBh", "685FoK6S1KMbjQlmmMXIJW", "1bwTrYRmxP8KT5VBxCH5uF", "5fyIExAVdPrIESdVacBDtY", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "b2rdsa/M1CDIRaL34Gxbmp", "7eMOWZFCZE0KrVYG3Ocv6E"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "_N$target", "data", "_N$disabledSprite", "goldbg"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -2, 4, 5, 9, 1, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "_lineHeight", "_styleFlags", "node", "_materials"], -5, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Layout", ["_N$layoutType", "_N$paddingLeft", "_N$spacingX", "_resize", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize"], -3, 1, 5], ["cc.Prefab", ["_name"], 2], ["1a644VbOf5FyLmzLWAm7TpK", ["node", "root", "goldbg"], 3, 1, 1, 6], ["64e2fai2llCn5yOIK50fwrS", ["node", "clickEvents"], 3, 1, 9], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], -1, 1, 3]], [[4, 0, 1, 2, 2], [0, 0, 8, 7, 5, 6, 9, 2], [2, 2, 3, 4, 1], [13, 0, 1, 2, 2], [0, 0, 8, 10, 7, 5, 6, 9, 2], [0, 0, 10, 7, 5, 6, 9, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 8, 7, 5, 6, 2], [2, 0, 2, 3, 4, 2], [7, 0, 1, 2, 3, 4], [8, 0, 1, 2, 6, 7, 4], [3, 0, 6, 1, 7, 2, 3, 4, 8, 9, 8], [3, 0, 5, 1, 7, 2, 3, 4, 8, 9, 8], [0, 0, 1, 8, 10, 7, 5, 11, 6, 3], [0, 0, 1, 8, 7, 5, 6, 9, 3], [2, 1, 0, 2, 3, 3], [5, 0, 1, 2, 3, 4], [7, 0, 3, 2], [12, 0, 1], [3, 0, 5, 1, 2, 3, 4, 8, 9, 7], [3, 0, 5, 6, 1, 2, 3, 4, 8, 9, 8], [9, 0, 2], [0, 0, 2, 10, 7, 5, 6, 3], [0, 0, 8, 10, 5, 6, 9, 2], [0, 0, 8, 10, 7, 5, 6, 12, 9, 2], [0, 0, 3, 8, 7, 5, 11, 6, 3], [0, 0, 8, 10, 7, 5, 6, 2], [0, 0, 7, 5, 6, 9, 2], [0, 0, 4, 8, 7, 5, 6, 9, 3], [0, 0, 1, 8, 10, 5, 6, 9, 3], [0, 0, 8, 7, 5, 11, 6, 9, 2], [10, 0, 1, 2, 1], [4, 1, 2, 1], [2, 2, 3, 1], [11, 0, 1, 1], [5, 0, 1, 3, 3], [6, 1, 0, 2, 3, 3], [6, 0, 2, 3, 4, 5, 6, 7, 2], [8, 3, 0, 4, 5, 6, 7, 5], [3, 0, 5, 6, 1, 7, 2, 3, 4, 8, 9, 9], [14, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "ggtipicon2", "rect": [0, 0, 44, 43], "offset": [0, 0], "originalSize": [44, 43], "capInsets": [21, 20, 21, 20]}], [1], 0, [0], [2], [5]], [[{"name": "img_qrqd_db01", "rect": [0, 0, 112, 112], "offset": [0, 0], "originalSize": [112, 112], "capInsets": [26, 25, 23, 21]}], [1], 0, [0], [2], [6]], [[{"name": "img_qrqd_db2", "rect": [0, 0, 399, 366], "offset": [0, 0], "originalSize": [399, 366], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[{"name": "img_qipao_01", "rect": [4, 0, 42, 34], "offset": [-5.5, 0], "originalSize": [61, 34], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[{"name": "img_qipao_02", "rect": [0, 0, 164, 102], "offset": [0, 0], "originalSize": [164, 102], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [9]], [[{"name": "wz_qrqd", "rect": [0, 0, 509, 194], "offset": [0, 0], "originalSize": [509, 194], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [10]], [[{"name": "img_qrqd_db02", "rect": [0, 0, 112, 112], "offset": [0, 0], "originalSize": [112, 112], "capInsets": [29, 26, 23, 27]}], [1], 0, [0], [2], [11]], [[{"name": "img_day_db", "rect": [0, 0, 107, 46], "offset": [0, 0], "originalSize": [107, 46], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [12]], [[{"name": "default_sprite", "rect": [0, 2, 40, 36], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [13]], [[{"name": "img_qrqd_line", "rect": [0, 0, 4, 88], "offset": [0, 0], "originalSize": [4, 88], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [14]], [[{"name": "img_qrqd_db", "rect": [0, 0, 693, 730], "offset": [0, 0], "originalSize": [693, 730], "capInsets": [439, 672, 254, 58]}], [1], 0, [0], [2], [15]], [[{"name": "img_qrqd_qipaodb", "rect": [0, 0, 263, 170], "offset": [0, 0], "originalSize": [263, 170], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [16]], [[{"name": "pic_gold_01", "rect": [0, 0, 144, 104], "offset": [0, 0], "originalSize": [144, 104], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [17]], [[[21, "SignIn_View"], [22, "SignIn_View", 1, [-4, -5], [[31, -3, -2, 60]], [32, -1, 0], [5, 750, 1334]], [5, "box", [-7, -8, -9, -10, -11, -12], [[6, 1, 0, -6, [58], 59]], [0, "12fDKz7P5Il50t+G0pY7Rp", 1, 0], [5, 693, 1050], [0, -74.69, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "itemAdSign", [-15, -16, -17, -18, -19], [[6, 1, 0, -13, [45], 46], [34, -14, [[16, "1a644VbOf5FyLmzLWAm7TpK", "Sign", "1", 1]]]], [0, "3cD6CugGVBnL3SlTK6zm5x", 1, 0], [5, 370, 107], [132, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "itemSign", [-22, -23, -24, -25], [[6, 1, 0, -20, [26], 27], [36, 1.1, 3, -21, [[16, "1a644VbOf5FyLmzLWAm7TpK", "Sign", "0", 1]]]], [0, "a3BP39tl1Fn6D4ZJUyHX/c", 1, 0], [5, 217, 107], [-196.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "img_new", 2, [-27, -28, -29], [[2, -26, [8], 9]], [0, "9648wbEhhIHJu9zwFQksg5", 1, 0], [5, 399, 366], [196.177, 507.763, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Sign", [4, 3], [[17, 16, -30], [10, 1, 20, 35, -31, [5, 650, 107]]], [0, "c9nZdCAlVHoYLEXcdTfvNJ", 1, 0], [5, 650, 107], [0, -53.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "item", [-33, -34, -35], [[8, 0, -32, [15], 16]], [0, "99gIHShGJFMa3o39ui81px", 1, 0], [5, 85, 85], [-47.5, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [13, "unenable", false, 4, [-39], [[15, 1, 0, -36, [22]], [9, 45, 100, 100, -37], [18, -38]], [0, "65AojK331ClYpskrB8UHW5", 1, 0], [4, 4278190080], [5, 217, 107]], [5, "item", [-41, -42, -43], [[8, 0, -40, [37], 38]], [0, "ddr6rtJrZNeaC24FiL+nm2", 1, 0], [5, 85, 85], [-97.5, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [13, "unenable", false, 3, [-47], [[15, 1, 0, -44, [41]], [9, 45, 100, 100, -45], [18, -46]], [0, "f4kpZMgyZPKKFptsL3494J", 1, 0], [4, 4278190080], [5, 370, 107]], [23, "descNode", 2, [-48, -49, -50, -51], [0, "58mcqJOn9HtavOjgEDLAhS", 1, 0], [5, 84.49, 50.4], [1560.686, 496.078, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_qrqd_qipaodb", 5, [-53, -54], [[2, -52, [6], 7]], [0, "f6a7IraIlCpKlDNXsObOG+", 1, 0], [5, 263, 170], [-232.278, 78.735, 0, 0, 0, 0, 1, -1, 1, 1]], [24, "content", 2, [6], [[38, 1, 2, 10, 15, -55, [5, 750, 117]]], [0, "648YOckgROgaG1na5l68oS", 1, 0], [5, 750, 117], [0, 0.5, 1], [0, 343.264, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "maskbg", 180, 1, [[17, 45, -56], [8, 0, -57, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [26, "bg", 1, [2], [[9, 45, 750, 1000, -58]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [1, "New Label", 5, [[11, "霸王龙", 35, false, 1, 1, 1, 2, -59, [3]], [3, 3, -60, [4, 4278190080]]], [0, "a1dzwQdltI8rfZT96kZWKE", 1, 0], [5, 394.40999999999997, 97.4], [-2.758, -109.615, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 12, [[39, "签到送", 36, 45, false, 1, 1, 1, 2, -61, [4]], [3, 3, -62, [4, 4280953524]]], [0, "bfeTIDgqdDYbkXOULi5uYp", 1, 0], [5, 175, 50.7], [0, 50.495, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "New Label copy", 12, [[11, "传说宠物", 45, false, 1, 1, 1, 2, -63, [5]], [3, 3, -64, [4, 4278203059]]], [0, "6fr9UcbdxOXoLcm8CgM5L6", 1, 0], [5, 239, 62.7], [0, -7.821, 0, 0, 0, 0, 1, -1, 1, 1]], [27, "New Label", [[11, "签到", 35, false, 1, 1, 1, 2, -65, [10]], [3, 3, -66, [4, 4278190080]]], [0, "34fSUVvUlNdpPk2fakeiev", 1, 0], [5, 394.40999999999997, 97.4], [-2.758, 25.879, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "List", 4, [7], [[10, 1, 10, 15, -67, [5, 200, 100]]], [0, "54LOV0hmpN+5D0VsjNz39o", 1, 0], [5, 200, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "count", 7, [[12, "x0", 28, false, 1, 1, 1, 2, -68, [14]], [3, 2, -69, [4, 4278190080]]], [0, "f1U75R771HkKtkb9C2HPAi", 1, 0], [5, 90, 40], [0, -29.44, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_day_db", 4, [-71], [[2, -70, [18], 19]], [0, "a5803QXMJK6Yjj3Arnnmw2", 1, 0], [5, 107, 46], [-70.57, 36.988, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [7, "txt_day", 22, [[12, "Day 1", 30, false, 1, 1, 1, 2, -72, [17]], [3, 2, -73, [4, 4278190080]]], [0, "34vljWtitOvZbzvztPdG69", 1, 0], [5, 100, 50]], [4, "redpoint", 4, [-75], [[6, 1, 0, -74, [24], 25]], [0, "771YaGmlxOVpXNTXnP6O1W", 1, 0], [5, 98, 43], [84, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 24, [[19, "可领取", 24, false, 1, 1, 2, -76, [23]], [3, 2, -77, [4, 4278190080]]], [0, "0eG8BmsHFDcJlsy0O/EFpJ", 1, 0], [5, 76, 54.4], [0, 2.219, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "List", 3, [9], [[10, 1, 10, 10, -78, [5, 300, 100]]], [0, "d7qjA1DgxPMaxPa+VxA2rM", 1, 0], [5, 300, 100], [34.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "count", 9, [[12, "x3000", 28, false, 1, 1, 1, 2, -79, [36]], [3, 2, -80, [4, 4278190080]]], [0, "02AhcYpAtLt7hkmoXIGbby", 1, 0], [5, 90, 40], [0, -31.349, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "redpoint", 3, [-82], [[6, 1, 0, -81, [43], 44]], [0, "9et2tJZKhJyad0LWva2hMw", 1, 0], [5, 98, 43], [159.772, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 28, [[19, "可领取", 24, false, 1, 1, 2, -83, [42]], [3, 2, -84, [4, 4278190080]]], [0, "48wtc01npBPKKi0gJUD3B3", 1, 0], [5, 76, 54.4], [0, 2.219, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 11, [[20, "双截棍", 18, 25.799999999999976, false, 1, 1, 1, -85, [54]], [3, 2, -86, [4, 4278190080]]], [0, "c0AVxtCtZCub4KlTUhBWVY", 1, 0], [5, 132, 37.5], [0, 33.447, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnclose", 2, [-89], [[37, 3, -88, [[35, "1a644VbOf5FyLmzLWAm7TpK", "close", 1]], [4, 4293322470], [4, 3363338360], -87, 57]], [0, "52pM5br7ZFWrDS3wz//3Dc", 1, 0], [5, 60, 60], [324.797, 480.775, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "Background", 512, 31, [[2, -90, [55], 56]], [0, "aaH4Jvs8hCwIpeMRKVD/oW", 1, 0], [5, 52, 56], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "dragon4", 5, [[40, "default", "idle", 0, "idle", -91, [2]]], [0, "21hRT9+9dATZWrqAR1YtfK", 1, 0], [5, 140.3917236328125, 148.6699676513672], [0, -86.634, 0, 0, 0, 0, 1, -1.5, 1.5, 1]], [29, "img_normal", false, 2, [19], [0, "acQpRTZwJFDbWXLR+LRxIa", 1, 0], [5, 222, 216], [180.831, 515.232, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pic_gold_01", 7, [[33, -92, [11]]], [0, "f4+6YnAbBBubYOQWJAqUem", 1, 0], [5, 104, 87], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [14, "img_chip", false, 7, [[2, -93, [12], 13]], [0, "fam8jPaYFAK6/Hcn4tAOXq", 1, 0], [5, 35, 35], [42, 29, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "icon_tick", 8, [[2, -94, [20], 21]], [0, "31Ez0atO1I7bNLpq1RqogN", 1, 0], [5, 61, 46]], [1, "videoicon", 3, [[2, -95, [28], 29]], [0, "5djUEqHQhDoa3znR1Frz6B", 1, 0], [5, 50, 52], [-150.837, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [30, "img_qrqd_line", 3, [[2, -96, [30], 31]], [0, "3d187ydDNIvLHjT50ymTeM", 1, 0], [4, 4283663308], [5, 4, 88], [-119.045, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pic_gold_01", 9, [[2, -97, [32], 33]], [0, "0aTqblt8BLQICi5tTmx36U", 1, 0], [5, 144, 104], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [14, "img_chip", false, 9, [[2, -98, [34], 35]], [0, "3clqnGFyRFtbNeZqqWdM57", 1, 0], [5, 35, 35], [42, 29, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "icon_tick", 10, [[2, -99, [39], 40]], [0, "3bkGisuCRAJbmRtt6HRFrc", 1, 0], [5, 61, 46]], [1, "wz_qrqd", 2, [[2, -100, [47], 48]], [0, "87q9ypDyZARYFbDOGvA127", 1, 0], [5, 509, 194], [-110.866, 445.443, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "img_qipao_01", 11, [[2, -101, [49], 50]], [0, "b5j6VFuIhJFrdCbGWVyezL", 1, 0], [5, 42, 34], [0, -58.686, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "img_qipao_02", 11, [[2, -102, [51], 52]], [0, "46RjfjCFNM2pobI2kYW2ts", 1, 0], [5, 164, 102]], [1, "desc", 11, [[20, "周杰伦的双截棍 哼哼哈嘿", 18, 25.799999999999976, false, 1, 1, 3, -103, [53]]], [0, "efy8HjxphFLIYyaE/RuB21", 1, 0], [5, 132, 56.49999999999999], [0, -12.553, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 4, 13, 0, 0, 1, 0, -1, 14, 0, -2, 15, 0, 0, 2, 0, -1, 5, 0, -2, 34, 0, -3, 13, 0, -4, 43, 0, -5, 11, 0, -6, 31, 0, 0, 3, 0, 0, 3, 0, -1, 38, 0, -2, 39, 0, -3, 26, 0, -4, 10, 0, -5, 28, 0, 0, 4, 0, 0, 4, 0, -1, 20, 0, -2, 22, 0, -3, 8, 0, -4, 24, 0, 0, 5, 0, -1, 33, 0, -2, 16, 0, -3, 12, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 35, 0, -2, 36, 0, -3, 21, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 37, 0, 0, 9, 0, -1, 40, 0, -2, 41, 0, -3, 27, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 42, 0, -1, 44, 0, -2, 45, 0, -3, 46, 0, -4, 30, 0, 0, 12, 0, -1, 17, 0, -2, 18, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -1, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -1, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 5, 32, 0, 0, 31, 0, -1, 32, 0, 0, 32, 0, 0, 33, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 6, 1, 2, 3, 15, 3, 3, 6, 4, 3, 6, 6, 3, 13, 7, 3, 20, 9, 3, 26, 19, 3, 34, 103], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, 7, -1, 1, 8], [0, 18, 19, 0, 0, 0, 0, 20, 0, 21, 0, 0, 0, 1, 0, 0, 22, 0, 0, 23, 0, 2, 0, 0, 0, 3, 0, 4, 0, 24, 0, 25, 0, 26, 0, 1, 0, 0, 27, 0, 2, 0, 0, 0, 3, 0, 4, 0, 28, 0, 29, 0, 30, 0, 0, 0, 31, 32, 0, 33, 34]]]]