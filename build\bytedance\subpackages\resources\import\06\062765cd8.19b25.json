[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "07uTi5pw5INIAU5mtPfWfY", "0bm2U5W/hPSqqiWqZ+ub9D", "e4TMDxF3lEk7wOCbX7r9mN", "73jGpne/9JUI/171Zur5Pr", "ebJXGx5cNJ+b1BpmgufQFT", "4dMZmkcjlJEbhEI0rAtegJ", "76wSFl701Ol4wBNdBuEgT3", "e3q84hOUtFEo1aiRNVD8I7", "52Fp3G4IFEKpbJiFKbX2RL", "b9fVs6m5lMxaD9PYOQhpaG", "cb1VFt7lZM5bK3HPiU1WUY", "f8npR6F8ZIZoCp3cKXjJQz", "04hrSis0lGRaoR0wXcOY6u"], ["node", "_spriteFrame", "_N$file", "_parent", "_textureSetter", "root", "data", "_N$font"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_anchorPoint", "_color"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$overflow", "_lineHeight", "_N$horizontalAlign", "_styleFlags", "_enableWrapText", "_N$cacheMode", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_top", "_originalHeight", "alignMode", "_left", "_right", "_horizontalCenter", "_bottom", "node"], -6, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], 2, 1, 12, 4, 5, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingBottom", "_N$spacingX", "_N$affectedByScale", "node", "_layoutSize"], -3, 1, 5], ["cc.Prefab", ["_name"], 2], ["c70d8xCHl5Lb6UWniFjk88x", ["node", "nodeArr", "labelArr"], 3, 1, 2, 2], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["00798YhOSNDMq8AyX+/Wp5o", ["my<PERSON>ey", "node"], 2, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "node"], 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node", "_N$font"], -1, 1, 6]], [[6, 0, 1, 2, 2], [12, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 8, 2], [3, 1, 0, 2, 3, 4, 3], [0, 0, 7, 6, 4, 11, 5, 8, 2], [3, 2, 3, 1], [3, 2, 3, 4, 1], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 10, 8, 2], [2, 0, 9, 2], [2, 0, 1, 3, 9, 4], [2, 0, 7, 9, 3], [7, 0, 1, 4, 2, 5, 6, 7, 6], [1, 0, 3, 5, 1, 7, 2, 4, 10, 11, 12, 8], [8, 0, 2], [0, 0, 1, 9, 6, 4, 5, 3], [0, 0, 9, 6, 4, 5, 10, 8, 2], [0, 0, 2, 7, 9, 6, 4, 5, 8, 3], [0, 0, 7, 9, 4, 2], [0, 0, 7, 6, 4, 5, 10, 8, 2], [0, 0, 2, 3, 7, 6, 4, 11, 5, 4], [0, 0, 2, 3, 7, 6, 4, 5, 8, 4], [0, 0, 2, 3, 7, 6, 4, 11, 5, 8, 4], [0, 0, 1, 7, 9, 4, 5, 8, 3], [0, 0, 1, 7, 9, 6, 4, 5, 8, 3], [0, 0, 1, 7, 6, 4, 5, 8, 3], [0, 0, 3, 7, 6, 4, 11, 5, 3], [0, 0, 7, 6, 4, 5, 2], [0, 0, 1, 7, 6, 4, 5, 10, 8, 3], [5, 0, 1, 2, 3, 7, 4, 5, 6, 2], [5, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 2, 1], [6, 1, 2, 1], [3, 1, 0, 2, 3, 3], [3, 0, 2, 3, 4, 2], [2, 0, 5, 6, 2, 1, 9, 6], [2, 0, 5, 6, 2, 1, 3, 9, 7], [2, 4, 0, 1, 3, 9, 5], [2, 4, 0, 8, 9, 4], [2, 4, 0, 2, 9, 4], [7, 0, 1, 3, 2, 6, 7, 5], [10, 0, 1, 2, 3, 4, 5, 6, 6], [11, 0, 1, 2, 2], [1, 0, 3, 5, 1, 6, 2, 4, 10, 11, 12, 8], [1, 0, 3, 5, 1, 6, 2, 4, 10, 11, 8], [1, 0, 3, 5, 1, 2, 4, 10, 11, 7], [1, 0, 3, 5, 8, 1, 7, 6, 2, 4, 9, 10, 11, 12, 11], [1, 0, 3, 8, 1, 7, 6, 2, 9, 10, 11, 12, 9], [1, 0, 3, 5, 1, 7, 6, 2, 4, 10, 11, 9], [1, 0, 3, 5, 1, 7, 2, 4, 10, 11, 8], [1, 0, 8, 1, 6, 2, 4, 9, 10, 11, 8], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4], [15, 0, 1, 2], [16, 0, 1], [17, 0, 1, 2, 3], [18, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "btn_3", "rect": [2, 0, 132, 53], "offset": [0, 0.5], "originalSize": [136, 54], "capInsets": [62, 11.5, 62, 36.5]}], [4], 0, [0], [4], [3]], [[[16, "M20_PrePare_Activity"], [17, "M20_PrePare_Activity", 1, [-7, -8], [[33, -6, [-4, -5], [-2, -3]]], [34, -1, 0], [5, 750, 1334]], [9, "item", [-10, -11, -12, -13, -14, -15, -16, -17], [[5, -9, [35]]], [0, "15d+v7hWNHBrR2E2hJNjI+", 1, 0], [5, 686, 313], [0, -156.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "content", [2, -20], [[37, 41, 10, 10, 9.69000244140625, 220, -18], [42, 1, 2, 150, 10, -19, [5, 730, 463]]], [0, "3fIL1lSvlNbYVCFAgJRzWN", 1, 0], [5, 730, 463], [0, 0.5, 1], [0, 567.3099975585938, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "maskbg", 1, [-23, -24, -25], [[11, 45, -21], [3, 1, 0, -22, [6], 7]], [0, "3fGEFqd4NKf4qijONDA7ZZ", 1, 0], [5, 750, 1334]], [19, "MoreGames", false, 3, [-27, -28, -29, -30], [[5, -26, [42]]], [0, "55/l1LNBBBcIg8zepIF4FE", 1, 0], [5, 686, 313], [0, -479.5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "New ScrollView", [-33], [[43, false, 0.75, 0.23, null, null, -31, 3], [38, 5, 255, 255, 180, 240, 250, -32]], [0, "8eUaiqcm9PfKFeWgKezX4i", 1, 0], [5, 750, 1154], [0, -90, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "view", 6, [3], [[44, 0, -34, [43]], [12, 45, 240, 250, -35]], [0, "91RV3R0g5IxpYZbbjT/O6F", 1, 0], [5, 750, 1154]], [8, "btn", 2, [-37, -38], [[3, 1, 0, -36, [18], 19]], [0, "85t2MqdsVPrZvm+ysbtuNB", 1, 0], [5, 220, 90], [185.494, -91.11, 0, 0, 0, 0, 1, 1, 1, 0]], [20, "mask", 2, [-39, -40, -41], [0, "2f6tIK2KdGmIl5RdyC50eF", 1, 0]], [2, "lockMsg", 9, [[45, "第?章解锁", 32, 34, false, 1, 1, 2, -42, [33], 34], [11, 16, -43], [1, 3, -44, [4, 4278190080]]], [0, "e17uDm8ZJO8aR4OmrXknoP", 1, 0], [5, 441.43, 95.52], [0, -47.146, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "name", 5, [[46, "玩法合集", 42, 52, false, 1, 1, 2, -45, [36]], [13, 16, -233.173, -46], [1, 4, -47, [4, 4278190080]]], [0, "bdX8r0uxJGu5VwdJfQl6RH", 1, 0], [4, 4293062126], [5, 281, 73.52], [-233.173, 114.877, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "num", 5, [[47, "趣味小游戏快来挑战吧！", 26, 52, false, 1, 2, -48, [37]], [13, 16, -128.058, -49], [1, 4, -50, [4, 4278190080]]], [0, "2bftuK1thEFZY3qNyHtVOg", 1, 0], [5, 403.27, 73.52], [0, 0, 0.5], [-329.693, -106.056, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn", 5, [-53], [[35, 1, 0, -51, [39]], [53, 3, -52, [[54, "c70d8xCHl5Lb6UWniFjk88x", "onBtn", "ui/setting/MoreGamesView", 1]]]], [0, "836udLKhxGMbQyBvE56RBZ", 1, 0], [5, 220, 90], [185.494, -91.131, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "rewardList", 5, [-55, -56], [[14, 1, 1, 15, 15, true, -54, [5, 247, 120]]], [0, "c5+w940G9N96uabp6v1LEr", 1, 0], [5, 247, 120], [0, 0, 0.5], [-319.347, 0.394, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "img_long", false, 10, 4, [[3, 2, 0, -57, [0], 1], [39, 0, 45, 750, 1624, -58]], [0, "13HfTIPpZOj5vdZ9tI/0uS", 1, 0], [4, 4278190080], [5, 750, 1334]], [23, "img_dian", false, 79, 4, [[3, 2, 0, -59, [2], 3], [40, 0, 4, 2, -60]], [0, "83ZHy563xKULFxOsSvGeW+", 1, 0], [5, 750, 320], [0, -505, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "img_dian", false, 30, 4, [[3, 2, 0, -61, [4], 5], [41, 0, 1, 2, -62]], [0, "c1o6rLnABI+7KOiRhAdF0D", 1, 0], [4, 4278190080], [5, 750, 320], [0, 505, 0, 0, 0, 0, 1, 1, -1, 1]], [7, "bg", 1, [6], [[12, 45, 750, 1334, -63]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [4, "name", 2, [[15, "每日挑战", 43, 52, false, 1, 1, 2, -64, [8], 9], [1, 3, -65, [4, 4278190080]]], [0, "ef9qM1Pa1GzJNPy3Su7GKh", 1, 0], [4, 4293062126], [5, 281, 73.52], [-188.606, 118.186, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "tips", 2, [[15, "(难度随着通关递增)", 20, 52, false, 1, 1, 2, -66, [10], 11], [1, 2, -67, [4, 4278190211]]], [0, "32JkGQi+pALrgec3AldbEy", 1, 0], [4, 4293062126], [5, 281, 73.52], [-190.286, 72.788, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "time", 2, [[-68, [1, 2, -69, [4, 4278190080]]], 1, 4], [0, "dc0fLspqlKu7sg3hrAGSJE", 1, 0], [4, 4287099133], [5, 200, 73.52], [0, 0, 0.5], [81.62, -26.1, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "num", 2, [[-70, [1, 2, -71, [4, 4278190080]]], 1, 4], [0, "c7o0WVzKNEBqFoPIM4dopd", 1, 0], [5, 403.27, 73.52], [0, 0, 0.5], [-328.916, -107.428, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 8, [[48, "进入", 36, 36, false, false, 1, 1, 1, 1, 1, -72, [14], 15], [1, 2, -73, [4, 4278190080]]], [0, "95gWenkFJBMIQ+6+foYb5S", 1, 0], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Badge", 8, [[6, -74, [16], 17], [55, 1009, -75]], [0, "56TDsrSuFMNoifNG8wACvV", 1, 0], [5, 65, 66], [93.367, 59.651, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "rewardList", 2, [-77], [[14, 1, 1, 15, 15, true, -76, [5, 92, 120]]], [0, "c1C7TwO/xEqqPRmj8df6YD", 1, 0], [5, 92, 120], [0, 0, 0.5], [-319.347, -25.723, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "rewardItem", 25, [-79], [[36, 0, -78, [22], 23]], [0, "f4GU3A8UVCnbmkK0dTL4Vz", 1, 0], [5, 92, 96], [46, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "DiffSelect", 1, 2, [-80, -81], [0, "4cxJNG/51B8rAOgHN6Zc+Q", 1, 0], [5, 360, 70], [152.569, 107.806, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btn", 1, 27, [-83], [[3, 1, 0, -82, [26], 27]], [0, "efb+p1XnpLIZdENJqbs2Ga", 1, 0], [5, 160, 73], [89.505, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [27, "txt", 1, 28, [[49, "难度选择", 29, false, false, 1, 1, 1, 1, -84, [24], 25], [1, 2, -85, [4, 4278190080]]], [0, "96AMOVbvNKJZrSe3/v7Mvs", 1, 0], [5, 120, 54.4], [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "img_challenge", 120, 9, [[6, -86, [29], 30], [56, -87]], [0, "07RBx93M1DcpFk0b6+2mC2", 1, 0], [4, 4278190080], [5, 686, 313]], [2, "lock", 9, [[6, -88, [31], 32], [57, 2, 0.1, -89]], [0, "2bUjoPndBLvaQsTfpQvfHg", 1, 0], [5, 34, 41], [0, 42.032, 0, 0, 0, 0, 1, 2, 2, 1]], [2, "icon_sjxyx", 14, [[5, -90, [41]]], [0, "bdDlC7kApB2ay0DXyLU3G9", 1, 0], [5, 116, 116], [189, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "剩余时间：13:51:57", 20, 52, false, 1, 2, 1, 2, 21, [12]], [51, "今日剩余挑战次数:3", 28, 52, false, 1, 1, 2, 22, [13]], [29, "icon", 26, [[6, -91, [20], 21]], [0, "51gHIQymlB+rBdxgkxUk9h", 1, 0], [5, 64, 59]], [30, "New RichText", 1, 27, [[58, false, "<outline color=black width=3>难度:<color=#00ff00>??</c>", 30, 30, -92, 28]], [0, "29JB2L9ndNrbLM6LxuHL9b", 1, 0], [5, 108.78, 37.8], [0, 1, 0.5], [-5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label", 13, [[52, "进入", false, false, 1, 1, 1, 1, -93, [38]]], [0, "5aeaESNCFNmbHKH02Fo9SN", 1, 0], [4, 4281295134], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_zbzd", 14, [[5, -94, [40]]], [0, "14ZO/kxktJxKP+zEG+6+6q", 1, 0], [5, 116, 116], [58, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, -1, 33, 0, -2, 34, 0, -1, 3, 0, -2, 32, 0, 0, 1, 0, -1, 4, 0, -2, 18, 0, 0, 2, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -5, 8, 0, -6, 25, 0, -7, 27, 0, -8, 9, 0, 0, 3, 0, 0, 3, 0, -2, 5, 0, 0, 4, 0, 0, 4, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, 0, 5, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, -4, 14, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 23, 0, -2, 24, 0, -1, 30, 0, -2, 31, 0, -3, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 37, 0, 0, 14, 0, -1, 38, 0, -2, 32, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 33, 0, 0, 21, 0, -1, 34, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -1, 26, 0, 0, 26, 0, -1, 35, 0, -1, 28, 0, -2, 36, 0, 0, 28, 0, -1, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 6, 1, 2, 3, 3, 3, 3, 7, 6, 3, 18, 94], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 34], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, 2], [0, 4, 0, 2, 0, 2, 0, 5, 0, 1, 0, 1, 0, 0, 0, 1, 0, 6, 0, 7, 0, 8, 0, 9, 0, 1, 0, 10, 1, 0, 11, 0, 12, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1]], [[{"name": "img_jzsl", "rect": [0, 0, 686, 313], "offset": [0, 0], "originalSize": [686, 313], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [14]]]]