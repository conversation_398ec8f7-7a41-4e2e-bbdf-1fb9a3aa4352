[1, ["ecpdLyjvZBwrvm+cedCcQy", "7bvouSjDBJkayaYUJwyZdY", "bfXECmWsRPWKRPzS0bWbE/", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO", "f4zEKbzkxD9pCfiqDqEJQu", "08On+gNqxJBJ6h7ZmY7Ala", "a3HUjt/HVOtJE+mIHeicfl"], ["node", "_spriteFrame", "root", "_N$background", "_N$placeholderLabel", "_N$textLabel", "data", "_parent", "_N$normalSprite", "_N$disabledSprite", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_children", "_trs", "_parent", "_anchorPoint"], 1, 9, 4, 5, 2, 7, 1, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "alignMode", "_left", "node"], -3, 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_groupIndex", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], -1, 1, 12, 4, 5, 5, 7, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_enableWrapText", "_N$overflow", "node", "_materials"], -4, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["dc64fnP0uFB+6Sc+58wlepD", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -3, 1, 5], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [0, 0, 1, 7, 5, 2, 3, 4, 6, 3], [0, 0, 1, 7, 2, 3, 4, 3], [12, 0, 1, 2, 3, 4], [6, 0, 1, 2, 2], [4, 4, 0, 1, 2, 3, 7, 8, 6], [13, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 3], [1, 4, 0, 5, 1, 2, 6, 6], [8, 0, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 1, 5, 2, 3, 4, 8, 6, 3], [0, 0, 1, 7, 5, 2, 3, 4, 8, 6, 3], [0, 0, 1, 7, 2, 3, 4, 6, 3], [2, 0, 1, 2, 4, 5, 6, 7, 4], [2, 0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 5], [2, 0, 1, 2, 4, 5, 6, 10, 7, 8, 9, 4], [9, 0, 1], [5, 1, 2, 1], [1, 0, 1, 6, 3], [1, 0, 3, 1, 2, 6, 5], [1, 0, 3, 6, 3], [1, 4, 0, 1, 2, 6, 5], [1, 0, 6, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 7], [11, 0, 1, 2, 3, 4, 5, 3], [3, 2, 3, 4, 1], [6, 0, 1, 2, 3, 4, 5, 6, 2], [4, 0, 5, 1, 2, 3, 6, 7, 8, 7], [4, 4, 0, 5, 1, 2, 3, 6, 7, 8, 8]], [[[[10, "M33_TestBox"], [11, "M33_TestBox", 2, [-3, -4], [[18, -2]], [19, -1, 0], [5, 750, 1334]], [12, "bottonUI", 2, [-7, -8, -9, -10, -11, -12], [[20, 44, 600, -5], [25, 1, 3, 10, 10, 10, 10, -6, [5, 750, 170]]], [0, "f3xMePUptLpI+sbMIURS5n", 1, 0], [5, 750, 170], [0, 0.5, 0], [0, -607, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "EidtBox", 2, 2, [-17, -18, -19], [[26, 10000, 6, -16, -15, -14, -13]], [0, "5ayCa/8yFL24wyLxcED0FG", 1, 0], [5, 326, 80], [-12, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 2, 1, [-21, 2], [[21, 45, 120, 750, 1334, -20]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1214], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_zdtxdk", 2, 2, [-24], [[7, 1, 0, -22, [5], 6], [4, 3, -23, [[3, "dc64fnP0uFB+6Sc+58wlepD", "onBtn", "AddPoint", 1]]]], [0, "68SABj1VhK/oIXCY1v6FAZ", 1, 0], [5, 180, 80], [-275, 130, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "img_zdtxdk", 2, 2, [-27], [[7, 1, 0, -25, [8], 9], [4, 3, -26, [[3, "dc64fnP0uFB+6Sc+58wlepD", "onBtn", "SubPoint", 1]]]], [0, "e3Y2JoGElNu7Uy8fl40rD3", 1, 0], [5, 180, 80], [-85, 130, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "img_zdtxdk", 2, 2, [-30], [[8, 1, 0, -28, [11]], [4, 3, -29, [[3, "dc64fnP0uFB+6Sc+58wlepD", "onBtn", "Export", 1]]]], [0, "c0U5B8BZtP97l7ECNzeHHc", 1, 0], [5, 180, 80], [105, 130, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "img_zdtxdk", 2, 2, [-33], [[7, 1, 0, -31, [13], 14], [4, 3, -32, [[3, "dc64fnP0uFB+6Sc+58wlepD", "onBtn", "AddNew<PERSON><PERSON><PERSON>", 1]]]], [0, "50/bpQvyJKZpPQvBZ1opXw", 1, 0], [5, 180, 80], [-275, 40, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "img_zdtxdk", 2, 2, [-36], [[8, 1, 0, -34, [19]], [4, 3, -35, [[3, "dc64fnP0uFB+6Sc+58wlepD", "onBtn", "Reset", 1]]]], [0, "deozmCB8dFXqXo89KS8vZs", 1, 0], [5, 180, 80], [251, 40, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "topUI", 2, 4, [-38], [[22, 41, -47.60900000000004, -37]], [0, "8bkbxxodpNcpnBOLn5m5Hf", 1, 0], [5, 750, 100], [0, 0.5, 1], [0, 654.609, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnpause", 2, 10, [-40], [[28, 3, -39, [[3, "dc64fnP0uFB+6Sc+58wlepD", "onBtn", "Close", 1]], [4, 4293322470], [4, 3363338360], 2, 3]], [0, "dfUReHEtdNp6p7DWejBiVU", 1, 0], [5, 88, 93], [-297.339, -61.426, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "<PERSON><PERSON>", 2, 5, [[5, "增加点", 30, false, 1, 1, -41, [4]], [6, 4, -42, [4, 4278190080]]], [0, "03JlgUDXdA/Y1odIdVkFsd", 1, 0], [5, 98, 58.4]], [2, "<PERSON><PERSON>", 2, 6, [[5, "删除点", 30, false, 1, 1, -43, [7]], [6, 4, -44, [4, 4278190080]]], [0, "68PuTQwGdA9aEzQHYC65MH", 1, 0], [5, 98, 58.4]], [2, "<PERSON><PERSON>", 2, 7, [[5, "导出", 30, false, 1, 1, -45, [10]], [6, 4, -46, [4, 4278190080]]], [0, "a1Ubsz14dOArY4AeYwdt7f", 1, 0], [5, 68, 58.4]], [2, "<PERSON><PERSON>", 2, 8, [[5, "加一条龙", 30, false, 1, 1, -47, [12]], [6, 4, -48, [4, 4278190080]]], [0, "12t4FMu+ZJcbEQhZb4lz8N", 1, 0], [5, 128, 58.4]], [15, "BACKGROUND_SPRITE", 512, 2, 3, [[-49, [23, 0, 45, 160, 40, -50]], 1, 4], [0, "36vCyRoCRJfJKU+z8E+JkQ", 1, 0], [5, 326, 80]], [16, "TEXT_LABEL", 512, false, 2, 3, [[-51, [9, 0, 45, 2, 158, 40, -52]], 1, 4], [0, "2eEvCaFTJF+5wOu59E/LjI", 1, 0], [5, 118, 44], [0, 0, 1], [-58, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "PLACEHOLDER_LABEL", 512, 2, 3, [[-53, [9, 0, 45, 2, 158, 40, -54]], 1, 4], [0, "e2k9n9AcVA44/ielH5Du7W", 1, 0], [4, 4290493371], [5, 324, 80], [0, 0, 1], [-161, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "<PERSON><PERSON>", 2, 9, [[5, "生成", 30, false, 1, 1, -55, [18]], [6, 4, -56, [4, 4278190080]]], [0, "13V9zGjsJB5oj9eg2f8M4u", 1, 0], [5, 68, 58.4]], [2, "maskbg", 2, 1, [[24, 45, -57]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [14, "tpdg_icon_zanting", 2, 11, [[27, -58, [0], 1]], [0, "41NQUbmGdJHJr6qSEvZucS", 1, 0], [5, 98, 97], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [8, 1, 0, 16, [15]], [29, 30, false, false, 1, 1, 1, 17, [16]], [30, "输入指定路径", 30, false, false, 1, 1, 1, 18, [17]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 20, 0, -2, 4, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 3, 0, -6, 9, 0, 3, 22, 0, 4, 24, 0, 5, 23, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, 0, 4, 0, -1, 10, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, 0, 8, 0, 0, 8, 0, -1, 15, 0, 0, 9, 0, 0, 9, 0, -1, 19, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, -1, 21, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -1, 22, 0, 0, 16, 0, -1, 23, 0, 0, 17, 0, -1, 24, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 6, 1, 2, 7, 4, 58], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22], [-1, 1, 8, 9, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, -1, 1], [0, 2, 3, 4, 0, 0, 1, 0, 0, 5, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 6]], [[{"name": "button_return", "rect": [0, 0, 98, 97], "offset": [0, 0], "originalSize": [98, 97], "capInsets": [0, 0, 0, 0]}], [7], 0, [0], [10], [7]]]]