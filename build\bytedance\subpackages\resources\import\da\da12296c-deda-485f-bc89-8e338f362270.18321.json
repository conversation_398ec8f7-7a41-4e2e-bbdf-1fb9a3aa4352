[1, ["ecpdLyjvZBwrvm+cedCcQy", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO", "1fFOBaKA1Ii4S/B7748zch", "98y+xwDxlJZbDcGV61DKoh"], ["node", "_spriteFrame", "_N$normalSprite", "_N$disabledSprite", "root", "richText", "_N$barSprite", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 0, 9, 4, 5, 1, 2, 7, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_originalWidth", "_left", "_right", "_originalHeight", "node"], -4, 1], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], -2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["d2b55LDmLlCOJqQBxTdfMoR", ["UItype", "node", "nodeArr", "richText"], 2, 1, 2, 1]], [[4, 0, 1, 2, 2], [0, 0, 6, 7, 3, 4, 5, 8, 2], [5, 0, 2], [0, 0, 1, 7, 3, 4, 5, 3], [0, 0, 6, 3, 4, 5, 2], [0, 0, 6, 7, 3, 4, 5, 9, 8, 2], [0, 0, 6, 3, 4, 5, 8, 2], [0, 0, 2, 6, 7, 3, 4, 5, 8, 3], [3, 0, 2, 3, 4, 5, 6, 2], [3, 0, 1, 2, 3, 4, 5, 7, 6, 3], [2, 0, 7, 2], [2, 0, 1, 2, 3, 7, 5], [2, 0, 4, 5, 1, 2, 3, 6, 7, 8], [4, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1], [1, 0, 1, 2, 3, 3], [1, 0, 1, 2, 3, 4, 3], [1, 2, 3, 1], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3, 2]], [[2, "M38_FightUIView"], [3, "M38_FightUIView", 1, [-5, -6], [[22, 0, -4, [-3], -2]], [13, -1, 0], [5, 750, 1334]], [1, "bg", 1, [-9, -10], [[12, 45, -187.5, -187.5, -540.5, -660.5, 750, 1334, -7], [18, -8, [7]]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 1125, 2535], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "my_progressbar", false, 2, [-14], [[17, 1, 0, -11, [5], 6], [21, 750, 0.1, -13, -12]], [0, "f6qRDjN6NJqati9swVAEqE", 1, 0], [5, 750, 25], [-2, -600, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bottonUI", 2, [-16, -17], [[11, 41, 667.5, 1178.55, 600, -15]], [0, "f3xMePUptLpI+sbMIURS5n", 1, 0], [5, 1125, 140], [0, 0.5, 1], [0, 600, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnpause", 4, [-19], [[19, 3, -18, [[20, "d2b55LDmLlCOJqQBxTdfMoR", "pauseGame", 1]], [4, 4293322470], [4, 3363338360], 2, 3]], [0, "f9GNN/CC1FuKkToYib1rUY", 1, 0], [5, 88, 93], [-340, 50, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "maskbg", 1, [[10, 45, -20]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [8, "New RichText", 4, [-21], [0, "60s8WqPKRGU4VVweYlwD8F", 1, 0], [5, 322.18, 67.8], [0, 53.307, 0, 0, 0, 0, 1, 1, 1, 1]], [14, false, "<b><outline color=black width=2>通关第2关,从主界面的玩法合集\n进入<color=#00ff00>守中路</c>玩法</outline></b>", 1, 23, 30, 7], [6, "tpdg_icon_zanting", 5, [[15, -22, [0], 1]], [0, "a8QRB8oz1FO4+rAyUDJNmx", 1, 0], [5, 66, 65], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [9, "bar", 512, 3, [-23], [0, "37eSiVFpJNGojVjgeLrFCw", 1, 0], [5, 75, 19], [0, 0, 0.5], [-370, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 1, 0, 10, [4]]], 0, [0, 4, 1, 0, 5, 8, 0, -1, 3, 0, 0, 1, 0, -1, 6, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 3, 0, 0, 3, 0, 6, 11, 0, 0, 3, 0, -1, 10, 0, 0, 4, 0, -1, 7, 0, -2, 5, 0, 0, 5, 0, -1, 9, 0, 0, 6, 0, -1, 8, 0, 0, 9, 0, -1, 11, 0, 7, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0, 11], [-1, 1, 2, 3, -1, -1, 1, -1, 1], [0, 1, 1, 2, 0, 0, 3, 0, 4]]