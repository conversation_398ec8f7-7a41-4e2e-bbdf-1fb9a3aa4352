[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "e6dhZc0plHNZ0eB0rGEyBk", "4613FnL15GjKE6Ci5QeOnx", "c46cSnrYpELobhdnhwO2KF", "2f2eePlx1M9LE0GdmtDq1k", "60pNS9bqZIObqUpkGQ3p5Q", "fbDdLW1uRLCZEp7HFyQS7U", "5b0HySO0xNLI16cb6FYAR2", "14u6VJEV9Dg6dihzhomQUz"], ["node", "_spriteFrame", "root", "numLabel", "cardBtn", "icon", "tag", "desc", "skillname", "target", "data", "_N$file", "_N$font", "_textureSetter"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color"], 0, 4, 9, 5, 1, 7, 2, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_N$overflow", "_enableWrapText", "_styleFlags", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["5c4f9jgVJpAr55NnFbZ1r/i", ["node", "skillname", "desc", "tag", "icon", "cardBtn", "numLabel", "videoList"], 3, 1, 1, 1, 1, 1, 1, 1, 2], ["cc.<PERSON><PERSON>", ["_enabled", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "node", "_layoutSize"], -2, 1, 5], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -2, 1]], [[5, 0, 1, 2, 2], [0, 0, 6, 4, 3, 5, 7, 2], [2, 1, 0, 3, 4, 5, 3], [13, 0, 1, 2, 2], [0, 0, 1, 6, 8, 4, 3, 5, 7, 3], [2, 3, 4, 5, 1], [7, 0, 2], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 1, 6, 8, 3, 3], [0, 0, 2, 6, 4, 3, 9, 5, 3], [0, 0, 1, 6, 4, 3, 5, 7, 3], [4, 0, 2, 3, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 2, 3, 4, 5, 6, 2], [3, 0, 2, 3, 4, 5, 7, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 1], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3], [5, 1, 2, 1], [2, 0, 2, 3, 4, 3], [2, 3, 4, 1], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2], [1, 0, 1, 5, 2, 3, 4, 10, 11, 7], [1, 0, 1, 5, 2, 3, 4, 6, 10, 11, 8], [1, 0, 1, 5, 7, 2, 8, 3, 4, 6, 9, 10, 11, 11], [1, 0, 1, 5, 7, 2, 8, 3, 4, 10, 11, 9], [1, 0, 1, 2, 3, 4, 10, 11, 6], [14, 0, 1], [15, 0, 1, 2, 3, 4, 5, 6]], [[[[6, "BuffCardBar"], [7, "BuffCardBar", [-13, -14, -15, -16, -17, -18, -19, -20, -21, -22], [[16, -10, -9, -8, -7, -6, -5, -4, [-2, -3]], [17, false, -12, [[18, "5c4f9jgVJpAr55NnFbZ1r/i", "onClick", -11]]]], [19, -1, 0], [5, 613, 162], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "CardBtn", false, 1, [-25, -26], [[2, 1, 0, -23, [10], 11], [22, 1, 1, 10, 10, 6, -24, [5, 137, 56]]], [0, "7eEX6l3NVEALh3Ll4CT8ln", 1, 0], [5, 137, 56], [236.172, -77.305, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoIcon", 2, [[5, -27, [7], 8], [23, 2, -28]], [0, "28UTnPyLpNUroh998MLke7", 1, 0], [5, 53, 41], [-32, 1.586, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lock", false, 1, [-29, -30, -31], [0, "19bJuxHwlCTqQu98oTJdS3", 1, 0]], [11, "name", 1, [[-32, [3, 2, -33, [4, 4278190080]]], 1, 4], [0, "87DSaDvGVLLaamuqimxtMl", 1, 0], [5, 97.77, 41.8], [-220.613, -56.843, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 2, [[24, "体验", 26, 30, false, 1, 1, -34, [9]], [3, 3, -35, [4, 3456106496]]], [0, "56fV+FstlH6IXzq37l3PYa", 1, 0], [5, 58, 43.8], [29.5, 3.279, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "isExclusive", false, 1, [-37], [[2, 1, 0, -36, [13], 14]], [0, "d7gqln6IdAI4wrfUY7tSTO", 1, 0], [5, 123, 40], [-223.615, -72.452, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 7, [[25, "专属", 20, 30, false, 1, 1, 2, -38, [12]], [3, 3, -39, [4, 4278190080]]], [0, "03H3DkRXFNvoB3U3pbwo3G", 1, 0], [5, 96, 41.8], [-1.741, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "num", false, 1, [[-40, [3, 3, -41, [4, 4278190080]]], 1, 4], [0, "7bvCuAnxdAaLBjY/1/7puE", 1, 0], [5, 22.31, 56.4], [277.96, -43.25, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lockbg", 88, 4, [[2, 1, 0, -42, [16], 17], [29, -43]], [0, "b3qZxUb41Ic7BjvihNNoED", 1, 0], [4, 4278190080], [5, 613, 162]], [1, "Label", 4, [[26, "通关主线第4章解锁", 26, 50, false, false, 1, 1, 1, 2, 1, -44, [20]], [3, 3, -45, [4, 4278190080]]], [0, "78V5RNXrZKMpZveWw3tBiy", 1, 0], [5, 340, 69], [0, -52.305, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 1, [[2, 1, 0, -46, [0], 1]], [0, "8dQi1VzuhPd5s2mn3IFcek", 1, 0], [5, 613, 170], [0, 1.579, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "frame", false, 1, [[2, 1, 0, -47, [2], 3]], [0, "684Z0rXdBIsaGHvawENKQb", 1, 0], [5, 116, 120], [-222.694, 5.652, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "icon", 1, [-48], [0, "92ax78fO1Kkp8sRcxEyiNN", 1, 0], [5, 105, 105], [-220.928, 6.146, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [20, 2, false, 14, [4]], [27, "Buff名称", 24, 30, false, false, 1, 1, 1, 5, [5]], [14, "desc", 1, [-49], [0, "83uet4KSxNA4jsMFzxCJkG", 1, 0], [5, 420, 76.83999999999999], [0, 0, 0.5], [-131.338, 3.932, 0, 0, 0, 0, 1, 1, 1, 1]], [30, false, "背包中与魔法帽相邻的所有\n魔法武器攻击+20%", 32, 420, 34, 17], [15, "tag", false, 1, [-50], [0, "a2vYQG5NxHapIZRSXYHzoR", 1, 0], [5, 67, 47], [-276.239, 62.031, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [21, 19, [6]], [28, "1", 36, false, 1, 1, 9, [15]], [1, "icon_lock_small", 4, [[5, -51, [18], 19]], [0, "43rOTmrOlH9pQ033ex5Qy1", 1, 0], [5, 47, 56], [285, 65, 0, 0, 0, 0, 1, 1.2, 1.2, 1]]], 0, [0, 2, 1, 0, -1, 3, 0, -2, 2, 0, 3, 21, 0, 4, 2, 0, 5, 15, 0, 6, 20, 0, 7, 18, 0, 8, 16, 0, 0, 1, 0, 9, 1, 0, 0, 1, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 5, 0, -5, 17, 0, -6, 19, 0, -7, 2, 0, -8, 7, 0, -9, 9, 0, -10, 4, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 6, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, -2, 22, 0, -3, 11, 0, -1, 16, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, -1, 21, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, -1, 15, 0, -1, 18, 0, -1, 20, 0, 0, 22, 0, 10, 1, 51], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 18], [-1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 11, 12], [0, 2, 0, 3, 0, 0, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 8, 0, 1, 1]], [[{"name": "bg_buff_01", "rect": [15, 16, 290, 170], "offset": [0, -1], "originalSize": [320, 200], "capInsets": [172, 10, 36, 11]}], [6], 0, [0], [13], [9]]]]