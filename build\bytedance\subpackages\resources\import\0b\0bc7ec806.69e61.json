[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "d0jVaiAUJGjLG4minrewTO", "daghT3bJpHgaaHY4ed9e3B", "ddFhwuPitMK6rcBkC3ddyJ", "2b4vWm6xxFHJg6KVm92Xlr", "eer4QofNNLOqZtK9df0rqC", "c0VITW/OJML7691KcQocPY", "35RFcv50lBVotRgr46UtiC", "40OtPssnZP9antMAWsDtDT", "39b4iAhlFKH5qIjY+MvRfu", "4dMZmkcjlJEbhEI0rAtegJ", "aesuIQKRdLu4pxwhK6R5NI", "f0BIwQ8D5Ml7nTNQbh1YlS", "6eFZ9xCBFLRYlecnzVr/1r", "14ecb2FtlFAr3kkMu2GkKC", "92RmafvZhKQpEqHh3JkaEc"], ["node", "_spriteFrame", "_N$file", "_N$normalSprite", "_N$disabledSprite", "_textureSetter", "_N$target", "_parent", "root", "btnuse", "btndel", "dropNode", "tag", "puzzleNode", "upgradeNode", "equipLv", "equipFrame", "equipCellIcon", "equipBg", "equipImg", "equipName", "target", "_N$barSprite", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_trs", "_children", "_components", "_parent", "_anchorPoint"], 1, 4, 5, 7, 2, 9, 1, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 2, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "_styleFlags", "_N$cacheMode", "node", "_materials", "_N$file"], -7, 1, 3, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 12, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "alignMode", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.Prefab", ["_name"], 2], ["c696acQ5o5JPbZh2AEzZStl", ["node", "equipName", "equipImg", "equipBg", "equipCellIcon", "equipFrame", "equipLv", "upgradeNode", "puzzleNode", "tag", "dropNode", "btndel", "btnuse"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[7, 0, 1, 2, 2], [0, 0, 7, 5, 6, 2, 3, 4, 2], [14, 0, 1, 2, 2], [0, 0, 7, 6, 2, 3, 4, 2], [11, 0, 1, 2, 3], [6, 0, 2, 3, 4, 5, 6, 7, 8, 2], [1, 2, 0, 3, 4, 5, 3], [1, 3, 4, 1], [3, 0, 1, 2, 6, 3, 4, 5, 9, 10, 11, 12, 9], [0, 0, 5, 6, 2, 3, 4, 2], [0, 0, 7, 5, 2, 4, 2], [2, 0, 2, 3, 4, 5, 6, 2], [5, 0, 1, 2, 3, 4, 5, 2], [1, 3, 4, 5, 1], [8, 0, 4, 2], [9, 0, 2], [0, 0, 5, 2, 3, 2], [0, 0, 1, 5, 6, 2, 3, 8, 4, 3], [0, 0, 1, 7, 6, 2, 3, 4, 3], [2, 0, 2, 7, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 2, 3, 4, 5, 8, 6, 2], [5, 0, 1, 2, 3, 6, 4, 5, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [6, 1, 0, 2, 3, 4, 5, 6, 7, 8, 3], [7, 1, 2, 1], [12, 0, 1, 2, 2], [1, 0, 1, 3, 4, 5, 3], [1, 0, 1, 3, 4, 3], [1, 2, 0, 3, 4, 3], [13, 0, 1, 2, 3, 4, 4], [8, 1, 0, 2, 3, 4, 5], [3, 0, 1, 2, 6, 3, 4, 5, 7, 10, 11, 9], [3, 0, 1, 2, 3, 8, 4, 5, 10, 11, 8], [3, 0, 1, 2, 6, 3, 8, 4, 5, 7, 10, 11, 10]], [[[{"name": "img_jndk03", "rect": [3, 4, 167, 213], "offset": [1.5, -2], "originalSize": [170, 217], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [5], [4]], [[{"name": "button03_small", "rect": [0, 0, 56, 52], "offset": [0, 0], "originalSize": [56, 52], "capInsets": [16, 10, 12, 14]}], [4], 0, [0], [5], [5]], [[[15, "equipGridItem"], [9, "equipGridItem", [-18], [[23, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2], [24, 0.9, 3, -17, [[4, "c696acQ5o5JPbZh2AEzZStl", "onClick", -16]], [4, 4293322470], [4, 3363338360], -15, 32, 33]], [25, -1, 0], [5, 165, 221], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "alwaysin", [-19, -20, -21, -22, -23, -24], [0, "a6ZUxzc9pAM77+hpj8vvnQ", 1, 0], [5, 154, 230]], [1, "New ProgressBar", 2, [-28, -29, -30], [[26, 96, -26, -25], [27, 2, false, -27, [9], 10]], [0, "b0DijN+I9IAImM6IGkoEzh", 1, 0], [5, 98, 15], [0, -64.343, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [17, "droplist", false, [-32, -33, -34], [[30, 1, 2, 5, -31, [5, 154, 160]]], [0, "c35FrrkElGmpfBS0rvykWE", 1, 0], [5, 154, 160], [0, 0.5, 1], [0, -28.001, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Background", [-37], [[6, 1, 0, -35, [15], 16], [31, 0, 45, 100, 40, -36]], [0, "00HiaY6Z5Fk4p8G5Axbpk3", 1, 0], [5, 130, 50], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "content", 1, [2, 4], [-38], [0, "6aRoZzy4hEd7NFrfqTWruy", 1, 0], [5, 167, 213]], [12, "lv", 2, [[-39, [14, 16, -40], [2, 2, -41, [4, 4278190080]]], 1, 4, 4], [0, "baqhDqZI1MmovmuSzboe+U", 1, 0], [5, 134, 35.5], [0, 85.763, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "name", 2, [[-42, [14, 16, -43], [2, 2, -44, [4, 4278190080]]], 1, 4, 4], [0, "ecLiIqzStPH4eaKpr9PdNv", 1, 0], [5, 120, 31.5], [0, -28.322, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btndel", 4, [-47], [[5, 3, -46, [[4, "c696acQ5o5JPbZh2AEzZStl", "delete", 1]], [4, 4293322470], [4, 3363338360], -45, 23, 24]], [0, "1b4jJsWvBOXZV7Rvn+f8rE", 1, 0], [5, 130, 50], [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 9, [-49], [[6, 1, 0, -48, [21], 22]], [0, "6fQdzC8hZKMaqMpR4g+Sdg", 1, 0], [5, 130, 50], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnuse", 4, [-52], [[5, 3, -51, [[4, "c696acQ5o5JPbZh2AEzZStl", "select", 1]], [4, 4293322470], [4, 3363338360], -50, 29, 30]], [0, "57QtLbk4VNxo43Si035Fj/", 1, 0], [5, 130, 50], [0, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 11, [-54], [[6, 1, 0, -53, [27], 28]], [0, "5dW4UTZsJCXJ+cOCOp6sZt", 1, 0], [5, 130, 50], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [22, "progress", 3, [[-55, [2, 2, -56, [4, 4278190080]]], 1, 4], [0, "b5HrMiNE5DpKRnR1R0+jmi", 1, 0], [4, 4293062126], [5, 44.13, 29.2], [2.102, 0.301, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "state", 3, [-57, -58], [0, "e3Yk3WeK1Pb5s8+FZyk3zP", 1, 0], [0, 1.16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_pintu", 14, [[13, -59, [5], 6]], [0, "aeGXhoMR1Bxa0yMRdlXa0a", 1, 0], [5, 33, 33], [-55.988, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "icon_shengji", false, 14, [[13, -60, [7], 8]], [0, "bbdC4G0RZIwqPks1jktxvp", 1, 0], [5, 29, 34], [62.919, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btninfo", 4, [5], [[5, 3, -61, [[4, "c696acQ5o5JPbZh2AEzZStl", "showInfo", 1]], [4, 4293322470], [4, 3363338360], 5, 17, 18]], [0, "d7/45BKcFEaLQQ9Q9/r7MZ", 1, 0], [5, 130, 50], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Label", 5, [[8, "信息", 28, 30, false, false, 1, 1, 1, -62, [13], 14], [2, 2, -63, [4, 4278190080]]], [0, "e9wfmoAZ5ML4vddirFhks6", 1, 0], [5, 60, 41.8], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Label", 10, [[8, "移除", 28, 30, false, false, 1, 1, 1, -64, [19], 20], [2, 2, -65, [4, 4278190080]]], [0, "9emxCGEp5IQJLSIazx8SHB", 1, 0], [5, 60, 41.8], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Label", 12, [[8, "使用", 28, 30, false, false, 1, 1, 1, -66, [25], 26], [2, 2, -67, [4, 4278190080]]], [0, "44U8ZkCUBDWYebpaoM7qK/", 1, 0], [5, 60, 41.8], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "tag", false, 2, [-68], [0, "9ff9MBB1JKrrBhv/sRRnEb", 1, 0], [5, 91, 91], [56, 83, 0, 0, 0, 0, 1, 0.55, 0.55, 1]], [7, 21, [0]], [10, "goodnode", 2, [-69], [0, "e1h4rPz+dPG6ehmjte6bHk", 1, 0], [0, 13.724, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "goodimg", 23, [-70], [0, "58vtE0dfdGYoHyD15mpSOX", 1, 0], [5, 80, 80], [0, 16.999, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 0, false, 24, [1]], [32, "等级1", 18, 25, false, false, 1, 1, 2, 7, [2]], [21, "bar", 3, [-71], [0, "4bhKv+V/VE9oW63F4vgGAn", 1, 0], [5, 96, 13], [0, 0, 0.5], [-48, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, 1, 0, 27, [3]], [33, "0/25", 18, 20, false, 1, 1, 1, 13, [4]], [34, "手枪哈哈哈", 20, 25, false, false, 1, 1, 1, 2, 8, [11]], [11, "icon_gezi_1", 2, [-72], [0, "95alIwcuJJipHDsNNZ9dbe", 1, 0], [5, 26, 26], [52.973, -21.903, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 31, [12]], [7, 6, [31]]], 0, [0, 8, 1, 0, 9, 11, 0, 10, 9, 0, 11, 4, 0, 12, 22, 0, 13, 15, 0, 14, 16, 0, 15, 26, 0, 16, 29, 0, 17, 32, 0, 18, 33, 0, 19, 25, 0, 20, 30, 0, 0, 1, 0, 6, 1, 0, 21, 1, 0, 0, 1, 0, -1, 6, 0, -1, 21, 0, -2, 23, 0, -3, 7, 0, -4, 3, 0, -5, 8, 0, -6, 31, 0, 22, 28, 0, 0, 3, 0, 0, 3, 0, -1, 27, 0, -2, 13, 0, -3, 14, 0, 0, 4, 0, -1, 17, 0, -2, 9, 0, -3, 11, 0, 0, 5, 0, 0, 5, 0, -1, 18, 0, -1, 33, 0, -1, 26, 0, 0, 7, 0, 0, 7, 0, -1, 30, 0, 0, 8, 0, 0, 8, 0, 6, 10, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, -1, 19, 0, 6, 12, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, -1, 20, 0, -1, 29, 0, 0, 13, 0, -1, 15, 0, -2, 16, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 22, 0, -1, 24, 0, -1, 25, 0, -1, 28, 0, -1, 32, 0, 23, 1, 2, 7, 6, 4, 7, 6, 5, 7, 17, 72], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 28, 29, 30, 33], [-1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, 3, 4, -1, 2, -1, 1, 3, 4, -1, 2, -1, 1, 3, 4, -1, 3, 4, 2, 1, 2, 2, 1], [0, 0, 0, 0, 0, 0, 6, 0, 7, 0, 8, 0, 0, 0, 2, 0, 9, 10, 1, 0, 2, 0, 11, 12, 1, 0, 2, 0, 13, 14, 1, 0, 15, 1, 3, 16, 3, 3, 17]], [[{"name": "button04_small", "rect": [0, 0, 56, 52], "offset": [0, 0], "originalSize": [56, 52], "capInsets": [16, 10, 12, 14]}], [4], 0, [0], [5], [18]]]]