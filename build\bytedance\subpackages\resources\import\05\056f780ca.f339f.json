[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "cdcyuSrvZI+Zt6e7zKAXsr", "33SeWw7BRK6KTkqP6rtLKx", "f4MVAEsEdHvZTuDxJOxWUC", "69iFHLFrtIPKpKHm905GvR", "a2MjXRFdtLlYQ5ouAFv/+R", "65OUoZ25pGHqftEDT5VTS4", "baHW41841PF5Yr61qWY9O/", "045EqalblHKa103kI8VG6C", "cdvDZRLPZKb70zilA0TpQ8", "aarElN97tAsYmFrE/KSwwA", "86IEClttZOcbBlnDuaCb28", "05eSk4v5lGVpnDoJG09Lxt"], ["node", "_spriteFrame", "_textureSetter", "root", "checkMark", "_N$target", "_parent", "_N$disabledSprite", "asset", "data", "_normalMaterial"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], 0, 4, 5, 9, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "_N$affectedByScale", "node", "_layoutSize"], -2, 1, 5], ["cc.Node", ["_name", "_active", "_parent", "_children", "_components", "_prefab", "_contentSize"], 1, 1, 2, 2, 4, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents", "_normalMaterial"], 1, 1, 5, 1, 1, 9, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_lineHeight", "node", "_materials"], -5, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["3a5846vj/ZGL56t1T7ZmNJR", ["node", "nodeArr", "labelArr"], 3, 1, 2, 2], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [0, 0, 6, 7, 5, 3, 4, 8, 2], [0, 0, 6, 5, 3, 4, 8, 2], [17, 0, 1, 2, 2], [9, 0, 6, 1, 2, 3, 4, 5, 8, 9, 8], [1, 1, 0, 3, 4, 5, 3], [0, 0, 7, 5, 3, 4, 2], [0, 0, 6, 7, 3, 4, 8, 2], [0, 0, 6, 3, 2], [13, 0, 1, 2, 3, 3], [1, 1, 0, 2, 3, 4, 5, 4], [1, 1, 0, 2, 3, 4, 4], [16, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 3, 3], [6, 0, 1, 2, 3, 4], [10, 0, 2], [0, 0, 1, 7, 5, 3, 4, 3], [0, 0, 2, 6, 5, 3, 9, 4, 3], [0, 0, 6, 7, 5, 3, 4, 2], [11, 0, 1, 2, 3, 4, 2], [4, 0, 2, 3, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [12, 0, 1, 2, 1], [5, 1, 2, 1], [1, 1, 0, 3, 4, 3], [1, 0, 3, 4, 5, 2], [1, 3, 4, 5, 1], [3, 0, 1, 2, 5, 6, 4], [3, 0, 1, 5, 6, 3], [3, 0, 1, 3, 4, 5, 6, 5], [14, 0, 1], [15, 0, 1], [7, 0, 2, 3, 4, 5, 6, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 3], [8, 0, 3, 2], [8, 0, 1, 2, 3, 4], [9, 0, 7, 1, 2, 3, 4, 5, 8, 9, 8]], [[[{"name": "icon_jt", "rect": [0, 0, 34, 44], "offset": [0, 0], "originalSize": [34, 44], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [4]], [[{"name": "btn_yq01", "rect": [0, 0, 56, 43], "offset": [0, 0], "originalSize": [56, 43], "capInsets": [20, 10, 20, 20]}], [2], 0, [0], [2], [5]], [[[15, "Role_EquipRecast"], [16, "Role_EquipRecast", 1, [-7, -8], [[22, -6, [-3, -4, -5], [-2]]], [23, -1, 0], [5, 750, 1334]], [6, "content", [-12, -13, -14, -15, -16, -17], [[5, 1, 0, -9, [26], 27], [27, 1, 2, 30, -10, [5, 580, 631]], [30, -11]], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 580, 631]], [1, "New ToggleContainer", 2, [-20, -21], [[31, -18], [28, 1, 1, -19, [5, 430, 0]]], [0, "b6poiebe1K0ZwB64l3ctz+", 1, 0], [5, 430, 0], [0, -315.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "upgrade_btn", 2, [-24], [[12, 3, -22, [[13, "3a5846vj/ZGL56t1T7ZmNJR", "onClickDown", 1]], [4, 4293322470], [4, 3363338360], 13], [24, 1, 0, -23, [14]]], [0, "8f8D9/uUJN4q3FeNVBDgK7", 1, 0], [5, 200, 102], [0, -234.5, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "toggle1", 3, [-28, -29], [[32, 3, -27, [4, 4292269782], -26, -25, [[14, "3a5846vj/ZGL56t1T7ZmNJR", "onTap", "1", 1]], 20]], [0, "ccBu1gvXtL6oemu9U4DlNp", 1, 0], [5, 215, 73], [-107.5, -32.07, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "toggle2", 3, [-33, -34], [[33, 3, false, -32, [4, 4292269782], -31, -30, [[14, "3a5846vj/ZGL56t1T7ZmNJR", "onTap", "2", 1]]]], [0, "c2qEE/yNdET6dJKz7qWgny", 1, 0], [5, 215, 73], [107.5, -32.07, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "New Node", [-36], [[29, 1, 1, 10, true, -35, [5, 116, 200]]], [0, "ef0283mP1BGLmnUyUPyrM8", 1, 0], [5, 116, 200]], [17, "maskbg", 200, 1, [[34, 45, -37], [25, 0, -38, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [18, "bg", 1, [2], [[35, 45, 750, 1334, -39]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [7, "title", 2, [-40, -41], [0, "83721xahlPxrI3QU1AOids", 1, 0], [5, 685, 86], [0, 272.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnclose", 10, [[12, 3, -42, [[13, "3a5846vj/ZGL56t1T7ZmNJR", "close", 1]], [4, 4293322470], [4, 3363338360], 2], [5, 1, 0, -43, [3], 4]], [0, "36xVJDaMVFEIqm24FNfpQR", 1, 0], [5, 61, 65], [228.168, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "name", 10, [[-44, [3, 3, -45, [4, 4278190080]]], 1, 4], [0, "96EwPQSxVFhZFDjkhyNwdi", 1, 0], [5, 166, 58.92]], [1, "count", 2, [7], [[5, 1, 0, -46, [10], 11]], [0, "e08J856BtKdpT6MT6hpSnr", 1, 0], [5, 520, 109], [0, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 4, [[4, "降级", 36, false, false, 1, 1, 1, -47, [12]], [3, 3, -48, [4, 4278190080]]], [0, "a3kim8CXhHbKw0zacnLNpd", 1, 0], [5, 78, 56.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 5, [-50], [[10, 1, 0, false, -49, [16], 17]], [0, "25MLYGmIlIC66wAL+lWh7z", 1, 0], [5, 215, 58], [0, 3.979, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 15, [[4, "降级", 32, false, false, 1, 1, 1, -51, [15]], [3, 3, -52, [4, 4278190080]]], [0, "657Jzb925AVbXJ+Z/GDIVm", 1, 0], [5, 70, 56.4], [0, 8.036, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "checkmark", 5, [-54], [-53], [0, "bbfbhHhIlC3Ipo/8HwyryA", 1, 0], [5, 215, 73]], [2, "val", 17, [[4, "降级", 34, false, false, 1, 1, 1, -55, [18]], [3, 3, -56, [4, 4278190080]]], [0, "d3MRvE8zVEyZapxLN9Trm0", 1, 0], [5, 74, 56.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 6, [-58], [[10, 1, 0, false, -57, [22], 23]], [0, "8cHyuyjixDCqoMESaSAxBx", 1, 0], [5, 215, 58], [0, 3.979, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 19, [[4, "降品", 32, false, false, 1, 1, 1, -59, [21]], [3, 3, -60, [4, 4278190080]]], [0, "ffwEXSwydE8rq4zEZ9FuNV", 1, 0], [5, 70, 56.4], [0, 8.036, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "checkmark", false, 6, [-62], [-61], [0, "b42rnRyZNJH4xXcmHi9uSr", 1, 0], [5, 215, 73]], [2, "val", 21, [[4, "降品", 34, false, false, 1, 1, 1, -63, [24]], [3, 3, -64, [4, 4278190080]]], [0, "denRgfVrhOGLZ8Riob8Zg6", 1, 0], [5, 74, 56.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "装备降级", 42, false, false, 1, 1, 1, 12, [5]], [7, "msg", 2, [-65], [0, "6bcoioY6NFL5Sn8mtFNT3/", 1, 0], [5, 520, 140], [0, 129.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Role_EquipItem", 24, [9, "1dk+fEprtP7KYT6W29RZTy", true, -66, 6]], [2, "icon_jt", 2, [[26, -67, [7], 8]], [0, "cbn1MPfz1G7I07KkUoyiuN", 1, 0], [5, 34, 44], [0, 7.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "RewardItem", 7, [9, "dcQ93ekgFNPJxmceAZpR9w", true, -68, 9]], [11, 1, 0, false, 17, [19]], [11, 1, 0, false, 21, [25]]], 0, [0, 3, 1, 0, -1, 23, 0, -1, 7, 0, -2, 4, 0, -3, 3, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 10, 0, -2, 24, 0, -3, 26, 0, -4, 13, 0, -5, 4, 0, -6, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, -2, 6, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, 4, 28, 0, 5, 5, 0, 0, 5, 0, -1, 15, 0, -2, 17, 0, 4, 29, 0, 5, 6, 0, 0, 6, 0, -1, 19, 0, -2, 21, 0, 0, 7, 0, -1, 27, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 11, 0, -2, 12, 0, 0, 11, 0, 0, 11, 0, -1, 23, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 16, 0, 0, 16, 0, 0, 16, 0, -1, 28, 0, -1, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 20, 0, -1, 29, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, -1, 25, 0, 3, 25, 0, 0, 26, 0, 3, 27, 0, 9, 1, 2, 6, 9, 7, 6, 13, 68], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 29], [-1, 1, 7, -1, 1, -1, 8, -1, 1, 8, -1, 1, -1, 7, -1, -1, -1, 1, -1, -1, 10, -1, -1, 1, -1, -1, -1, 1, 1, 1], [0, 6, 1, 0, 7, 0, 8, 0, 9, 10, 0, 11, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2, 0, 0, 0, 12, 3, 3]], [[{"name": "btn_yq02", "rect": [0, 0, 56, 43], "offset": [0, 0], "originalSize": [56, 43], "capInsets": [20, 10, 20, 20]}], [2], 0, [0], [2], [13]]]]