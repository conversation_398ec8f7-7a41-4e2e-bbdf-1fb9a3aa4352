[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "c0VITW/OJML7691KcQocPY", "a2MjXRFdtLlYQ5ouAFv/+R", "c5cCoWLYlAoIZDaLpFL1A+", "94EziLmzlAirmOfZrgDe1Z", "58L+DSdUdF4pTKifGiWAVB", "7c0TM1GLRK5ZCrsHCjb0gP", "f3p84uzd5EVLXvLuhlyoRY", "4dMZmkcjlJEbhEI0rAtegJ", "c46cSnrYpELobhdnhwO2KF", "40OtPssnZP9antMAWsDtDT", "3844rbY/dMubjfZ9ec678s", "dc3taBoSJNqJaAecgVyYRX"], ["node", "_spriteFrame", "_N$file", "_parent", "root", "_N$font", "_textureSetter", "titleBg", "btnList", "skillRoot", "data", "asset"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color"], 0, 4, 5, 1, 9, 7, 2, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "_styleFlags", "_N$overflow", "_spacingX", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_N$paddingTop", "_enabled", "_N$paddingLeft", "node", "_layoutSize"], -4, 1, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$horizontalAlign", "node"], -3, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["4b26dgdcGhNa4xRUVmroeY7", ["node", "nodeArr", "labelArr", "skillRoot", "richTextArr", "btnList", "titleBg"], 3, 1, 2, 2, 1, 2, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "desc", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1]], [[6, 0, 1, 2, 2], [0, 0, 5, 6, 3, 4, 7, 2], [17, 0, 1, 2, 2], [0, 0, 5, 8, 6, 3, 4, 7, 2], [2, 2, 3, 4, 1], [13, 0, 1, 2, 3], [14, 0, 1, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 8, 6, 3, 4, 2], [0, 0, 8, 6, 3, 4, 7, 2], [4, 0, 2, 3, 4, 5, 6, 2], [12, 0, 1, 2, 3, 3], [15, 0, 1, 2, 3, 3], [1, 0, 1, 5, 2, 7, 3, 4, 10, 11, 12, 8], [1, 0, 1, 5, 2, 3, 4, 10, 11, 12, 7], [1, 0, 1, 5, 2, 3, 4, 8, 6, 10, 11, 12, 9], [18, 0, 1], [7, 0, 1, 5, 2, 3, 4, 6, 7], [8, 0, 2], [0, 0, 2, 5, 6, 3, 9, 4, 3], [0, 0, 5, 8, 3, 4, 2], [0, 0, 5, 8, 3, 7, 2], [0, 0, 1, 5, 6, 3, 4, 7, 3], [0, 0, 1, 5, 6, 3, 9, 4, 7, 3], [0, 0, 5, 3, 2], [9, 0, 1, 2, 3, 4, 5, 2], [4, 0, 2, 3, 4, 5, 7, 6, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [10, 0, 1, 2, 3, 4, 5, 6, 1], [6, 1, 2, 1], [11, 0, 1, 2, 3, 3], [2, 0, 2, 3, 4, 2], [2, 2, 3, 1], [3, 0, 1, 4, 3, 7, 8, 5], [3, 0, 1, 2, 7, 8, 4], [3, 5, 0, 1, 2, 7, 8, 5], [3, 0, 1, 6, 2, 3, 7, 8, 6], [16, 0, 1, 2, 3, 4], [1, 0, 1, 2, 9, 3, 4, 6, 10, 11, 8], [1, 0, 1, 2, 3, 4, 10, 11, 6], [7, 0, 1, 2, 3, 4, 6, 6]], [[[[18, "M33_FightBuffView"], [8, "M33_FightBuffView", [-12, -13], [[28, -11, [-9, -10], [-8], -7, [-4, -5, -6], -3, -2]], [29, -1, 0], [5, 750, 1334]], [9, "btn", [-17, -18, -19, -20], [[11, "BagAdRefreshBuff", "是否观看广告刷新属性", -14, [[5, "4b26dgdcGhNa4xRUVmroeY7", "adResetAllBuff", 1]]], [6, 2, -15], [7, 1, 0, -16, [28], 29]], [0, "41Ox0ZK0FA7pvkXvMJ4ISg", 1, 0], [5, 246, 108], [143, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btn", [-24, -25, -26], [[11, "BagAdGetAllBuff", "是否观看广告获得所有属性", -21, [[5, "4b26dgdcGhNa4xRUVmroeY7", "adGetAllSkill", 1]]], [7, 1, 0, -22, [34], 35], [6, 2, -23]], [0, "82OV+8XgRKJrkYTwbOuIlx", 1, 0], [5, 246, 108], [429, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "box", [-28, -29, -30, -31, -32], [[33, 1, 2, -40, 40, -27, [5, 750, 622]]], [0, "cbh74iG2JNWYggDA+9Cz25", 1, 0], [5, 750, 622]], [3, "New Layout", 4, [-34, -35, 2, 3], [[34, 1, 1, 40, -33, [5, 1104, 260]]], [0, "ddwmWeCCZECaLHLX1UI7xn", 1, 0], [5, 1104, 260], [0, -181, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "GiveUpBtn_1", 5, [-39, -40, -41], [[7, 1, 0, -36, [16], 17], [6, 2, -37], [12, 0.9, 3, -38, [[5, "4b26dgdcGhNa4xRUVmroeY7", "onClickGiveUp", 1]]]], [0, "707BuQVzNA+rSUxvdnPVVb", 1, 0], [5, 246, 108], [-429, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "GiveUpBtn_2", 5, [-45, -46], [[7, 1, 0, -42, [22], 23], [6, 2, -43], [12, 0.9, 3, -44, [[5, "4b26dgdcGhNa4xRUVmroeY7", "onClickGiveUp", 1]]]], [0, "bdNOfiHOBInpi/FfNhNIUl", 1, 0], [5, 246, 108], [-143, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "title", 4, [-48, -49, -50], [[35, false, 1, 1, 27.999999999999996, -47, [5, 484, 120]]], [0, "5dMzaKAelOAJOpgYdVL4S+", 1, 0], [5, 484, 120], [0, 251, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "skillroot", 4, [-52], [[36, 1, 2, 5, 39, 34, -51, [5, 750, 162]]], [0, "46KwqWUtNMdontI7Tm6diE", 1, 0], [5, 750, 162], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "maskbg", 161, 1, [[31, 0, -53, [0], 1], [37, 45, 750, 1334, -54]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, **********], [5, 750, 1334]], [25, "title", 8, [[-55, [2, 3, -56, [4, **********]]], 1, 4], [0, "8cXYiN4o5B7qlQz0sxbw/+", 1, 0], [5, 150, 56.4], [0, 5.345, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 6, [[13, "损失当前50%生命", 24, false, false, 1, 1, 1, -57, [12], 13], [2, 2, -58, [4, **********]]], [0, "21IEEODlBEIaNxOhL9XeE0", 1, 0], [5, 196.74, 54.4], [18.478, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 6, [[14, "放弃", 36, false, false, 1, 1, -59, [14], 15], [2, 2, -60, [4, **********]]], [0, "bbDjT5aQJIAZ67HlM+sT3w", 1, 0], [5, 76, 54.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 7, [[13, "获得一个减益buff", 24, false, false, 1, 1, 1, -61, [18], 19], [2, 2, -62, [4, **********]]], [0, "48brJejyNOU6T1pshMRgCP", 1, 0], [5, 192.83, 54.4], [0, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 7, [[14, "放弃", 36, false, false, 1, 1, -63, [20], 21], [2, 2, -64, [4, **********]]], [0, "4dyu6PsilFPa/MRL0Nkf33", 1, 0], [5, 76, 54.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoicon", 2, [[4, -65, [24], 25], [16, -66]], [0, "88dY/mOVxIt7SPaRkhyhZV", 1, 0], [5, 50, 52], [-72.522, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 2, [[15, "刷新", 36, false, false, 1, 1, 2, 1, -67, [26], 27], [2, 2, -68, [4, **********]]], [0, "06pVZh+rpFzooNnlVfBZtD", 1, 0], [5, 140, 50.4], [30.198, 2.371, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 3, [[15, "全部获得", 36, false, false, 1, 1, 2, 1, -69, [30], 31], [2, 2, -70, [4, **********]]], [0, "668sJpPHxOf6EOqNOk8S32", 1, 0], [5, 166, 50.4], [31.847, 2.371, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoicon", 3, [[4, -71, [32], 33], [16, -72]], [0, "1cSWDXKk1BfKRW4AzlM3Bh", 1, 0], [5, 53, 41], [-76.776, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "bg", 1, [4], [0, "81vg/003JNFJr3x8kM830u", 1, 0], [5, 750, 1334]], [21, "titleBg", 4, [-73], [0, "49GkTZRoBJaanU0fQqnAVE", 1, 0], [0, 351, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "img_btembx", 21, [-74], [0, "97eeEopGNPn4I+eHaf/UbK", 1, 0], [5, 711, 508], [0, 0.5, 0], [0, -256.852, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 22, [2]], [1, "line", 8, [[4, -75, [3], 4]], [0, "17VcVYMEtBKoqKo8P+8gFZ", 1, 0], [5, 531, 47], [9.494, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [38, "选择强化", 36, false, 1, 1, 1, 1, 11, [5]], [22, "line", false, 8, [[4, -76, [6], 7]], [0, "1d302R389LPYYOQewVA6+j", 1, 0], [5, 87, 30], [198.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "getinfo", false, 4, [[39, "选择1个宝物", 32, false, 1, 1, -77, [8]]], [0, "2c0uXyodtBBIqa2In1n3N6", 1, 0], [4, 4282298874], [5, 174.5, 50.4], [0, 340.00000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "BuffCardBar", 9, [30, "dbHEMp70NBhbaB+oJoZSdC", true, -78, 9]], [1, "icon_xs", 6, [[4, -79, [10], 11]], [0, "94DQryEWdNH4enR+aAauPu", 1, 0], [5, 33, 28], [-101.322, -72.585, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "richtext_remain", 2, [-80], [0, "63VTUp8zNBSIbBxN+9F1Ul", 1, 0], [5, 240, 37.8], [0, 79.285, 0, 0, 0, 0, 1, 1, 1, 1]], [17, false, "剩余次数<color=#00ff00>%d/%d</c>", 1, 24, 240, 30, 30], [27, "richtext_tips", false, 2, [-81], [0, "d0N8pbnOVILqG8p3ZZhcu4", 1, 0], [5, 240, 37.8], [0, -69.93, 0, 0, 0, 0, 1, 1, 1, 1]], [40, false, "高概率刷出<color=#0fffff>稀有</color>或以上属性", 20, 240, 30, 32], [10, "richtext_remain", 3, [-82], [0, "83CWKPXRNG75hhs/jjfofG", 1, 0], [5, 240, 37.8], [0, 79.285, 0, 0, 0, 0, 1, 1, 1, 1]], [17, false, "剩余次数<color=#00ff00>%d/%d</c>", 1, 24, 240, 30, 34]], 0, [0, 4, 1, 0, 7, 23, 0, 8, 5, 0, -1, 31, 0, -2, 35, 0, -3, 33, 0, 9, 9, 0, -1, 25, 0, -1, 2, 0, -2, 3, 0, 0, 1, 0, -1, 10, 0, -2, 20, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -2, 30, 0, -3, 32, 0, -4, 17, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 18, 0, -2, 19, 0, -3, 34, 0, 0, 4, 0, -1, 21, 0, -2, 8, 0, -3, 27, 0, -4, 9, 0, -5, 5, 0, 0, 5, 0, -1, 6, 0, -2, 7, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 29, 0, -2, 12, 0, -3, 13, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, -2, 15, 0, 0, 8, 0, -1, 24, 0, -2, 11, 0, -3, 26, 0, 0, 9, 0, -1, 28, 0, 0, 10, 0, 0, 10, 0, -1, 25, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, -1, 22, 0, -1, 23, 0, 0, 24, 0, 0, 26, 0, 0, 27, 0, 4, 28, 0, 0, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, 10, 1, 2, 3, 5, 3, 3, 5, 4, 3, 20, 82], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 31, 35], [-1, 1, -1, -1, 1, -1, -1, 1, -1, 11, -1, 1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, 2, 5, 5], [0, 4, 0, 0, 5, 0, 0, 6, 0, 7, 0, 8, 0, 2, 0, 1, 0, 3, 0, 2, 0, 1, 0, 3, 0, 9, 0, 1, 0, 10, 0, 1, 0, 11, 0, 12, 1, 2, 2]], [[{"name": "icon_xs", "rect": [0, 0, 33, 28], "offset": [0, 0], "originalSize": [33, 28], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [6], [13]], [[{"name": "pic_jl_00_title", "rect": [0, 0, 531, 47], "offset": [0, 0], "originalSize": [531, 47], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [6], [14]]]]