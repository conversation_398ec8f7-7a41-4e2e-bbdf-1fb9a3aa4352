[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "c3+ruR7+FEnKfu8yo+WDeT", "aarElN97tAsYmFrE/KSwwA", "41iFMUVchFyLfNhNlKc09M", "c3lIDRIIZADL0Y5TbtK8lV", "a2MjXRFdtLlYQ5ouAFv/+R", "5esB9LxCNIpozu/DuPYufl", "faMhRKCydAILeNcuY/b9en", "0bNy06XqNOgKsDrtBlOZTu", "baHW41841PF5Yr61qWY9O/", "5cPBdXs75J7qCoTehjEdKA", "35cV31bWJIPoeSAXzUMuns", "9c7tgItYRPOrAJBZiIN8w2", "3dnAUe0QtKe53/ClCH9ijd", "5b0HySO0xNLI16cb6FYAR2", "b2gseIigJABZr/xG0zwT9Y", "29R34cGbFB7YQJIiPE06Fl", "f0AfIe6sJDqKt0ah0Tz7NB", "eb7rP3sXpIBYE7q4zDYjkf", "baW3aYIVdD0qgz0BhSkzPy", "dfgTHB4CNF7qIHJdO+DhcU", "0d+ftfsHBE6IIs/gS3yojO", "6elmb5lxVGiZ9x6rFRQopX", "df6TYg3npB2JQl7EYGFXJp"], ["node", "_spriteFrame", "_textureSetter", "_N$disabledSprite", "_parent", "root", "lockImg", "buffList", "btnLayer", "upCountNode", "attrText", "data", "asset"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_anchorPoint", "_color"], 0, 4, 5, 9, 1, 2, 7, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingBottom", "_N$spacingY", "_N$spacingX", "_N$paddingTop", "_N$paddingLeft", "_N$affectedByScale", "node", "_layoutSize"], -5, 1, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_bottom", "node"], -2, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "_N$maxWidth", "_N$horizontalAlign", "node"], -3, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_lineHeight", "node", "_materials"], -5, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["a8566IxCPFJlLgf5rV5+6Sp", ["node", "nodeArr", "labelArr", "attrText", "upCountNode", "btnLayer", "buffList", "lockImg"], 3, 1, 2, 2, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[7, 0, 1, 2, 2], [0, 0, 6, 7, 5, 3, 4, 8, 2], [0, 0, 6, 5, 3, 4, 8, 2], [9, 0, 6, 1, 2, 3, 4, 5, 8, 9, 8], [18, 0, 1, 2, 2], [2, 1, 0, 2, 3, 4, 3], [2, 2, 3, 4, 1], [15, 0, 1, 2, 3], [8, 0, 2, 3, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 6, 5, 2], [2, 0, 2, 3, 4, 2], [0, 0, 7, 5, 3, 4, 2], [0, 0, 6, 7, 3, 4, 8, 2], [0, 0, 6, 7, 5, 3, 4, 2], [0, 0, 6, 5, 3, 10, 4, 9, 8, 2], [4, 0, 1, 2, 3, 4, 5, 2], [2, 1, 0, 2, 3, 3], [5, 0, 1, 2, 5, 4], [8, 1, 0, 2, 3, 3], [10, 0, 2], [0, 0, 1, 7, 5, 3, 4, 3], [0, 0, 7, 5, 3, 4, 9, 8, 2], [0, 0, 2, 6, 5, 3, 10, 4, 3], [0, 0, 6, 7, 5, 3, 4, 9, 8, 2], [0, 0, 6, 3, 8, 2], [4, 0, 1, 2, 3, 4, 2], [11, 0, 1, 2, 3, 4, 5, 6, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 1], [7, 1, 2, 1], [13, 0, 1, 2, 3, 3], [2, 2, 3, 1], [3, 0, 1, 2, 3, 8, 9, 5], [3, 0, 1, 5, 2, 3, 8, 9, 6], [3, 0, 1, 4, 8, 9, 4], [3, 0, 1, 6, 4, 7, 8, 9, 6], [14, 0, 1], [5, 3, 0, 4, 1, 2, 5, 6], [5, 0, 5, 2], [16, 0, 1, 2, 3, 4, 5, 6, 6], [17, 0, 1, 2, 2], [9, 0, 7, 1, 2, 3, 4, 5, 8, 9, 8], [6, 0, 1, 2, 4, 3, 6, 6], [6, 0, 1, 2, 3, 6, 5], [6, 0, 1, 5, 2, 6, 5]], [[[{"name": "img_zld", "rect": [0, 0, 317, 57], "offset": [0, 0], "originalSize": [317, 57], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [4]], [[{"name": "img_tcd2", "rect": [0, 0, 71, 100], "offset": [0, 0], "originalSize": [71, 100], "capInsets": [25, 62, 24, 22]}], [1], 0, [0], [2], [5]], [[[19, "Role_EquipInfo"], [20, "Role_EquipInfo", 1, [-15, -16], [[27, -14, [-12, -13], [-7, -8, -9, -10, -11], -6, -5, -4, -3, -2]], [28, -1, 0], [5, 750, 1334]], [11, "content", [-20, -21, -22, -23, -24, -25], [[5, 1, 0, -17, [50], 51], [31, 1, 2, 35, 20, -18, [5, 685, 981]], [35, -19]], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 685, 981]], [12, "msg", 2, [-26, -27, -28, -29, -30, -31], [0, "6bcoioY6NFL5Sn8mtFNT3/", 1, 0], [5, 680, 140], [0, 314.5, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "content", [-34], [[36, 0, 41, 17, 240, 250, -32], [32, 1, 2, 20, 100, 12, -33, [5, 640, 162.84]]], [0, "56PNZt3b9HGor07mKnmqbn", 1, 0], [5, 640, 162.84], [0, 0.5, 1], [0, 133.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnLayer", 2, [-36, -37, -38], [[33, 1, 1, 18, -35, [5, 636, 100]]], [0, "4bm6iVqDhFEJvERlrMmhlu", 1, 0], [5, 636, 100], [0, -405.5, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Node", [-40, -41], [[34, 1, 1, 10, 10, true, -39, [5, 172.87, 50]]], [0, "655tTmIPhAk57HhIn91DJ7", 1, 0], [5, 172.87, 50]], [12, "title", 2, [-42, -43, -44], [0, "83721xahlPxrI3QU1AOids", 1, 0], [5, 685, 86], [0, 447.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_zld", 3, [-46, -47], [[6, -45, [15], 16]], [0, "98H/wNODpOobHx/Y+/8ngW", 1, 0], [5, 317, 57], [-16.917, 33.191, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lv", 3, [-49, -50], [[10, 0, -48, [19], 20]], [0, "70HZzvJNVFpJXOlwjBLPXf", 1, 0], [5, 203, 48], [-76.316, -36.724, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lv", 3, [-52, -53], [[10, 0, -51, [23], 24]], [0, "c8Yxc+p7lNc4dIKus7DkLz", 1, 0], [5, 203, 48], [153.637, -36.724, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "reset", 3, [[6, -54, [25], 26], [18, 0.9, 3, -55, [[7, "a8566IxCPFJlLgf5rV5+6Sp", "openRecastView", 1]]]], [0, "61jCyu005JULPtfp1rejwB", 1, 0], [5, 46, 46], [293.635, -36.309, 0, 0, 0, 0, 1, 1, 1, 0]], [15, "lock", 3, [[-56, [18, 0.9, 3, -57, [[7, "a8566IxCPFJlLgf5rV5+6Sp", "onClickLock", 1]]]], 1, 4], [0, "5afWHuj8ZIN6T6s3FaTGMG", 1, 0], [5, 46, 46], [293.635, 36.737, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "atr", 2, [-59, -60], [[5, 1, 0, -58, [29], 30]], [0, "9ffEkoY79JxIwY8OqM83Ww", 1, 0], [5, 640, 180], [0, 134.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "buffList", 2, [-63], [[5, 1, 0, -61, [34], 35], [38, false, 0.75, 0.23, null, null, -62, 4]], [0, "8clJSIdaFOXohMEi8F+LNR", 1, 0], [5, 640, 267], [0, -109, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "view", 14, [4], [[39, 0, -64, [33]], [17, 45, 240, 250, -65]], [0, "c1rtpqoP5MpJs7y63Ubujf", 1, 0], [5, 640, 267]], [1, "upgrade_btn", 5, [-68], [[8, 3, -66, [[7, "a8566IxCPFJlLgf5rV5+6Sp", "setFitOut", 1]], [4, 4293322470], [4, 3363338360], 41], [5, 1, 0, -67, [42], 43]], [0, "bdMueYkWpHDJ9S3pQdR6ps", 1, 0], [5, 200, 102], [-218, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "upgrade_btn", 5, [-71], [[8, 3, -69, [[7, "a8566IxCPFJlLgf5rV5+6Sp", "upLevel", 1]], [4, 4293322470], [4, 3363338360], 45], [16, 1, 0, -70, [46]]], [0, "6cZWxAU0VGIZF0J8RzWOMH", 1, 0], [5, 200, 102], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "upgrade_btn", 5, [-74], [[8, 3, -72, [[7, "a8566IxCPFJlLgf5rV5+6Sp", "upLevelTen", 1]], [4, 4293322470], [4, 3363338360], 48], [16, 1, 0, -73, [49]]], [0, "c6xnZ3C4RJ5YFYrDuEPfoK", 1, 0], [5, 200, 102], [218, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [22, "maskbg", 140, 1, [[37, 45, -75], [10, 0, -76, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [13, "bg", 1, [2], [[17, 45, 750, 1334, -77]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [2, "btnclose", 7, [[8, 3, -78, [[7, "a8566IxCPFJlLgf5rV5+6Sp", "close", 1]], [4, 4293322470], [4, 3363338360], 2], [5, 1, 0, -79, [3], 4]], [0, "36xVJDaMVFEIqm24FNfpQR", 1, 0], [5, 61, 65], [282.919, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "title", 7, [-81], [[6, -80, [6], 7]], [0, "c4d5LdhplM26t0je0Q8p8K", 1, 0], [5, 472, 103], [0, 30.685, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "name", 22, [[-82, [4, 3, -83, [4, 4278190080]]], 1, 4], [0, "a0FZ97sutNF4JUpt7PhuBV", 1, 0], [5, 166, 58.92], [0, 10.509, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_jspzd1", 7, [-85], [[5, 1, 0, -84, [9], 10]], [0, "78anhRODJEXbh2AE0LT4yH", 1, 0], [5, 163, 42], [0, -36.41, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "rname", 24, [[-86, [4, 3, -87, [4, 4278190080]]], 1, 4], [0, "7aLJd/AyVO7bkLiJDaHTiL", 1, 0], [5, 56, 56.4]], [9, "val", 8, [[-88, [4, 3, -89, [4, 4278190080]]], 1, 4], [0, "c7DpXJFqdLGYLyBZZdr0fK", 1, 0], [5, 23.8, 56.4], [0, 0, 0.5], [-74.788, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "val", 9, [[-90, [4, 3, -91, [4, 4278190080]]], 1, 4], [0, "67qPRXGzZElqhZB+IuoXb3", 1, 0], [5, 64.39, 56.4], [0, 1, 0.5], [91.601, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "val", 10, [[-92, [4, 3, -93, [4, 4278190080]]], 1, 4], [0, "f4RkJInP1LnpznJ1d292S9", 1, 0], [5, 66, 56.4], [0, 1, 0.5], [91.601, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "title", 13, [[3, "基础属性", 32, false, false, 1, 1, 1, -94, [28]], [4, 3, -95, [4, 4278190080]]], [0, "e5pqk4KudKq6FzEsQPIWC5", 1, 0], [5, 134, 56.4], [0, 58.436, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "item", 4, [-97], [[41, false, "<color=#353839>攻击力</c><outline color=black width=3><color=#00ff00>+22</c></outline>", 32, 540, 34, -96]], [0, "d1YfTmYLNF8pQH7KL5gSFn", 1, 0], [5, 540, 42.84], [0, 0, 0.5], [-260.248, -41.42, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "count", 2, [6], [[5, 1, 0, -98, [38], 39]], [0, "e08J856BtKdpT6MT6hpSnr", 1, 0], [5, 640, 73], [0, -299, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 16, [[3, "装备", 36, false, false, 1, 1, 1, -99, [40]], [4, 3, -100, [4, 4278190080]]], [0, "205OogLHFCmbrfkgsBjSLM", 1, 0], [5, 78, 56.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 17, [[3, "强化", 36, false, false, 1, 1, 1, -101, [44]], [4, 3, -102, [4, 4278190080]]], [0, "34yXrfIyJOjq+ZX1MzWsMc", 1, 0], [5, 78, 56.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 18, [[3, "一键强化", 36, false, false, 1, 1, 1, -103, [47]], [4, 3, -104, [4, 4278190080]]], [0, "06jMkj+U9Ii5xJ9VBxnhmC", 1, 0], [5, 150, 56.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "流浪长剑", 42, false, false, 1, 1, 1, 23, [5]], [3, "普通", 25, false, false, 1, 1, 1, 25, [8]], [24, "Role_EquipItem", 3, [29, "1dk+fEprtP7KYT6W29RZTy", true, -105, 11], [-257.328, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_cp", 8, [[6, -106, [12], 13]], [0, "c7kz1YRilHqYOU/C/oSCr5", 1, 0], [5, 53, 51], [-120.492, 5.817, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "0", 32, false, false, 1, 1, 1, 26, [14]], [3, "1/10", 30, false, false, 1, 1, 1, 27, [17]], [14, "name", 9, [[3, "等级", 26, false, false, 1, 1, 1, -107, [18]]], [0, "ecQKIQ4jpMu79VFK5ZdW13", 1, 0], [4, 4281940021], [5, 52, 50.4], [0, 0, 0.5], [-89.566, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "武器", 30, false, false, 1, 1, 1, 28, [21]], [14, "name", 10, [[3, "类型", 26, false, false, 1, 1, 1, -108, [22]]], [0, "2bX9cqb31Az7XPzxsD1trQ", 1, 0], [4, 4281940021], [5, 52, 50.4], [0, 0, 0.5], [-89.566, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 12, [27]], [26, "val", 13, [-109], [0, "f5P9LwzI1Az7e5vug/z5q9", 1, 0], [5, 156.28, 63.00000000000003], [0, 0, 1], [-296.222, 28.9, 0, 0, 0, 0, 1, 1, 1, 1]], [42, false, "<color=#353839>攻击力</c><outline color=black width=3><color=#00ff00>+22</c></outline>", 32, 50.00000000000002, 45], [2, "icon", 30, [[6, -110, [31], 32]], [0, "f9lsuCEGVMLYxIztwrbTQF", 1, 0], [5, 47, 56], [-25.01, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [2, "icon", 6, [[6, -111, [36], 37]], [0, "ee/r14B5dPv7trPo73FSOI", 1, 0], [5, 76, 64], [-46.035, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "val", 6, [[43, false, "<outline color=black width=3><color=#00ff00>22</c>/10</outline>", 1, 32, -112]], [0, "97keLqMqdBOonqEytXRcmM", 1, 0], [5, 92.07, 50.4], [40.400000000000006, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 6, 44, 0, 7, 4, 0, 8, 5, 0, 9, 6, 0, 10, 46, 0, -1, 39, 0, -2, 40, 0, -3, 42, 0, -4, 36, 0, -5, 35, 0, -1, 11, 0, -2, 12, 0, 0, 1, 0, -1, 19, 0, -2, 20, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 3, 0, -3, 13, 0, -4, 14, 0, -5, 31, 0, -6, 5, 0, -1, 37, 0, -2, 8, 0, -3, 9, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, 0, 4, 0, 0, 4, 0, -1, 30, 0, 0, 5, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, 0, 6, 0, -1, 48, 0, -2, 49, 0, -1, 21, 0, -2, 22, 0, -3, 24, 0, 0, 8, 0, -1, 38, 0, -2, 26, 0, 0, 9, 0, -1, 27, 0, -2, 41, 0, 0, 10, 0, -1, 28, 0, -2, 43, 0, 0, 11, 0, 0, 11, 0, -1, 44, 0, 0, 12, 0, 0, 13, 0, -1, 29, 0, -2, 45, 0, 0, 14, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 32, 0, 0, 17, 0, 0, 17, 0, -1, 33, 0, 0, 18, 0, 0, 18, 0, -1, 34, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -1, 23, 0, -1, 35, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, -1, 36, 0, 0, 25, 0, -1, 39, 0, 0, 26, 0, -1, 40, 0, 0, 27, 0, -1, 42, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, -1, 47, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 5, 37, 0, 0, 38, 0, 0, 41, 0, 0, 43, 0, -1, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 11, 1, 2, 4, 20, 4, 4, 15, 6, 4, 31, 112], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44], [-1, 1, 3, -1, 1, -1, -1, 1, -1, -1, 1, 12, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, -1, 3, -1, -1, 1, 1], [0, 6, 1, 0, 7, 0, 0, 8, 0, 0, 9, 10, 0, 11, 0, 0, 12, 0, 0, 0, 2, 0, 0, 0, 2, 0, 13, 0, 0, 0, 14, 0, 15, 0, 0, 3, 0, 16, 0, 3, 0, 1, 0, 17, 0, 1, 0, 0, 1, 0, 0, 18, 19]], [[{"name": "icon_cp", "rect": [0, 0, 53, 51], "offset": [0, 0], "originalSize": [53, 51], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [20]], [[{"name": "icon_chongzhi", "rect": [0, 0, 46, 46], "offset": [0, 0], "originalSize": [46, 46], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [21]], [[{"name": "icon_jiesuo", "rect": [0, 0, 46, 46], "offset": [0, 0], "originalSize": [46, 46], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [22]], [[{"name": "img_tch", "rect": [0, 0, 181, 127], "offset": [0, 0], "originalSize": [181, 127], "capInsets": [61, 87, 106, 31]}], [1], 0, [0], [2], [23]], [[{"name": "img_js_btqz1", "rect": [0, 0, 472, 103], "offset": [0, 0], "originalSize": [472, 103], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [24]]]]