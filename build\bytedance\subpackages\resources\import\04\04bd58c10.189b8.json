[1, ["ecpdLyjvZBwrvm+cedCcQy", "87wjnW6VxDhr7KoX1pDK+q", "1a6aecbfb"], ["node", "root", "data", "_spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize"], 2, 9, 4, 5], ["cc.Sprite", ["_sizeMode", "_fillType", "_fillRange", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["9d788GOLNtIE6uIjHxLzG7S", ["node"], 3, 1], ["cc.PrefabInfo", ["root", "asset"], 3, 1, 1]], [[1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1], [5, 0, 1, 1]], [[[[0, "Effect_Herald"], [1, "Effect_Herald", [[2, 0, 2, 0.2, -2, [0], 1], [3, -3]], [4, -1, 0], [5, 120, 80]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 3], [0, 0], [-1, 3], [0, 1]], [[{"name": "yujing_15", "rect": [134, 557, 107, 70], "offset": [-1.5, -4], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [2]]]]