[1, ["7a/QZLET9IDreTiBfRn2PD", "1a6aecbfb", "ecpdLyjvZBwrvm+cedCcQy", "4c0txFnuhA37kKFqfmJvDL"], ["node", "_textureSetter", "root", "data", "_spriteFrame"], [["cc.Node", ["_name", "_opacity", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_bottom", "_top", "node"], 0, 1], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "premultipliedAlpha", "node", "_materials"], -2, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["36ac1Nmq0RDA4Cdq1A+7+KF", ["clipImgName", "clipImgRef", "frame_time", "wrapMode", "node"], -1, 1], ["cc.Sprite", ["_dstBlendFactor", "_type", "_sizeMode", "_fillType", "_fillRange", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -3, 1, 3, 6], ["9d788GOLNtIE6uIjHxLzG7S", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [5, 0, 2], [0, 0, 1, 8, 3, 4, 5, 9, 3], [0, 0, 2, 6, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 5, 7, 2], [6, 0, 1, 2, 3, 4, 5], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [8, 0, 1], [1, 1, 2, 1], [2, 0, 1, 3, 3], [2, 0, 2, 3, 3], [3, 0, 1, 2, 3, 5, 6, 5], [3, 0, 1, 2, 4, 3, 5, 6, 6]], [[[{"name": "lightning01", "rect": [134, 375, 176, 136], "offset": [0, 0], "originalSize": [176, 136], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [1], [1]], [[[1, "Effect_1040"], [2, "Effect_1040", 218, [-5, -6], [[5, "img/fight/effect/lightning0", "1-8", 33.2, 2, -2], [6, 772, 2, 0, 2, 0.2, false, -3, [2], 3], [7, -4]], [8, -1, 0], [5, 173, 136], [0, 0.5, 0]], [3, "fx_spiralknife_star", false, 1, [[9, 4, -46, -7], [11, "default", "fx", 0, "fx", -8, [0]]], [0, "8cpqfabehHsou9Nd3R/dGk", 1, 0], [5, 92, 92], [0, -9.199999999999996, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [4, "fx_lightning", 1, [[10, 1, -26.475003051757824, -9], [12, "default", "fx", 0, false, "fx", -10, [1]]], [0, "1cLXIWiFRLk4OGsb3QbeAQ", 1, 0], [5, 87.99999237060547, 88.00001525878906], [0, 144.875, 0, 0, 0, 0, 1, 0.4, 0.4, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 3, 1, 10], [0, 0, 0, 0], [-1, -1, -1, 4], [0, 0, 2, 3]]]]