[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "f3p84uzd5EVLXvLuhlyoRY", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr", "29R34cGbFB7YQJIiPE06Fl", "22cjIamqZH/Lbdvp80pFLv"], ["node", "_spriteFrame", "_N$disabledSprite", "_parent", "_N$skeletonData", "root", "data"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], -1, 9, 4, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_N$overflow", "_enabled", "_lineHeight", "node", "_materials"], -7, 1, 3], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 2, 4, 5, 5, 7], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 1, 1, 5], ["3e17frZZvBH/pfFcp7VIscz", ["node", "nodeArr", "labelArr", "imgArr"], 3, 1, 12, 12, 2]], [[5, 0, 1, 2, 2], [11, 0, 1, 2, 2], [0, 0, 7, 4, 5, 6, 9, 2], [2, 0, 1, 2, 3, 4, 7, 5, 10, 11, 8], [6, 0, 1, 2, 3, 4], [3, 0, 2, 3, 4, 5, 6, 2], [1, 1, 0, 3, 4, 3], [2, 0, 6, 1, 2, 3, 4, 7, 5, 10, 11, 9], [7, 0, 1, 2, 3, 4, 5, 2], [0, 0, 7, 8, 4, 5, 6, 2], [0, 0, 1, 7, 8, 4, 5, 6, 9, 3], [0, 0, 7, 8, 4, 5, 6, 9, 2], [4, 0, 1, 2, 3, 4], [1, 0, 3, 4, 5, 2], [8, 0, 2], [0, 0, 2, 8, 4, 5, 6, 3], [0, 0, 7, 4, 5, 6, 2], [0, 0, 3, 7, 4, 5, 10, 6, 3], [0, 0, 1, 7, 4, 5, 6, 9, 3], [0, 0, 8, 4, 5, 6, 9, 2], [0, 0, 1, 8, 4, 5, 6, 9, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 2], [3, 0, 1, 2, 3, 4, 5, 6, 3], [4, 0, 3, 2], [5, 1, 2, 1], [1, 1, 3, 4, 2], [1, 3, 4, 5, 1], [1, 1, 0, 3, 4, 5, 3], [1, 2, 1, 0, 3, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 6, 5], [2, 8, 0, 6, 9, 1, 2, 3, 4, 5, 10, 11, 10], [2, 0, 6, 1, 2, 3, 4, 5, 10, 11, 8], [12, 0, 1, 2, 2], [6, 0, 1, 3, 3], [7, 1, 2, 1], [13, 0, 1], [14, 0, 1, 2, 3, 3], [15, 0, 1, 2, 3, 1]], [[14, "M38_Pop_GameEnd"], [15, "M38_Pop_GameEnd", 1, [-11, -12], [[37, -10, [[null, -7, -8, null, -9], 0, 1, 1, 0, 1], [[-3, null, null, -4, -5, -6], 1, 0, 0, 1, 1, 1], [-2]]], [24, -1, 0], [5, 750, 1334]], [20, "btn_adunlock", false, [-15, -16, -17, -18], [[6, 1, 0, -13, [11]], [32, "ModeUnlock", -14, [[33, "3e17frZZvBH/pfFcp7VIscz", "onVideoUnlock", 1]]]], [0, "2cvD77zsxI5aAz7i1O1NGH", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "New Layout", [2, -20, -21, -22], [[36, 2, 20, -19, [5, 536, 100]]], [0, "af0PhPKIRLu7si1FMxkRrR", 1, 0], [5, 536, 100], [0, -172.378, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 1, [-24, -25, -26, -27], [[12, 45, 750, 1334, -23]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [10, "btn_next", false, 3, [-30], [[8, 3, -28, [[4, "3e17frZZvBH/pfFcp7VIscz", "onBtn", "NextLevel", 1]], [4, 4293322470], [4, 3363338360], 13], [6, 1, 0, -29, [14]]], [0, "72ILAahsFECZuKla08Qe/C", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "btn_playagain", 3, [-33, -34], [[8, 3, -31, [[4, "3e17frZZvBH/pfFcp7VIscz", "onBtn", "Replay", 1]], [4, 4293322470], [4, 3363338360], 18], [27, 1, 0, -32, [19], 20]], [0, "13YRsAbzpBU7NRucCRvu3D", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "tryPlayTips", false, 3, [-37, -38], [[8, 3, -35, [[4, "3e17frZZvBH/pfFcp7VIscz", "onBtn", "BackToMain", 1]], [4, 4293322470], [4, 3363338360], 23], [6, 1, 0, -36, [24]]], [0, "71695rGRNKdJpFf6anJ/Vo", 1, 0], [5, 248, 100], [0, -120, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "content", 4, [-40, 3], [[28, false, 1, 0, -39, [25], 26]], [0, "cfPCIfUtVMApgW4f3jCFGW", 1, 0], [5, 540, 600]], [17, "mask", 140, 4, [[13, 0, -41, [0], 1], [12, 45, 100, 100, -42]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [21, "title_zhua<PERSON><PERSON>", 8, [-44], [-43], [0, "7a6P9Sp61LoLoXSyszH2uD", 1, 0], [5, 577, 247], [0, 0.5, 0], [0, 164.049, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "Label", false, 10, [[-45, [1, 6, -46, [4, 4278190080]]], 1, 4], [0, "06nHHxbKBK8KhcgpLJvlt0", 1, 0], [5, 332, 125.4], [0, 77.721, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 2, [[7, "提前解锁", 24, false, false, 1, 1, 2, 1, -47, [8]], [1, 3, -48, [4, 3573547008]]], [0, "90ZE0oH1NBnrp+oDkUlj8l", 1, 0], [5, 136, 62], [34.75, -14.885, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "tips", 2, [[-49, [1, 3, -50, [4, 3573547008]]], 1, 4], [0, "6eH1GRdEdBG7E20+CVvru0", 1, 0], [5, 274, 62], [-5.791, -78.888, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "adcount", 2, [[-51, [1, 3, -52, [4, 3573547008]]], 1, 4], [0, "efSk83kCZJbZnNOCr4NxzB", 1, 0], [5, 136, 62], [34.75, 23.965, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 5, [[3, "下一关", false, false, 1, 1, 2, 1, -53, [12]], [1, 3, -54, [4, 3573547008]]], [0, "d8JigGxTFNLqWxREl01EQ5", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_ads", 6, [[13, 0, -55, [15], 16], [35, -56]], [0, "59MsfeRb1N+Ic+4fq0HVQV", 1, 0], [5, 45, 35], [-70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 6, [[3, "再次挑战", false, false, 1, 1, 2, 1, -57, [17]], [1, 3, -58, [4, 3573547008]]], [0, "70mth7lkpD/YaaJt+TupoO", 1, 0], [5, 136, 62], [25, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "tips", 7, [[-59, [1, 3, -60, [4, 3573547008]]], 1, 4], [0, "21IV6GUvZAgbGYrKtH5Td1", 1, 0], [5, 375.56, 56.4], [0, -74.018, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 7, [[3, "开始主线", false, false, 1, 1, 2, 1, -61, [22]], [1, 3, -62, [4, 3573547008]]], [0, "67xswwqsFEwobHRMRKAIsS", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btn_close", 4, [-64], [[34, -63, [[4, "3e17frZZvBH/pfFcp7VIscz", "onBtn", "BackToMain", 1]]]], [0, "06V3Drg69L5LT4opWReggT", 1, 0], [5, 248, 100], [0, -364.376, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "maskbg", 1, [[23, 45, -65]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [18, "effect_ribbons", false, 4, [[29, "default", "animation", 0, "animation", -66, [2], 3]], [0, "c60DQJ3S5BYKleDWdDZmFO", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 646.266, 0, 0, 0, 0, 1, 1, 1, 1]], [30, false, "游戏失败", 80, 90, false, false, 1, 1, 1, 11, [4]], [25, 1, 10, [5]], [2, "icon_ads", 2, [[26, -67, [6], 7]], [0, "23CmJ2i05BPL6t8VIOYwdq", 1, 0], [5, 54, 43], [-71.43, 4.466, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "主线通关第%d关可解锁下一关", 24, false, false, 1, 1, 2, 1, 13, [9]], [7, "0/5", 32, false, false, 1, 1, 2, 1, 14, [10]], [31, "通关主线第2关后可以继续", 32, false, false, 1, 1, 1, 18, [21]], [2, "Label", 20, [[3, "点击关闭", false, false, 1, 1, 2, 1, -68, [27]]], [0, "18NirINrJDaLJ1yKda1SWR", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, -1, 24, 0, -1, 23, 0, -4, 26, 0, -5, 28, 0, -6, 27, 0, -2, 2, 0, -3, 5, 0, -5, 3, 0, 0, 1, 0, -1, 21, 0, -2, 4, 0, 0, 2, 0, 0, 2, 0, -1, 25, 0, -2, 12, 0, -3, 13, 0, -4, 14, 0, 0, 3, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, 0, 4, 0, -1, 9, 0, -2, 22, 0, -3, 8, 0, -4, 20, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, 0, 6, 0, 0, 6, 0, -1, 16, 0, -2, 17, 0, 0, 7, 0, 0, 7, 0, -1, 18, 0, -2, 19, 0, 0, 8, 0, -1, 10, 0, 0, 9, 0, 0, 9, 0, -1, 24, 0, -1, 11, 0, -1, 23, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 26, 0, 0, 13, 0, -1, 27, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 28, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -1, 29, 0, 0, 21, 0, 0, 22, 0, 0, 25, 0, 0, 29, 0, 6, 1, 2, 3, 3, 3, 3, 8, 68], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 4, -1, -1, -1, 1, -1, -1, -1, -1, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, -1, 2, -1, -1, 1, -1], [0, 3, 4, 5, 0, 0, 0, 2, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 1, 0, 6, 0, 0, 1, 0, 0, 7, 0]]