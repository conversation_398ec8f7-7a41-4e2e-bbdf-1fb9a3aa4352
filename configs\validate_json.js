import fs from 'fs';

function validateJsonFile(filePath) {
    try {
        console.log(`Validating ${filePath}...`);
        
        // 读取文件
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 尝试解析JSON
        const data = JSON.parse(content);
        
        // 获取一些统计信息
        const keys = Object.keys(data);
        console.log(`✅ JSON is valid`);
        console.log(`📊 Contains ${keys.length} entries`);
        
        // 显示第一个条目作为示例
        if (keys.length > 0) {
            const firstKey = keys[0];
            const firstEntry = data[firstKey];
            console.log(`📝 First entry (ID: ${firstKey}):`);
            console.log(`   Name: ${firstEntry.name || 'N/A'}`);
            console.log(`   Description: ${firstEntry.desc ? firstEntry.desc.substring(0, 50) + '...' : 'N/A'}`);
        }
        
        // 检查中文字符
        const chineseMatches = content.match(/[\u4e00-\u9fff]/g);
        if (chineseMatches) {
            console.log(`🈶 Contains ${chineseMatches.length} Chinese characters`);
        }
        
        console.log('---');
        return true;
    } catch (error) {
        console.log(`❌ JSON validation failed: ${error.message}`);
        console.log('---');
        return false;
    }
}

// 验证几个关键的JSON文件
const filesToValidate = [
    './json/Buff.json',
    './json/BagGuide.json',
    './json/DropSkill.json',
    './json/randomName.json'
];

console.log('Validating JSON files...\n');

let allValid = true;
for (const file of filesToValidate) {
    if (fs.existsSync(file)) {
        const isValid = validateJsonFile(file);
        allValid = allValid && isValid;
    } else {
        console.log(`⚠️  File not found: ${file}`);
        console.log('---');
    }
}

console.log(`\n${allValid ? '✅ All JSON files are valid!' : '❌ Some JSON files have issues.'}`);
