[1, ["ecpdLyjvZBwrvm+cedCcQy", "89YqH0KIZJ26WKtSNfrXcv", "70F/PynC1O5KjZzbBQaXb6"], ["node", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 1, 9, 4, 5, 2, 1, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_offset", "_size"], 3, 1, 5, 5], ["cc.RigidBody", ["_type", "node"], 2, 1], ["cc.PhysicsBoxCollider", ["_friction", "node", "_offset", "_size"], 2, 1, 5, 5], ["54b7bzLQztBJIK+IWxIEjMQ", ["moveDis", "node"], 2, 1]], [[1, 0, 1, 2, 2], [0, 0, 6, 2, 3, 4, 7, 2], [2, 1, 0, 2, 3, 4, 3], [8, 0, 1, 2, 3, 2], [3, 0, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 1, 6, 5, 2, 3, 4, 7, 3], [4, 0, 1, 2, 3, 4, 5, 6, 6], [5, 0, 1, 2, 2], [1, 1, 2, 1], [2, 0, 2, 3, 4, 2], [6, 0, 1, 2, 1], [7, 0, 1, 2], [9, 0, 1, 2]], [[4, "M31_MoveWall"], [5, "M31_MoveWall", 8, [-6, -7, -8], [[12, 0, -2], [3, 0, -3, [0, -125, 0], [5, 50, 120]], [3, 0, -4, [0, 125, 0], [5, 50, 120]], [13, 100, -5]], [9, -1, 0], [5, 300, 120]], [6, "2", 5, 1, [-11], [[10, 0, -9, [1], 2], [11, -10, [0, 0, 45], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 1, 0], [5, 200, 80], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [1, "label_multiple", 2, [[7, "x2", 50, false, 1, 1, -12, [0]], [8, 3, -13, [4, 4278190080]]], [0, "06/2jfsSBICI4jM9YCpnfz", 1, 0], [5, 58.81, 56.4], [2.905, 2.905, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wall", 1, [[2, 1, 0, -14, [3], 4]], [0, "24c+KvVkdL4bvegRYe3/1M", 1, 0], [5, 50, 120], [-125, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wall", 1, [[2, 1, 0, -15, [5], 6]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 1, 0], [5, 50, 120], [125, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, -3, 5, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 3, 1, 15], [0, 0, 0, 0, 0, 0, 0], [-1, -1, 1, -1, 1, -1, 1], [0, 0, 2, 0, 1, 0, 1]]