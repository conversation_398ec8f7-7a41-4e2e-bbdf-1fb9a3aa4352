[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "d4B+DYbXxEwJSqC2bYujr3", "ac6GfVFzFPop8lMcHyWBk/"], ["node", "_parent", "_spriteFrame", "root", "data", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_children", "_trs", "_parent", "_anchorPoint", "_color"], 0, 4, 5, 9, 2, 7, 1, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_left", "_right", "_originalHeight", "node"], -3, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["dcf5bi2vp9JwoOpCOJ5mG2k", ["node", "nodeArr"], 3, 1, 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingTop", "_N$paddingBottom", "node", "_layoutSize"], -1, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -1, 1]], [[1, 0, 1, 2, 2], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 2, 1, 8, 5, 3, 10, 4, 4], [2, 0, 1, 5, 6, 4], [5, 0, 2], [0, 0, 1, 6, 5, 3, 4, 9, 7, 3], [0, 0, 1, 8, 6, 5, 3, 4, 7, 3], [0, 0, 1, 6, 5, 3, 4, 7, 3], [0, 0, 1, 8, 6, 3, 4, 3], [0, 0, 1, 8, 5, 3, 4, 9, 7, 3], [6, 0, 1, 1], [1, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 5], [2, 2, 0, 3, 4, 1, 6, 6], [8, 0, 1, 2, 2], [9, 0, 1, 1], [10, 0, 1, 2, 3], [11, 0, 1], [3, 0, 2, 3, 4, 2], [3, 1, 0, 2, 3, 4, 3], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 3, 4, 5]], [[[[4, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [1, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 1, [-4, -5], [[10, -3, [-2]]], [11, -1, 0], [5, 750, 1334]], [5, "content", 1, [-8], [[12, 1, 2, 10, 40, -6, [5, 480, 120]], [13, 0, 41, 10, 10, 220, -7]], [0, "f1GrHr+XtFoLN7J+JJbEP1", 1, 0], [5, 480, 120], [0, 0.5, 1], [0, 380, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "view", 1, [2], [[14, 0, -9, [4]], [3, 45, 240, 250, -10], [15, -11, [[16, "74be8AqTt1El7XQ5AoSEjaI", "close", 1]]]], [0, "0bIwaMh4hI67j18bfarQzz", 1, 0], [5, 500, 760]], [6, "item", 1, 2, [-13, -14], [[17, -12]], [0, "c72LB6nmpDL6FlU7+27L6b", 1, 0], [5, 500, 70], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "maskbg", 170, 1, 1, [[18, 0, -15, [0], 1], [3, 45, 750, 1334, -16]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4281542699], [5, 750, 1334]], [7, "ScrollView", 1, [3], [[20, false, 0.75, 0.23, null, null, -17, 2]], [0, "d2XJRJdzpKBYy/3lCF2ZBl", 1, 0], [5, 500, 760], [0, -19.739, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 1, 1, [6], [0, "3aQ5JCUrlBML/7PcXsKi9d", 1, 0], [5, 750, 1334]], [2, "gg_wzdb_ds", 100, 1, 4, [[19, 1, 0, -18, [2], 3]], [0, "63hegKdqJJZINBqHWHSWMU", 1, 0], [4, 4278190080], [5, 500, 60]], [9, "text", 1, 4, [[21, false, 26, 460, 28, -19]], [0, "6bYWeGlv1J44vBYXVZJBOB", 1, 0], [5, 460, 35.28], [0, 0, 0.5], [-228.242, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, -1, 2, 0, 0, 1, 0, -1, 5, 0, -2, 7, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -1, 8, 0, -2, 9, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 8, 0, 0, 9, 0, 4, 1, 2, 1, 3, 3, 1, 6, 6, 1, 7, 19], [0, 0, 0, 0, 0], [-1, 2, -1, 2, -1], [0, 1, 0, 2, 0]], [[{"name": "gg_wzdb_ds", "rect": [0, 0, 20, 20], "offset": [0, 0], "originalSize": [20, 20], "capInsets": [7, 9, 8, 7]}], [4], 0, [0], [5], [3]]]]