[1, ["ecpdLyjvZBwrvm+cedCcQy", "e2lW50791KQaJY7ekc0TYs", "21uRv626FNqZzk4HcG2TVx", "81WuzqDTVGz5tqEnPIL9aY", "7a/QZLET9IDreTiBfRn2PD", "cb1VFt7lZM5bK3HPiU1WUY", "b1GZGSsftGg6dNJAM9m6pt", "f8npR6F8ZIZoCp3cKXjJQz", "5aS2OC53NM+p6lDMBeCes/", "f7293wEF9JhIuMEsrLuqng", "b9yrW21/tFWrnj0pMJeUHe"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "root", "lv", "starBar", "<PERSON><PERSON><PERSON>", "bg", "ske", "data"], [["cc.Sprite", ["_sizeMode", "_type", "_fillRange", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_prefab", "_children", "_parent", "_components", "_contentSize", "_trs"], 1, 4, 2, 1, 12, 5, 7], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 1, 1, 9, 4, 5, 7, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["d53e6+Z/UxPX6pvwhTm9U8P", ["node", "ske", "bg", "<PERSON><PERSON><PERSON>", "starBar", "lv"], 3, 1, 1, 1, 1, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "toValue", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["bbcc410J6hMNqmnBqUklFiE", ["path", "node"], 2, 1]], [[4, 0, 1, 2, 2], [2, 0, 2, 3, 4, 5, 6, 2], [1, 0, 4, 5, 2, 6, 7, 2], [2, 0, 2, 3, 4, 5, 2], [11, 0, 1, 2, 2], [0, 3, 4, 5, 1], [6, 0, 2], [1, 0, 3, 5, 2, 6, 2], [1, 0, 4, 3, 5, 2, 6, 7, 2], [1, 0, 1, 4, 3, 2, 3], [1, 0, 1, 4, 3, 2, 7, 3], [2, 0, 1, 2, 3, 4, 7, 5, 3], [7, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 5, 1], [4, 1, 2, 1], [9, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3, 4], [5, 0, 1, 2, 3, 4, 6, 7, 8, 6], [5, 0, 1, 2, 5, 3, 4, 6, 7, 7], [0, 1, 0, 3, 4, 5, 3], [0, 3, 4, 1], [0, 2, 3, 4, 2], [0, 0, 3, 4, 5, 2], [0, 0, 3, 4, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2]], [[[{"name": "img_jsd01", "rect": [0, 0, 281, 283], "offset": [0, 0], "originalSize": [281, 283], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [2]], [[[6, "Role_Item"], [7, "Role_Item", [-9, -10, -11, -12, -13, -14, -15], [[-2, [13, -8, -7, -6, -5, -4, -3]], 1, 4], [14, -1, 0], [5, 154, 230]], [8, "starBar", 1, [-18], [[[15, 1, 1, -16, [5, 35, 50]], -17], 4, 1], [0, "83iqarOb9BEYjoTC0+lnG5", 1, 0], [5, 35, 50], [0, -79.725, 0, 0, 0, 0, 1, 0.66, 0.66, 1]], [2, "<PERSON><PERSON><PERSON>", 1, [[-19, [16, 6, 1, [10], -20]], 1, 4], [0, "ac4akw1lRMXaLE37Kv8Te+", 1, 0], [5, 281, 283], [0, 28, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "lv", 1, [[-21, [4, 2, -22, [4, 4278190080]]], 1, 4], [0, "e5l9o+X1hPgKvjxg8U15gN", 1, 0], [5, 49.53, 54.4], [0, 87.849, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lock", false, 1, [-23, -24], [0, "810eihiZ9MxKQuCXY5zwNt", 1, 0]], [10, "isFitOut", false, 1, [-25, -26], [0, "0fGK4L2h9LGrdop1VarVlL", 1, 0], [6.678, -123.665, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lv", 6, [[17, "已上阵", 20, false, 1, 1, -27, [12], 13], [4, 2, -28, [4, 4278190080]]], [0, "c1jmQR7KRCjpb6Q7zfHzjd", 1, 0], [5, 64, 54.4]], [1, "isSelect", 1, [[19, 1, 0, -29, [0], 1]], [0, "d0cB+mABpHz5WWMW9PifB0", 1, 0], [5, 151.4, 227.9], [-0.2, 3.4, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 3, [2]], [12, "ske", 1, [-30], [0, "e1ZINHqGdAZIN8FDrF+nba", 1, 0], [5, 352.03460693359375, 361.4834899902344], [0, -44.675, 0, 0, 0, 0, 1, 0.4, 0.4, 0.9]], [24, 1, "idle", 0, false, "idle", 10, [3]], [18, "等级2", 18, false, 1, 1, 1, 4, [4]], [3, "icon_xxh", 2, [[21, 5.551115123125783e-17, -31, [5]]], [0, "42NL5EOc5KMY0SnjY9Mkww", 1, 0], [5, 35, 35]], [25, ["img/Pet/icon_xxhui", "img/Pet/icon_xxh"], 2], [11, "bg_option_01", 111, 5, [[22, 0, -32, [6], 7]], [0, "c7rJORsaFJEqIyzzS93gVK", 1, 0], [4, 4278190080], [5, 154, 230]], [1, "icon_lock_small", 5, [[5, -33, [8], 9]], [0, "628+fJeERI2peRoSYkhLoQ", 1, 0], [5, 34, 41], [63.226, 98.226, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "icon_rw_rhy", 6, [[5, -34, [10], 11]], [0, "55P2SbILpHWJTh5srTQgQ/", 1, 0], [5, 79, 92], [-41.907, -3.008, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [23, 0, 1, [14]]], 0, [0, 4, 1, 0, -1, 18, 0, 5, 12, 0, 6, 14, 0, 7, 9, 0, 8, 18, 0, 9, 11, 0, 0, 1, 0, -1, 8, 0, -2, 3, 0, -3, 10, 0, -4, 4, 0, -5, 2, 0, -6, 5, 0, -7, 6, 0, 0, 2, 0, -2, 14, 0, -1, 13, 0, -1, 9, 0, 0, 3, 0, -1, 12, 0, 0, 4, 0, -1, 15, 0, -2, 16, 0, -1, 17, 0, -2, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 11, 0, 0, 13, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 10, 1, 34], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 12, 18], [-1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, 3, 1], [0, 3, 0, 4, 0, 0, 0, 1, 0, 5, 0, 6, 0, 7, 0, 8, 9, 1]], [[{"name": "img_jndk01", "rect": [3, 4, 166, 213], "offset": [1, -2], "originalSize": [170, 217], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [10]]]]