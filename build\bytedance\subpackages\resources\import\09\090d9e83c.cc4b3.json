[1, ["ecpdLyjvZBwrvm+cedCcQy", "20M8pJf8ZMi5Ace1AQ1cbJ", "7agrH5SjtK6rYdzIhytT0y", "16HLuML9JK+au0PImsvlVj", "fdBrPosC9JubBzByQ+hCMe", "064230/nZCrJ88Nmyy/rnO", "aaykaTHB5NNrUWT8SIjQaa", "88fe2a7YdBja2bwtv64uH+", "0btlhzWrRIYr34sA95LRp1", "6diOSOMXRDTZQoDTfQeEVD", "5elRwD0DdODol54diH9QGg", "4fSCCNhqlEpooZJ4Sq74nQ", "b27CHPk5lCna5xZLwaDYdZ", "847uA966ZMM4xY2ASONO3n", "a2MjXRFdtLlYQ5ouAFv/+R", "42YUPbnM9HG4/lgz1lZ8GZ", "a8lPnHTihBmrRgij+r2Olk", "3dMFMKP41EqLsfqo0y6Gxh", "44eMEJM4JEPoGuKxAvVF6K", "b2N+g3ZtBDBKpgMP6MCMG7", "8fK8yT2/BOQr6UyMn6jfLR", "6abN51mdJBnKuBv1cxTTs/", "b8v4BMuvlEVJo2UUhlDVX3"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "_N$target", "data"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], -1, 9, 4, 5, 1, 2, 7, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["5577b1UkIxK4aPtCpYc7jAP", ["node", "nodeArr", "lbArr"], 3, 1, 2, 2], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$target"], 2, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["_enabled", "node"], 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[4, 0, 1, 2, 2], [2, 0, 2, 3, 4, 2], [0, 0, 7, 4, 5, 6, 9, 2], [0, 0, 7, 4, 5, 6, 2], [2, 2, 3, 4, 1], [0, 0, 7, 8, 4, 5, 6, 9, 2], [0, 0, 8, 4, 5, 6, 2], [0, 0, 7, 4, 5, 10, 6, 9, 2], [5, 1, 0, 2, 3, 4, 4], [12, 0, 1, 2, 2], [3, 0, 4, 1, 2, 3, 6, 7, 6], [6, 0, 2], [0, 0, 2, 8, 4, 5, 6, 9, 3], [0, 0, 1, 7, 8, 4, 5, 6, 3], [0, 0, 7, 8, 4, 5, 6, 2], [0, 0, 3, 7, 4, 5, 10, 6, 3], [0, 0, 1, 7, 4, 5, 6, 9, 3], [7, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 1], [4, 1, 2, 1], [5, 0, 3, 4, 2], [2, 1, 0, 2, 3, 4, 3], [9, 0, 1, 2, 3, 2], [10, 0, 1, 2, 3], [11, 0, 1, 2], [3, 0, 4, 5, 1, 2, 3, 6, 7, 7], [3, 0, 5, 1, 2, 3, 6, 7, 6]], [[[{"name": "img_tzxld", "rect": [0, 0, 410, 192], "offset": [0, 0], "originalSize": [410, 192], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [1]], [[[11, "FundTaskItem"], [12, "FundTaskItem", 1, [-14, -15, -16, -17, -18, -19, -20, -21], [[18, -13, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12], [-2]]], [19, -1, 0], [5, 1125, 192], [0, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New Node", false, 1, [-23, -24, -25, -26], [[20, 1, -22, [5, 1125, 192]]], [0, "7drwqb5YJGF63FDWT96tAk", 1, 0], [5, 1125, 192]], [5, "btn_home_yellow", 1, [-30, -31], [[21, 1, 0, -27, [34], 35], [22, 3, -29, [[23, "5577b1UkIxK4aPtCpYc7jAP", "onClickItem", 1]], -28]], [0, "e20e2arb1APZ44NP5Ag8h5", 1, 0], [5, 146, 70], [0, 95.015, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "img_txz_huang", [-33, -34], [[4, -32, [5], 6]], [0, "4daSiR7Y1G54xloB0hZg1L", 1, 0], [5, 248, 115]], [6, "img_txz_huang", [-36, -37], [[4, -35, [12], 13]], [0, "21eSrQIAZPzqXykSQYf3P+", 1, 0], [5, 248, 114]], [5, "img_tzxhd", 2, [4, -39], [[1, 0, -38, [7], 8]], [0, "adlUW+1yhCaLuGqheikEPe", 1, 0], [5, 410, 192], [-205.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_tzxhd", 2, [5, -41], [[1, 0, -40, [14], 15]], [0, "669fqqO4VIb4V1na9ghNQd", 1, 0], [5, 410, 192], [204.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon_djd", 1, [-43], [[4, -42, [25], 26]], [0, "a9SGUi1dJAnK/5HaGJ8qqh", 1, 0], [5, 70, 76]], [15, "New Sprite(Splash)", 156, 1, [[1, 0, -44, [27], 28], [24, false, -45]], [0, "39khVMDw1Oe413Lsg+xUUA", 1, 0], [4, 4278190080], [5, 1125, 198]], [3, "New Node", 6, [[8, 1, 1, 20, -46, [5, 252, 200]]], [0, "664cYVIn1MPaBv5ngRpb78", 1, 0], [5, 252, 200]], [3, "New Node", 7, [[8, 1, 1, 20, -47, [5, 116, 200]]], [0, "5cd0+FiDxPT6XFVs68m0xl", 1, 0], [5, 116, 200]], [3, "img_jdt01", 1, [[1, 0, -48, [22], 23]], [0, "dadgfsSqxA2ZY/OAnSO6YC", 1, 0], [5, 26, 198]], [17, "New Label", 8, [[-49, [9, 3, -50, [4, 4279571992]]], 1, 4], [0, "0dO/nqTLRCAZJ0oc6JlNn6", 1, 0], [5, 28.25, 36.239999999999995], [0.907, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_jdfgx", 1, [[1, 0, -51, [29], 30]], [0, "betkG31VFKlah+bIP8Aq9M", 1, 0], [5, 1125, 19], [0, 93.737, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Label", 3, [[25, "购买等级", 25, 25, 1, 1, 1, -52, [33]], [9, 3, -53, [4, 4278190080]]], [0, "97NG6ozDhJ4oJwPpfAfFBP", 1, 0], [5, 106, 37.5]], [2, "img_tzxld2", 2, [[4, -54, [0], 1]], [0, "9b0L3v98dD/rJ++mRN85yx", 1, 0], [5, 152, 192], [-486.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_txz01", 4, [[1, 0, -55, [2], 3]], [0, "cchl4fECFNRLfdHWmOWp86", 1, 0], [5, 70, 52], [-44.974, 1.313, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Label", 4, [[10, "免费", 32, 1, 1, 1, -56, [4]]], [0, "95isqUEtZCNbSzyh27y61/", 1, 0], [4, 4283578161], [5, 64, 50.4], [34.072, 5.579, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_txz01", 5, [[1, 0, -57, [9], 10]], [0, "a3C3rn/kBEWotLI4zZVfzv", 1, 0], [5, 70, 52], [-47.763, 4.302, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Label", 5, [[10, "通行证", 32, 1, 1, 1, -58, [11]]], [0, "7cGgLAju9Bf4mSr9MuFwn9", 1, 0], [4, 4279115152], [5, 96, 50.4], [43.651, 4.184, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_tzxhd2", 2, [[4, -59, [16], 17]], [0, "02zRJ7sIFF1Y2SwCINOIVK", 1, 0], [5, 152, 192], [485.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_fgx2", 1, [[1, 0, -60, [18], 19]], [0, "6c24g6SH5NrbgHvBUDpA7R", 1, 0], [5, 1125, 7], [0, -96.296, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_jdt01", 1, [[1, 0, -61, [20], 21]], [0, "1fBIYoo+hKS6PlfgTYF+b3", 1, 0], [5, 26, 198]], [26, "1", 24, 1, 1, 1, 13, [24]], [16, "shuijing", false, 3, [[1, 0, -62, [31], 32]], [0, "f3gvBudbdDU6ePpRcoDRya", 1, 0], [5, 38, 35], [-33.176, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 24, 0, -1, 4, 0, -2, 10, 0, -3, 5, 0, -4, 11, 0, -5, 3, 0, -6, 12, 0, -7, 8, 0, -8, 14, 0, -9, 9, 0, -10, 2, 0, 0, 1, 0, -1, 2, 0, -2, 22, 0, -3, 23, 0, -4, 12, 0, -5, 8, 0, -6, 9, 0, -7, 14, 0, -8, 3, 0, 0, 2, 0, -1, 16, 0, -2, 6, 0, -3, 7, 0, -4, 21, 0, 0, 3, 0, 5, 3, 0, 0, 3, 0, -1, 25, 0, -2, 15, 0, 0, 4, 0, -1, 17, 0, -2, 18, 0, 0, 5, 0, -1, 19, 0, -2, 20, 0, 0, 6, 0, -2, 10, 0, 0, 7, 0, -2, 11, 0, 0, 8, 0, -1, 13, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, -1, 24, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 25, 0, 6, 1, 4, 3, 6, 5, 3, 7, 62], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1], [0, 2, 0, 3, 0, 0, 4, 0, 5, 0, 6, 0, 0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 0, 13, 0, 14, 0, 15, 0, 16, 0, 0, 17]], [[{"name": "btn_home_yellow", "rect": [0, 0, 82, 124], "offset": [0, 0], "originalSize": [82, 124], "capInsets": [38, 0, 38, 0]}], [1], 0, [0], [2], [18]], [[{"name": "img_tzxhd2", "rect": [0, 0, 152, 192], "offset": [0, 0], "originalSize": [152, 192], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [19]], [[{"name": "img_tzxld2", "rect": [0, 0, 152, 192], "offset": [0, 0], "originalSize": [152, 192], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [20]], [[{"name": "img_txz_huang", "rect": [0, 0, 248, 114], "offset": [0, 0.5], "originalSize": [248, 115], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [21]], [[{"name": "img_txz_lan", "rect": [0, 0, 248, 115], "offset": [0, 0], "originalSize": [248, 115], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [22]]]]