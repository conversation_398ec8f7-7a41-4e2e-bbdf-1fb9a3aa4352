[1, ["ecpdLyjvZBwrvm+cedCcQy", "07uTi5pw5INIAU5mtPfWfY", "aaykaTHB5NNrUWT8SIjQaa", "ebJXGx5cNJ+b1BpmgufQFT", "16HLuML9JK+au0PImsvlVj", "90IbluUqBClagaVvf27vV3", "52O3lFKIJF9LvJlXRhkBGB", "19dJambRxGSpW8A4TGPagI", "e4TMDxF3lEk7wOCbX7r9mN", "7b5pwt4BFGo5zbF5aU5B9U", "65YOAit9REIZhiX5FIJc7d", "24j0q6iMpKCYsRGD0VYmMw", "847uA966ZMM4xY2ASONO3n", "fccdMVhRZEaKQVy++fJ9MV", "dbNkclouNG/oj/cNi+uAJ8", "5b0HySO0xNLI16cb6FYAR2", "27RTpTPjZN9KOAJJnUPPk1", "3ae7efMv1CLq2ilvUY/tQi", "ffCXtneVZM4Zd1mMmw1u1v", "70ujmzFIlC0b99Xyu9gv5l", "729aZcYxhE/LsYnxYpNkSz"], ["node", "_spriteFrame", "_textureSetter", "checkMark", "_N$target", "_parent", "root", "my<PERSON>rid<PERSON>iew", "_N$content", "data", "_normalMaterial", "_grayMaterial", "templete"], [["cc.Node", ["_name", "_opacity", "_active", "_groupIndex", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "_top", "alignMode", "_left", "_right", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Label", ["_string", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "_materials"], -4, 1, 3], ["cc.Node", ["_name", "_active", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Toggle", ["zoomScale", "_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents", "_normalMaterial", "_grayMaterial"], 0, 1, 5, 1, 1, 9, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["22d49D+YmlDtpe7X2bZRn7I", ["node", "my<PERSON>rid<PERSON>iew"], 3, 1, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents", "_N$target"], 3, 1, 9, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["00798YhOSNDMq8AyX+/Wp5o", ["my<PERSON>ey", "node"], 2, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content"], 3, 1, 5, 1]], [[6, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 8, 2], [2, 0, 3, 4, 5, 2], [2, 1, 0, 3, 4, 5, 3], [2, 3, 4, 5, 1], [0, 0, 2, 7, 6, 4, 5, 8, 3], [16, 0, 1, 2, 2], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 11, 8, 2], [5, 0, 2, 3, 4, 5, 6, 7, 2], [8, 0, 1, 2, 3, 4], [1, 0, 8, 2], [1, 0, 6, 7, 4, 2, 3, 8, 7], [1, 0, 2, 3, 8, 4], [2, 1, 0, 2, 3, 4, 4], [4, 0, 4, 6, 1, 2, 3, 7, 8, 7], [17, 0, 1, 2], [9, 0, 2], [0, 0, 3, 9, 6, 4, 5, 3], [0, 0, 2, 9, 6, 4, 5, 8, 3], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 7, 9, 4, 5, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 1, 7, 6, 4, 5, 8, 3], [0, 0, 1, 7, 6, 4, 10, 5, 8, 3], [0, 0, 7, 6, 4, 10, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [10, 0, 1, 1], [6, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4], [7, 0, 1, 3, 4, 5, 6, 7, 3], [8, 0, 1, 3, 3], [1, 0, 6, 4, 1, 2, 3, 8, 7], [1, 0, 4, 1, 3, 8, 5], [1, 0, 6, 1, 8, 4], [1, 5, 0, 2, 8, 4], [1, 5, 0, 2, 3, 8, 5], [1, 5, 0, 1, 8, 4], [1, 5, 0, 4, 8, 4], [1, 0, 1, 8, 3], [1, 0, 4, 1, 2, 3, 8, 6], [11, 0, 1], [12, 0, 1, 2, 3, 3], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 1], [15, 0, 1, 2, 2], [4, 0, 4, 5, 1, 2, 3, 7, 8, 7], [4, 0, 5, 1, 2, 3, 7, 8, 6], [18, 0, 1, 2, 1]], [[[{"name": "bg_txz_hyd", "rect": [0, 0, 1125, 164], "offset": [0, 0], "originalSize": [1125, 164], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [6]], [[{"name": "6", "rect": [0, 0, 10, 10], "offset": [0, 0], "originalSize": [10, 10], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [7]], [[[18, "StartChallangeView"], [19, "StartChallangeView", 1, [-4, -5], [[29, -3, -2]], [30, -1, 0], [5, 750, 1334]], [20, "toggle1", false, [-9, -10, -11, -12, -13, -14], [[31, 1.1, 3, false, -8, [4, 4292269782], -7, -6, [[11, "ac839SWwQxIIby+3cQqqS1n", "onToggle", "2", 1]], 40, 41]], [0, "1aX5q+rAdFHrVISU2SR0yQ", 1, 0], [5, 137, 128], [-68.5, -7.448, 0, 0, 0, 0, 1, 1, 1, 0]], [21, "toggle0", [-18, -19, -20, -21], [[32, 1.1, 3, -17, [4, 4292269782], -16, -15, [[11, "ac839SWwQxIIby+3cQqqS1n", "onToggle", "1", 1]]]], [0, "a9x1m+8ctJBYmM4jOdFuAw", 1, 0], [5, 137, 128], [-68.5, -7, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "maskbg", 1, [-24, -25, -26], [[12, 45, -22], [2, 0, -23, [6], 7]], [0, "38BN9FY3ZGlpjA8xdiIyFc", 1, 0], [5, 750, 1334]], [22, "bg", 1, [-27, -28, -29, -30, -31], [0, "99b1Qdgu5GApqcw6DScSX1", 1, 0], [5, 748, 1332]], [9, "ToggleMenu", 5, [3, 2], [[43, -32], [34, 36, 277.03125, 636.5, 4.115000000000009, 354, 61, -33], [44, 1, 1, -34, [5, 137, 145]]], [0, "62/j1DwztK7IVC/3y3w9aI", 1, 0], [5, 137, 145], [0, 1, 0.5], [374, -589.385, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "top", 5, [-36, -37, -38], [[12, 41, -35]], [0, "5b+0in5M1AyLKBNmqiGxvf", 1, 0], [5, 748, 100], [0, 616, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "New ScrollView", 5, [-43], [[[45, false, 0.75, 0.23, null, null, -40, -39], -41, [35, 5, 164.55900000000003, 167.44099999999997, 1000, -42]], 4, 1, 4], [0, "07to0YFi1Bsp1AwxVHerqL", 1, 0], [5, 620, 1000], [0, 1.440999999999974, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon_jytb", 7, [-45, -46], [[3, 1, 0, -44, [16], 17]], [0, "a6D/qvD7hPrbhKHpjtu/ln", 1, 0], [5, 100, 76], [295.814, -18.547, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "checkmark", 3, [-49], [[-47, [13, 45, -1.5, -1.5, -18.5, 40, 40, -48]], 1, 4], [0, "f5CUzj9PdJpZsctaD6j54S", 1, 0], [5, 140, 146.5], [0, 9.25, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "checkmark", false, 2, [-52], [[-50, [13, 45, -1.5, -1.5, -18.5, 40, 40, -51]], 1, 4], [0, "6686jGMXdGrYcP9ZRyBIVh", 1, 0], [5, 140, 146.5], [0, 9.25, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button_return", 5, [[4, -53, [42], 43], [36, 4, 10.100000000000023, 18.326000000000022, -54], [46, -55, [[33, "22d49D+YmlDtpe7X2bZRn7I", "close", 1]], 1]], [0, "67LeqngJNNboxapxq8OFpN", 1, 0], [5, 94, 99], [-297.175, -598.174, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 8, [-58], [[47, 0, -56, [44]], [14, 45, 240, 250, -57]], [0, "cdIwU4lTJINKl6pzor6fX/", 1, 0], [5, 620, 1000], [0, 0.5, 1], [0, 500, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "content", 13, [[37, 0, 41, 614, -59]], [0, "b97x+4n7xP5bA61Ofje3ed", 1, 0], [5, 620, 274], [0, 0, 1], [-310, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "img_long", 10, 4, [[3, 2, 0, -60, [0], 1], [38, 0, 45, 750, 1624, -61]], [0, "32Xd2RHtxDva9sgs7I6QB/", 1, 0], [4, 4278190080], [5, 750, 1334]], [25, "img_dian", 79, 4, [[3, 2, 0, -62, [2], 3], [39, 0, 4, 2, -63]], [0, "faF4gGntNInIxyJHP+CVr6", 1, 0], [5, 750, 320], [0, -505, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "img_dian", 30, 4, [[3, 2, 0, -64, [4], 5], [40, 0, 1, 2, -65]], [0, "a5Zs0NK+1LP68FnD2aPwTc", 1, 0], [4, 4278190080], [5, 750, 320], [0, 505, 0, 0, 0, 0, 1, 1, -1, 1]], [8, "img_sjd", 7, [-67], [[3, 1, 0, -66, [11], 12]], [0, "00zDNY+7tJk6FTshudLkk6", 1, 0], [5, 280, 62], [-227.511, -20.664, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "New Label", 18, [[48, "第一赛季", 32, 31, 1, 1, 1, -68, [10]], [6, 3, -69, [4, 4278190080]]], [0, "84590dy69JBrqpv68k8g5g", 1, 0], [4, 4283563357], [5, 134, 45.06]], [1, "New Label", 9, [[49, "100", 24, 1, 1, 1, -70, [13]], [6, 3, -71, [4, 4279571992]]], [0, "39whD9/aNCcLvEK9z3CZcZ", 1, 0], [5, 72.74, 36.239999999999995], [3.056, 1.008, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_zhuangbei_sz", 5, [[3, 1, 0, -72, [18], 19], [41, 4, 0.02800000000002001, -73]], [0, "7bZzjRlFJKCboKdVsgkkUG", 1, 0], [5, 1125, 150], [0, -590.972, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 3, [-75], [[42, 45, -1.3322676295501878e-15, 1.3322676295501878e-15, 40, 40, -74]], [0, "a5KdKK4MlOtLi3XVz8c03Y", 1, 0], [5, 137, 128]], [5, "ggtipicon", false, 3, [[4, -76, [25], 26], [17, 1021, -77]], [0, "3eZVkK4+5Ge74UfOLECZLC", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [1, "tgNe", 3, [[16, "奖励", 26, false, 1, 1, 1, -78, [27]], [6, 3, -79, [4, 4281545529]]], [0, "e5JrWx4dpMT7r4PFkEnI07", 1, 0], [5, 58, 56.4], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 2, [-81], [[14, 45, 40, 40, -80]], [0, "0cibCAyDBFaKjSWuB6+0L1", 1, 0], [5, 137, 128]], [5, "ggtipicon", false, 2, [[4, -82, [33], 34], [17, 1001, -83]], [0, "24ZAjTdclHWLgdkQeFQXQX", 1, 0], [5, 65, 66], [54.208, 51.862, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [1, "tgNe", 2, [[16, "星级奖励", 26, false, 1, 1, 1, -84, [35]], [6, 3, -85, [4, 4281545529]]], [0, "eeMdKzCpFG9ICcSW2ymOZo", 1, 0], [5, 110, 56.4], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_txz_hyd", 7, [[2, 0, -86, [8], 9]], [0, "a7PLl8iVxBxYym87vI/sqw", 1, 0], [5, 800, 164], [0, -28.019, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_djd", 9, [[4, -87, [14], 15]], [0, "4fox+w2+dEsJ7VAB27ZF6x", 1, 0], [5, 70, 76], [-56.505, 0.767, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_store", 22, [[2, 0, -88, [20], 21]], [0, "ee+ttzpgpCa4Tmkj0NDhvC", 1, 0], [5, 107, 82], [0, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_store", 10, [[2, 0, -89, [22], 23]], [0, "28sSdq6HpAeI7Zklbbzgj9", 1, 0], [5, 107, 82], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [15, 1, 0, false, 10, [24]], [1, "icon_store", 25, [[2, 0, -90, [28], 29]], [0, "2fbZxwZZJIVK/4QTjXe5bt", 1, 0], [5, 107, 82], [0, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_store", 11, [[2, 0, -91, [30], 31]], [0, "87HkyFvxRIqZzndRfoYASf", 1, 0], [5, 107, 82], [0, 5, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [15, 1, 0, false, 11, [32]], [5, "lock", false, 2, [[2, 0, -92, [36], 37]], [0, "90rIHa7ChKNp3o0HtVXmiz", 1, 0], [5, 37, 39], [-27.724, 35.33, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "hand", false, 2, [[4, -93, [38], 39]], [0, "80ZdYULiFD1alBj9Jd0oVb", 1, 0], [5, 85, 83], [37.802, -25.201, 0, 0, 0, 0, 1, 1, 1, 1]], [50, 8, [0, 0, 20], 14]], 0, [0, 6, 1, 0, 7, 38, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, 3, 35, 0, 4, 2, 0, 0, 2, 0, -1, 25, 0, -2, 11, 0, -3, 26, 0, -4, 27, 0, -5, 36, 0, -6, 37, 0, 3, 32, 0, 4, 3, 0, 0, 3, 0, -1, 22, 0, -2, 10, 0, -3, 23, 0, -4, 24, 0, 0, 4, 0, 0, 4, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -1, 7, 0, -2, 21, 0, -3, 6, 0, -4, 12, 0, -5, 8, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 28, 0, -2, 18, 0, -3, 9, 0, 8, 14, 0, 0, 8, 0, -2, 38, 0, 0, 8, 0, -1, 13, 0, 0, 9, 0, -1, 20, 0, -2, 29, 0, -1, 32, 0, 0, 10, 0, -1, 31, 0, -1, 35, 0, 0, 11, 0, -1, 34, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -1, 30, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -1, 33, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 33, 0, 0, 34, 0, 0, 36, 0, 0, 37, 0, 9, 1, 2, 5, 6, 3, 5, 6, 93], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 35, 38], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, 10, 11, -1, 1, -1, 1, 1, 12], [0, 8, 0, 1, 0, 1, 0, 9, 0, 10, 0, 0, 11, 0, 0, 12, 0, 13, 0, 14, 0, 2, 0, 2, 0, 0, 3, 0, 0, 4, 0, 4, 0, 0, 3, 0, 0, 15, 0, 16, 0, 17, 0, 18, 0, 5, 5, 19]], [[{"name": "img_gmtxzjld", "rect": [0, 0, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [16, 16, 19, 27]}], [3], 0, [0], [2], [20]]]]