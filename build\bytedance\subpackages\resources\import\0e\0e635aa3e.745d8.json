[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "caMPy3olVDcLOJIdYu6NXd", "c1QZebCHxMXqdoHXnFgQyK", "0dlQtcv8lBJoPcSim9cxZD", "c462hEeYFIrpRk/VTxYhaB", "aa+qQcLiVOmYjL878NcTrO", "dal4o32KpO75yexm9hALiM", "7aSL72M71OCKrrZfvV9xtf", "19SYMHJJRCYbzQgcAl+7q4", "71cvTz3uFKs7p/yq4iHHDe"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "root", "_N$target", "target", "data"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children"], 1, 9, 4, 5, 1, 7, 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_styleFlags", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["3b40bR050dIRJrBkj6qAkU5", ["node", "sprArr", "lbArr"], 3, 1, 2, 2], ["cc.<PERSON><PERSON>", ["node", "clickEvents", "_N$target"], 3, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_N$horizontalDirection", "node", "_layoutSize"], 0, 1, 5], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -4, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [13, 0, 1, 2, 2], [0, 0, 5, 2, 3, 4, 6, 2], [2, 0, 1, 2, 3, 4, 6, 5, 8, 9, 8], [0, 0, 1, 5, 2, 3, 4, 6, 3], [3, 2, 3, 4, 1], [3, 0, 2, 3, 4, 2], [1, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 7, 5, 2], [7, 0, 2], [0, 0, 7, 2, 3, 4, 2], [0, 0, 5, 7, 2, 3, 4, 6, 2], [4, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 3, 6, 4, 7, 5, 2], [1, 0, 1, 2, 3, 6, 4, 5, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3], [5, 1, 2, 1], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8], [2, 0, 1, 2, 3, 4, 6, 5, 8, 9, 10, 8], [2, 0, 1, 2, 3, 4, 5, 8, 9, 7], [2, 0, 1, 2, 3, 4, 7, 8, 9, 7], [3, 1, 0, 2, 3, 3], [3, 0, 2, 3, 2]], [[[{"name": "bm-2", "rect": [0, 0, 536, 199], "offset": [0, 0], "originalSize": [536, 199], "capInsets": [389, 55, 106, 113]}], [6], 0, [0], [3], [2]], [[[9, "FundListViewItem"], [10, "FundListViewItem", [-14, -15, -16, -17, -18, -19, -20, -21, -22, -23], [[16, -10, [-8, -9], [-2, -3, -4, -5, -6, -7]], [17, -13, [[18, "3b40bR050dIRJrBkj6qAkU5", "onClickItem", -12]], -11]], [19, -1, 0], [5, 614, 274]], [11, "New Node", 1, [-26, -27], [[20, 1, 5, 1, -24, [5, 300, 45]], [21, 37, 372.781, 10, 223.77800000000002, 5.221999999999994, 300, 50, -25]], [0, "8ba5DXv5FPj5Ib+fKVU1e4", 1, 0], [5, 300, 45], [147, -109.27800000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "img_jj_txz", 1, [-29, -30], [-28], [0, "50aw/yollE8beRovLrrAzp", 1, 0], [5, 614, 274]], [13, "img_jhd", 1, [-32, -33], [-31], [0, "47j1vcRV1NRJSe2+5Dm5aK", 1, 0], [5, 200.1, 60], [219.584, 111.73, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Label", 4, [[-34, [1, 2, -35, [4, 4278190080]]], 1, 4], [0, "0cmWEe3VtLuIsY3wbuwfMx", 1, 0], [5, 79, 43.06], [22.542, 1.403, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "New Label", 1, [[-36, [1, 2, -37, [4, 4278190080]]], 1, 4], [0, "c6uje7pa1CWIwNvp129QnK", 1, 0], [4, 4285133171], [5, 116, 43.06], [0, 0, 0.5], [-270, 34.972, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 1, [[22, "进度:", 24, 30, false, 1, 1, 1, -38, [12], 13], [1, 2, -39, [4, 4278190080]]], [0, "faw9K1zJJAW5/1K2cCwiV2", 1, 0], [5, 58.02, 41.8], [-250.892, -109.526, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Label", 1, [[-40, [1, 2, -41, [4, 4278190080]]], 1, 4], [0, "a47Ilhpk1NYZCisVXHN5Kv", 1, 0], [5, 84.46, 41.8], [0, 0, 0.5], [-177.737, -109.526, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Label", 2, [[-42, [1, 2, -43, [4, 4278190080]]], 1, 4], [0, "c4nb53XVJK5KUAphA24uAs", 1, 0], [5, 161.99, 41.8], [69.005, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Label", 1, [[-44, [1, 2, -45, [4, 4278190080]]], 1, 4], [0, "b9xSXCVBRO1J8dLp1av9Ya", 1, 0], [5, 400, 36.76], [0, 0, 0.5], [-270, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New Label", 1, [[-46, [1, 2, -47, [4, 4278190080]]], 1, 4], [0, "51o4sKLFtEW7E+dyrUr6Ov", 1, 0], [4, 4289648113], [5, 132, 44.32], [-183.909, 114.516, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gift", 3, [[5, -48, [0], 1]], [0, "8d/NEjl/lI+6AJtEvYtT/c", 1, 0], [5, 191, 176], [172.195, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "pc", false, 3, [[6, 0, -49, [2], 3]], [0, "31EfakXDdAg60wbBGSs8o6", 1, 0], [5, 551.8, 264.2], [27.984, -1.358, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 1, 0, 3, [4]], [2, "icon_sm_lock1", 4, [[5, -50, [5], 6]], [0, "75lFnXoghFLaFJuol1MG6d", 1, 0], [5, 57, 43], [-56.391, -4.423, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "未激活", 25, 31, false, 1, 1, 1, 5, [7]], [26, 0, 4, [8]], [2, "icon_jytb", 1, [[6, 0, -51, [9], 10]], [0, "a7sHzgIwJKDaZ/XlpfB2cP", 1, 0], [5, 33, 36], [-199.149, -109.972, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "第一赛季", 28, 31, false, 1, 1, 1, 6, [11]], [23, "20/100", 24, 30, false, 1, 1, 8, [14]], [3, "8天21时后结束", 24, 30, false, 1, 1, 1, 9, [15]], [4, "icon_clock", false, 2, [[6, 0, -52, [16], 17]], [0, "d6TG55qxBEgoVLuGZJ6Hf5", 1, 0], [5, 32, 38], [-43.71000000000001, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "完成日常任务，即可获得通行证奖励", 22, 26, false, 1, 3, 10, [18]], [3, "坏蛋降临", 32, 32, false, 1, 1, 1, 11, [19]], [4, "ggtipicon", false, 1, [[5, -53, [20], 21]], [0, "7ahnBSpJJEtKflgYann7gN", 1, 0], [5, 44, 43], [285.052, 132.137, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]]], 0, [0, 4, 1, 0, -1, 19, 0, -2, 16, 0, -3, 20, 0, -4, 21, 0, -5, 23, 0, -6, 24, 0, -1, 14, 0, -2, 17, 0, 0, 1, 0, 5, 1, 0, 6, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 18, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 2, 0, -8, 10, 0, -9, 11, 0, -10, 25, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 22, 0, -1, 14, 0, -1, 12, 0, -2, 13, 0, -1, 17, 0, -1, 15, 0, -2, 5, 0, -1, 16, 0, 0, 5, 0, -1, 19, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 20, 0, 0, 8, 0, -1, 21, 0, 0, 9, 0, -1, 23, 0, 0, 10, 0, -1, 24, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 15, 0, 0, 18, 0, 0, 22, 0, 0, 25, 0, 7, 1, 53], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 16, 17, 19, 20, 21, 23, 24], [-1, 1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, 2, -1, -1, -1, 1, -1, -1, -1, 1, 1, 2, 1, 2, 2, 2, 2, 2], [0, 3, 0, 4, 0, 0, 5, 0, 0, 0, 6, 0, 0, 1, 0, 0, 0, 7, 0, 0, 0, 8, 9, 1, 10, 1, 1, 1, 1, 1]]]]