[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "29vXfyOiNKfJ2kQ3+RZypx", "54F9Hwcp5MxKvwbJ6CPQ4f"], ["node", "_file", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_is3DNode", "_prefab", "_components", "_parent", "_trs", "_children", "_contentSize", "_eulerAngles", "_anchorPoint"], 0, 4, 9, 1, 7, 2, 5, 5, 5], ["cc.CurveRange", ["mode", "constantMax", "constant", "constantMin"], -1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.GradientRange", ["_mode", "colorMin", "gradient"], 2, 5, 4], ["cc.<PERSON><PERSON><PERSON>", ["time", "alpha"], 1], ["cc.ParticleSystem3D", ["scaleSpace", "_capacity", "node", "_materials", "startDelay", "startLifetime", "startColor", "startSize", "startSpeed", "startRotation", "gravityModifier", "rateOverTime", "rateOverDistance", "bursts", "_shapeModule", "_colorOverLifetimeModule", "_velocityOvertimeModule", "_limitVelocityOvertimeModule"], 1, 1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 4, 4, 4, 4], ["cc.<PERSON><PERSON>", ["time"], 2], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", ["count"], 3, 4], ["cc.ShapeModule", ["enable", "emitFrom", "radius", "_angle", "arcSpeed", "_scale"], -1, 4, 5], ["cc.ColorOvertimeModule", ["enable", "color"], 2, 4], ["cc.Gradient", ["colorKeys", "alphaKeys"], 3, 9, 9], ["cc.VelocityOvertimeModule", ["enable", "x", "y", "z", "speedModifier"], 2, 4, 4, 4, 4], ["cc.LimitVelocityOvertimeModule", ["enable", "dampen", "limit", "limitX", "limitY", "limitZ"], 1, 4, 4, 4, 4], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "_stopped", "autoRemoveOnFinish", "totalParticles", "emissionRate", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "emitterMode", "speed", "speedVar", "tangentialAccel", "tangentialAccelVar", "radialAccel", "radialAccelVar", "startRadius", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file"], -17, 1, 3, 8, 8, 8, 8, 5, 5, 6], ["90417mcft9EuLa4kj5jrJVQ", ["node"], 3, 1]], [[1, 1], [1, 0, 3, 1, 4], [1, 2, 2], [2, 0, 1, 2, 2], [2, 0, 1, 2], [6, 0, 2], [0, 0, 5, 4, 3, 6, 9, 2], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 21], [7, 0, 2], [0, 0, 1, 7, 4, 3, 8, 3], [0, 0, 1, 5, 4, 3, 8, 3], [0, 0, 5, 7, 3, 6, 2], [0, 0, 2, 5, 4, 3, 10, 6, 3], [0, 0, 2, 5, 4, 3, 6, 9, 3], [8, 0, 1, 1], [2, 1, 2, 1], [5, 1, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 3], [5, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 2], [1, 0, 1, 3], [3, 1], [3, 0, 1, 2], [3, 0, 2, 2], [9, 0, 1], [10, 0, 1, 2, 3, 4, 5, 5], [11, 0, 1, 2], [12, 0, 1, 1], [6, 1], [4, 1], [4, 1, 0, 3], [4, 0, 2], [13, 0, 1, 2, 3, 4, 2], [14, 0, 1, 2, 3, 4, 5, 3], [16, 0, 1]], [[8, "BabyBox"], [9, "BabyBox", 7, [-3, -4, -5, -6], [[32, -2]], [15, -1, 0], [5, 64, 64]], [11, "FX_baoxiangda<PERSON>ju", 1, [-8, -9], [4, "1c7jPtXmNOCLmJWAnepbpO", -7], [0, 633.639, 0, 0, 0, 0, 1, 8, 65, 1]], [10, "icon", 7, 1, [[14, -10, [0]]], [3, "d78d0qyh1LmrUU2o4FIhBY", 1, 0], [5, 54, 50]], [12, "gaung<PERSON>hu", true, 2, [[16, 1002, 0, -11, [1], [0], [2, 5], [19], [2, 5], [0], [0], [0], [0], [0], [[22, [2, 1]]]]], [4, "b2crpDDnBB3ZwZlQu1W5oJ", 2], [0, 0, 0.5], [0, 0, 0, 0, 0, 0, 1, 1, 4, 1]], [13, "LIZI", true, 2, [[17, 0, -12, [2], [0], [1, 3, 0.8, 1.5], [20, 2, [4, 4282381311]], [1, 3, 0.2, 0.7], [1, 3, 5, 18], [18, 3, 360], [0], [2, 10], [0], [23, true, 0, 0.6, 0, [0], [1, 1.2, 0, 1]], [24, true, [21, 1, [25, [[26], [5, 0.6409090909090909], [5, 0.6409090909090909], [5, 0.6409090909090909]], [[27], [28, 255, 0.37727272727272726], [29, 0.9954545454545454]]]]], [30, true, [1, 3, 0.3, -0.3], [1, 3, 0.3, -0.3], [1, 3, 0.3, -0.3], [2, 1]], [31, true, 0.2, [0], [0], [0], [0]]]], [4, "achF9QyGxDUL1x5HCgFEr9", 2], [0, -9.717, 0, 0.7071067811865475, 0, 0, 0.7071067811865476, 1, 1, 1], [1, 90, 0, 0]], [6, "New Particle", 1, [[7, 1, true, false, true, 10, 3, 2, 0, 0, 55, 50, 30.31999969482422, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 150, -13, [3], [4, 4278224383], [4, 0], [4, 4278245119], [4, 0], [0, 7, 7], [0, 0.25, 0.8600000143051147], 4]], [3, "afqpJVVTJI0oNpzslExw00", 1, 0], [-21, 132.994, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [6, "New Particle", 1, [[7, 1, true, false, true, 10, 3, 2, 0, 0, 55, 50, 30.31999969482422, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, 150, -14, [5], [4, 4278224383], [4, 0], [4, 4278245119], [4, 0], [0, 7, 7], [0, 0.25, 0.8600000143051147], 6]], [3, "c41ACmWftJ35CIJpGRDU0D", 1, 0], [21, 132.994, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -3, 6, 0, -4, 7, 0, 2, 2, 0, -1, 4, 0, -2, 5, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 3, 1, 14], [0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, -1, 1, -1, 1], [0, 2, 3, 0, 1, 0, 1]]