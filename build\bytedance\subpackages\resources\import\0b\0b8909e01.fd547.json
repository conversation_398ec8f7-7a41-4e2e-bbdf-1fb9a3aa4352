[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "29FYIk+N1GYaeWH/q1NxQO", "586MyaYRZBYbdgSvAl61dB", "c0VITW/OJML7691KcQocPY", "f5YmCCSe1OrIVUT+Qote1i", "cb1VFt7lZM5bK3HPiU1WUY", "1bNB28UHFHTZMCOz1pkGd+", "17720zgH9NJ5L8Vi/KDQf6", "aej5KCfVhFI6ZBtIoyh/zN", "94EziLmzlAirmOfZrgDe1Z", "acwGy12d5B/LLRv26y1lcC", "a9QHx+UxdJh6B5EOZDgg4X", "85GOloFFVKh6PuE5BPRuHG", "87nvObUldMQ5HHgzxx4RJh", "b3g2DJjzRK3JXxItAf+Tyl", "fdPjK0CYBIvb+AsThw8kyW", "db3bhtO2JEZrrsv1i6gtCU", "73jGpne/9JUI/171Zur5Pr", "5aS2OC53NM+p6lDMBeCes/", "7a/QZLET9IDreTiBfRn2PD", "52z9vNKwFF5pO0st0cDLy7", "c770npCgpHvrrj8apNO90q", "6dhW1mCMBIcJTWznUAi4dw", "8cjCuo4gZNtaOR/uAGL8Cn", "65hdeSelhOVq5sz7eNtoFd", "3a8G6JDxdKkqiuSqpsbfFd", "d0YBDfZjhIg4DQTMtTbWjl", "40OtPssnZP9antMAWsDtDT", "fbAr5JudJM5aKLpDRh+KL/", "e4kONSuyFG7I81ppU+bSbk", "f1oLpJwq1EEYtLpd1sEGaF", "dbNkclouNG/oj/cNi+uAJ8", "58Ux1ovWNBB6A9SQqn+okX", "485eGEfEJLOqZGExd6w/kd", "9alhDeZttPnIjWWf28gp1y", "b2gseIigJABZr/xG0zwT9Y", "6eU+/4h0pMNr5pAC0+conq"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_parent", "_N$disabledSprite", "_N$font", "root", "data", "_N$skeletonData", "rolePrefab"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color"], -1, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_right", "_bottom", "alignMode", "_left", "node"], -5, 1], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_N$paddingLeft", "_N$spacingY", "_N$paddingRight", "_N$paddingBottom", "_N$paddingTop", "_enabled", "node", "_layoutSize"], -6, 1, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_enableWrapText", "_N$cacheMode", "_lineHeight", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Sprite", ["_type", "_sizeMode", "_fillRange", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node", "_N$font"], -2, 1, 6], ["cc.Prefab", ["_name"], 2], ["4dd47WxUhhIfYnZqokgsZpT", ["UItype", "node", "nodeArr", "rolePrefab"], 2, 1, 2, 6], ["061b8/OBgdEfI0ajbX8IA//", ["<PERSON><PERSON>", "KnapsackName", "node"], 1, 1], ["bbcc410J6hMNqmnBqUklFiE", ["path", "node"], 2, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "toValue", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6]], [[8, 0, 1, 2, 2], [0, 0, 7, 5, 4, 6, 8, 2], [5, 4, 5, 6, 1], [5, 0, 1, 4, 5, 6, 3], [0, 0, 7, 9, 5, 4, 6, 8, 2], [17, 0, 1, 2, 2], [0, 0, 1, 7, 5, 4, 6, 8, 3], [4, 0, 1, 2, 3, 4, 9, 10, 11, 6], [0, 0, 7, 5, 4, 6, 2], [6, 1, 0, 2, 3, 4, 5, 6, 3], [7, 0, 1, 3, 3], [0, 0, 1, 7, 9, 5, 4, 6, 8, 3], [0, 0, 7, 5, 4, 6, 10, 8, 2], [0, 0, 2, 7, 5, 4, 6, 8, 3], [2, 1, 0, 3, 5, 2, 9, 10, 6], [12, 0, 1, 2, 3], [4, 0, 1, 6, 2, 3, 4, 7, 9, 10, 11, 8], [4, 0, 1, 2, 3, 4, 9, 10, 6], [0, 0, 9, 5, 4, 6, 10, 8, 2], [0, 0, 2, 7, 9, 5, 4, 6, 8, 3], [0, 0, 9, 5, 4, 6, 8, 2], [0, 0, 2, 7, 9, 4, 3], [0, 0, 7, 9, 5, 4, 6, 10, 8, 2], [0, 0, 3, 7, 5, 4, 11, 6, 3], [1, 0, 2, 3, 8, 4], [1, 6, 0, 8, 3], [2, 1, 0, 2, 9, 10, 4], [2, 8, 1, 0, 2, 9, 10, 5], [2, 1, 0, 3, 5, 6, 2, 4, 9, 10, 8], [6, 2, 3, 1], [7, 0, 1, 2, 3, 4], [7, 0, 1, 2, 4], [4, 0, 1, 2, 5, 3, 4, 9, 10, 11, 7], [10, 0, 2], [0, 0, 1, 9, 5, 4, 6, 3], [0, 0, 9, 5, 4, 6, 10, 2], [0, 0, 7, 9, 5, 4, 6, 2], [0, 0, 7, 5, 4, 11, 6, 10, 8, 2], [11, 0, 1, 2, 3, 2], [8, 1, 2, 1], [1, 0, 1, 8, 3], [1, 6, 0, 4, 5, 2, 3, 8, 7], [1, 0, 7, 4, 1, 8, 5], [1, 0, 7, 4, 1, 2, 3, 8, 7], [1, 0, 1, 5, 2, 3, 8, 6], [1, 0, 1, 5, 2, 8, 5], [1, 0, 8, 2], [5, 2, 4, 5, 6, 2], [5, 3, 4, 5, 6, 2], [2, 1, 0, 7, 6, 4, 9, 10, 6], [2, 1, 0, 3, 5, 9, 10, 5], [2, 0, 3, 2, 9, 10, 4], [2, 1, 0, 9, 10, 3], [2, 1, 0, 4, 9, 10, 4], [6, 1, 0, 2, 3, 3], [6, 0, 2, 3, 2], [13, 0, 1, 2], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 2], [16, 0, 1, 2, 3, 4], [4, 0, 1, 6, 2, 5, 3, 4, 7, 9, 10, 11, 9], [4, 0, 1, 8, 6, 2, 5, 3, 4, 7, 9, 10, 11, 10], [18, 0, 1, 2, 3, 4, 5, 6, 7, 6], [9, 0, 1, 2, 3, 4, 5, 6, 6], [9, 0, 1, 2, 5, 6, 4]], [[[{"name": "frame_qd_04", "rect": [1, 0, 71, 71], "offset": [0.5, 0.5], "originalSize": [72, 72], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [12]], [[{"name": "bg_gf_tag_01", "rect": [0, 0, 83, 31], "offset": [0, 0], "originalSize": [83, 31], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [13]], [[{"name": "skill_10036", "rect": [4, 4, 80, 80], "offset": [0.5, -0.5], "originalSize": [87, 87], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [14]], [[{"name": "s", "rect": [0, 0, 34, 45], "offset": [0, 0], "originalSize": [34, 45], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [15]], [[{"name": "sp_lv", "rect": [0, 0, 40, 41], "offset": [0, 0], "originalSize": [40, 41], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [16]], [[{"name": "skill_101", "rect": [8, 7, 76, 76], "offset": [2.5, -1.5], "originalSize": [87, 87], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [17]], [[{"name": "icon_xc", "rect": [6, 12, 69, 57], "offset": [0.5, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [18]], [[[33, "Role_Select"], [34, "Role_Select", 1, [-6, -7, -8], [[38, 0, -5, [-2, -3, -4], 108]], [39, -1, 0], [5, 750, 1334]], [18, "top", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [[40, 1, 135.5, -9]], [0, "d9Xg1OZkxMaIK6jD9wq//8", 1, 0], [5, 750, 402], [0, 0.5, 1], [0, 531.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "skill", 2, [-21, -22, -23, -24, -25, -26], [[3, 1, 0, -20, [35], 36]], [0, "18q+pU40NCZ4dZ+SAu/6Zn", 1, 0], [5, 340, 146], [-182, -440, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "content", [-29, -30, -31, -32], [[41, 0, 9, 510, 120, 240, 250, -27], [49, 1, 2, 20, 20, 20, -28, [5, 750, 209]]], [0, "09qVo8x8BKl6Ktsi8YKzbx", 1, 0], [5, 750, 209], [0, 0.5, 1]], [4, "btnLayer", 2, [-34, -35, -36, -37], [[26, 1, 1, 18, -33, [5, 935.4300000000001, 100]]], [0, "70AzXjhBdIbbEGFuxeaHCw", 1, 0], [5, 935.4300000000001, 100], [0, -568.805, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn", 5, [-41, -42], [[9, 0.9, 3, -38, [[10, "4dd47WxUhhIfYnZqokgsZpT", "onClickUnLock", 1]], [4, 4293322470], [4, 3363338360], 69], [3, 1, 0, -39, [70], 71], [50, 1, 1, 40, 40, -40, [5, 281.43, 90]]], [0, "dcFDVDorhBIq6uD6l7rFFM", 1, 0], [5, 281.43, 90], [327, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "top", false, 1, [-45, -46, -47], [[42, 1, 375, 375, 65, -43], [51, 1, 40, 25, -44, [5, 750, 100]]], [0, "25FoabOftKA6KaXRXR7tZg", 1, 0], [5, 750, 100], [0, 552, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "coin", 1, 7, [-51, -52], [[15, "2", "MCatGame", -48], [3, 1, 0, -49, [96], 97], [14, 1, 1, -25, 25, 5, -50, [5, 69.73, 43]]], [0, "cbaQEj5n1HOYU5vtZKUgj8", 1, 0], [5, 69.73, 43], [-300.135, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "diamond", 1, 7, [-56, -57], [[15, "11", "MCatGame", -53], [3, 1, 0, -54, [101], 102], [14, 1, 1, -25, 25, 5, -55, [5, 68.73, 43]]], [0, "b6Tps23KVAabh8owgWluPB", 1, 0], [5, 68.73, 43], [-205.90499999999997, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "adcoin", 1, 7, [-61, -62], [[15, "53", "MCatGame", -58], [3, 1, 0, -59, [106], 107], [14, 1, 1, -25, 25, 5, -60, [5, 97.73, 43]]], [0, "5aOLi8dSxIn44yU8KUcIwQ", 1, 0], [5, 97.73, 43], [-97.67499999999995, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "item", [-64, -65, -66], [[3, 1, 0, -63, [18], 19]], [0, "ab/FOsCg9NfLiG4YsKQBjr", 1, 0], [5, 310, 54], [0, -27, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "buff", 2, [-68, -69, -70], [[3, 1, 0, -67, [49], 50]], [0, "3fIo12uTVBb7HW9lJZLKqb", 1, 0], [5, 340, 146], [182, -440, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "bottom", [-73, -74], [[3, 1, 0, -71, [91], 92], [43, 5, 325, 325, 762, 100, 100, -72]], [0, "22HLaDIO5NeKI+vrZ8/+Ki", 1, 0], [5, 750, 572], [0, 0.5, 0], [0, -667, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 4, [-76, -77, -78], [[27, false, 1, 1, 20, -75, [5, 414, 54]]], [0, "c23+dFB0NCKLB4SWZh1JFD", 1, 0], [5, 414, 54], [0, -47, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title", 4, [-80, -81, -82], [[27, false, 1, 1, 20, -79, [5, 414, 55]]], [0, "9dpW+HdVdLWbmtJ+TClRXp", 1, 0], [5, 414, 55], [0, -141.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "bg", 1, [2, 13], [[24, 45, 750, 1334, -83]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [4, "starBar", 2, [-86], [[52, 1, 1, -84, [5, 35, 50]], [56, ["img/Pet/icon_xxhui", "img/Pet/icon_xxh"], -85]], [0, "a2VjnDJ4ZI55Z86f92IRnM", 1, 0], [5, 35, 50], [-171.448, -316.748, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lock", false, 3, [-87, -88, -89], [0, "cawtTdaVhAnbrOotM0OZI8", 1, 0]], [20, "item", [-91, -92], [[3, 1, 0, -90, [47], 48]], [0, "75+8Wz1F1PJakUgs3u83Tf", 1, 0], [5, 96, 96], [0, 1.958, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn", 5, [-95], [[9, 0.9, 3, -93, [[10, "4dd47WxUhhIfYnZqokgsZpT", "onClickFitOut", 1]], [4, 4293322470], [4, 3363338360], 53], [3, 1, 0, -94, [54], 55]], [0, "94fxB8BBROBKVmvP2ZCAdN", 1, 0], [5, 200, 90], [-367.71500000000003, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn", 5, [-98], [[9, 0.9, 3, -96, [[30, "4dd47WxUhhIfYnZqokgsZpT", "onClickUpGrade", "1", 1]], [4, 4293322470], [4, 3363338360], 58], [3, 1, 0, -97, [59], 60]], [0, "15zbIfJvlHFoGAvPkq2nhx", 1, 0], [5, 200, 90], [-149.71500000000003, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn", 5, [-101], [[9, 0.9, 3, -99, [[30, "4dd47WxUhhIfYnZqokgsZpT", "onClickUpGrade", "2", 1]], [4, 4293322470], [4, 3363338360], 63], [3, 1, 0, -100, [64], 65]], [0, "86bQn8JStKo62fF50LI/T5", 1, 0], [5, 200, 90], [68.28499999999997, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Recast", 2, [[2, -102, [72], 73], [54, 0.9, 3, -103, [[10, "4dd47WxUhhIfYnZqokgsZpT", "onClickRecast", 1]]]], [0, "d52nfRvZ5J+4zh5Xbk76lx", 1, 0], [5, 56, 55], [-321.925, -310.809, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "ScrollView", 13, [-106], [[57, false, 0.75, 0.23, null, null, -104, 4], [44, 45, 8, 86, 240, 250, -105]], [0, "15MHpYYEtBf6lG3HK+xoxx", 1, 0], [5, 750, 478], [0, 325, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "view", 24, [4], [[58, 0, -107, [86]], [24, 45, 240, 250, -108]], [0, "a5ytvCvYBNQ7uWRhUt9U0q", 1, 0], [5, 750, 478], [0, 0.5, 1], [0, 239, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "UnLockContent", 4, [[28, 1, 3, 37, 37, 10, 20, 25, -109, [5, 750, 0]], [25, 0, 40, -110]], [0, "23zYLVCctBS7spMLONx1L8", 1, 0], [5, 750, 0], [0, -94, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lock<PERSON><PERSON><PERSON>", 4, [[28, 1, 3, 37, 37, 80, 20, 25, -111, [5, 750, 0]], [25, 0, 40, -112]], [0, "d2H9qYY0hMfL6ZYBnv31GF", 1, 0], [5, 750, 0], [0, -189, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "bg_zhuangbei_sz", false, 13, [-115], [[3, 1, 0, -113, [89], 90], [45, 44, 714.12, -10.754999999999995, 20, -114]], [0, "b4Rg89sXRFBborwCJZljWC", 1, 0], [5, 750, 100], [0, 39.245000000000005, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "maskbg", 1, [[46, 45, -116], [3, 1, 0, -117, [0], 1]], [0, "5eZWKfNM5DUIZUjxNM1w8r", 1, 0], [5, 750, 1334]], [1, "<PERSON><PERSON><PERSON>", 2, [[2, -118, [2], 3], [59, 6, 1, [10], -119]], [0, "f1jD2I879DSp9AJSjV8VPn", 1, 0], [5, 281, 283], [-171.448, -162.688, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 2, [[32, "魔神哪吒", 30, false, 1, 1, 1, -120, [6], 7], [5, 2, -121, [4, 4278190080]]], [0, "e32YKsxcVFmao4UqrByt5m", 1, 0], [5, 124, 54.4], [-171.448, -279.243, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "attrLayout", 2, [11], [[53, 1, 2, 16, -122, [5, 200, 54]]], [0, "beNmqb1klNkKLm7tArGX5I", 1, 0], [5, 200, 54], [0, 0.5, 1], [203.046, -31.733, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "val", 11, [[60, "0", 32, false, false, 1, 1, 1, 1, -123, [14], 15], [5, 2, -124, [4, 4278190080]]], [0, "ab+NK1YnhKk5QPX4zU3XB3", 1, 0], [4, 4280090492], [5, 21.76, 54.4], [0, 1, 0.5], [132.444, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "name", 11, [[61, "等级", 28, 28, false, false, 1, 1, 1, 1, -125, [16], 17], [5, 2, -126, [4, 4278190080]]], [0, "7ddYPWSbZBaoABwKcxneai", 1, 0], [5, 60, 39.28], [0, 0, 0.5], [-75.305, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "name", 3, [[32, "火焰放射", 30, false, 1, 1, 1, -127, [22], 23], [5, 2, -128, [4, 4278190080]]], [0, "52zvqpOM5IEoZ5jsqvHSyk", 1, 0], [5, 124, 54.4], [0, 0, 0.5], [-14.26, 41.31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 18, [[7, "角色1星后解锁", 30, false, 1, 1, -129, [27], 28], [5, 2, -130, [4, 4280098849]]], [0, "6818UVeUBP+b2U/OHUilTb", 1, 0], [5, 192.13, 54.4], [0, -48.657, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 3, [[7, "主动技", 28, false, 1, 1, -131, [33], 34], [5, 2, -132, [4, 4278190080]]], [0, "4bW7URDhdF7L3hI6qx/Js6", 1, 0], [5, 88, 54.4], [-102.806, 75.935, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 12, [[7, "被动", 28, false, 1, 1, -133, [39], 40], [5, 2, -134, [4, 4278190080]]], [0, "f0hbuSxu5MDqCsYWYf4MXG", 1, 0], [5, 60, 54.4], [-100.781, 75.522, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Layout", 12, [19], [[26, 1, 1, 10, -135, [5, 96, 100]]], [0, "1e+EAsxJtHwZBOJH3Y3WUZ", 1, 0], [5, 96, 100], [0, -14.038, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lock", false, 19, [-136, -137], [0, "93fEjaKQpHj71oFI2/3/el", 1, 0]], [1, "val", 20, [[16, "选择", 36, false, false, 1, 1, 1, -138, [51], 52], [5, 2, -139, [4, 4278190080]]], [0, "fboKDX8DhLJqgjfSxaX/tK", 1, 0], [5, 76, 54.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "val", 21, [[16, "升级", 36, false, false, 1, 1, 1, -140, [56], 57], [5, 2, -141, [4, 4278190080]]], [0, "b2Wo+RX95Ei5WEvKkyZtEv", 1, 0], [5, 76, 54.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "val", 22, [[16, "升星", 36, false, false, 1, 1, 1, -142, [61], 62], [5, 2, -143, [4, 4278190080]]], [0, "0brC12KL9AOabQS0jJwnDn", 1, 0], [5, 76, 54.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Label", 14, [[7, "已解锁", 36, false, 1, 1, -144, [76], 77], [5, 2, -145, [4, 4278190080]]], [0, "2d7tr7QqNOX6+P7lQZrpI7", 1, 0], [5, 112, 54.4]], [8, "New Label", 15, [[7, "待解锁", 36, false, 1, 1, -146, [82], 83], [5, 2, -147, [4, 4278190080]]], [0, "6bLMcu3vVGQLyvpJUWpvhq", 1, 0], [5, 112, 54.4]], [1, "button_return", 28, [[2, -148, [87], 88], [55, 3, -149, [[10, "4dd47WxUhhIfYnZqokgsZpT", "close", 1]]]], [0, "16SJN4VRZMh7B/xxexW5CN", 1, 0], [5, 66, 69], [-322.549, 3.946, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "head", 1, 8, [[2, -150, [93], 94], [29, -151, [[31, "74d27XR4shG97qUPGx7qvw9", "onBtn", "TTTTTTTTTTs"]]]], [0, "b25i1JJKhNz6r2huBF75DY", 1, 0], [5, 48, 49], [-35.86500000000001, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "head", 1, 9, [[2, -152, [98], 99], [29, -153, [[31, "74d27XR4shG97qUPGx7qvw9", "onBtn", "gm_add_resources"]]]], [0, "faa4RaTyhPnZJOqSV8TlIo", 1, 0], [5, 47, 55], [-35.86500000000001, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "ske", 2, [[62, 1, "idle", 0, false, "idle", -154, [4], 5]], [0, "85xGc6s3BCwpHFTElavL80", 1, 0], [5, 352.03460693359375, 361.4834899902344], [-171.448, -282.602, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [8, "icon_xxh", 17, [[47, 5.551115123125783e-17, -155, [8], 9]], [0, "2e9maZbnlFoLv4XwhYaRTP", 1, 0], [5, 35, 35]], [13, "tag", false, 2, [[2, -156, [10], 11]], [0, "2eCRtp0I1I3L3L9z+7qX3b", 1, 0], [5, 34, 45], [-302.02, -64.489, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_dj", 11, [[48, false, -157, [12], 13]], [0, "80SvIVEdlCAqfa6jhYJySo", 1, 0], [5, 40, 41], [-114.267, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon", 3, [[2, -158, [20], 21]], [0, "47uHMWJIxA1r9GpLfY3SrN", 1, 0], [5, 80, 80], [-111.303, -15.715, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "desc", 3, [[63, false, "<outline color=black width=2>释放后4s内手枪技能速度+20%，暴击率+10%，暴击伤害+30%</outline>", 22, 220, 26, -159, 24]], [0, "91w0AHslJEJqRH3vmoXvfL", 1, 0], [5, 220, 84.75999999999999], [0, 0, 1], [-55.663, 22.914, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "img_jnd", 155, 18, [[3, 1, 0, -160, [25], 26]], [0, "a7wXEhMtNJBYet0+pXdm9y", 1, 0], [4, 4278190080], [5, 340, 146]], [1, "icon_lock_small", 18, [[2, -161, [29], 30]], [0, "8bEMxt7v5JFKWLAkOBEzOG", 1, 0], [5, 34, 41], [148.075, 62.164, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_gf_tag_01", 3, [[2, -162, [31], 32]], [0, "b5KD2N20VNSYZleG0njYB1", 1, 0], [5, 83, 31], [-101.96, 71.267, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [1, "bg_gf_tag_01", 12, [[2, -163, [37], 38]], [0, "c4ZcMAsipG/rj60th4BmFO", 1, 0], [5, 83, 31], [-100.253, 71.584, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [1, "icon", 19, [[2, -164, [41], 42]], [0, "eaVZX6S/FF9Khvigy1mJGM", 1, 0], [5, 76, 76], [0, 0, 0, 0, 0, 0, 1, 0.86, 0.86, 1]], [23, "img_sxfg", 163, 40, [[3, 1, 0, -165, [43], 44]], [0, "de3rnIxoVAN4rRMBaQhHjG", 1, 0], [4, 4278190080], [5, 96, 96]], [1, "icon_lock_small", 40, [[2, -166, [45], 46]], [0, "79PRgxfrhJLq69Z1D1NkmI", 1, 0], [5, 34, 41], [37.918, 39.583, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "icon_xc", 6, [[2, -167, [66], 67]], [0, "977NtCwDdNWZePEFWMDIIK", 1, 0], [5, 69, 57], [-66.215, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "val", 6, [[64, false, "<b><outline color=black width=2>0/80 解锁</outline>", 30, -168, 68]], [0, "0878zUrW9Asp+D070Bi8yX", 1, 0], [5, 132.43, 50.4], [34.5, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 14, [[2, -169, [74], 75]], [0, "c7K4hza19OhqxfOgN9W72B", 1, 0], [5, 531, 47], [12, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [13, "line copy", false, 14, [[2, -170, [78], 79]], [0, "d8kRWoI+hM7KwRXCNIUXHS", 1, 0], [5, 87, 30], [163.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 15, [[2, -171, [80], 81]], [0, "996GYG7gdO9b/LXjTLu2Kl", 1, 0], [5, 531, 47], [12, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [13, "line copy", false, 15, [[2, -172, [84], 85]], [0, "55UIQEDRdJfaQNRGnzy1wM", 1, 0], [5, 87, 30], [163.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "val", 1, 8, [[17, "0", 30, false, 1, 1, -173, [95]]], [0, "2biIAQbt9JpIT2rM37SQJ4", 1, 0], [5, 16.73, 50.4], [1.4999999999999911, 1.858, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "val", 1, 9, [[17, "0", 30, false, 1, 1, -174, [100]]], [0, "72h/NAb/xEK4LZg/9fLa9J", 1, 0], [5, 16.73, 50.4], [0.9999999999999911, 1.858, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "head", 1, 10, [[2, -175, [103], 104]], [0, "8bsACRhcRJcqGbUXfuFp+V", 1, 0], [5, 76, 64], [-35.86500000000001, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [6, "val", 1, 10, [[17, "0", 30, false, 1, 1, -176, [105]]], [0, "55n7uOBB5OpaGXExqYpNZi", 1, 0], [5, 16.73, 50.4], [15.499999999999991, 1.858, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 7, 1, 0, -1, 26, 0, -2, 27, 0, -3, 23, 0, 0, 1, 0, -1, 29, 0, -2, 16, 0, -3, 7, 0, 0, 2, 0, -1, 30, 0, -2, 49, 0, -3, 31, 0, -4, 17, 0, -5, 51, 0, -6, 32, 0, -7, 3, 0, -8, 12, 0, -9, 5, 0, -10, 23, 0, 0, 3, 0, -1, 53, 0, -2, 35, 0, -3, 54, 0, -4, 18, 0, -5, 57, 0, -6, 37, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 26, 0, -3, 15, 0, -4, 27, 0, 0, 5, 0, -1, 20, 0, -2, 21, 0, -3, 22, 0, -4, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 62, 0, -2, 63, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 47, 0, -2, 68, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 48, 0, -2, 69, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 70, 0, -2, 71, 0, 0, 11, 0, -1, 52, 0, -2, 33, 0, -3, 34, 0, 0, 12, 0, -1, 58, 0, -2, 38, 0, -3, 39, 0, 0, 13, 0, 0, 13, 0, -1, 24, 0, -2, 28, 0, 0, 14, 0, -1, 64, 0, -2, 44, 0, -3, 65, 0, 0, 15, 0, -1, 66, 0, -2, 45, 0, -3, 67, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 50, 0, -1, 55, 0, -2, 36, 0, -3, 56, 0, 0, 19, 0, -1, 59, 0, -2, 40, 0, 0, 20, 0, 0, 20, 0, -1, 41, 0, 0, 21, 0, 0, 21, 0, -1, 42, 0, 0, 22, 0, 0, 22, 0, -1, 43, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, -1, 46, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, -1, 60, 0, -2, 61, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 8, 1, 2, 4, 16, 4, 4, 25, 11, 4, 32, 13, 4, 16, 19, 4, 39, 176], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 9, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 2, 6, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, 5, -1, 1, -1, 2, 5, -1, 1, -1, 2, 5, -1, 1, -1, 1, 6, 5, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, 10], [0, 19, 0, 20, 21, 22, 0, 2, 0, 23, 0, 24, 0, 25, 0, 2, 0, 2, 0, 26, 0, 27, 0, 2, 2, 0, 4, 0, 1, 0, 7, 0, 8, 0, 1, 0, 4, 0, 8, 0, 1, 0, 28, 0, 9, 0, 7, 0, 9, 0, 4, 0, 1, 3, 0, 29, 0, 1, 3, 0, 5, 0, 1, 3, 0, 5, 0, 30, 2, 3, 0, 5, 0, 31, 0, 10, 0, 1, 0, 11, 0, 10, 0, 1, 0, 11, 0, 0, 32, 0, 33, 0, 34, 0, 35, 0, 0, 6, 0, 36, 0, 0, 6, 0, 37, 0, 0, 6, 38]]]]