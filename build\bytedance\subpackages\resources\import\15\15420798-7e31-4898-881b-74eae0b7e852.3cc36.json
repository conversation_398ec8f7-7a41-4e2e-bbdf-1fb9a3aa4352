[1, ["ecpdLyjvZBwrvm+cedCcQy", "22cjIamqZH/Lbdvp80pFLv", "7a/QZLET9IDreTiBfRn2PD", "d7kp9q8qNN7Z27vh39fJSC"], ["node", "_spriteFrame", "root", "lbl_angle", "lbl_dynamics", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_parent", "_children", "_anchorPoint", "_trs", "_color"], 1, 9, 4, 5, 1, 2, 5, 7, 5], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_styleFlags", "node", "_materials"], -5, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "_fillType", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 5, 7], ["cc.LabelOutline", ["_enabled", "_width", "node", "_color"], 1, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_offset", "_size"], 3, 1, 5, 5], ["1c7427fD/hF1r7fu53LlTgX", ["node", "lbl_dynamics", "lbl_angle"], 3, 1, 1, 1]], [[2, 0, 1, 2, 2], [6, 0, 1, 2, 3, 3], [0, 0, 5, 2, 3, 9, 4, 8, 2], [5, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 3, 4, 5, 8, 9, 7], [1, 0, 6, 1, 2, 7, 3, 4, 5, 8, 9, 9], [4, 0, 2], [0, 0, 1, 6, 2, 3, 4, 7, 3], [0, 0, 5, 6, 2, 3, 4, 8, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 5, 2, 3, 4, 7, 2], [2, 1, 2, 1], [3, 0, 1, 3, 4, 5, 3], [3, 0, 1, 2, 3, 4, 5, 4], [7, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 1, 2, 1], [9, 0, 1, 2, 1]], [[6, "role"], [7, "role", 4, [-6, -7, -8], [[15, -2, [0, 0, 48.5], [5, 65, 101]], [16, -5, -4, -3]], [11, -1, 0], [5, 64.9917145, 150], [0, 0.5, 0]], [8, "uibg01", 1, [-10, -11, -12, -13], [[12, 1, 0, -9, [4], 5]], [0, "a8GAFuP49O2a+nV+1yZD/2", 1, 0], [5, 252, 113], [0, 376.293, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Label_Dynamics", 2, [[-14, [1, false, 3, -15, [4, 4278190080]]], 1, 4], [0, "8dMF4Qm2VPvaa3bwSa9xOz", 1, 0], [4, 4281940021], [5, 57.81, 50.4], [-52.945, 22.483, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 2, [[5, "力度", 30, false, false, 1, 1, 1, 1, -16, [1]], [1, false, 3, -17, [4, 4278190080]]], [0, "90kHschR5Et5QhuCWmY+UF", 1, 0], [4, 4281940021], [5, 60, 50.4], [-52.945, -20.375, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Label_Angle", 2, [[-18, [1, false, 3, -19, [4, 4278190080]]], 1, 4], [0, "97uTZYtC1GlZejM1LteLoY", 1, 0], [4, 4281940021], [5, 38.24, 50.4], [63.385, 22.483, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 2, [[5, "角度", 30, false, false, 1, 1, 1, 1, -20, [3]], [1, false, 3, -21, [4, 4278190080]]], [0, "a2vht7VjdBgL3l3dSVDB4Y", 1, 0], [4, 4281940021], [5, 60, 50.4], [63.385, -20.375, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "0%", false, false, 1, 1, 1, 3, [0]], [4, "0°", false, false, 1, 1, 1, 5, [2]], [9, "role", 4, 1, [[14, "default", "idle", 0, false, "idle", -22, [6]]], [0, "bd51k+F4ZG4b5QHfXuLnIo", 1, 0], [5, 146.39971923828125, 270.92010498046875]], [10, "line", 1, [[13, 3, 2, 1, -23, [7], 8]], [0, "cftEFQ8dRB6JrZnwKFYNsX", 1, 0], [5, 26, 487], [0, 0.5, -0.1]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 8, 0, 4, 7, 0, 0, 1, 0, -1, 2, 0, -2, 9, 0, -3, 10, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -1, 7, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 9, 0, 0, 10, 0, 5, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, -1, -1, 1, -1, -1, 1], [0, 0, 0, 0, 0, 1, 2, 0, 3]]