[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "47LfXTNedBhJ5sf0G+5l7j", "7a/QZLET9IDreTiBfRn2PD"], ["node", "_file", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_parent", "_children", "_anchorPoint", "_trs"], 1, 9, 4, 5, 1, 2, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "lifeVar", "angle", "angleVar", "startSize", "endSize", "startSpinVar", "endSpin", "endSpinVar", "emitterMode", "speed", "speedVar", "tangentialAccel", "tangentialAccelVar", "radialAccel", "radialAccelVar", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -16, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "isPlayerOnLoad", "node", "_materials"], -3, 1, 3], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node"], 2, 1], ["3a459s+FYlJZr8x50hlK/uP", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 1, 6, 2, 3, 4, 3], [0, 0, 5, 2, 3, 2], [0, 0, 1, 5, 2, 3, 4, 7, 8, 3], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 20], [1, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 7, 7], [5, 0, 1, 2], [6, 0, 1]], [[1, "Bullet_7020"], [2, "Bullet_7000", 6, [-4, -5], [[8, 65, -2], [9, -3]], [6, -1, 0], [5, 100, 100]], [3, "New Particle", 1, [[5, 1, true, 50, 50, 0.2, 0, 15, 80, 70, 15, 15, 30, 1, 0, 190.7899932861328, -92.11000061035156, 65.79000091552734, -671.0499877929688, 65.79000091552734, -6, [0], [4, 1056964607], [4, 0], [4, 16777215], [4, 0], [0, 10, 10], [0, 0.25, 0.8600000143051147], 1, 2]], [0, "ceE2gklP1ADo+AA3Ug7c5l", 1, 0]], [4, "img", 6, 1, [[7, "default", "animation", 0, false, "animation", true, -7, [3]]], [0, "cdX5ugck5A+Y5xEC5RRm6d", 1, 0], [5, 752, 1337], [0, 0.5, 0.2], [0, 0, 0, 0, 0, 0, 1, 1.2, 1.2, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 4, 1, 7], [0, 0, 0, 0], [-1, 1, 2, -1], [0, 1, 2, 3]]