[1, ["ecpdLyjvZBwrvm+cedCcQy", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO", "601bh6lQFFZaXFW8HHrZDC"], ["node", "_spriteFrame", "_N$normalSprite", "_N$disabledSprite", "root", "data", "_parent"], [["cc.Node", ["_name", "_active", "_groupIndex", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_top", "_originalHeight", "node"], -1, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], -2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["1abe3N3E6pJNKVia4ArjOis", ["UItype", "node"], 2, 1]], [[3, 0, 1, 2, 2], [0, 0, 1, 6, 3, 4, 5, 7, 3], [7, 0, 1, 2, 3], [0, 0, 6, 3, 4, 5, 7, 2], [6, 0, 1, 2, 3, 4, 5, 6], [4, 1, 2, 1], [5, 0, 2], [0, 0, 2, 8, 3, 4, 5, 3], [0, 0, 6, 3, 4, 5, 2], [0, 0, 6, 8, 3, 4, 5, 2], [0, 0, 8, 3, 4, 5, 9, 7, 2], [0, 0, 6, 8, 3, 4, 5, 7, 2], [0, 0, 1, 6, 3, 4, 5, 9, 7, 3], [0, 0, 6, 3, 4, 5, 9, 7, 2], [1, 0, 4, 2], [1, 0, 2, 4, 3], [1, 0, 1, 4, 3], [1, 0, 1, 3, 4, 4], [3, 1, 2, 1], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [2, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 7], [9, 0, 1, 2, 2], [10, 0, 1, 2]], [[6, "M50_FightUIView"], [7, "M50_FightUIView", 1, [-3, -4], [[25, 0, -2]], [18, -1, 0], [5, 750, 1334]], [10, "topUI", [-6, -7, -8, -9, -10, -11, -12], [[15, 41, 11.140999999999963, -5]], [0, "8bkbxxodpNcpnBOLn5m5Hf", 1, 0], [5, 750, 100], [0, 0.5, 1], [0, 655.859, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 1, [2, -14], [[17, 45, 750, 1334, -13]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [11, "btnpause", 2, [-16], [[22, 3, -15, [[2, "1abe3N3E6pJNKVia4ArjOis", "pauseGame", 1]], [4, 4293322470], [4, 3363338360], 2, 3]], [0, "dfUReHEtdNp6p7DWejBiVU", 1, 0], [5, 80, 80], [-297.339, -93.348, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "level", false, 2, [[23, "当前第%d关", 23, false, 1, 1, 1, -17, [6]], [24, 3, -18, [4, 4278190080]]], [0, "57+Z5SGi9Ne6qdaUOIXS/w", 1, 0], [5, 132.35, 56.4], [0, -95.986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "td_bless", false, 2, [[21, -19, [7]], [5, -20, [[2, "cc262IT+8VN4JA3vb8qu5Uj", "onClickDamagePanel", 1]]]], [0, "d8K6IRsIhKiKsaQ4hEEhXE", 1, 0], [5, 112, 95], [-275.797, -284.162, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [8, "maskbg", 1, [[14, 45, -21]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [1, "New RichText", false, 2, [[4, false, "<b><outline color=black width=2>通关主玩法第2关,游戏主界面\n点击玩法合集进入<color=#00ff00>末日屠龙</c>玩法</outline></b>", 1, 23, 30, -22]], [0, "adj8RgtcJITIZVgv9OisPx", 1, 0], [5, 334, 67.8], [0, -73.237, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "tpdg_icon_zanting", 4, [[19, -23, [0], 1]], [0, "41NQUbmGdJHJr6qSEvZucS", 1, 0], [5, 66, 65], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "test", 2, [[5, -24, [[2, "cc262IT+8VN4JA3vb8qu5Uj", "onClickTTTTTTTTTs", 1]]]], [0, "0435LBv8tMsooX44hChu1f", 1, 0], [5, 50, 50], [-227.518, -94.016, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite(Splash)", false, 2, [[20, 0, -25, [4], 5]], [0, "36kVUgFmdGt6y/6slcTPvl", 1, 0], [5, 100, 100], [-73.065, -92.346, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [12, "diff", false, 2, [[4, false, "<b><outline color=black width=3>当前难度:<color=#00ff00>普通</c>", 1, 23, 32, -26]], [0, "4dKLrwKglItL6XCQ1AP8h+", 1, 0], [5, 155.84, 40.32], [0, 0.5, 1], [0, -112.432, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "bottonUI", 3, [[16, 44, 600, -27]], [0, "f3xMePUptLpI+sbMIURS5n", 1, 0], [5, 750, 140], [0, 0.5, 0], [0, -667, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 7, 0, -2, 3, 0, 0, 2, 0, -1, 8, 0, -2, 4, 0, -3, 10, 0, -4, 11, 0, -5, 5, 0, -6, 6, 0, -7, 12, 0, 0, 3, 0, -2, 13, 0, 0, 4, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 5, 1, 2, 6, 3, 27], [0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, 2, 3, -1, 1, -1, -1], [0, 1, 1, 2, 0, 3, 0, 0]]