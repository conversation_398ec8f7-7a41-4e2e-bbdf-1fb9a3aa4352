#!/usr/bin/env node

/**
 * 配置文件压缩工具
 * 
 * 这个脚本用于将JSON格式的配置文件压缩为使用LZ-string压缩的txt格式。
 * 
 * 使用方法:
 * node compress-configs.js [options]
 * 
 * 选项:
 *   -f, --file <path>       指定要压缩的单个JSON文件路径
 *   -o, --output <path>     指定输出目录，默认为"assets/resources/config"
 *   -h, --help              显示帮助信息
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 引入项目中的LZ-string库
const lzstringPath = path.resolve(__dirname, '../assets/scripts/lzstring.js');
const lzstringContent = fs.readFileSync(lzstringPath, 'utf8');

// 创建一个模块环境来执行LZ-string库
const lzModule = { exports: {} };
const lzFunction = new Function('module', 'exports', lzstringContent);
lzFunction(lzModule, lzModule.exports);
const $lzstring = lzModule.exports;

// 解析命令行参数
const args = process.argv.slice(2);
let singleFile = null;
let outputDir = './config';

for (let i = 0; i < args.length; i++) {
  if (args[i] === '-f' || args[i] === '--file') {
    singleFile = args[i + 1];
    i++;
  } else if (args[i] === '-o' || args[i] === '--output') {
    outputDir = args[i + 1];
    i++;
  } else if (args[i] === '-h' || args[i] === '--help') {
    showHelp();
    process.exit(0);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
配置文件压缩工具

使用方法:
  node compress-configs.js [options]

选项:
  -f, --file <path>       指定要压缩的单个JSON文件路径
  -o, --output <path>     指定输出目录，默认为"./config"
  -h, --help              显示帮助信息
  `);
}

// 确保输出目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 压缩单个文件
function compressFile(filePath, outputPath) {
  try {
    console.log(`正在处理: ${filePath}`);
    
    // 读取JSON文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    try {
      // 解析JSON以确保它是有效的
      const jsonData = JSON.parse(content);
      
      // 将JSON转换为字符串
      const jsonString = JSON.stringify(jsonData);
      
      // 使用LZ-string压缩内容
      const compressed = $lzstring.compressToBase64(jsonString);
      
      if (!compressed) {
        console.error(`  错误: 无法压缩文件 ${filePath}`);
        return false;
      }
      
      // 写入压缩后的内容到文件
      fs.writeFileSync(outputPath, compressed, 'utf8');
      console.log(`  成功: 已压缩并保存到 ${outputPath}`);
      return true;
    } catch (e) {
      console.error(`  错误: 文件内容不是有效的JSON (${filePath})`);
      console.error(`  错误详情: ${e.message}`);
      return false;
    }
  } catch (e) {
    console.error(`  错误: 处理文件 ${filePath} 时出错`);
    console.error(`  错误详情: ${e.message}`);
    return false;
  }
}

// 主函数
async function main() {
  // 解压后的JSON文件目录
  const decompressedDir = path.resolve(__dirname, './json');
  
  // 输出目录
  const fullOutputDir = path.resolve(__dirname, outputDir);
  ensureDirectoryExists(fullOutputDir);
  
  console.log(`配置文件压缩工具`);
  console.log(`输出目录: ${fullOutputDir}`);
  
  let files = [];
  
  if (singleFile) {
    // 处理单个文件
    const fullPath = path.resolve(process.cwd(), singleFile);
    if (!fs.existsSync(fullPath)) {
      console.error(`错误: 文件不存在 ${fullPath}`);
      process.exit(1);
    }
    files.push(fullPath);
  } else {
    // 处理所有解压后的JSON文件
    if (!fs.existsSync(decompressedDir)) {
      console.error(`错误: 解压目录不存在 ${decompressedDir}`);
      process.exit(1);
    }
    
    const allFiles = fs.readdirSync(decompressedDir);
    files = allFiles
      .filter(file => file.endsWith('.json') && !file.endsWith('.raw.txt'))
      .map(file => path.join(decompressedDir, file));
  }
  
  console.log(`找到 ${files.length} 个文件需要处理\n`);
  
  let successCount = 0;
  let failCount = 0;
  
  // 处理所有文件
  for (const file of files) {
    const fileName = path.basename(file, '.json');
    const outputPath = path.join(fullOutputDir, `${fileName}.txt`);
    
    const success = compressFile(file, outputPath);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log(`\n处理完成!`);
  console.log(`成功: ${successCount} 个文件`);
  console.log(`失败: ${failCount} 个文件`);
}

// 运行主函数
main().catch(err => {
  console.error('发生错误:', err);
  process.exit(1);
});