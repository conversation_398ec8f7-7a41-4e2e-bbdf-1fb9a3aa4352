[1, ["ecpdLyjvZBwrvm+cedCcQy", "c652mbNl9J4a8bhnXtvZUo", "f7293wEF9JhIuMEsrLuqng", "cb1VFt7lZM5bK3HPiU1WUY", "ebJXGx5cNJ+b1BpmgufQFT", "da1MOW0vBAXbRiTUTJrnjX", "afCtKfv95IoaVflTUGdRyW", "64X0E40SpNHpDAsB/BcRaH", "cdvDZRLPZKb70zilA0TpQ8", "e52TQxfs9FgYg1TfDFagDC", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "efXBJkjZVIELqSE5w63CwO", "f8npR6F8ZIZoCp3cKXjJQz", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "73jGpne/9JUI/171Zur5Pr", "717g+vrGpOc7Vyj3dplAH9", "65hdeSelhOVq5sz7eNtoFd", "9cpQ3wcGlFtKTW+u7dowuj", "3dx1OyHXxL7JKFCbx/bLMu", "bcSUl7PiRDErqcyX1BDORs", "b1GZGSsftGg6dNJAM9m6pt", "b9CtqqKmNE64nnbTzPOg0L", "19ObL9XKNNF5F6Sf9ExtqF", "1f3LBOiL9P1pFPKez2BEe+", "ad9he8yMNMGo4tdoQfxDYb", "c7W9GILNlE4KfkN+xAdI7o", "c6sOQBnydEF4L89vME95uo", "2addB3MqlFl48zEG2nRhH5", "96D9Wi185FO52mThA6NJ4C", "37gU2p1G5Jw6NhSGenZjhG"], ["node", "_spriteFrame", "_N$file", "_N$target", "checkMark", "_textureSetter", "_parent", "root", "asset", "toggleC<PERSON><PERSON>", "my<PERSON>rid<PERSON>iew", "_N$barSprite", "_N$content", "data", "_N$disabledSprite", "templete"], [["cc.Node", ["_name", "_active", "_opacity", "_groupIndex", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -2, 4, 5, 9, 1, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_enabled", "_top", "_bottom", "alignMode", "node"], -4, 1], ["cc.Label", ["_N$horizontalAlign", "_N$verticalAlign", "_string", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_lineHeight", "_N$overflow", "_enableWrapText", "_N$cacheMode", "_N$fontFamily", "node", "_materials", "_N$file"], -8, 1, 3, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent"], 1, 12, 4, 5, 7, 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_N$layoutType", "_N$paddingTop", "_N$spacingY", "_N$spacingX", "node", "_layoutSize"], -1, 1, 5], ["cc.Toggle", ["zoomScale", "_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 0, 1, 5, 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cf163TZdwhCbpx0iLbmVmE1", ["node", "nodeArr", "labelArr", "my<PERSON>rid<PERSON>iew", "toggleC<PERSON><PERSON>"], 3, 1, 2, 2, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ToggleContainer", ["node"], 3, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content"], 3, 1, 5, 1]], [[7, 0, 1, 2, 2], [2, 1, 3, 4, 5, 2], [0, 0, 8, 7, 5, 6, 9, 2], [2, 3, 4, 5, 1], [0, 0, 1, 8, 7, 5, 6, 9, 3], [0, 0, 8, 10, 7, 5, 6, 9, 2], [18, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4], [0, 0, 8, 10, 7, 5, 6, 2], [2, 0, 1, 3, 4, 5, 3], [4, 2, 3, 6, 4, 5, 0, 1, 11, 12, 13, 8], [0, 0, 8, 7, 5, 6, 2], [0, 0, 1, 2, 8, 7, 5, 11, 6, 4], [0, 0, 1, 8, 7, 5, 6, 3], [2, 0, 1, 2, 3, 4, 4], [6, 0, 1, 2, 2], [3, 3, 0, 4, 5, 1, 2, 7, 7], [3, 3, 0, 1, 2, 7, 5], [4, 2, 3, 4, 5, 0, 1, 11, 12, 13, 7], [5, 0, 1, 7, 6, 2, 3, 4, 5, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 4], [0, 0, 8, 10, 5, 9, 2], [0, 4, 0, 8, 5, 9, 3], [5, 0, 7, 6, 2, 3, 4, 5, 2], [14, 0, 1, 2, 3, 3], [3, 0, 7, 2], [3, 0, 1, 2, 7, 4], [11, 0, 2], [0, 0, 3, 10, 7, 5, 6, 3], [0, 0, 10, 5, 6, 2], [0, 0, 10, 7, 5, 6, 9, 2], [0, 0, 10, 5, 9, 2], [0, 0, 1, 8, 10, 7, 5, 6, 3], [0, 0, 8, 10, 7, 5, 6, 12, 9, 2], [0, 0, 8, 7, 5, 6, 12, 9, 2], [0, 0, 2, 8, 7, 5, 11, 6, 3], [0, 0, 1, 8, 7, 5, 11, 6, 9, 3], [5, 0, 6, 2, 3, 4, 5, 2], [5, 0, 7, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3, 4, 5, 6, 2], [13, 0, 1, 2, 3, 4, 1], [7, 1, 2, 1], [2, 0, 3, 4, 5, 2], [2, 0, 1, 3, 4, 3], [8, 0, 1, 2, 4, 5, 4], [8, 0, 3, 4, 5, 3], [9, 0, 1, 3, 4, 5, 6, 7, 3], [10, 0, 1, 3, 3], [6, 0, 1, 2, 3, 4, 5, 6, 2], [6, 1, 2, 1], [3, 6, 0, 1, 7, 4], [15, 0, 1, 2, 3, 3], [16, 0, 1, 2, 3, 4, 5, 6, 6], [17, 0, 1, 2, 2], [4, 2, 3, 6, 4, 5, 0, 1, 7, 11, 12, 13, 9], [4, 2, 8, 4, 0, 1, 7, 9, 11, 12, 8], [4, 3, 6, 5, 0, 1, 10, 11, 12, 7], [19, 0, 1], [20, 0, 1, 2, 1]], [[[[27, "SevenTaskView"], [28, "SevenTaskView", 1, [-11, -12], [[40, -10, [-5, -6, -7, -8, -9], [-4], -3, -2]], [41, -1, 0], [5, 750, 1334]], [37, "img_tcd01", [-16, -17, -18, -19, -20, -21, -22], [[[9, 1, 0, -13, [152], 153], -14, [44, 2, 28, 32, -15, [5, 136, 769]]], 4, 1, 4], [0, "8foK6+tTxLSr/VngMztlVl", 1, 0], [5, 136, 769], [-263, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "content", [-23, -24, -25, -26, -27, -28, 2, -29, -30], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 710, 1080]], [30, "proNode", [-32, -33, -34, -35, -36, -37, -38], [[45, 1, 40, -31, [5, 554, 100]]], [0, "dfUWU7tkdCVqmOXrTxg3Re", 1, 0], [5, 554, 100], [-46.019, -14.262, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "toggle1", 2, [-42, -43, -44, -45, -46, -47], [[46, 1.1, 3, -41, [4, 4292269782], -40, -39, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "1", 1]]]], [0, "4d/nXLQOFIQaGczxTUDcBv", 1, 0], [5, 116, 52], [0, 330.5, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "toggle2", 2, [-51, -52, -53, -54, -55, -56], [[20, 1.1, 3, false, -50, [4, 4292269782], -49, -48, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "2", 1]]]], [0, "53X/zFUYJO7KReGP9L+YtR", 1, 0], [5, 116, 52], [0, 246.5, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "toggle3", 2, [-60, -61, -62, -63, -64, -65], [[20, 1.1, 3, false, -59, [4, 4292269782], -58, -57, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "3", 1]]]], [0, "f9wXtlBr9NlbeWFkwAobap", 1, 0], [5, 116, 52], [0, 162.5, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "toggle4", 2, [-69, -70, -71, -72, -73, -74], [[20, 1.1, 3, false, -68, [4, 4292269782], -67, -66, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "4", 1]]]], [0, "99R5IWDatFZ5Hzb1A94akw", 1, 0], [5, 116, 52], [0, 78.5, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "toggle5", 2, [-78, -79, -80, -81, -82, -83], [[20, 1.1, 3, false, -77, [4, 4292269782], -76, -75, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "5", 1]]]], [0, "4dyRht9vBPv6f7VqMNZ++B", 1, 0], [5, 116, 52], [0, -5.5, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "toggle6", 2, [-87, -88, -89, -90, -91, -92], [[20, 1.1, 3, false, -86, [4, 4292269782], -85, -84, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "6", 1]]]], [0, "06aArWYoZGALg6Anx5uz9u", 1, 0], [5, 116, 52], [0, -89.5, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "toggle7", 2, [-96, -97, -98, -99, -100, -101], [[20, 1.1, 3, false, -95, [4, 4292269782], -94, -93, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onToggle", "7", 1]]]], [0, "beeGkzLYNLzbuuN80zuNbe", 1, 0], [5, 116, 52], [0, -173.5, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "title_bg", 3, [-103, -104, -105], [[9, 1, 0, -102, [12], 13]], [0, "45NuY+WQ9L5J6TOpjXxAQa", 1, 0], [5, 715, 1080]], [5, "Background", 12, [-109], [[42, 1, -106, [9], 10], [48, 3, -108, [[47, "cf163TZdwhCbpx0iLbmVmE1", "close", 1]], [4, 4293322470], [4, 3363338360], -107, 11]], [0, "90OlI7eu5N6YuKmHM/sieV", 1, 0], [5, 64, 65], [304.512, 499.402, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "img_djsd", 3, [-111, -112, -113], [[9, 1, 0, -110, [23], 24]], [0, "adIpT26d5Cl7d2N+1WA4lj", 1, 0], [5, 330, 40], [-152.287, 433.172, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "reNd", [-114, -115, -116], [0, "f6FXSPyfpHtIW8HDTPQZEQ", 1, 0], [242.663, -23.897, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "rewardGoods", false, 3, [-119], [[25, 45, -117], [49, -118, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "reNd", 1]]]], [0, "c7Y9+MoMRNdLihVn0crCBa", 1, 0], [5, 710, 1080]], [21, "reNd", 16, [-120, -121, -122], [0, "4avrTwRGtIWroeuvPV8Dzg", 1, 0], [0, -23, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "taskDayily", 3, [-123, 4, 15], [0, "ddqt82fK9Jup2wsJk6QT/E", 1, 0], [0, 327.82, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "proBar", 18, [-127], [[9, 1, 0, -124, [26], 27], [51, 554, 0.5, -126, -125]], [0, "02c7skPjxDR5lzCFzDVkTz", 1, 0], [5, 554, 34], [-19.756, -32.744, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_jt", 4, [-130], [[1, 0, -128, [34], 35], [15, 3, -129, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "box1", 1]]]], [0, "30xWpeQAVFQIzusBgax7M9", 1, 0], [5, 50, 45], [-162, -10, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "img_jt", 4, [-133], [[1, 0, -131, [38], 39], [15, 3, -132, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "box2", 1]]]], [0, "18C4idPhJDppU2TMhAI9cw", 1, 0], [5, 50, 45], [-72, -10, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "img_jt", 4, [-136], [[1, 0, -134, [42], 43], [15, 3, -135, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "box3", 1]]]], [0, "7cwtSUaCJCtY7qUb1ODKGD", 1, 0], [5, 50, 45], [18, -10, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "img_jt", 4, [-139], [[1, 0, -137, [46], 47], [15, 3, -138, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "box4", 1]]]], [0, "7fhQXvEgdOorWhc5leZV8p", 1, 0], [5, 50, 45], [108, -10, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "img_jt", 4, [-142], [[1, 0, -140, [50], 51], [15, 3, -141, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "box5", 1]]]], [0, "786VW+w35AK7d2gWjxRsLm", 1, 0], [5, 50, 45], [198, -10, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "img_jt", 4, [-145], [[1, 0, -143, [54], 55], [15, 3, -144, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "box6", 1]]]], [0, "71x9ES1p1E3apHAI2cF3sV", 1, 0], [5, 50, 45], [288, -10, 0, 0, 0, 0, 1, 1, 1, 0]], [23, "checkmark", 5, [-148], [[-146, [16, false, 45, -9.25, 9.25, 40, 40, -147]], 1, 4], [0, "5domeB9q9FBb+ct6cqL7XU", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "checkmark", false, 6, [-151], [[-149, [16, false, 45, -9.25, 9.25, 40, 40, -150]], 1, 4], [0, "9b0oz3JuxMWpX5T8hEQytd", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "checkmark", false, 7, [-154], [[-152, [16, false, 45, -9.25, 9.25, 40, 40, -153]], 1, 4], [0, "b8o0zgXZNBY4Tv1nHgWMtc", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "checkmark", false, 8, [-157], [[-155, [16, false, 45, -9.25, 9.25, 40, 40, -156]], 1, 4], [0, "5bHKvXl4lJ25yYj/F/TGV0", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "checkmark", false, 9, [-160], [[-158, [16, false, 45, -9.25, 9.25, 40, 40, -159]], 1, 4], [0, "1eUqW9/zZGuoVMEs0kU/gq", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "checkmark", false, 10, [-163], [[-161, [16, false, 45, -9.25, 9.25, 40, 40, -162]], 1, 4], [0, "beS0W5s/xBYqmAwhkZgO3o", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "checkmark", false, 11, [-166], [[-164, [16, false, 45, -9.25, 9.25, 40, 40, -165]], 1, 4], [0, "c2A0NFSwREy7opiAkT4tvV", 1, 0], [5, 160, 70], [11.702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "New ScrollView", 3, [-170], [[[52, false, 0.75, 0.23, null, null, -168, -167], -169], 4, 1], [0, "c16wooUcRFu7atWQTNsgll", 1, 0], [5, 500, 746], [81.384, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "view", 33, [-173], [[53, 0, -171, [154]], [26, 45, 240, 250, -172]], [0, "f6rJOdci5LpqR2itdXzuYz", 1, 0], [5, 500, 746], [0, 0.5, 1], [0, 373, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "content", 34, [[50, 0, 41, 614, -174]], [0, "ecKprdHUBMRoHqCj7C6P4V", 1, 0], [5, 500, 274], [0, 0, 1], [-250, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "maskbg", 230, 1, [[25, 45, -175], [1, 0, -176, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [8, "bg", 1, [3], [[26, 45, 750, 1334, -177]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [2, "Label_title", 12, [[54, "七日嘉年华", 48, 48, false, 1, 1, 1, 2, -178, [6], 7], [6, 3, -179, [4, 4278190080]]], [0, "6aRvpyzjtLSqbRuVrwAnNW", 1, 0], [5, 285, 56.4], [0, 509.067, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_cw_bz", 14, [[3, -180, [18], 19], [15, 3, -181, [[7, "cf163TZdwhCbpx0iLbmVmE1", "onBtn", "info", 1]]]], [0, "b6QWo+iaJBSK6D6NorjS1w", 1, 0], [5, 62, 44], [144.692, 1.952, 0, 0, 0, 0, 1, 1, 1, 0]], [38, "lbTime", 14, [[-182, [6, 2, -183, [4, 4278190080]]], 1, 4], [0, "edEpEiNCRMHIeGPzZlDr8C", 1, 0], [5, 4, 34.239999999999995], [32.947, 0.826, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbNe", 14, [[10, "结束时间：", 22, 24, false, 1, 1, 1, -184, [21], 22], [6, 2, -185, [4, 4278190080]]], [0, "63hhbORxlOOalxyzNsSIKj", 1, 0], [5, 114, 34.239999999999995], [-89.466, 0.826, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_jt", 4, [-187], [[1, 0, -186, [30], 31]], [0, "35EFekxb5EAounTnl4sDoy", 1, 0], [5, 50, 60], [-252, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 42, [[10, "0", 18, 20, false, 1, 1, 1, -188, [28], 29], [6, 2, -189, [4, 4278190080]]], [0, "47qLRkD0FMGYBqZ09wPE7v", 1, 0], [5, 13.99, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 20, [[10, "20", 18, 20, false, 1, 1, 1, -190, [32], 33], [6, 2, -191, [4, 4278190080]]], [0, "9fBSaO2dlMCIU0YqV5G4an", 1, 0], [5, 23.98, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 21, [[10, "20", 18, 20, false, 1, 1, 1, -192, [36], 37], [6, 2, -193, [4, 4278190080]]], [0, "dcT4cjIDFNm5Hg0eq877hr", 1, 0], [5, 23.98, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 22, [[10, "20", 18, 20, false, 1, 1, 1, -194, [40], 41], [6, 2, -195, [4, 4278190080]]], [0, "6f+gDVtddKfq1jkaIVtADM", 1, 0], [5, 23.98, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 23, [[10, "20", 18, 20, false, 1, 1, 1, -196, [44], 45], [6, 2, -197, [4, 4278190080]]], [0, "a8fZ4CbiVNDJR+4dz/F6Vb", 1, 0], [5, 23.98, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 24, [[10, "20", 18, 20, false, 1, 1, 1, -198, [48], 49], [6, 2, -199, [4, 4278190080]]], [0, "a0A8Hr75hHirtPMEQZsw01", 1, 0], [5, 23.98, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pecValue", 25, [[10, "20", 18, 20, false, 1, 1, 1, -200, [52], 53], [6, 2, -201, [4, 4278190080]]], [0, "c754rcINZDvYI6WpyCUW7C", 1, 0], [5, 23.98, 29.2], [0, -19.36, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 512, "RewardItem", 15, [24, "23KX3a51xHK7ooFecFwTrX", true, -202, 58], [0, 99.301, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 5, [-204], [[17, false, 45, 40, 40, -203]], [0, "6cdWBh/FJHuJ8ykojFf0wO", 1, 0], [5, 120, 70]], [2, "tgNe", 5, [[18, "1 天", 35, false, 1, 1, 1, -205, [66], 67], [6, 2, -206, [4, 4278190080]]], [0, "eez6/6FwtFC7grluQqkobu", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 6, [-208], [[17, false, 45, 40, 40, -207]], [0, "1aFaZIPipPlITilRLW74IH", 1, 0], [5, 120, 70]], [2, "tgNe", 6, [[18, "1 天", 35, false, 1, 1, 1, -209, [79], 80], [6, 2, -210, [4, 4278190080]]], [0, "1aCOr8eQhPa5IRXJJ/pw99", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 7, [-212], [[17, false, 45, 40, 40, -211]], [0, "9bY7lfiEBIypSGUOYOeS6O", 1, 0], [5, 120, 70]], [2, "tgNe", 7, [[18, "1 天", 35, false, 1, 1, 1, -213, [92], 93], [6, 2, -214, [4, 4278190080]]], [0, "652uVCfdtI2p0O9MvrexFu", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 8, [-216], [[17, false, 45, 40, 40, -215]], [0, "33Y3QYv81DcYMxHIxD+g3P", 1, 0], [5, 120, 70]], [2, "tgNe", 8, [[18, "1 天", 35, false, 1, 1, 1, -217, [105], 106], [6, 2, -218, [4, 4278190080]]], [0, "7f2tHX3X5Ifo+DF5v3YWfx", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 9, [-220], [[17, false, 45, 40, 40, -219]], [0, "70XzEQVINC+pH0YxH5MyeK", 1, 0], [5, 120, 70]], [2, "tgNe", 9, [[18, "1 天", 35, false, 1, 1, 1, -221, [118], 119], [6, 2, -222, [4, 4278190080]]], [0, "8ey9WvVCpEzI4sOp1GKbJo", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 10, [-224], [[17, false, 45, 40, 40, -223]], [0, "60dDEEvKtGd4yYBO+ofO/C", 1, 0], [5, 120, 70]], [2, "tgNe", 10, [[18, "1 天", 35, false, 1, 1, 1, -225, [131], 132], [6, 2, -226, [4, 4278190080]]], [0, "b6hbb22TlJT61tXQe6vCy1", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 11, [-228], [[17, false, 45, 40, 40, -227]], [0, "1e8GGdb09BHpkZVXpwTUk0", 1, 0], [5, 120, 70]], [2, "tgNe", 11, [[18, "1 天", 35, false, 1, 1, 1, -229, [144], 145], [6, 2, -230, [4, 4278190080]]], [0, "38GM/NlNZAM7jWH2zzJor/", 1, 0], [5, 66.25999999999999, 54.4], [0, 0.159, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 512, "RewardItem", 17, [24, "23KX3a51xHK7ooFecFwTrX", true, -231, 157], [0, 124.639, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg", 3, [[9, 1, 0, -232, [2], 3]], [0, "32XWbHc3VCJoxAH6kEEwjT", 1, 0], [5, 680, 914], [0, -72.512, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_js_btqz5", false, 12, [[3, -233, [4], 5]], [0, "b81Xl/xBVOgrYGnQaE0S6g", 1, 0], [5, 472, 103], [0, 530, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "Label", false, 13, [[55, "返回", false, false, 1, 1, 1, 1, -234, [8]]], [0, "32xj1jPPxLkrFw1riXxUhd", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "banner_qrjnh", false, 3, [[3, -235, [14], 15]], [0, "547He41G5OQqOfGrdnVcfl", 1, 0], [5, 712, 242], [0, 344.585, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "fkbg_02", 3, [[9, 1, 0, -236, [16], 17]], [0, "ba3dgY0OJGsr6W/o7C1onV", 1, 0], [5, 680, 780], [0, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [56, 22, 24, 1, 1, 1, "Consola", 40, [20]], [39, "bar", 19, [-237], [0, "55axI/8t1C16I6lZtMSnvp", 1, 0], [5, 277, 30], [0, 0, 0.5], [-277, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 1, 0, 72, [25]], [2, "img_tipsd", 15, [[9, 1, 0, -238, [56], 57]], [0, "ecxaXGrl5CwI5W9367KOtx", 1, 0], [5, 133.4, 135.5], [-0.3, 90.962, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_jt", false, 15, [[3, -239, [59], 60]], [0, "40T5QFwtVAcZFZNNWDrKGk", 1, 0], [5, 47, 38], [0, 48.901, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "icon_store", 51, [[1, 0, -240, [61], 62]], [0, "d0+A1tXX9KOarFsyvO+O45", 1, 0], [5, 120, 70]], [4, "icon_store", false, 26, [[3, -241, [63], 64]], [0, "2cu3BSYmxOfq0mmOkuB7ki", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 26, [65]], [12, "glay", false, 155, 5, [[1, 0, -242, [68], 69]], [0, "90VdGjEL9DiqBT36Ai+4dT", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 5, [[3, -243, [70], 71]], [0, "46PzakTEJOIZEH3pOEtqYA", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 5, [[1, 0, -244, [72], 73]], [0, "f2T1OkfDxAsqzeCFgQE/80", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [11, "icon_store", 53, [[1, 0, -245, [74], 75]], [0, "bdlXgoEENFA40g230jKMEl", 1, 0], [5, 120, 70]], [4, "icon_store", false, 27, [[3, -246, [76], 77]], [0, "b6YZfcA/lERItaT0Ypw3L4", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 27, [78]], [12, "glay", false, 155, 6, [[1, 0, -247, [81], 82]], [0, "63M9hJRa9NlJwk3Cp5AG7a", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 6, [[3, -248, [83], 84]], [0, "1fhfYU2+pHm4mwRRVP/24j", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 6, [[1, 0, -249, [85], 86]], [0, "b89ybFKTVN7YIk1chGKIbG", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [11, "icon_store", 55, [[1, 0, -250, [87], 88]], [0, "66wxkosHdNLpIpm0hqS03a", 1, 0], [5, 120, 70]], [4, "icon_store", false, 28, [[3, -251, [89], 90]], [0, "36oH7c2xlLKI0LSvqeHRVt", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 28, [91]], [12, "glay", false, 155, 7, [[1, 0, -252, [94], 95]], [0, "7fHGXO1P1I8Jw06/TYE1bz", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 7, [[3, -253, [96], 97]], [0, "7fcSSRKXhHKYv9A5ovX7Zo", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 7, [[1, 0, -254, [98], 99]], [0, "61zWOHC2BAnJs9uWKz7puG", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [11, "icon_store", 57, [[1, 0, -255, [100], 101]], [0, "bd7LIgMeROJrfaP0k+DL4h", 1, 0], [5, 120, 70]], [4, "icon_store", false, 29, [[3, -256, [102], 103]], [0, "28ougedgFGJav3POdbPD+P", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 29, [104]], [12, "glay", false, 155, 8, [[1, 0, -257, [107], 108]], [0, "92o2rUso9LZaPM1r4kRjCu", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 8, [[3, -258, [109], 110]], [0, "10XS/g8j5FxY4PNzQIDcV4", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 8, [[1, 0, -259, [111], 112]], [0, "ebf0JKnqtDP7Vtz37Pr7JC", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [11, "icon_store", 59, [[1, 0, -260, [113], 114]], [0, "61c86/GKdKe4uOf/0pUQct", 1, 0], [5, 120, 70]], [4, "icon_store", false, 30, [[3, -261, [115], 116]], [0, "beGTcwMX5IX4rBfhC2vwsy", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 30, [117]], [12, "glay", false, 155, 9, [[1, 0, -262, [120], 121]], [0, "0boSd76g5N15BrPmBHGkQz", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 9, [[3, -263, [122], 123]], [0, "a9nwLWKHNE47WAqCZWNamn", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 9, [[1, 0, -264, [124], 125]], [0, "8fge35xntOzKfhR2AIjJaN", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [11, "icon_store", 61, [[1, 0, -265, [126], 127]], [0, "bfRbLsPaFGiKZcCtuGmYVC", 1, 0], [5, 120, 70]], [4, "icon_store", false, 31, [[3, -266, [128], 129]], [0, "8fP7DYz9VNMrh/9sAA4i0S", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 31, [130]], [12, "glay", false, 155, 10, [[1, 0, -267, [133], 134]], [0, "a24FGVLS9KybmoIYB3pj2s", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 10, [[3, -268, [135], 136]], [0, "7fzvjssAdCfI5uWQYeiwEb", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 10, [[1, 0, -269, [137], 138]], [0, "35AbnD7Q1GnpKJtFF9MT9Y", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [11, "icon_store", 63, [[1, 0, -270, [139], 140]], [0, "24/JlpNXFHfLhuWJS5QRDK", 1, 0], [5, 120, 70]], [4, "icon_store", false, 32, [[3, -271, [141], 142]], [0, "59WltlxxNE2Lppz1YLwQeI", 1, 0], [5, 122, 62], [0, 0, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [14, 1, 0, false, 32, [143]], [12, "glay", false, 155, 11, [[1, 0, -272, [146], 147]], [0, "7dNi3u5oJHxbGvlyIxFy5S", 1, 0], [4, 4278190080], [5, 120, 70]], [13, "lock", false, 11, [[3, -273, [148], 149]], [0, "21U7M4Z4pO/qTOMp3O6X8n", 1, 0], [5, 34, 41]], [4, "ggtipicon", false, 11, [[1, 0, -274, [150], 151]], [0, "33g0/5LEBOC48nYUh8PDy4", 1, 0], [5, 45, 45], [70, 35, 0, 0, 0, 0, 1, 1, 1, -1]], [57, 2], [58, 33, [0, 0, 20], 35], [2, "img_tipsd", 17, [[9, 1, 0, -275, [155], 156]], [0, "05sQYv9u9EWYt8aHLUQUBQ", 1, 0], [5, 138, 138], [0, 117.501, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_jt", false, 17, [[3, -276, [158], 159]], [0, "2ctSrpBvhKvreJiiGD1JH8", 1, 0], [5, 47, 38], [0, 48.901, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 7, 1, 0, 9, 118, 0, 10, 119, 0, -1, 71, 0, -1, 16, 0, -2, 17, 0, -3, 65, 0, -4, 15, 0, -5, 50, 0, 0, 1, 0, -1, 36, 0, -2, 37, 0, 0, 2, 0, -2, 118, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, -1, 66, 0, -2, 12, 0, -3, 69, 0, -4, 70, 0, -5, 14, 0, -6, 18, 0, -8, 33, 0, -9, 16, 0, 0, 4, 0, -1, 42, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -5, 23, 0, -6, 24, 0, -7, 25, 0, 4, 78, 0, 3, 5, 0, 0, 5, 0, -1, 51, 0, -2, 26, 0, -3, 52, 0, -4, 79, 0, -5, 80, 0, -6, 81, 0, 4, 84, 0, 3, 6, 0, 0, 6, 0, -1, 53, 0, -2, 27, 0, -3, 54, 0, -4, 85, 0, -5, 86, 0, -6, 87, 0, 4, 90, 0, 3, 7, 0, 0, 7, 0, -1, 55, 0, -2, 28, 0, -3, 56, 0, -4, 91, 0, -5, 92, 0, -6, 93, 0, 4, 96, 0, 3, 8, 0, 0, 8, 0, -1, 57, 0, -2, 29, 0, -3, 58, 0, -4, 97, 0, -5, 98, 0, -6, 99, 0, 4, 102, 0, 3, 9, 0, 0, 9, 0, -1, 59, 0, -2, 30, 0, -3, 60, 0, -4, 103, 0, -5, 104, 0, -6, 105, 0, 4, 108, 0, 3, 10, 0, 0, 10, 0, -1, 61, 0, -2, 31, 0, -3, 62, 0, -4, 109, 0, -5, 110, 0, -6, 111, 0, 4, 114, 0, 3, 11, 0, 0, 11, 0, -1, 63, 0, -2, 32, 0, -3, 64, 0, -4, 115, 0, -5, 116, 0, -6, 117, 0, 0, 12, 0, -1, 67, 0, -2, 38, 0, -3, 13, 0, 0, 13, 0, 3, 13, 0, 0, 13, 0, -1, 68, 0, 0, 14, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, -1, 74, 0, -2, 50, 0, -3, 75, 0, 0, 16, 0, 0, 16, 0, -1, 17, 0, -1, 120, 0, -2, 65, 0, -3, 121, 0, -1, 19, 0, 0, 19, 0, 11, 73, 0, 0, 19, 0, -1, 72, 0, 0, 20, 0, 0, 20, 0, -1, 44, 0, 0, 21, 0, 0, 21, 0, -1, 45, 0, 0, 22, 0, 0, 22, 0, -1, 46, 0, 0, 23, 0, 0, 23, 0, -1, 47, 0, 0, 24, 0, 0, 24, 0, -1, 48, 0, 0, 25, 0, 0, 25, 0, -1, 49, 0, -1, 78, 0, 0, 26, 0, -1, 77, 0, -1, 84, 0, 0, 27, 0, -1, 83, 0, -1, 90, 0, 0, 28, 0, -1, 89, 0, -1, 96, 0, 0, 29, 0, -1, 95, 0, -1, 102, 0, 0, 30, 0, -1, 101, 0, -1, 108, 0, 0, 31, 0, -1, 107, 0, -1, 114, 0, 0, 32, 0, -1, 113, 0, 12, 35, 0, 0, 33, 0, -2, 119, 0, -1, 34, 0, 0, 34, 0, 0, 34, 0, -1, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, -1, 71, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, -1, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 7, 50, 0, 0, 51, 0, -1, 76, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, -1, 82, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, -1, 88, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, -1, 94, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, -1, 100, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, -1, 106, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, -1, 112, 0, 0, 64, 0, 0, 64, 0, 7, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, -1, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 120, 0, 0, 121, 0, 13, 1, 2, 6, 3, 3, 6, 37, 4, 6, 18, 15, 6, 18, 276], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 73, 78, 84, 90, 96, 102, 108, 114, 119], [-1, 1, -1, 1, -1, 1, -1, 2, -1, -1, 1, 14, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, 8, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 8, -1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15], [0, 10, 0, 11, 0, 12, 0, 13, 0, 0, 14, 15, 0, 16, 0, 17, 0, 18, 0, 19, 0, 0, 2, 0, 20, 0, 0, 21, 0, 2, 0, 22, 0, 2, 0, 6, 0, 2, 0, 6, 0, 2, 0, 6, 0, 2, 0, 6, 0, 2, 0, 6, 0, 2, 0, 6, 0, 7, 8, 0, 9, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 3, 0, 4, 0, 23, 0, 0, 7, 8, 0, 9, 24, 5, 5, 5, 5, 5, 5, 5, 25]], [[{"name": "progress_arm_bar", "rect": [5, 5, 85, 18], "offset": [0, 0], "originalSize": [95, 28], "capInsets": [13, 8, 12, 8]}], [1], 0, [0], [5], [26]], [[{"name": "banner_qrjnh", "rect": [0, 0, 712, 242], "offset": [0, 0], "originalSize": [712, 242], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [27]], [[{"name": "boxicon3_01", "rect": [0, 0, 100, 67], "offset": [0, 0], "originalSize": [100, 67], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [28]], [[{"name": "progress_hp_di", "rect": [1, 1, 93, 26], "offset": [0, 0], "originalSize": [95, 28], "capInsets": [14, 12, 16, 12]}], [1], 0, [0], [5], [29]], [[{"name": "btn_qr_wxz", "rect": [0, 0, 122, 62], "offset": [0, 0], "originalSize": [122, 62], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [30]], [[{"name": "btn_qr_xz", "rect": [0, 0, 145, 62], "offset": [0, 0], "originalSize": [145, 62], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [31]]]]