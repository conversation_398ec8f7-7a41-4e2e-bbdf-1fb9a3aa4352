[1, ["ecpdLyjvZBwrvm+cedCcQy", "71hAiNEz1NnL0peBNRF4KU", "81WuzqDTVGz5tqEnPIL9aY", "b1sfs/71dH36xFdx2cteMF", "4fyn6BKSdOWZDaD/Dxh9EK", "97YNGC1C1I2pqJZCjZoytn", "79pSzulgNP44HUqOy1seBN", "7aSL72M71OCKrrZfvV9xtf", "fcj/6RA1tIM6VnUdNsH/6n", "04OyNr4fZFpImoYalJ66/j", "8bQGcy865Cma4PNF5u8Gep"], ["node", "_spriteFrame", "_textureSetter", "root", "target", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["fcecfrPbX1MOYHZOs2XEqen", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -4, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[3, 0, 1, 2, 2], [0, 0, 6, 4, 3, 5, 7, 2], [0, 0, 6, 4, 3, 5, 2], [2, 2, 3, 4, 1], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [9, 0, 1, 2, 2], [2, 0, 2, 3, 4, 2], [4, 0, 2], [0, 0, 1, 8, 4, 3, 5, 7, 3], [0, 0, 6, 8, 3, 2], [0, 0, 6, 4, 3, 5, 9, 7, 2], [0, 0, 2, 6, 4, 3, 5, 7, 3], [5, 0, 1], [6, 0, 1, 1], [7, 0, 1, 2, 3], [3, 1, 2, 1], [2, 1, 0, 2, 3, 4, 3]], [[[{"name": "icon_cwtb_101", "rect": [24, 21, 80, 87], "offset": [0, -0.5], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [1]], [[[7, "PetInfoItem"], [8, "PetInfoItem", 1, [-5, -6, -7, -8, -9, -10, -11], [[12, -2], [13, -4, [[14, "fcecfrPbX1MOYHZOs2XEqen", "onClickItem", -3]]]], [15, -1, 0], [5, 128, 128], [-296, -74, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "czNd", 1, [-12, -13], [0, "e8cuOuMy1Jp4FRFgMcX6vF", 1, 0]], [1, "lbFt", 2, [[4, "出战", 20, 20, false, 1, 2, 1, -14, [8]], [5, 2, -15, [4, 4278190080]]], [0, "7eWJ71LEtGlLOsb5+x+HHG", 1, 0], [5, 44, 29.2], [0, -47.628, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbLv", 1, [[4, "等级2", 18, 20, false, 1, 2, 1, -16, [9]], [5, 2, -17, [4, 4278190080]]], [0, "22rb1WCJdObqtmq+Xu57Bi", 1, 0], [5, 50.01, 29.2], [0, 1, 0.5], [47.161, 39.765, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_icon_xz", 1, [[16, 1, 0, -18, [0], 1]], [0, "04A6rGRTRCvKBCREsgmRHJ", 1, 0], [5, 136, 136]], [2, "imgBg", 1, [[6, 0, -19, [2], 3]], [0, "a9WfiV2ptHeYetlmDYFT6B", 1, 0], [5, 128, 128]], [2, "icon", 1, [[3, -20, [4], 5]], [0, "1e34+vSVRNNJg55xwAlVii", 1, 0], [5, 80, 87]], [1, "img_cw_jdt01", 2, [[6, 0, -21, [6], 7]], [0, "a2YQElwGRIVL45BZ3ihged", 1, 0], [5, 122, 30], [0, -50.321, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "imgGou", 1, [[3, -22, [10], 11]], [0, "309HjQRlZJgbn26AgjB0FG", 1, 0], [5, 37, 37], [49.878, -48.878, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "ggtipicon", false, 1, [[3, -23, [12], 13]], [0, "21IBEeKZtNP5qLXMmum0/H", 1, 0], [5, 44, 43], [49, 58.428, 0, 0, 0, 0, 1, 0.5, 0.5, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 1, 0, 0, 1, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 2, 0, -5, 4, 0, -6, 9, 0, -7, 10, 0, -1, 8, 0, -2, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 5, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1], [0, 2, 0, 3, 0, 4, 0, 5, 0, 0, 0, 6, 0, 7]], [[{"name": "img_cw_dh", "rect": [0, 0, 37, 37], "offset": [0, 0], "originalSize": [37, 37], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[{"name": "img_cw_czd", "rect": [0, 0, 62, 30], "offset": [0, 0], "originalSize": [62, 30], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [9]], [[{"name": "bg_icon_01", "rect": [0, 0, 128, 128], "offset": [0, 0], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [10]]]]