[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "f8npR6F8ZIZoCp3cKXjJQz", "465G3jhE5MWp+Ei+MTpCx3", "a2MjXRFdtLlYQ5ouAFv/+R", "5esB9LxCNIpozu/DuPYufl", "c3+ruR7+FEnKfu8yo+WDeT", "4dMZmkcjlJEbhEI0rAtegJ", "40OtPssnZP9antMAWsDtDT", "73jGpne/9JUI/171Zur5Pr", "f5YmCCSe1OrIVUT+Qote1i", "b2gseIigJABZr/xG0zwT9Y", "f7293wEF9JhIuMEsrLuqng", "02TdF+VAtPyaV2VX4yLDch", "e2ahDoVfBDKYQLeo29Zsyo"], ["node", "_spriteFrame", "_parent", "_N$disabledSprite", "_N$file", "_N$target", "root", "guanbiIcon", "icon", "videoRIcon", "videoLIcon", "cancelText", "confirmText", "wayDesc", "reasonDesc", "checkMark", "data", "_normalMaterial", "_N$font", "_textureSetter"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_trs", "_parent", "_children", "_color"], -2, 4, 5, 9, 7, 1, 2, 5], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$paddingBottom", "_N$spacingY", "_enabled", "_N$paddingTop", "_N$affectedByScale", "node", "_layoutSize"], -4, 1, 5], ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 0, 1, 2, 4, 5, 7], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_enableWrapText", "_N$cacheMode", "_styleFlags", "_N$overflow", "node", "_materials"], -6, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite", "_N$target"], 1, 1, 9, 5, 5, 6, 1], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "node"], -1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["f96940xNIBCdITf18XZ5Xl6", ["node", "reasonDesc", "wayDesc", "confirmText", "cancelText", "videoLIcon", "videoRIcon", "icon", "guanbiIcon"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents", "_normalMaterial"], 1, 1, 5, 1, 1, 9, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "node"], -2, 1]], [[6, 0, 1, 2, 2], [1, 3, 4, 5, 1], [13, 0, 1, 2, 3], [15, 0, 1, 2, 2], [0, 0, 10, 7, 5, 6, 8, 2], [1, 0, 1, 3, 4, 5, 3], [4, 0, 4, 5, 1, 2, 3, 6, 9, 10, 8], [0, 0, 9, 10, 7, 5, 6, 8, 2], [0, 0, 9, 10, 5, 6, 8, 2], [0, 0, 1, 9, 7, 5, 6, 8, 3], [5, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1], [7, 1, 0, 2, 3, 4, 5, 6, 3], [8, 0, 2, 3, 4, 4], [10, 0, 2], [0, 0, 2, 10, 7, 5, 6, 3], [0, 0, 10, 7, 5, 6, 2], [0, 0, 1, 10, 7, 5, 6, 8, 3], [0, 0, 9, 10, 7, 5, 6, 2], [0, 0, 3, 9, 7, 5, 11, 6, 3], [0, 0, 4, 9, 7, 5, 6, 8, 3], [0, 0, 9, 7, 5, 6, 8, 2], [0, 0, 1, 9, 7, 5, 11, 6, 8, 3], [5, 0, 1, 2, 3, 4, 2], [3, 0, 1, 3, 4, 5, 6, 7, 3], [3, 0, 3, 4, 5, 6, 7, 2], [3, 0, 2, 1, 3, 4, 5, 6, 4], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [6, 1, 2, 1], [1, 0, 3, 4, 5, 2], [1, 1, 3, 4, 5, 2], [1, 0, 3, 4, 2], [1, 1, 2, 3, 4, 3], [2, 1, 0, 2, 3, 7, 8, 5], [2, 4, 0, 7, 8, 3], [2, 1, 0, 7, 8, 3], [2, 1, 0, 5, 2, 6, 7, 8, 6], [7, 0, 2, 3, 4, 5, 7, 6, 2], [8, 0, 1, 4, 3], [14, 0, 1, 2, 3, 4, 5, 6, 7, 3], [4, 0, 4, 1, 7, 2, 3, 9, 10, 7], [4, 0, 5, 1, 2, 3, 8, 6, 9, 10, 8], [16, 0, 1, 2, 3, 4, 5, 6]], [[[[14, "Select<PERSON>lert"], [15, "Select<PERSON>lert", 1, [-11, -12], [[27, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [28, -1, 0], [5, 750, 1335]], [16, "content", [-16, -17, -18], [[11, -13], [5, 1, 0, -14, [22], 23], [33, 1, 2, 30, 10, -15, [5, 488, 476.4]]], [0, "3fBLGLK/9I64VoqeVraltV", 1, 0], [5, 488, 476.4]], [17, "closeBtn", false, [-22], [[29, 1, -19, [4], 5], [37, 3, -21, [[2, "f96940xNIBCdITf18XZ5Xl6", "close", 1]], [4, 4293322470], [4, 3363338360], -20, 6]], [0, "53Q3OWWbZAf5FDQivmFzQB", 1, 0], [5, 52, 56], [187.235, 0.007999999999999119, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn_coutinue", [-26, -27], [[12, 0.9, 3, -23, [[2, "f96940xNIBCdITf18XZ5Xl6", "onConfirm", 1]], [4, 4293322470], [4, 3363338360], 19], [1, -24, [20], 21], [34, false, 1, -25, [5, 183, 90]]], [0, "5fGKRdpPNCYa8Ir/LcM6jW", 1, 0], [5, 206, 80], [116.19, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "ToggleBox", [-31, -32], [[35, 1, 1, -28, [5, 268, 50]], [11, -29], [38, 4, -68.10700000000001, -30]], [0, "b4Y5g/KP9LoLnwZ6GwOG3a", 1, 0], [5, 268, 50], [0, -710.607, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "box", 2, [-35, -36], [[36, 1, 2, 100, 100, true, -33, [5, 448, 250.4]], [5, 1, 0, -34, [8], 9]], [0, "ccEn+zzZdJhLLkCtV3N77a", 1, 0], [5, 448, 250.4], [0, 16.999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn_coutinue", [-39, -40], [[12, 0.9, 3, -37, [[2, "f96940xNIBCdITf18XZ5Xl6", "onCancel", 1]], [4, 4293322470], [4, 3363338360], 13], [1, -38, [14], 15]], [0, "cbrPcPLhtAsZY497T29/qv", 1, 0], [5, 206, 80], [-114.285, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "bg", 1, [2, 5], [[13, 45, 750, 1334, -41]], [0, "0fUFUTX/FJ9oWQdPB+OXtu", 1, 0], [5, 750, 1335]], [7, "Toggle", 5, [-45, -46], [[39, 3, false, -44, [4, 4292269782], -43, -42, [[2, "f96940xNIBCdITf18XZ5Xl6", "onToggle", 1]], 27]], [0, "488ji+j2xLiZk4NnyFr9br", 1, 0], [5, 80, 80], [-94, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "mask", 177.98999999999998, 1, [[30, 0, -47, [0], 1], [13, 45, 750, 1334, -48]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4281542699], [5, 750, 1335]], [8, "title_zhua<PERSON><PERSON>", 2, [-49, 3], [0, "b5RklW5wtFCoeQEs7JmIZC", 1, 0], [5, 488, 86], [0, 195.2, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "Label_title", 11, [[-50, [3, 4, -51, [4, 4278190080]]], 1, 4], [0, "f0YAxpgTVEL5P9vFDZFbvd", 1, 0], [5, 104, 58.4]], [8, "New Node", 2, [7, 4], [0, "1c2IkOBOtEkac9qP8ydIVq", 1, 0], [5, 488, 90], [0, -163.20000000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "icon_ads", false, 7, [[1, -52, [10], 11]], [0, "64Dhmgj0VCn6ejAnHmHSIy", 1, 0], [5, 54, 43], [-72.483, 31.156, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [10, "Label", 7, [[-53, [3, 2, -54, [4, 4278190080]]], 1, 4], [0, "bdYDKJX69O5Zsx4/ahX+9C", 1, 0], [5, 76, 54.4], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "icon_ads", false, 4, [[1, -55, [16], 17]], [0, "2eayZCUyNNJIHs3vDdpgU+", 1, 0], [5, 54, 43], [-72.483, 31.156, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [10, "Label", 4, [[-56, [3, 2, -57, [4, 4278190080]]], 1, 4], [0, "a1EH3TFARF8o2VAxtlSpSL", 1, 0], [5, 76, 54.4], [0, 3.08, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Background", 512, 9, [[5, 1, 0, -58, [24], 25]], [0, "bbpy28iypK0IlcElCowor3", 1, 0], [5, 50, 50], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [21, "Label", 5, [[6, "今日不再弹出", 30, false, false, 1, 1, 1, -59, [28]], [3, 4, -60, [4, 4278190080]]], [0, "e0F/dW8rxIqJGGbAfD0gdg", 1, 0], [5, 188, 58.4], [40, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "提示", 48, false, 1, 1, 1, 12, [2]], [22, "Label", false, 3, [[41, "返回", false, false, 1, 1, 1, 1, -61, [3]]], [0, "1aQbEAvkxEN595hWWjJ+9i", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "icon_adcard", false, 6, [-62], [0, "1fbyHoApZMdJ5BVKx+s1Lk", 1, 0], [5, 76, 64], [0, 25.19999999999999, 0, 0, 0, 0, 1, 2, 2, 1]], [31, 1, 22, [7]], [25, "Label_tips", 6, [-63], [0, "654qLQYVdHYZTKmGce0Qva", 1, 0], [5, 400, 50.4], [0, 3.552713678800501e-15, 0, 0, 0, 0, 1, 1, 1, 1]], [42, false, "<outline color=#474747 width=3>之前有未完成的战斗，是否继续？</outline>", 1, 26, 400, 24], [6, "关闭", 36, false, false, 1, 1, 1, 15, [12]], [6, "确认", 36, false, false, 1, 1, 1, 17, [18]], [26, "checkmark", 512, false, 9, [-64], [0, "7buxVpxDVC44UU1OwNM+mb", 1, 0], [5, 55, 40]], [32, 2, false, 28, [26]]], 0, [0, 6, 1, 0, 7, 3, 0, 8, 23, 0, 9, 16, 0, 10, 14, 0, 11, 26, 0, 12, 27, 0, 13, 20, 0, 14, 25, 0, 0, 1, 0, -1, 10, 0, -2, 8, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 11, 0, -2, 6, 0, -3, 13, 0, 0, 3, 0, 5, 3, 0, 0, 3, 0, -1, 21, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 16, 0, -2, 17, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 9, 0, -2, 19, 0, 0, 6, 0, 0, 6, 0, -1, 22, 0, -2, 24, 0, 0, 7, 0, 0, 7, 0, -1, 14, 0, -2, 15, 0, 0, 8, 0, 15, 29, 0, 5, 18, 0, 0, 9, 0, -1, 18, 0, -2, 28, 0, 0, 10, 0, 0, 10, 0, -1, 12, 0, -1, 20, 0, 0, 12, 0, 0, 14, 0, -1, 26, 0, 0, 15, 0, 0, 16, 0, -1, 27, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 21, 0, -1, 23, 0, -1, 25, 0, -1, 29, 0, 16, 1, 2, 2, 8, 3, 2, 11, 4, 2, 13, 5, 2, 8, 7, 2, 13, 64], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 23, 25, 26, 27, 29], [-1, 1, -1, -1, -1, 1, 3, -1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 17, -1, 4, 1, 18, 4, 4, 1], [0, 4, 0, 0, 0, 5, 1, 0, 0, 6, 0, 3, 0, 1, 0, 7, 0, 3, 0, 1, 0, 8, 0, 9, 0, 10, 0, 0, 0, 2, 11, 12, 2, 2, 13]], [[{"name": "icon_ads_yellow", "rect": [2, 4, 50, 52], "offset": [0, 0], "originalSize": [54, 60], "capInsets": [0, 0, 0, 0]}], [9], 0, [0], [19], [14]]]]