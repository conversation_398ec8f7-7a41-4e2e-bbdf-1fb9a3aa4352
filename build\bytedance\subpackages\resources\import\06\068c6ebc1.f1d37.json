[1, ["ecpdLyjvZBwrvm+cedCcQy", "ddFhwuPitMK6rcBkC3ddyJ", "29FYIk+N1GYaeWH/q1NxQO", "4613FnL15GjKE6Ci5QeOnx", "deFEOle59AvpQNSLtdxntU", "e3Exe4amlDno4sEjnRpash"], ["node", "_spriteFrame", "root", "lbPrice", "sanjao", "btnBg", "goodNode", "bg", "goodimg", "goodsbg", "Count", "data", "_parent", "_N$disabledSprite", "_textureSetter"], [["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent"], 1, 9, 4, 5, 7, 2, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 4, 5, 7], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -5, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["95da9P4jEtNuoaRyFibMcnC", ["node", "bg", "goodNode", "btnBg", "sanjao", "lbPrice"], 3, 1, 1, 1, 1, 1, 1], ["588e9YJTR5JAoebMnGix+fw", ["node", "Count", "goodsbg", "goodimg"], 3, 1, 1, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[3, 0, 1, 2], [3, 0, 1, 2, 2], [1, 0, 6, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 4, 5, 2], [13, 0, 1, 2, 3, 4], [14, 0, 1, 2, 2], [0, 3, 4, 1], [7, 0, 2], [1, 0, 7, 6, 2, 3, 4, 5, 2], [1, 0, 1, 7, 2, 3, 4, 5, 3], [2, 0, 1, 6, 2, 3, 4, 5, 2], [4, 0, 2, 3, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4, 5, 1], [3, 1, 2, 1], [9, 0, 1, 2, 3, 1], [10, 0, 1, 2, 3, 3], [11, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3, 4], [0, 1, 0, 2, 3, 4, 4], [0, 0, 3, 4, 2], [0, 3, 4, 5, 1], [0, 1, 0, 3, 4, 3], [5, 0, 5, 1, 2, 3, 4, 8, 9, 7], [5, 0, 6, 1, 2, 3, 4, 7, 8, 9, 8]], [[[[8, "SuperRewardItem"], [2, "SuperRewardItem", [-8, -9, -10, -11], [[14, -7, -6, -5, -4, -3, -2]], [15, -1, 0], [5, 547, 229], [0, -124.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "goodItem", [-17, -18, -19, -20], [[16, -16, -15, -14, -13]], [0, "6eq/A/rfdIHoMWX0MM17+b", -12], [5, 116, 120], [0, 0, 0, 0, 0, 0, 1, 0.75, 0.75, 0.75]], [9, "goodList", 1, [2], [[17, 1, 1, -21, [5, 116, 100]]], [1, "22XCiXuGFHiZyH5SttuWSe", 1, 0], [5, 116, 100], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btn_next", 1, [-24], [[[18, 3, -22, [[19, "95da9P4jEtNuoaRyFibMcnC", "onClickItem", "1", 1]], [4, 4293322470], [4, 3363338360], 8], -23], 4, 1], [1, "e6ElpFd0pFvbH5h3hbhHb0", 1, 0], [5, 248, 100], [0, -59.219, 0, 0, 0, 0, 1, 0.75, 0.75, 0]], [3, "bg", 1, [[-25, [5, 45, 116, 120, -26]], 1, 4], [1, "a3lph4JQ9Kc7Wv2gjJpFZT", 1, 0], [5, 547, 229]], [3, "bg", 2, [[-27, [5, 45, 116, 120, -28]], 1, 4], [0, "a3lph4JQ9Kc7Wv2gjJpFZT", 2], [5, 116, 120]], [4, "New Label", 2, [[-29, [6, 2, -30, [4, 4278190080]]], 1, 4], [0, "168nscGDFOFLCeNsfJoSlR", 2], [5, 70.53, 56.4], [22.161, -32.235, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label", 4, [[-31, [6, 3, -32, [4, 3573547008]]], 1, 4], [1, "8fMkoT+2xPc4llMHvZcEUv", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 1, 0, false, 5, [0]], [21, 0, 6, [1]], [12, "img", 2, [-33], [0, "c8TQPIne1PvJf+EhL/y/tz", 2], [5, 90, 90], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [7, 11, [2]], [24, "x40", 30, false, 2, 1, 2, 7, [3]], [10, "icon_pintu", false, 2, [[22, -34, [4], 5]], [0, "81jE12cbtAJaj7tyX8R5Ym", 2], [5, 35, 35], [51.182, 48.061, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "img_jiant", false, 1, [-35], [1, "99plbyRFZFD7b5/eyB6d2L", 1, 0], [5, 61, 40], [0, 120.133, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 15, [6]], [25, "18元", false, false, 1, 1, 2, 1, 8, [7]], [23, 1, 0, 4, [9]]], 0, [0, 2, 1, 0, 3, 17, 0, 4, 16, 0, 5, 18, 0, 6, 3, 0, 7, 9, 0, 0, 1, 0, -1, 5, 0, -2, 3, 0, -3, 15, 0, -4, 4, 0, 2, 2, 0, 8, 12, 0, 9, 10, 0, 10, 13, 0, 0, 2, 0, -1, 6, 0, -2, 11, 0, -3, 7, 0, -4, 14, 0, 0, 3, 0, 0, 4, 0, -2, 18, 0, -1, 8, 0, -1, 9, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, -1, 17, 0, 0, 8, 0, -1, 12, 0, 0, 14, 0, -1, 16, 0, 11, 1, 2, 12, 3, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 16], [-1, -1, -1, -1, -1, 1, -1, -1, 13, -1, 1, 1], [0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 3, 4]], [[{"name": "img_jiant", "rect": [0, 0, 61, 40], "offset": [0, 0], "originalSize": [61, 40], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [14], [5]]]]