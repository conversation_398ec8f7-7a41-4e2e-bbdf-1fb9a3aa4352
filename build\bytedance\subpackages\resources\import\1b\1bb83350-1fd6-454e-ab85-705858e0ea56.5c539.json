[1, ["ecpdLyjvZBwrvm+cedCcQy", "89YqH0KIZJ26WKtSNfrXcv", "d7YtQnVZBE5rFPc19m/Ces", "04y3cprWxFQrha4WRCk4AY", "eb9DL+eSlPd7ZR6QiZlQpM", "a2MjXRFdtLlYQ5ouAFv/+R", "9cY26KROhOPJckDfCoiAR5", "b4kzUQNQ5ANJgHGz81Nv+w", "b062b4UcNDzL4BXZ+0tCvT", "a3KBGNCUJOJY37lTxHe3Cp"], ["node", "_spriteFrame", "root", "_parent", "<PERSON><PERSON><PERSON><PERSON>", "hand_box", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_contentSize", "_parent", "_trs", "_components", "_eulerAngles", "_anchorPoint", "_children", "_color"], -1, 4, 5, 1, 7, 9, 5, 5, 12, 5], ["e4e15lKTddHTL8uXy9TOACk", ["setSetSpringAngle1", "setSetSpringAngle2", "setAngle1", "setAngle2", "setSpring1", "setAngle", "setSetSpringAngle", "setSpring", "setSpring2", "node"], -6, 1], ["cc.Node", ["_name", "_groupIndex", "_active", "_children", "_prefab", "_parent", "_trs", "_components", "_contentSize"], 0, 2, 4, 1, 7, 9, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.PhysicsBoxCollider", ["_friction", "node", "_size", "_offset"], 2, 1, 5, 5], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_size", "_offset"], 3, 1, 5, 5], ["54b7bzLQztBJIK+IWxIEjMQ", ["moveTime", "firstPos", "afterPos", "moveDis", "node"], -1, 1], ["cc.PhysicsPolygonCollider", ["_friction", "node", "points"], 2, 1, 12], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.RigidBody", ["_type", "node"], 2, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -1, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["22ab43vY61En7yPvJO1zuHS", ["num", "node", "<PERSON><PERSON><PERSON><PERSON>"], 2, 1, 1], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node"], 1, 1], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node"], 2, 1], ["5a55e3TjupL/I9+DxL+YRF8", ["node"], 3, 1], ["5688fiOKklDfL+crWJqzZ67", ["num", "node", "<PERSON><PERSON><PERSON><PERSON>"], 2, 1, 1], ["04988ueXn9CcY+dvg+aDUGW", ["isUseMPUBGoodsBullets2", "MPUBGoodsBullets2GravityScale", "MPUBGoodsBullets2GeneraTime", "handStyle", "hand_left_x", "hand_right_x", "pipePower", "bulletNum", "node", "hand_box"], -5, 1, 1]], [[4, 0, 1, 2], [6, 0, 1, 1], [4, 0, 1, 2, 2], [0, 0, 1, 6, 8, 4, 5, 7, 3], [3, 1, 0, 2, 3, 4, 3], [0, 0, 1, 6, 8, 4, 5, 7, 9, 3], [1, 5, 6, 2, 3, 4, 0, 1, 9, 8], [11, 0, 1, 2], [0, 0, 6, 8, 4, 5, 7, 2], [2, 0, 5, 3, 7, 4, 8, 6, 2], [5, 0, 1, 3, 2, 2], [1, 7, 4, 8, 0, 1, 9, 6], [1, 5, 6, 4, 8, 0, 1, 9, 7], [2, 0, 1, 5, 3, 7, 4, 8, 6, 3], [12, 0, 1, 2, 3, 4, 5, 5], [13, 0, 1, 2, 2], [2, 0, 1, 3, 7, 4, 8, 6, 3], [6, 0, 2, 1, 1], [7, 3, 0, 1, 2, 4, 5], [0, 0, 2, 1, 6, 8, 4, 5, 7, 9, 4], [0, 0, 2, 1, 6, 8, 4, 5, 7, 4], [3, 0, 2, 3, 4, 2], [5, 1, 2, 1], [2, 0, 2, 1, 5, 3, 7, 4, 8, 6, 4], [2, 0, 2, 5, 3, 4, 6, 3], [10, 0, 1, 2, 3, 4, 2], [3, 2, 3, 4, 1], [5, 0, 1, 2, 2], [2, 0, 5, 3, 4, 2], [3, 2, 3, 1], [14, 0, 1, 2, 2], [1, 7, 2, 3, 0, 1, 9, 6], [1, 7, 2, 3, 4, 0, 1, 9, 7], [7, 0, 1, 2, 4, 4], [8, 1, 2, 1], [8, 0, 1, 2, 2], [9, 0, 2], [0, 0, 1, 11, 8, 4, 5, 3], [0, 0, 6, 8, 4, 12, 5, 7, 2], [0, 0, 6, 8, 4, 5, 2], [0, 0, 6, 4, 5, 7, 2], [0, 0, 6, 4, 5, 10, 7, 2], [0, 0, 1, 6, 4, 5, 10, 7, 3], [0, 0, 3, 1, 6, 8, 4, 5, 7, 4], [2, 0, 5, 3, 4, 6, 2], [3, 0, 2, 3, 2], [4, 1, 2, 1], [1, 5, 7, 6, 2, 3, 0, 1, 9, 8], [1, 7, 2, 3, 4, 8, 0, 1, 9, 8], [1, 5, 6, 2, 3, 0, 9, 6], [1, 5, 6, 2, 3, 0, 1, 9, 7], [1, 5, 6, 2, 3, 4, 1, 9, 7], [1, 5, 7, 6, 2, 3, 4, 8, 0, 1, 9, 10], [15, 0, 1, 2, 3], [16, 0, 1, 2], [17, 0, 1], [18, 0, 1, 2, 2], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9]], [[36, "map2"], [37, "map2", 2, [[-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, [40, "role_box", -22, [2, "daT578aVhJrK1ty+kbCUVG", -21, 0], [5, 200, 150], [-277.474, 384.052, 0, 0, 0, 0, 1, 1, 1, 1]], -23, -24, -25, -26, [41, "monster_box", -28, [2, "7caPzGfrJCRKG8RIbhhKvo", -27, 0], [5, 200, 200], [0, 0, 0], [479.879, 285.974, 0, 0, 0, 0, 1, 1, 1, 1]], -29, -30, -31], 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 4, 1, 1, 1], [[57, true, -2.4, 0.3, 2, -300, 300, 600, 500, -3, -2]], [46, -1, 0], [5, 750, 1334]], [13, "pipeline", 8, 1, [-34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49], [[7, 0, -32], [35, 0.1, -33, [[[0, 209.6, -37.5], [0, 271.7, -67.2], [0, 320.8, -112], [0, 331.2, -136.3], [0, 374.3, -136.4], [0, 374, 91.3], [0, 102.3, 91.2], [0, -234.8, 89.7], [0, -209.6, 71], [0, -100, 72], [0, -73, 72], [0, 0.5, 70], [0, 44.7, 66.8], [0, 66.4, 52.6], [0, 74.4, 33.3], [0, 76, 11.1], [0, 76.7, -5], [0, 147.8, -19.7]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [2, "b6C/GzKtVOpJa3CN0aAAz0", 1, 0], [5, 300, 100], [0.021, 204.961, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-55, -56, -57], [[7, 0, -51], [10, 0, -52, [0, -105, 0], [5, 40, 80]], [10, 0, -53, [0, 105, 0], [5, 40, 80]], [18, -200, 5, 400, 0, -54]], [0, "84eJAjzNxHm6HJCd4czJ/S", -50], [5, 180, 40], [-200, 371.48, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-63, -64, -65], [[7, 0, -59], [10, 0, -60, [0, -105, 0], [5, 40, 80]], [10, 0, -61, [0, 105, 0], [5, 40, 80]], [18, 200, 5, -400, 0, -62]], [0, "70VfxFqypP7oWN02nBzqKg", -58], [5, 180, 40], [0, 700, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-71, -72, -73], [[7, 0, -67], [10, 0, -68, [0, -105, 0], [5, 40, 80]], [10, 0, -69, [0, 105, 0], [5, 40, 80]], [18, -200, 5, 400, 0, -70]], [0, "84eJAjzNxHm6HJCd4czJ/S", -66], [5, 180, 40], [0, 371.48, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-79, -80, -81], [[7, 0, -75], [10, 0, -76, [0, -105, 0], [5, 40, 80]], [10, 0, -77, [0, 105, 0], [5, 40, 80]], [18, 200, 5, -400, 0, -78]], [0, "70VfxFqypP7oWN02nBzqKg", -74], [5, 180, 40], [0, 700, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-87, -88, -89], [[7, 0, -83], [10, 0, -84, [0, -75, 0], [5, 40, 80]], [10, 0, -85, [0, 80, 0], [5, 40, 80]], [33, 5, 200, -200, -86]], [0, "81+5e+g+JEl7ODdDGVgNZe", -82], [5, 120, 40], [0, 511.985, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-95, -96, -97], [[7, 0, -91], [10, 0, -92, [0, -105, 0], [5, 40, 80]], [10, 0, -93, [0, 105, 0], [5, 40, 80]], [18, -200, 10, 400, 0, -94]], [0, "84eJAjzNxHm6HJCd4czJ/S", -90], [5, 180, 40], [0, 371.48, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-103, -104, -105], [[7, 0, -99], [10, 0, -100, [0, -75, 0], [5, 40, 80]], [10, 0, -101, [0, 80, 0], [5, 40, 80]], [33, 3, 200, -200, -102]], [0, "f43n60hehAgqeKJ30qdulq", -98], [5, 120, 40], [-0.19, 850, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-111, -112, -113], [[7, 0, -107], [10, 0, -108, [0, -105, 0], [5, 40, 80]], [10, 0, -109, [0, 105, 0], [5, 40, 80]], [18, 200, 5, -400, 0, -110]], [0, "70VfxFqypP7oWN02nBzqKg", -106], [5, 180, 40], [0, 700, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-119, -120, -121], [[7, 0, -115], [10, 0, -116, [0, -75, 0], [5, 40, 80]], [10, 0, -117, [0, 80, 0], [5, 40, 80]], [18, -200, 5, 400, 0, -118]], [0, "81+5e+g+JEl7ODdDGVgNZe", -114], [5, 120, 40], [0, 511.985, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "M31_MoveWall", 8, [-127, -128, -129], [[7, 0, -123], [10, 0, -124, [0, -105, 0], [5, 40, 80]], [10, 0, -125, [0, 105, 0], [5, 40, 80]], [18, 200, 10, -400, 0, -126]], [0, "84eJAjzNxHm6HJCd4czJ/S", -122], [5, 180, 40], [0, 371.48, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "R", 2, [-130, -131, -132, -133, -134, -135, -136, -137, -138, -139, -140, -141, -142, -143, -144, -145], [2, "90Ak3Jh4JCPaxw22XI/Xqb", 1, 0]], [28, "L", 2, [-146, -147, -148, -149, -150, -151, -152, -153, -154, -155, -156, -157, -158, -159, -160], [2, "b4rgnBjC5KM7UeP7QR3iI9", 1, 0]], [23, "MPUBEatWallWeapon", false, 8, 1, [-167, -168, -169, -170], [[7, 0, -162], [56, 80, -164, -163], [34, -165, [[[0, 117.7, -46.2], [0, 124.2, -36.1], [0, 123.3, 41.6], [0, 87.8, 41.2], [0, 86.3, -35.7], [0, 93.3, -47.5], [0, 105.9, -50.5]], 8, 8, 8, 8, 8, 8, 8]], [34, -166, [[[0, -120.5, -37.7], [0, -115.1, -45.9], [0, -104.7, -49.9], [0, -93.9, -48.2], [0, -86.3, -37.9], [0, -86.3, 42.4], [0, -120.5, 42.9]], 8, 8, 8, 8, 8, 8, 8]]], [0, "a5f9FqgAdC6aHSCPve+Xk+", -161], [5, 300, 120], [-210.508, -204.031, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "MPUBEatWall", false, 8, 1, [-175, -176, -177], [[7, 0, -172], [30, 30, -174, -173]], [0, "f7+CspfV5C/59Asm137mE9", -171], [5, 300, 120], [-207.831, -347.061, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "MPUBEatWall", false, 8, 1, [-182, -183, -184], [[7, 0, -179], [30, 30, -181, -180]], [0, "56csi53m5L+62MmjS4YgSm", -178], [5, 300, 120], [209.409, -353.316, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "MPUBWeapon", 7, 15, [-191], [[29, -186, [105]], [7, 0, -187], [53, 0.05, 20, -188], [54, 30, -189], [55, -190]], [0, "49bKh3lAZAp5+Yjfgns5s+", -185], [5, 102, 82], [0, 94.47, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [24, "walkNode4", false, 1, [9, 10, 11, 12], [2, "5fxZJjMuhHGK/bSepvKs4i", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "wall", 8, 1, [[21, 0, -192, [11], 12], [7, 0, -193], [27, 0.1, -194, [5, 750, 50]]], [2, "a88NVQgZpMdabylC609PqV", 1, 0], [5, 750, 50], [-1.056, 614.104, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "Eat", 5, 16, [-197], [[4, 1, 0, -195, [14], 15], [1, -196, [5, 200, 60]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 16], [5, 200, 60], [0, 0, 1000, 0, 0, 0, 1, 1, 1, 1]], [13, "Eat", 5, 17, [-200], [[4, 1, 0, -198, [21], 22], [1, -199, [5, 200, 60]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 17], [5, 200, 60], [0, 0, 1000, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 3, [-203], [[4, 1, 0, -201, [28], 29], [17, -202, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 3], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 3, [-205, -206], [[4, 1, 0, -204, [30], 31]], [0, "24c+KvVkdL4bvegRYe3/1M", 3], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 3, [-208, -209], [[4, 1, 0, -207, [32], 33]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 3], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 4, [-212], [[4, 1, 0, -210, [35], 36], [17, -211, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 4], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 4, [-214, -215], [[4, 1, 0, -213, [37], 38]], [0, "24c+KvVkdL4bvegRYe3/1M", 4], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 4, [-217, -218], [[4, 1, 0, -216, [39], 40]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 4], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 5, [-221], [[4, 1, 0, -219, [42], 43], [17, -220, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 5], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 5, [-223, -224], [[4, 1, 0, -222, [44], 45]], [0, "24c+KvVkdL4bvegRYe3/1M", 5], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 5, [-226, -227], [[4, 1, 0, -225, [46], 47]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 5], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "walkNode3", false, 1, [6, 7, 8], [2, "99ckgGYiNLX5p8eNRdrsnB", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 6, [-230], [[4, 1, 0, -228, [49], 50], [17, -229, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 6], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 6, [-232, -233], [[4, 1, 0, -231, [51], 52]], [0, "24c+KvVkdL4bvegRYe3/1M", 6], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 6, [-235, -236], [[4, 1, 0, -234, [53], 54]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 6], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "3", 5, 7, [-239], [[4, 1, 0, -237, [56], 57], [17, -238, [0, 0, 20], [5, 100, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 7], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 7, [-241, -242], [[4, 1, 0, -240, [58], 59]], [0, "24c+KvVkdL4bvegRYe3/1M", 7], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 7, [-244, -245], [[4, 1, 0, -243, [60], 61]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 7], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 8, [-248], [[4, 1, 0, -246, [63], 64], [17, -247, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 8], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 8, [-250, -251], [[4, 1, 0, -249, [65], 66]], [0, "24c+KvVkdL4bvegRYe3/1M", 8], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 8, [-253, -254], [[4, 1, 0, -252, [67], 68]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 8], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "3", 5, 9, [-257], [[4, 1, 0, -255, [70], 71], [17, -256, [0, 0, 20], [5, 100, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 9], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 9, [-259, -260], [[4, 1, 0, -258, [72], 73]], [0, "24c+KvVkdL4bvegRYe3/1M", 9], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 9, [-262, -263], [[4, 1, 0, -261, [74], 75]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 9], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 10, [-266], [[4, 1, 0, -264, [77], 78], [17, -265, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 10], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 10, [-268, -269], [[4, 1, 0, -267, [79], 80]], [0, "24c+KvVkdL4bvegRYe3/1M", 10], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 10, [-271, -272], [[4, 1, 0, -270, [81], 82]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 10], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "3", 5, 11, [-275], [[4, 1, 0, -273, [84], 85], [17, -274, [0, 0, 20], [5, 100, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 11], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 11, [-277, -278], [[4, 1, 0, -276, [86], 87]], [0, "24c+KvVkdL4bvegRYe3/1M", 11], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 11, [-280, -281], [[4, 1, 0, -279, [88], 89]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 11], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "2", 5, 12, [-284], [[4, 1, 0, -282, [91], 92], [17, -283, [0, 0, 20], [5, 200, 10]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 12], [5, 200, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 12, [-286, -287], [[4, 1, 0, -285, [93], 94]], [0, "24c+KvVkdL4bvegRYe3/1M", 12], [5, 40, 80], [-107.673, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "wall", 12, [-289, -290], [[4, 1, 0, -288, [95], 96]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 12], [5, 40, 80], [106.315, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "Eat", 5, 15, [-293], [[4, 1, 0, -291, [98], 99], [1, -292, [5, 200, 60]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 15], [5, 200, 70], [0, 0, 1000, 0, 0, 0, 1, 1, 1, 1]], [43, "wall", 0, 8, 1, [[4, 1, 0, -294, [106], 107], [35, 0.1, -295, [[[0, 60.6, 5], [0, -138.3, 6.3], [0, -161, 14], [0, -179.6, 25.4], [0, -195.1, 51.2], [0, -198.6, 79.1], [0, -240.6, 79.3], [0, -242, -114], [0, -242, -216], [0, -197.4, -216], [0, -195.1, -140.8], [0, -173.6, -109.6], [0, -135.7, -79.8], [0, -88.6, -57.1], [0, -5.4, -30.1], [0, 58.3, -19.8]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], [7, 0, -296]], [2, "2a6wZ/LTdIv7IEIpi+H92u", 1, 0], [5, 100, 10], [-134.456, 214.418, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "scene2", 1, [-298], [[45, 0, -297, [8]]], [2, "89/VaAa4NF9YDLLEw8sPmq", 1, 0], [5, 750, 125], [0, -12.721, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[7, 0, -299], [22, -300, [5, 200, 2000]]], [2, "6aO2dPJHxE4oyfJXF/y40N", 1, 0], [5, 200, 2000], [477.053, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[7, 0, -301], [22, -302, [5, 200, 2000]]], [2, "58ijYrHHxGuL1RNywxfp48", 1, 0], [5, 200, 2000], [-477.053, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[7, 0, -303], [22, -304, [5, 1600, 200]]], [2, "fegieP/oRIx6qy3KKQJ7SH", 1, 0], [5, 1600, 200], [0, 974.565, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[7, 0, -305], [22, -306, [5, 1600, 200]]], [2, "cf+8HL3AJFzKEkq1bJ9o+G", 1, 0], [5, 1600, 200], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "label_multiple", 21, [[-307, [15, 3, -308, [4, 4278190080]]], 1, 4], [0, "06/2jfsSBICI4jM9YCpnfz", 16], [5, 75.71, 56.4]], [25, "label_multiple", 22, [[-309, [15, 3, -310, [4, 4278190080]]], 1, 4], [0, "06/2jfsSBICI4jM9YCpnfz", 17], [5, 75.71, 56.4]], [8, "label_x2", 23, [[14, "x2", false, 1, 1, -311, [27]], [15, 3, -312, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 3], [5, 48.25, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 24, [[1, -313, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -314]], [0, "552WdQUh5E26dccLfLj3JK", 3], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 24, [[1, -315, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -316]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 3], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 25, [[1, -317, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -318]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 3], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 25, [[1, -319, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -320]], [0, "58OapVrx9EcKUomVQjCAU+", 3], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "walkNode2", false, 1, [4, 5], [2, "24Qzcph8xO/b+w62VxIKPP", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 26, [[14, "x2", false, 1, 1, -321, [34]], [15, 3, -322, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 4], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 27, [[1, -323, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -324]], [0, "552WdQUh5E26dccLfLj3JK", 4], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 27, [[1, -325, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -326]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 4], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 28, [[1, -327, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -328]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 4], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 28, [[1, -329, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -330]], [0, "58OapVrx9EcKUomVQjCAU+", 4], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 29, [[14, "x2", false, 1, 1, -331, [41]], [15, 3, -332, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 5], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 30, [[1, -333, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -334]], [0, "552WdQUh5E26dccLfLj3JK", 5], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 30, [[1, -335, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -336]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 5], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 31, [[1, -337, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -338]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 5], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 31, [[1, -339, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -340]], [0, "58OapVrx9EcKUomVQjCAU+", 5], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 33, [[14, "x2", false, 1, 1, -341, [48]], [15, 3, -342, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 6], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 34, [[1, -343, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -344]], [0, "552WdQUh5E26dccLfLj3JK", 6], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 34, [[1, -345, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -346]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 6], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 35, [[1, -347, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -348]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 6], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 35, [[1, -349, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -350]], [0, "58OapVrx9EcKUomVQjCAU+", 6], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x3", 36, [[14, "x3", false, 1, 1, -351, [55]], [15, 3, -352, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 7], [5, 48.64, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 37, [[1, -353, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -354]], [0, "552WdQUh5E26dccLfLj3JK", 7], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 37, [[1, -355, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -356]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 7], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 38, [[1, -357, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -358]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 7], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 38, [[1, -359, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -360]], [0, "58OapVrx9EcKUomVQjCAU+", 7], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 39, [[14, "x2", false, 1, 1, -361, [62]], [15, 3, -362, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 8], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 40, [[1, -363, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -364]], [0, "552WdQUh5E26dccLfLj3JK", 8], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 40, [[1, -365, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -366]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 8], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 41, [[1, -367, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -368]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 8], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 41, [[1, -369, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -370]], [0, "58OapVrx9EcKUomVQjCAU+", 8], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x3", 42, [[14, "x3", false, 1, 1, -371, [69]], [15, 3, -372, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 9], [5, 48.64, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 43, [[1, -373, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -374]], [0, "552WdQUh5E26dccLfLj3JK", 9], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 43, [[1, -375, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -376]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 9], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 44, [[1, -377, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -378]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 9], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 44, [[1, -379, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -380]], [0, "58OapVrx9EcKUomVQjCAU+", 9], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 45, [[14, "x2", false, 1, 1, -381, [76]], [15, 3, -382, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 10], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 46, [[1, -383, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -384]], [0, "552WdQUh5E26dccLfLj3JK", 10], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 46, [[1, -385, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -386]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 10], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 47, [[1, -387, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -388]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 10], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 47, [[1, -389, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -390]], [0, "58OapVrx9EcKUomVQjCAU+", 10], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x3", 48, [[14, "x3", false, 1, 1, -391, [83]], [15, 3, -392, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 11], [5, 48.64, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 49, [[1, -393, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -394]], [0, "552WdQUh5E26dccLfLj3JK", 11], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 49, [[1, -395, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -396]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 11], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 50, [[1, -397, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -398]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 11], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 50, [[1, -399, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -400]], [0, "58OapVrx9EcKUomVQjCAU+", 11], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 51, [[14, "x2", false, 1, 1, -401, [90]], [15, 3, -402, [4, 4278190080]]], [0, "edgNuAz9lAC7yi3EZrn4OD", 12], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 52, [[1, -403, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -404]], [0, "552WdQUh5E26dccLfLj3JK", 12], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 52, [[1, -405, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -406]], [0, "f7a6pBcRJP+6C+rT9GoDgZ", 12], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 53, [[1, -407, [5, 20, 5]], [11, 0, 0.1, 0.1, 270, 270, -408]], [0, "97KdxG5ZNG2bvEDr+lQXP5", 12], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 53, [[1, -409, [5, 20, 5]], [12, 45, 45, 0.1, 0.1, 90, 90, -410]], [0, "58OapVrx9EcKUomVQjCAU+", 12], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "label_multiple", 54, [[-411, [15, 3, -412, [4, 4278190080]]], 1, 4], [0, "06/2jfsSBICI4jM9YCpnfz", 15], [5, 75.3, 56.4]], [3, "wall", 8, 1, [[7, 0, -413], [27, 0, -414, [5, 50, 1200]]], [2, "79Evhznb9FfooifRlvFcco", 1, 0], [5, 50, 1200], [376.336, -233.857, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "wall", 8, 1, [[7, 0, -415], [27, 0, -416, [5, 60, 950]]], [2, "30xpxupxJP5JIcP8o//W21", 1, 0], [5, 60, 950], [-378.21, -189.455, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 2, [[1, -417, [5, 160, 30]], [47, 90, 5, 90, 90, 90, 90, 90, -418]], [2, "d2xcrbilxFub4ahL+tfigR", 1, 0], [5, 160, 30], [0, 74.509, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [3, "pipe_v_l", 8, 2, [[1, -419, [5, 10, 50]], [31, 0, 90, 110, 90, 110, -420]], [2, "eeRjE8o3hLr4TFNDRbk0cK", 1, 0], [5, 10, 50], [-155.978, 44.022, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 2, [[1, -421, [5, 20, 100]], [48, 0, 350, 360, 0.1, 0.1, 350, 360, -422]], [2, "07sFCRCrZA2auTeI+bqwX3", 1, 0], [5, 20, 100], [-309.98, 54, 0, 0, 0, 0.25881904510252074, 0.9659258262890683, 1, 1, 1], [1, 0, 0, 30]], [3, "pipe_v_l", 8, 2, [[1, -423, [5, 10, 50]], [31, 0, 90, 100, 90, 100, -424]], [2, "9bF0bcJS5HAK+qrzgmp2Hd", 1, 0], [5, 10, 50], [-102.935, 43.033, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "pipe_v_l", false, 8, 2, [[1, -425, [5, 400, 50]], [49, 45, 45, 45, 60, 45, -426]], [2, "ebvbF1L5VKJpC2DZTVok1N", 1, 0], [5, 400, 50], [259.129, -45.635, 0, 0, 0, 0.17364817766693028, -0.984807753012208, 1, 1, 1], [1, 0, 0, 340]], [19, "pipe_v_l", false, 8, 2, [[1, -427, [5, 200, 30]], [32, 0, 290, 300, 0.5, 290, 300, -428]], [2, "d1SRDQfD5GiJ/+xtIO8qR1", 1, 0], [5, 200, 30], [-285.946, -66.714, 0, 0, 0, 0.25881904510252074, 0.9659258262890683, 1, 1, 1], [1, 0, 0, 30]], [20, "pipe_v_l", false, 8, 2, [[1, -429, [5, 200, 30]], [50, 315, 315, 315, 270, 315, 270, -430]], [2, "afKqLPsphMPLZyfHqf9Rtj", 1, 0], [5, 200, 30], [-185.1, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 13, [[1, -431, [5, 20, 20]], [6, 45, 45, 30, 40, 0.5, 30, 40, -432]], [2, "308Gu42BdA3q4ulKOuOWjz", 1, 0], [5, 20, 20], [335.389, -145.847, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, 1, 1], [1, 0, 0, 270]], [5, "pipe_v_l", 8, 13, [[1, -433, [5, 20, 20]], [6, 45, 45, 30, 40, 0.5, 30, 40, -434]], [2, "54cqsKMh1DwaJfxVJdmX1T", 1, 0], [5, 20, 20], [329.871, -123.552, 0, 0, 0, 0.5735764363510459, -0.8191520442889919, 1, 1, 1], [1, 0, 0, 290]], [5, "pipe_v_l", 8, 13, [[1, -435, [5, 20, 20]], [6, 45, 45, 30, 40, 0.5, 30, 40, -436]], [2, "7eqvlQl4RDVJwQ4QDLlyFX", 1, 0], [5, 20, 20], [320.691, -104.055, 0, 0, 0, 0.49999999999999994, -0.8660254037844387, 1, 1, 1], [1, 0, 0, 300]], [5, "pipe_v_l", 8, 13, [[1, -437, [5, 20, 20]], [6, 45, 45, 45, 50, 0.5, 45, 50, -438]], [2, "40610RxQpK3K9jxoW4THNi", 1, 0], [5, 20, 20], [306.957, -88.589, 0, 0, 0, 0.3420201433256689, -0.9396926207859083, 1, 1, 1], [1, 0, 0, 320]], [5, "pipe_v_l", 8, 13, [[1, -439, [5, 20, 20]], [6, 45, 45, 50, 56, 0.5, 50, 56, -440]], [2, "3cI0a1m5xOPYYmPTmStEdr", 1, 0], [5, 20, 20], [291.584, -76.138, 0, 0, 0, 0.3090169943749475, -0.9510565162951535, 1, 1, 1], [1, 0, 0, 324]], [5, "pipe_v_l", 8, 13, [[1, -441, [5, 20, 20]], [51, 45, 45, 50, 58, 0.5, 58, -442]], [2, "52wo83V5ZPeYTM2mYBCiih", 1, 0], [5, 20, 20], [274.378, -64.52, 0, 0, 0, 0.2756373558169992, -0.9612616959383189, 1, 1, 1], [1, 0, 0, 328]], [5, "pipe_v_l", 8, 13, [[1, -443, [5, 20, 20]], [6, 45, 45, 60, 65, 0.5, 60, 65, -444]], [2, "d0l9Pm9NdPtZfVsdCHNi0f", 1, 0], [5, 20, 20], [255.879, -54.632, 0, 0, 0, 0.21643961393810274, -0.9762960071199334, 1, 1, 1], [1, 0, 0, 335]], [5, "pipe_v_l", 8, 13, [[1, -445, [5, 20, 20]], [6, 45, 45, 60, 66, 0.5, 60, 66, -446]], [2, "1fvinjOSBOaLCZe+7Kx/d3", 1, 0], [5, 20, 20], [237.242, -46.279, 0, 0, 0, 0.20791169081775931, -0.9781476007338057, 1, 1, 1], [1, 0, 0, 336]], [5, "pipe_v_l", 8, 13, [[1, -447, [5, 20, 20]], [6, 45, 45, 65, 70, 0.5, 65, 70, -448]], [2, "4de3s63tBPO6eZuMEMrGTs", 1, 0], [5, 20, 20], [217.28, -37.667, 0, 0, 0, 0.17364817766693028, -0.984807753012208, 1, 1, 1], [1, 0, 0, 340]], [5, "pipe_v_l", 8, 13, [[1, -449, [5, 20, 20]], [6, 45, 45, 65, 75, 0.5, 65, 75, -450]], [2, "afpzT3hSJLEICVfOwLs7NK", 1, 0], [5, 20, 20], [197.666, -30.733, 0, 0, 0, 0.13052619222005157, -0.9914448613738104, 1, 1, 1], [1, 0, 0, 345]], [5, "pipe_v_l", 8, 13, [[1, -451, [5, 20, 20]], [6, 45, 45, 70, 75, 0.5, 70, 75, -452]], [2, "e26xFc0/BMuZ79J+46o9Nt", 1, 0], [5, 20, 20], [178.459, -24.692, 0, 0, 0, 0.12186934340514755, -0.992546151641322, 1, 1, 1], [1, 0, 0, 346]], [5, "pipe_v_l", 8, 13, [[1, -453, [5, 20, 20]], [6, 45, 45, 75, 78, 0.5, 75, 78, -454]], [2, "80EQZwaTRNgoEOMWf8zQS3", 1, 0], [5, 20, 20], [158.196, -19.929, 0, 0, 0, 0.10452846326765373, -0.9945218953682733, 1, 1, 1], [1, 0, 0, 348]], [5, "pipe_v_l", 8, 13, [[1, -455, [5, 20, 20]], [6, 45, 45, 75, 80, 0.5, 75, 80, -456]], [2, "9dByK3oT9OQIRDyTjrIbyh", 1, 0], [5, 20, 20], [137.81, -16.188, 0, 0, 0, 0.0871557427476582, -0.9961946980917455, 1, 1, 1], [1, 0, 0, 350]], [5, "pipe_v_l", 8, 13, [[1, -457, [5, 50, 20]], [6, 44.8, 45, 80, 85, 0.5, 80, 85, -458]], [2, "14bRz9TrxFr4vfVRavOydx", 1, 0], [5, 50, 20], [101.901, -9.73, 0, 0, 0, 0.0871557427476582, -0.9961946980917455, 1, 1, 1], [1, 0, 0, 350]], [3, "pipe_v_l", 8, 13, [[1, -459, [5, 5, 20]], [6, 44.8, 45, 45, 60, 0.5, 45, 60, -460]], [2, "80GSOv7WpN1Z0RNL4eG5vC", 1, 0], [5, 5, 20], [73.102, 21.508, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 13, [[1, -461, [5, 5, 45]], [6, 44.8, 45, 45, 60, 0.5, 45, 60, -462]], [2, "27DVFGMA1NuY23jXQwYOS0", 1, 0], [5, 5, 45], [57.905, 48.034, 0, 0, 0, 0.42261826174069944, 0.9063077870366499, 1, 1, 1], [1, 0, 0, 50]], [5, "pipe_v_l", 8, 14, [[1, -463, [5, 20, 20]], [6, 45, 45, 330, 330, 0.5, 330, 330, -464]], [2, "1aPyJeQQVFUI95htSC8opP", 1, 0], [5, 20, 20], [-333.433, -145.847, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, 1, 1], [1, 0, 0, 270]], [5, "pipe_v_l", 8, 14, [[1, -465, [5, 20, 20]], [32, 0, 315, 330, 0.5, 315, 330, -466]], [2, "44kb6sqYxDX6yk6tDp1nhi", 1, 0], [5, 20, 20], [-326.775, -121.772, 0, 0, 0, -0.25881904510252074, 0.9659258262890683, 1, 1, 1], [1, 0, 0, -30]], [5, "pipe_v_l", 8, 14, [[1, -467, [5, 20, 20]], [6, 45, 45, 315, 330, 0.5, 315, 330, -468]], [2, "19k3l20w1FPbOEh85+GsnL", 1, 0], [5, 20, 20], [-314.64, -103.748, 0, 0, 0, 0.3420201433256689, -0.9396926207859083, 1, 1, 1], [1, 0, 0, 320]], [5, "pipe_v_l", 8, 14, [[1, -469, [5, 20, 20]], [6, 45, 45, 305, 315, 0.5, 305, 315, -470]], [2, "5eiaSZJK5Pd7nmbtLM65ba", 1, 0], [5, 20, 20], [-299.617, -88.267, 0, 0, 0, 0.4226182617406995, -0.9063077870366499, 1, 1, 1], [1, 0, 0, 310]], [5, "pipe_v_l", 8, 14, [[1, -471, [5, 20, 20]], [6, 45, 45, 305, 315, 0.5, 305, 315, -472]], [2, "f15+qC3q9KeLl5j76miUKc", 1, 0], [5, 20, 20], [-283.094, -74.849, 0, 0, 0, 0.4617486132350339, -0.8870108331782217, 1, 1, 1], [1, 0, 0, 305]], [5, "pipe_v_l", 8, 14, [[1, -473, [5, 20, 20]], [6, 45, 45, 305, 315, 0.5, 305, 315, -474]], [2, "a8z9gAH0tMwZVVT2LZ2MJo", 1, 0], [5, 20, 20], [-265.344, -63.092, 0, 0, 0, 0.49999999999999994, -0.8660254037844387, 1, 1, 1], [1, 0, 0, 300]], [5, "pipe_v_l", 8, 14, [[1, -475, [5, 20, 20]], [6, 45, 45, 290, 300, 0.5, 290, 300, -476]], [2, "7eXH31u85IRZlnV9ZFCXbU", 1, 0], [5, 20, 20], [-246.988, -53.313, 0, 0, 0, 0.5372996083468238, -0.8433914458128857, 1, 1, 1], [1, 0, 0, 295]], [5, "pipe_v_l", 8, 14, [[1, -477, [5, 20, 20]], [6, 45, 45, 290, 300, 0.5, 290, 300, -478]], [2, "4dWHr8gE9BNLYWAJ/0pM+j", 1, 0], [5, 20, 20], [-227.879, -45.181, 0, 0, 0, 0.5735764363510459, -0.8191520442889919, 1, 1, 1], [1, 0, 0, 290]], [5, "pipe_v_l", 8, 14, [[1, -479, [5, 20, 20]], [6, 45, 45, 290, 300, 0.5, 290, 300, -480]], [2, "49QdXo+D1N0qtriFJPLybe", 1, 0], [5, 20, 20], [-208.85, -38.088, 0, 0, 0, 0.5807029557109399, -0.8141155183563191, 1, 1, 1], [1, 0, 0, 289]], [5, "pipe_v_l", 8, 14, [[1, -481, [5, 20, 20]], [6, 45, 45, 290, 295, 0.5, 290, 295, -482]], [2, "d9msERwMJEQZesQRjrS6qs", 1, 0], [5, 20, 20], [-189.701, -31.449, 0, 0, 0, 0.5877852522924732, -0.8090169943749473, 1, 1, 1], [1, 0, 0, 288]], [5, "pipe_v_l", 8, 14, [[1, -483, [5, 20, 20]], [6, 45, 45, 285, 290, 0.5, 285, 290, -484]], [2, "ca0xrHCaNLeoxypV8bVJwn", 1, 0], [5, 20, 20], [-170.299, -25.271, 0, 0, 0, 0.6087614290087209, -0.793353340291235, 1, 1, 1], [1, 0, 0, 285]], [5, "pipe_v_l", 8, 14, [[1, -485, [5, 20, 20]], [6, 45, 45, 285, 290, 0.5, 285, 290, -486]], [2, "8c1jDqPJ5DvL14+twYNWAi", 1, 0], [5, 20, 20], [-150.209, -20.294, 0, 0, 0, 0.6427876096865395, -0.7660444431189779, 1, 1, 1], [1, 0, 0, 280]], [5, "pipe_v_l", 8, 14, [[1, -487, [5, 20, 20]], [6, 45, 45, 285, 290, 0.5, 285, 290, -488]], [2, "c8WVeUDxdEpa/aBT0iM8KD", 1, 0], [5, 20, 20], [-130.286, -16.627, 0, 0, 0, 0.6427876096865395, -0.7660444431189779, 1, 1, 1], [1, 0, 0, 280]], [3, "pipe_v_l", 8, 14, [[1, -489, [5, 45, 20]], [6, 44.8, 45, 280, 285, 0.5, 280, 285, -490]], [2, "8d1/0AhhZNILL8g9PYZb4n", 1, 0], [5, 45, 20], [-96.4, -14.68, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 14, [[1, -491, [5, 5, 20]], [52, 90, 2, 90, 280, 300, 2, 2, 280, 300, -492]], [2, "23LsnE4qlJer+F0tZwuNjg", 1, 0], [5, 5, 20], [-70.784, 3.657, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [38, "New Sprite(Splash)", 1, [[21, 0, -493, [0], 1]], [2, "49sMHKpzhHkr1zzv+gMvgP", 1, 0], [4, 4292122449], [5, 3000, 3000], [0, 374.745, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scene", 1, [[26, -494, [2], 3]], [2, "0fdUgVo49Bkrx/fyvC5eHR", 1, 0], [5, 751, 742], [0, 345.285, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scenne", 1, [[26, -495, [4], 5]], [2, "6ddaGAvzRLdKTZwlCnVkYX", 1, 0], [5, 750, 1334], [0, -60.123, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scene2", 56, [[21, 0, -496, [6], 7]], [2, "507fqq/X1Ctpw8HaZPzzeO", 1, 0], [5, 750, 500], [0, -900.58, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Node", 1, [[21, 0, -497, [9], 10]], [2, "61NhlptCJEjaFDf1hj2kya", 1, 0], [5, 600, 20], [6.311, -602.21, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "-30", false, 1, 1, 61, [13]], [8, "wall", 16, [[4, 1, 0, -498, [16], 17]], [0, "24c+KvVkdL4bvegRYe3/1M", 16], [5, 40, 80], [-105.567, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 16, [[4, 1, 0, -499, [18], 19]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 16], [5, 40, 80], [107.605, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "-30", false, 1, 1, 62, [20]], [8, "wall", 17, [[4, 1, 0, -500, [23], 24]], [0, "24c+KvVkdL4bvegRYe3/1M", 17], [5, 40, 80], [-105.567, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 17, [[4, 1, 0, -501, [25], 26]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 17], [5, 40, 80], [107.605, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "walkNode1", 1, [3], [2, "7dXJLiCgNKmYPUGqefegYI", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "-80", false, 1, 1, 114, [97]], [8, "wall", 15, [[4, 1, 0, -502, [100], 101]], [0, "24c+KvVkdL4bvegRYe3/1M", 15], [5, 40, 100], [-105, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 15, [[4, 1, 0, -503, [102], 103]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 15], [5, 40, 100], [105, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "icon_gatling_02mask", 18, [[29, -504, [104]]], [0, "75HLE+mRBDup/XnLxil0Hj", 18], [5, 130, 110]], [20, "pipe_v_l", false, 8, 2, [[1, -505, [5, 80, 20]]], [2, "e4g6wl+kJHVbYfQlD/XlQ4", 1, 0], [5, 80, 20], [-2.091, 59.686, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "pipe_h_t", false, 8, 2, [[1, -506, [5, 100, 15]]], [2, "74iiLr/shGi4dxe+h/c2OF", 1, 0], [5, 100, 15], [-278.1, 77.305, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "pipe_v_l", false, 8, 2, [[1, -507, [5, 200, 50]]], [2, "a4u7EaPfVMloIl3uYSz56q", 1, 0], [5, 200, 50], [172.899, -32.759, 0, 0, 0, -0.08715574274765817, 0.9961946980917455, 1, 1, 1], [1, 0, 0, -10]], [19, "pipe_v_r", false, 8, 2, [[1, -508, [5, 200, 30]]], [2, "89fY7woF1CpYHni2PVdjl5", 1, 0], [5, 200, 30], [-176.837, -38.729, 0, 0, 0, 0.17364817766693033, 0.984807753012208, 1, 1, 1], [1, 0, 0, 20]], [19, "pipe_v_r", false, 8, 2, [[1, -509, [5, 100, 30]]], [2, "c3wKmjPfxIaZaGK1dW0Wj5", 1, 0], [5, 100, 30], [-289.538, -96.03, 0, 0, 0, 0.3420201433256687, 0.9396926207859084, 1, 1, 1], [1, 0, 0, 40]], [20, "pipe_v_l", false, 8, 2, [[1, -510, [5, 10, 40]]], [2, "8b6TP5xF1H671VVjZYICBl", 1, 0], [5, 10, 40], [-1.366, 43.631, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "pipe_v_l2", false, 8, 2, [[1, -511, [5, 80, 20]]], [2, "10Q+Z4H01KVYI4JhqVXQtI", 1, 0], [5, 80, 20], [-69.41, 71.556, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 1, 1, 1], [1, 0, 0, -5]], [3, "pipe_end", 8, 1, [[1, -512, [5, 100, 40]]], [2, "9eY60rERVEUpGTNsaaFOze", 1, 0], [5, 100, 40], [-278.636, 287.656, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "hand_box", 2, 1, [2, "54ysF2VvtCd5uzaEzSjQDb", 1, 0], [5, 122, 201], [0, 0.9, 0.2], [0, -513.263, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "img_bgup", 1, [[26, -513, [108], 109]], [2, "c2MdoOGiJEfZ1fvPzl8wD+", 1, 0], [5, 750, 955], [0, -184.001, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 5, 179, 0, 0, 1, 0, -1, 155, 0, -2, 156, 0, -3, 157, 0, -4, 56, 0, -5, 159, 0, -6, 20, 0, -7, 57, 0, -8, 58, 0, -9, 59, 0, -10, 60, 0, -11, 16, 0, -12, 17, 0, -13, 166, 0, -14, 68, 0, -15, 32, 0, -16, 19, 0, -17, 15, 0, 2, 1, 0, 3, 1, 0, -19, 115, 0, -20, 116, 0, -21, 2, 0, -22, 178, 0, 2, 1, 0, 3, 1, 0, -24, 179, 0, -25, 55, 0, -26, 180, 0, 0, 2, 0, 0, 2, 0, -1, 117, 0, -2, 171, 0, -3, 118, 0, -4, 119, 0, -5, 172, 0, -6, 173, 0, -7, 174, 0, -8, 175, 0, -9, 120, 0, -10, 176, 0, -11, 177, 0, -12, 121, 0, -13, 122, 0, -14, 123, 0, -15, 13, 0, -16, 14, 0, 2, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, 2, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 26, 0, -2, 27, 0, -3, 28, 0, 2, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 29, 0, -2, 30, 0, -3, 31, 0, 2, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 33, 0, -2, 34, 0, -3, 35, 0, 2, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, 2, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, 2, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 42, 0, -2, 43, 0, -3, 44, 0, 2, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 45, 0, -2, 46, 0, -3, 47, 0, 2, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 48, 0, -2, 49, 0, -3, 50, 0, 2, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 51, 0, -2, 52, 0, -3, 53, 0, -1, 124, 0, -2, 125, 0, -3, 126, 0, -4, 127, 0, -5, 128, 0, -6, 129, 0, -7, 130, 0, -8, 131, 0, -9, 132, 0, -10, 133, 0, -11, 134, 0, -12, 135, 0, -13, 136, 0, -14, 137, 0, -15, 138, 0, -16, 139, 0, -1, 140, 0, -2, 141, 0, -3, 142, 0, -4, 143, 0, -5, 144, 0, -6, 145, 0, -7, 146, 0, -8, 147, 0, -9, 148, 0, -10, 149, 0, -11, 150, 0, -12, 151, 0, -13, 152, 0, -14, 153, 0, -15, 154, 0, 2, 15, 0, 0, 15, 0, 4, 167, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -1, 54, 0, -2, 168, 0, -3, 169, 0, -4, 18, 0, 2, 16, 0, 0, 16, 0, 4, 160, 0, 0, 16, 0, -1, 21, 0, -2, 161, 0, -3, 162, 0, 2, 17, 0, 0, 17, 0, 4, 163, 0, 0, 17, 0, -1, 22, 0, -2, 164, 0, -3, 165, 0, 2, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 170, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, -1, 61, 0, 0, 22, 0, 0, 22, 0, -1, 62, 0, 0, 23, 0, 0, 23, 0, -1, 63, 0, 0, 24, 0, -1, 64, 0, -2, 65, 0, 0, 25, 0, -1, 66, 0, -2, 67, 0, 0, 26, 0, 0, 26, 0, -1, 69, 0, 0, 27, 0, -1, 70, 0, -2, 71, 0, 0, 28, 0, -1, 72, 0, -2, 73, 0, 0, 29, 0, 0, 29, 0, -1, 74, 0, 0, 30, 0, -1, 75, 0, -2, 76, 0, 0, 31, 0, -1, 77, 0, -2, 78, 0, 0, 33, 0, 0, 33, 0, -1, 79, 0, 0, 34, 0, -1, 80, 0, -2, 81, 0, 0, 35, 0, -1, 82, 0, -2, 83, 0, 0, 36, 0, 0, 36, 0, -1, 84, 0, 0, 37, 0, -1, 85, 0, -2, 86, 0, 0, 38, 0, -1, 87, 0, -2, 88, 0, 0, 39, 0, 0, 39, 0, -1, 89, 0, 0, 40, 0, -1, 90, 0, -2, 91, 0, 0, 41, 0, -1, 92, 0, -2, 93, 0, 0, 42, 0, 0, 42, 0, -1, 94, 0, 0, 43, 0, -1, 95, 0, -2, 96, 0, 0, 44, 0, -1, 97, 0, -2, 98, 0, 0, 45, 0, 0, 45, 0, -1, 99, 0, 0, 46, 0, -1, 100, 0, -2, 101, 0, 0, 47, 0, -1, 102, 0, -2, 103, 0, 0, 48, 0, 0, 48, 0, -1, 104, 0, 0, 49, 0, -1, 105, 0, -2, 106, 0, 0, 50, 0, -1, 107, 0, -2, 108, 0, 0, 51, 0, 0, 51, 0, -1, 109, 0, 0, 52, 0, -1, 110, 0, -2, 111, 0, 0, 53, 0, -1, 112, 0, -2, 113, 0, 0, 54, 0, 0, 54, 0, -1, 114, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, -1, 158, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, -1, 160, 0, 0, 61, 0, -1, 163, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, 0, 97, 0, 0, 97, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, 0, 99, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 0, 104, 0, 0, 105, 0, 0, 105, 0, 0, 106, 0, 0, 106, 0, 0, 107, 0, 0, 107, 0, 0, 108, 0, 0, 108, 0, 0, 109, 0, 0, 109, 0, 0, 110, 0, 0, 110, 0, 0, 111, 0, 0, 111, 0, 0, 112, 0, 0, 112, 0, 0, 113, 0, 0, 113, 0, -1, 167, 0, 0, 114, 0, 0, 115, 0, 0, 115, 0, 0, 116, 0, 0, 116, 0, 0, 117, 0, 0, 117, 0, 0, 118, 0, 0, 118, 0, 0, 119, 0, 0, 119, 0, 0, 120, 0, 0, 120, 0, 0, 121, 0, 0, 121, 0, 0, 122, 0, 0, 122, 0, 0, 123, 0, 0, 123, 0, 0, 124, 0, 0, 124, 0, 0, 125, 0, 0, 125, 0, 0, 126, 0, 0, 126, 0, 0, 127, 0, 0, 127, 0, 0, 128, 0, 0, 128, 0, 0, 129, 0, 0, 129, 0, 0, 130, 0, 0, 130, 0, 0, 131, 0, 0, 131, 0, 0, 132, 0, 0, 132, 0, 0, 133, 0, 0, 133, 0, 0, 134, 0, 0, 134, 0, 0, 135, 0, 0, 135, 0, 0, 136, 0, 0, 136, 0, 0, 137, 0, 0, 137, 0, 0, 138, 0, 0, 138, 0, 0, 139, 0, 0, 139, 0, 0, 140, 0, 0, 140, 0, 0, 141, 0, 0, 141, 0, 0, 142, 0, 0, 142, 0, 0, 143, 0, 0, 143, 0, 0, 144, 0, 0, 144, 0, 0, 145, 0, 0, 145, 0, 0, 146, 0, 0, 146, 0, 0, 147, 0, 0, 147, 0, 0, 148, 0, 0, 148, 0, 0, 149, 0, 0, 149, 0, 0, 150, 0, 0, 150, 0, 0, 151, 0, 0, 151, 0, 0, 152, 0, 0, 152, 0, 0, 153, 0, 0, 153, 0, 0, 154, 0, 0, 154, 0, 0, 155, 0, 0, 156, 0, 0, 157, 0, 0, 158, 0, 0, 159, 0, 0, 161, 0, 0, 162, 0, 0, 164, 0, 0, 165, 0, 0, 168, 0, 0, 169, 0, 0, 170, 0, 0, 171, 0, 0, 172, 0, 0, 173, 0, 0, 174, 0, 0, 175, 0, 0, 176, 0, 0, 177, 0, 0, 178, 0, 0, 180, 0, 6, 1, 3, 3, 166, 4, 3, 68, 5, 3, 68, 6, 3, 32, 7, 3, 32, 8, 3, 32, 9, 3, 19, 10, 3, 19, 11, 3, 19, 12, 3, 19, 513], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1], [0, 5, 0, 6, 0, 7, 0, 4, 0, 0, 4, 0, 8, 0, 0, 3, 0, 1, 0, 1, 0, 0, 3, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 2, 0, 1, 0, 1, 0, 0, 3, 0, 1, 0, 1, 0, 0, 0, 1, 0, 9]]