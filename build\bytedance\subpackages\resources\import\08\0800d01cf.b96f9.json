[1, ["ecpdLyjvZBwrvm+cedCcQy", "85blPbkQhFubOrMcHoFvIi", "e14bTA5z5AAb5h76T/03bw", "a2MjXRFdtLlYQ5ouAFv/+R", "86IEClttZOcbBlnDuaCb28", "645icTdZ1N84r/9XGMJIhQ"], ["node", "_spriteFrame", "root", "data", "_parent", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_anchorPoint"], 0, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_fontSize", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingBottom", "node", "_layoutSize"], -1, 1, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["a57527voyxHk5W712q4M2Ei", ["node", "nodeArr"], 3, 1, 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[3, 0, 1, 2, 2], [0, 0, 6, 5, 3, 4, 8, 2], [0, 0, 6, 5, 3, 9, 4, 10, 8, 2], [0, 0, 6, 7, 5, 3, 4, 8, 2], [0, 0, 6, 7, 5, 3, 4, 2], [2, 1, 0, 2, 3, 4, 3], [10, 0, 1, 2, 2], [0, 0, 6, 7, 3, 4, 8, 2], [2, 2, 3, 4, 1], [4, 0, 1, 2, 4, 5, 4], [5, 0, 1, 2, 3, 4], [1, 0, 5, 1, 4, 2, 3, 8, 9, 7], [1, 0, 5, 6, 1, 2, 3, 7, 8, 9, 8], [1, 0, 5, 6, 1, 4, 2, 3, 7, 8, 9, 9], [7, 0, 2], [0, 0, 1, 7, 5, 3, 4, 3], [0, 0, 7, 5, 3, 4, 2], [0, 0, 2, 6, 5, 3, 9, 4, 3], [0, 0, 6, 5, 3, 4, 2], [8, 0, 1, 1], [3, 1, 2, 1], [2, 0, 2, 3, 4, 2], [9, 0, 1], [4, 0, 1, 3, 2, 4, 5, 5], [5, 0, 3, 2], [1, 0, 1, 4, 2, 3, 8, 9, 6]], [[[[14, "Property_DisplayView"], [15, "Property_DisplayView", 1, [-5, -6], [[19, -4, [-2, -3]]], [20, -1, 0], [5, 750, 1335]], [16, "content", [-10, -11, -12, -13, -14], [[5, 1, 0, -7, [17], 18], [22, -8], [23, 1, 2, 30, 10, -9, [5, 680, 388]]], [0, "3fBLGLK/9I64VoqeVraltV", 1, 0], [5, 680, 388]], [3, "main", 2, [-16], [[9, 1, 2, 10, -15, [5, 680, 46]]], [0, "d6vFOZnItJQZmODWQBncmo", 1, 0], [5, 680, 46], [0, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "item", 3, [-18, -19], [[5, 1, 0, -17, [8], 9]], [0, "0c02bGGCRHyodlm4tTCsVa", 1, 0], [5, 640, 46]], [3, "sqe", 2, [-21], [[9, 1, 2, 10, -20, [5, 680, 46]]], [0, "cd7hrZkR5DZIsFjKZQL8DW", 1, 0], [5, 680, 46], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "item", 5, [-23, -24], [[5, 1, 0, -22, [15], 16]], [0, "03s8vcSLdO6IfnHju1Zvb/", 1, 0], [5, 640, 46]], [17, "mask", 177.98999999999998, 1, [[21, 0, -25, [0], 1], [10, 45, 750, 1334, -26]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4281542699], [5, 750, 1335]], [4, "bg", 1, [2], [[10, 45, 750, 1334, -27]], [0, "0fUFUTX/FJ9oWQdPB+OXtu", 1, 0], [5, 750, 1335]], [3, "title_zhua<PERSON><PERSON>", 2, [-29], [[24, 1, -28]], [0, "b5RklW5wtFCoeQEs7JmIZC", 1, 0], [5, 488, 86], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "Label_title", 9, [[25, "属性详情", false, 1, 1, 1, -30, [2]], [6, 4, -31, [4, 4278190080]]], [0, "f0YAxpgTVEL5P9vFDZFbvd", 1, 0], [5, 168, 58.4]], [7, "title", 2, [-32, -33], [0, "fd80GYMhlA2bl2eJPQUYgD", 1, 0], [5, 680, 70], [0, 63, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label_title", 11, [[11, "基础属性", 34, false, 1, 1, 1, -34, [5]], [6, 4, -35, [4, 4278190080]]], [0, "6e5u3tJrlI+IZ/DMAJal5x", 1, 0], [5, 144, 58.4], [-182.951, -7.484, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "title", 2, [-36, -37], [0, "d8hpIsnnRHhYlV47TeqD2e", 1, 0], [5, 680, 70], [0, -73, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label_title", 13, [[11, "高级属性", 34, false, 1, 1, 1, -38, [12]], [6, 4, -39, [4, 4278190080]]], [0, "9dvlwuvZhMW4LQdK/ZIS0u", 1, 0], [5, 144, 58.4], [-182.951, -7.484, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_xx", 11, [[8, -40, [3], 4]], [0, "8csvhArRZL+4f1lSx+WDoU", 1, 0], [5, 30, 30], [-286.07, -9.811, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 4, [[12, "0", 30, false, false, 1, 1, 1, -41, [6]]], [0, "8dVctmDFpCk5TXxEh98le1", 1, 0], [4, 4281940021], [5, 16.68, 50.4], [0, 1, 0.5], [300.686, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "name", 4, [[13, "等级", 25, false, false, 1, 1, 1, 1, -42, [7]]], [0, "36+9M7kjNDv4kAw7J+GkTl", 1, 0], [4, 4281940021], [5, 50, 50.4], [0, 0, 0.5], [-301.018, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_xx", 13, [[8, -43, [10], 11]], [0, "d7nqdREvFNs72gQpuPLW/C", 1, 0], [5, 30, 30], [-286.07, -9.811, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "val", 6, [[12, "0", 30, false, false, 1, 1, 1, -44, [13]]], [0, "30+pTDbb1FQaGRd/gc4zXz", 1, 0], [4, 4281940021], [5, 16.68, 50.4], [0, 1, 0.5], [300.686, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "name", 6, [[13, "等级", 25, false, false, 1, 1, 1, 1, -45, [14]]], [0, "16Ivw6QaNJ8I1KmmNDfB56", 1, 0], [4, 4281940021], [5, 50, 50.4], [0, 0, 0.5], [-301.018, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, -1, 3, 0, -2, 5, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 11, 0, -3, 3, 0, -4, 13, 0, -5, 5, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, -1, 16, 0, -2, 17, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 19, 0, -2, 20, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 10, 0, -1, 15, 0, -2, 12, 0, 0, 12, 0, 0, 12, 0, -1, 18, 0, -2, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 3, 1, 2, 4, 8, 45], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1], [0, 3, 0, 0, 1, 0, 0, 0, 0, 2, 0, 1, 0, 0, 0, 0, 2, 0, 4]], [[{"name": "img_xx", "rect": [0, 0, 30, 30], "offset": [0, 0], "originalSize": [30, 30], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [5], [5]]]]