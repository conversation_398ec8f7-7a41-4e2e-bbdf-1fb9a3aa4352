
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/start/scripts/Launcher.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '5b59eOsFG5FwJQQDX+d+/Zn', 'Launcher');
// start/scripts/Launcher.js

"use strict";

var i;
var $callID = require("./CallID");
var $cfg = require("./Cfg");
var $mVC = require("./MVC");
var $notifier = require("./Notifier");
var $listenID = require("./ListenID");
var $manager = require("./Manager");
var $time = require("./Time");
var $uIManager = require("./UIManager");
var $alertManager = require("./AlertManager");
var $eventController = require("./EventController");
var $game = require("./Game");
var $sdkConfig = require("./SdkConfig");
var $wonderSdk = require("./WonderSdk");
var $gameUtil = require("./GameUtil");
var $moduleLauncher = require("./ModuleLauncher");
var $sdkLauncher = require("./SdkLauncher");
var $uILauncher = require("./UILauncher");
var k = cc._decorator;
var O = k.ccclass;
var I = k.property;
var A = function (e) {
  function t() {
    var t;
    var o = null !== e && e.apply(this, arguments) || this;
    o.testMode = !1;
    o.CustomPlatform = $sdkConfig.EPlatform.WEB_DEV;
    o.bmsVersion = "";
    o.progress = null;
    o.progressText = null;
    o.wonderlogo = null;
    o.gamelogo = null;
    o.logos = [];
    o.scenebg = null;
    o.softRightText = null;
    o.softICPText = null;
    o.logomap = ((t = {}).tc = 0, t.en = 1, t.th = 2, t.vn = 3, t.cn = 4, t);
    o._saveOffset = 0;
    return o;
  }
  __extends(t, e);
  t.prototype.onLoad = function () {
    var e = this;
    console.log("[Launcher][onLoad]");
    cc._gameManager = $manager.Manager;
    cc.game.addPersistRootNode(this.node);
    cc.macro.ENABLE_MULTI_TOUCH = !1;
    if (cc.sys.isBrowser) {
      cc.view.enableAutoFullScreen(!1);
      this.scheduleOnce(function () {
        e.fit();
      });
    }
    cc.sys.hasFont = !1;
    $notifier.Notifier.send($listenID.ListenID.Event_SendEvent, "View", {
      Type: "show",
      Scene: "loading"
    });
    $manager.Manager.setPhysics(!0);
    $manager.Manager.setPhysics(!1);
  };
  t.prototype.fit = function () {
    var e = cc.view.getVisibleSize();
    var t = e.width / e.height;
    var o = Math.round(100 * t);
    if (o > 57) {
      if (o >= 100) {
        cc.Canvas.instance.fitHeight = !0, cc.Canvas.instance.fitWidth = !0;
      } else {
        cc.Canvas.instance.fitHeight = !0, cc.Canvas.instance.fitWidth = !1;
      }
    }
    cc.debug.setDisplayStats(!1);
  };
  t.prototype.onEnable = function () {
    this.changeListener(!0);
  };
  t.prototype.onDisable = function () {
    this.changeListener(!1);
  };
  t.prototype.changeListener = function (e) {
    $notifier.Notifier.changeListener(e, $listenID.ListenID.Login_Finish, this.onLogin_Finish, this, $notifier.PriorLowest);
    $notifier.Notifier.changeListener(e, $listenID.ListenID.Game_Load, this.onOpenGame, this, -200);
    $notifier.Notifier.changeListener(e, $listenID.ListenID.Fight_BackToMain, this.backToMain, this, 200);
  };
  t.prototype.lateUpdate = function () {};
  t.prototype.onLogin_Finish = function () {
    this.wonderlogo.parent.destroy();
    this.gameStart();
  };
  t.prototype.onOpenGame = function () {
    this.scenebg.setActive(!1);
  };
  t.prototype.backToMain = function () {
    this.scenebg.setActive(!0);
  };
  t.prototype.responsive = function () {
    var e = cc.view.getDesignResolutionSize();
    var t = cc.view.getFrameSize();
    var o = function o() {
      cc.Canvas.instance.fitHeight = !0;
      cc.Canvas.instance.fitWidth = !0;
    };
    var i = e.width / e.height;
    var n = t.width / t.height;
    if (i < 1) {
      if (n < 1) {
        if (n > i) {
          o();
        } else {
          cc.Canvas.instance.fitHeight = !1, cc.Canvas.instance.fitWidth = !0;
        }
      } else {
        o();
      }
    } else {
      if (n > 1) {
        if (n < i) {
          o();
        } else {
          cc.Canvas.instance.fitHeight = !0, cc.Canvas.instance.fitWidth = !1;
        }
      } else {
        o();
      }
    }
  };
  t.prototype.start = function () {
    var e;
    return __awaiter(this, void 0, void 0, function () {
      return __generator(this, function (t) {
        switch (t.label) {
          case 0:
            this.initWonderFrameWork();
            if (wonderSdk.isNative) {
              //
            } else {
              new $eventController.EventController();
            }
            console.log("[Launcher][BMS_APP_NAME]", wonderSdk.BMS_APP_NAME);
            console.log("[Launcher][BMS_VERSION]", wonderSdk.BMS_VERSION);
            this.checkPlatformInfo();
            return [4, this.loadConfig()];
          case 1:
            t.sent();
            window.tpdg = $cfg.Cfg.language.getAll();
            window.initlang("tpdg");
            this.initLanguageInfo();
            this.gamelogo.spriteFrame = this.logos[null !== (e = this.logomap[cc.sys.language]) && void 0 !== e ? e : 4];
            if (wonderSdk.isIOS) {
              this.wonderlogo.setActive(!1);
            }
            new $uILauncher.UILauncher();
            this.progressText.string = cc.js.formatStr("%d%", 0);
            this.progress.progress = 0;
            new $moduleLauncher.ModuleLauncher();
            new $sdkLauncher.SdkLauncher(this.progressText, this.progress);
            return [2];
        }
      });
    });
  };
  t.prototype.checkPlatformInfo = function () {
    if (wonderSdk.isNative) {
      this.wonderlogo.active = !1;
    }
    this.softRightText.string = $sdkConfig.SoftRightHodler[this.CustomPlatform];
    this.softICPText.string = $sdkConfig.SoftICP[this.CustomPlatform];
  };
  t.prototype.update = function (e) {
    $time.Time.update(e);
    $uIManager.UIManager.update(e);
  };
  t.prototype.initWonderFrameWork = function () {
    $wonderSdk.WonderSdk.init(this.CustomPlatform, this.testMode);
    if (this.bmsVersion.length > 0) {
      $sdkConfig.BMSInfoList[this.CustomPlatform].BMS_VERSION = this.bmsVersion;
      console.log("已修改Bms版本号");
    }
  };
  t.prototype.loadConfig = function () {
    var e = [];
    var t = wonderSdk.isNative;
    for (var o in $cfg.Cfg.keyJson) if (t || "language" != o) {
      e.push($cfg.Cfg.initByBaseConfig(o, this.progressText, this.progress));
      // e.push($cfg.Cfg.initLocalJson(o, this.progressText, this.progress));
    }

    return Promise.all(e);
  };
  t.prototype.initLanguageInfo = function () {
    var e = this.initLangCode();
    $gameUtil.CCTool.Language.init(e, function () {
      console.log("[languageFun][init]语言包初始化完成", e);
    });
  };
  t.prototype.initLangCode = function () {
    var e = cc.sys.LANGUAGE_ENGLISH;
    try {
      var t = cc.sys.language;
      var o = cc.sys.languageCode;
      cc.log("[lType]", t, o);
      if ("zh" === t) {
        if (-1 != o.indexOf("hant") || -1 != o.indexOf("tw") || -1 != o.indexOf("hk") || -1 != o.indexOf("mo")) {
          e = "tc", cc.sys.hasFont = !1;
        } else {
          e = "zh";
        }
      } else {
        if ("ja" == t) {
          e = "jp", cc.sys.hasFont = !1;
        } else {
          if ("ko" == t) {
            e = "kr", cc.sys.hasFont = !1;
          } else {
            if (-1 != o.indexOf("vi") || -1 != o.indexOf("vn")) {
              e = "vn", cc.sys.hasFont = !1;
            } else {
              e = -1 != o.indexOf("th") || -1 != t.indexOf("th") ? "en" : -1 != o.indexOf("id") || -1 != o.indexOf("in") ? "ina" : "en";
            }
          }
        }
      }
      console.log("[Language] --> 初始化语言:  lan: " + e + " systype: " + t + " syscode: " + cc.sys.languageCode);
      return e;
    } catch (e) {}
  };
  t.prototype.gameStart = function () {
    var e;
    console.log("[进入游戏 gameStart] 1.00", this.progress.progress);
    $manager.Manager.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);
    Object.defineProperty(cc.game, "collisionMatrix", JSON.parse($manager.Manager.oldGroupMatrix));
    if (!this.testMode && wonderSdk.isLive) {
      if ($manager.Manager.vo.userVo.code) {
        wonderSdk.requestLoginCode($manager.Manager.vo.userVo.code).then(function () {
          $alertManager.AlertManager.showNormalTips("验证成功");
        })["catch"](function () {
          // $manager.Manager.vo.userVo.code = null;
          // $manager.Manager.vo.userVo.ca_code = null;
          $alertManager.AlertManager.showNormalTips("验证码过期");
          $uIManager.UIManager.Open("ui/setting/H5CodeView");
          $manager.Manager.vo.saveUserData();
        });
      } else {
        $uIManager.UIManager.Open("ui/setting/H5CodeView");
      }
    }
    var t = null;
    if (null === (e = $notifier.Notifier.call($callID.CallID.Platform_Query)) || void 0 === e) {
      t = void 0;
    } else {
      t = e.mode;
    }
    if (t) {
      $notifier.Notifier.send($listenID.ListenID.Is_Back_From_Try_Play);
      var o = $cfg.Cfg.MiniGameLv.get(t);
      var i = $game.Game.getMouth(o.type);
      $notifier.Notifier.send(i.mouth, o.type, $mVC.MVC.openArgs().setParam({
        id: o.id,
        isTryPaly: !0
      }));
    } else {
      $notifier.Notifier.send($listenID.ListenID.BottomBar_OpenView, 1);
    }
    if (wonderSdk.isByteDance) {
      var n = $notifier.Notifier.call($callID.CallID.Platform_CdKey);
      $notifier.Notifier.send($listenID.ListenID.ByteDance_Check_Gift, n, !0);
    } else {
      if (wonderSdk.isBLMicro) {
        $notifier.Notifier.send($listenID.ListenID.Platform_CheckScene, $notifier.Notifier.call($callID.CallID.Platform_GetScene));
      }
    }
    $notifier.Notifier.send($listenID.ListenID.Event_SendEvent, "View", {
      Type: "hide",
      Scene: "loading"
    });
  };
  t.isBreak = !1;
  __decorate([I({
    displayName: "测试模式"
  })], t.prototype, "testMode", void 0);
  __decorate([I({
    type: $sdkConfig.EPlatform,
    displayName: "自定义平台"
  })], t.prototype, "CustomPlatform", void 0);
  __decorate([I({
    displayName: "BMS版本号"
  })], t.prototype, "bmsVersion", void 0);
  __decorate([I(cc.ProgressBar)], t.prototype, "progress", void 0);
  __decorate([I(cc.Label)], t.prototype, "progressText", void 0);
  __decorate([I(cc.Node)], t.prototype, "wonderlogo", void 0);
  __decorate([I(cc.Sprite)], t.prototype, "gamelogo", void 0);
  __decorate([I([cc.SpriteFrame])], t.prototype, "logos", void 0);
  __decorate([I(cc.Node)], t.prototype, "scenebg", void 0);
  __decorate([I(cc.Label)], t.prototype, "softRightText", void 0);
  __decorate([I(cc.Label)], t.prototype, "softICPText", void 0);
  return __decorate([O], t);
}(cc.Component);
exports["default"] = A;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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