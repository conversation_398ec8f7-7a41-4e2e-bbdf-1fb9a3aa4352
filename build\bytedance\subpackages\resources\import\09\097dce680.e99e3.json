[1, ["ecpdLyjvZBwrvm+cedCcQy", "c6e7Id/hNJv5R+BvIjjgmA", "97IAXRHnlLq7dNeJPB6SbL", "3er+4nfTdDraxqzfRo0/Px", "27Yfw1ozdKk4VytTPe0kb2", "71shDIeyVKb561nkxcA1rp", "a2MjXRFdtLlYQ5ouAFv/+R", "3abTDM3VVCaJ1tVX3h52C/", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "08On+gNqxJBJ6h7ZmY7Ala", "8d7t/9PoNN66/ncFvi+j5F"], ["node", "_spriteFrame", "_parent", "_textureSetter", "root", "rewardItem", "data", "_N$disabledSprite"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Label", ["_N$horizontalAlign", "_N$verticalAlign", "_string", "_isSystemFontUsed", "_styleFlags", "_fontSize", "_lineHeight", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 1, 2, 4, 5, 7, 1, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], "cc.SpriteFrame", ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["e57fdW4+I1HfaPtrOtgaFHx", ["node", "labelArr", "rewardItem"], 3, 1, 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["87bed3IyxNFlq8CyuFedRT0", ["node", "val", "icon", "bg", "tag"], 3, 1, 1, 1, 1, 1]], [[3, 0, 1, 2, 2], [12, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 8, 2], [4, 1, 0, 2, 3, 4, 3], [1, 2, 5, 6, 4, 0, 1, 10, 11, 7], [0, 0, 7, 9, 6, 4, 5, 8, 2], [3, 0, 1, 2], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3, 4], [4, 0, 2, 3, 4, 2], [4, 2, 3, 1], [2, 0, 1, 6, 2, 3, 4, 5, 3], [7, 0, 2, 3, 4, 5, 6, 2], [10, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 9, 4, 5, 2], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [0, 0, 1, 7, 6, 4, 10, 5, 8, 3], [0, 0, 3, 7, 6, 4, 10, 5, 8, 3], [2, 0, 1, 7, 2, 3, 4, 5, 3], [2, 0, 1, 6, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 7, 6, 3], [11, 0, 1, 2, 1], [3, 1, 2, 1], [5, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [5, 0, 8, 2], [5, 0, 1, 2, 8, 4], [8, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 3, 3], [1, 2, 3, 4, 0, 1, 10, 11, 6], [1, 2, 7, 3, 0, 1, 8, 9, 10, 11, 8], [1, 5, 3, 0, 1, 10, 11, 5], [13, 0, 1, 2, 3, 4, 1]], [[[{"name": "img_qr_lb", "rect": [0, 0, 212, 93], "offset": [0, 0], "originalSize": [212, 93], "capInsets": [23, 58, 167, 24]}], [6], 0, [0], [3], [4]], [[{"name": "button03", "rect": [0, 0, 91, 86], "offset": [0, 0], "originalSize": [91, 86], "capInsets": [32, 0, 35, 0]}], [6], 0, [0], [3], [5]], [[[13, "M20_Prepare_BagUse"], [14, "M20_Prepare_BagUse", 1, [-6, -7], [[25, -5, [-3, -4], -2]], [26, -1, 0], [5, 750, 1334]], [22, "RewardItem", 1, [-10, -11, -12, -13], [-9], [6, "76Ut5OpF5D9YBP8y6Vd+Mk", -8], [5, 116, 120], [0, 275, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "content", [-14, -15, 2, -16, -17, -18, -19, -20], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 645, 930]], [5, "img_zjjld", 3, [-22, -23, -24, -25, -26, -27, -28], [[3, 1, 0, -21, [35], 36]], [0, "50UFpLxvBIIIwUfA1vPXig", 1, 0], [5, 550, 150], [0, -92.161, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Background", [-31], [[3, 1, 0, -29, [8], 9], [27, 0, 45, 9.5, 9.5, 1.8549999999999995, 13.145, 100, 40, -30]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 61, 65], [0, 5.645, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "title_zhua<PERSON><PERSON>", 3, [-33, -34], [[3, 1, 0, -32, [5], 6]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 645, 750], [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_zjjld1", 3, [-36, -37], [[3, 1, 0, -35, [17], 18]], [0, "c8TozFMadFqJW9H757twfn", 1, 0], [5, 550, 150], [0, 80, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "button02", 4, [-40], [[7, 3, -38, [[8, "e57fdW4+I1HfaPtrOtgaFHx", "onBtn", "-10", 1]]], [3, 1, 0, -39, [24], 25]], [0, "50z0lOj2VDQ60HvlrMGtMI", 1, 0], [5, 150, 86], [-180, -15, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [5, "button02", 4, [-43], [[3, 1, 0, -41, [27], 28], [7, 3, -42, [[8, "e57fdW4+I1HfaPtrOtgaFHx", "onBtn", "-", 1]]]], [0, "45kxKYuitCB6LP2ghpWKHz", 1, 0], [5, 100, 86], [-80, -15, 0, 0, 0, 0, 1, 0.5, 0.5, 0]], [5, "button02", 4, [-46], [[3, 1, 0, -44, [30], 31], [7, 3, -45, [[8, "e57fdW4+I1HfaPtrOtgaFHx", "onBtn", "+", 1]]]], [0, "76hw/9bCFP1J2PrLfMgUR3", 1, 0], [5, 100, 86], [80, -15, 0, 0, 0, 0, 1, 0.5, 0.5, 0]], [5, "button02", 4, [-49], [[3, 1, 0, -47, [33], 34], [7, 3, -48, [[8, "e57fdW4+I1HfaPtrOtgaFHx", "onBtn", "+10", 1]]]], [0, "a2sD2CpRhO1L8WfU6MprC+", 1, 0], [5, 150, 86], [180, -15, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [5, "button03", 3, [-52], [[3, 1, 0, -50, [38], 39], [7, 3, -51, [[8, "e57fdW4+I1HfaPtrOtgaFHx", "onBtn", "use", 1]]]], [0, "d3qNKDnlJO0L/dynjorIrm", 1, 0], [5, 200, 86], [0, -250, 0, 0, 0, 0, 1, 1.2, 1.2, 0]], [17, "maskbg", 70, 1, [[28, 45, -53], [9, 0, -54, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [18, "bg", 1, [3], [[29, 45, 750, 1334, -55]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [2, "Label_title", 6, [[32, "使用", false, 1, 1, 1, -56, [4]], [1, 3, -57, [4, 4279374353]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 86, 56.4], [0, 335, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnclose", 3, [5], [[30, 3, -58, [[31, "e57fdW4+I1HfaPtrOtgaFHx", "close", 1]], [4, 4293322470], [4, 3363338360], 5, 10]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [270, 380, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "val", 1, 2, [[-59, [1, 3, -60, [4, 4278190080]]], 1, 4], [6, "e5J6wr5jNMgbXxWtPqgyAx", 2], [5, 6, 56.4], [0, 1, 0.5], [55, -31.859, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 7, [[4, "效果", 25, 25, 1, 1, 1, -61, [15]], [1, 3, -62, [4, 4278190080]]], [0, "adAicj4GtKvIf/BbXmi2aA", 1, 0], [5, 56, 37.5], [-226.252, 48.831, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "desc", 7, [[4, "发生大范围大范围大范围大范", 25, 25, 1, 1, 1, -63, [16]], [1, 3, -64, [4, 4278190080]]], [0, "6bYtfTbaJOprQtMnsxcslo", 1, 0], [5, 331, 37.5], [0, 0, 0.5], [-249.32, -10.760999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 4, [[4, "使用数量", 25, 25, 1, 1, 1, -65, [19]], [1, 3, -66, [4, 4278190080]]], [0, "bbln2mHqtLdK+eixt0xPzJ", 1, 0], [5, 106, 37.5], [-200, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "useNum", 4, [[-67, [1, 3, -68, [4, 4278190080]]], 1, 4], [0, "23cALHLy5E4JDtLeX+rLG6", 1, 0], [5, 61.62, 37.5], [0, -14.769, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 8, [[4, "-10", 35, 35, 1, 1, 1, -69, [23]], [1, 3, -70, [4, 4278190080]]], [0, "e7d+gue9tMgo+FvrsNCITT", 1, 0], [5, 56.59, 50.1], [0, 2.4, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 9, [[4, "-", 30, 30, 1, 1, 1, -71, [26]], [1, 3, -72, [4, 4278190080]]], [0, "4bs8UDtR9EFaQW4y/54W9z", 1, 0], [5, 15.99, 43.8], [0, 8, 0, 0, 0, 0, 1, 2, 2, 1]], [2, "New Label", 10, [[4, "+", 30, 30, 1, 1, 1, -73, [29]], [1, 3, -74, [4, 4278190080]]], [0, "2aRk5JNBdITLoZFqGWRnXo", 1, 0], [5, 23.52, 43.8], [0, 4, 0, 0, 0, 0, 1, 2, 2, 1]], [2, "New Label", 11, [[4, "+10", 35, 35, 1, 1, 1, -75, [32]], [1, 3, -76, [4, 4278190080]]], [0, "c3rrOCJrdGOIMDllrfGSRg", 1, 0], [5, 65.37, 50.1], [0, 2.4, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 12, [[4, "使用", 30, 30, 1, 1, 1, -77, [37]], [1, 3, -78, [4, 4278190080]]], [0, "dbgUT6hyhFCaE2SxK4Ok3m", 1, 0], [5, 66, 43.8], [0, 6.511, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "desc", 3, [[-79, [1, 3, -80, [4, 4278190080]]], 1, 4], [0, "78ri6mwl5AvIDCE/DX2FAF", 1, 0], [5, 331, 37.5], [0, 182.03, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "img_fgx", 62, 6, [[9, 0, -81, [2], 3]], [0, "9a72ch0RxBzJtGM7W5+RzH", 1, 0], [4, 4285252343], [5, 130, 130], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Label", false, 5, [[33, "返回", false, false, 1, 1, 1, 1, -82, [7]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "bg", 1, 2, [-83], [6, "c0PNkrYNhGKKfnBMRIeCKX", 2], [5, 116, 116]], [10, 30, [11]], [11, "icon", 1, 2, [-84], [6, "bdbdts/cdCX6DWFKTsgAm5", 2], [5, 64, 59], [0, 1.044, 0, 0, 0, 0, 1, 1, 1, 1]], [10, 32, [12]], [11, "tag", 1, 2, [-85], [6, "91sTlwcfxEKqcCGENXj3HV", 2], [5, 35, 35], [-47.789, 51.148, 0, 0, 0, 0, 1, 1.3, 1.3, 1.3]], [10, 34, [13]], [34, 30, false, 2, 1, 17, [14]], [35, 2, 36, 33, 31, 35], [2, "gg_tipwzdb", 4, [[3, 1, 0, -86, [20], 21]], [0, "f316rF9rJGD63yrcmDFnzQ", 1, 0], [5, 150, 40], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "1000", 25, 25, 1, 1, 1, 21, [22]], [2, "img_fgx", 3, [[9, 0, -87, [40], 41]], [0, "464HH5JRhJgKcnAUSQbgxd", 1, 0], [5, 550, 3], [0, -180, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "发生大范围大范围大范围大范", 25, 25, 1, 1, 1, 27, [42]]], 0, [0, 4, 1, 0, 5, 37, 0, -1, 39, 0, -2, 41, 0, 0, 1, 0, -1, 13, 0, -2, 14, 0, 4, 2, 0, -1, 37, 0, -1, 30, 0, -2, 32, 0, -3, 34, 0, -4, 17, 0, -1, 6, 0, -2, 16, 0, -4, 7, 0, -5, 4, 0, -6, 12, 0, -7, 40, 0, -8, 27, 0, 0, 4, 0, -1, 20, 0, -2, 38, 0, -3, 21, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, 0, 5, 0, 0, 5, 0, -1, 29, 0, 0, 6, 0, -1, 28, 0, -2, 15, 0, 0, 7, 0, -1, 18, 0, -2, 19, 0, 0, 8, 0, 0, 8, 0, -1, 22, 0, 0, 9, 0, 0, 9, 0, -1, 23, 0, 0, 10, 0, 0, 10, 0, -1, 24, 0, 0, 11, 0, 0, 11, 0, -1, 25, 0, 0, 12, 0, 0, 12, 0, -1, 26, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 36, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 39, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 41, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, 0, 38, 0, 0, 40, 0, 6, 1, 2, 2, 3, 3, 2, 14, 5, 2, 16, 87], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, -1, 1, 7, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1], [0, 6, 0, 2, 0, 0, 7, 0, 0, 8, 9, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 10, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 3, 0, 0, 11, 0, 2, 0]]]]