[1, ["ecpdLyjvZBwrvm+cedCcQy", "19ThLZdFBNGYDmlBfJ5t0W", "45wixcNMlOyqIIMmcLIcs3", "5b0HySO0xNLI16cb6FYAR2"], ["node", "_spriteFrame", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color"], 2, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_enabled", "node", "_materials"], -4, 1, 3], ["cc.LabelOutline", ["_width", "_enabled", "node", "_color"], 1, 1, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["26674weeJVB2a3g+1Zx+GHV", ["node"], 3, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_N$horizontalDirection", "node", "_layoutSize"], 0, 1, 5], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -4, 1], ["cc.RichText", ["_N$string", "_N$fontSize", "_N$lineHeight", "node"], 0, 1]], [[1, 0, 1, 2, 2], [0, 0, 4, 1, 2, 3, 5, 2], [2, 0, 1, 2, 3, 4, 5, 7, 8, 7], [3, 0, 2, 3, 2], [6, 0, 2], [0, 0, 6, 1, 2, 3, 2], [0, 0, 4, 6, 1, 2, 3, 5, 2], [0, 0, 4, 1, 2, 3, 7, 5, 2], [0, 0, 4, 1, 2, 8, 3, 5, 2], [0, 0, 4, 1, 2, 3, 2], [7, 0, 1], [1, 1, 2, 1], [8, 0, 1, 2, 3, 4, 4], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8], [2, 6, 0, 1, 2, 3, 4, 5, 7, 8, 8], [3, 1, 0, 2, 3, 3], [10, 0, 1, 2, 3, 4], [4, 1, 0, 2, 3, 4, 3], [4, 0, 2, 3, 4, 2]], [[[{"name": "img_cw_lieb05", "rect": [0, 0, 63, 112], "offset": [0, 0], "originalSize": [63, 112], "capInsets": [12, 56, 21, 29]}], [5], 0, [0], [2], [1]], [[[4, "PetSkillListItem"], [5, "PetSkillListItem", [-3, -4, -5, -6], [[10, -2]], [11, -1, 0], [5, 614, 112]], [6, "lockNode", 1, [-9, -10], [[12, 1, 5, 1, -7, [5, 200, 29.999999999999986]], [13, 37, 372.781, 8.936999999999983, 6.278000000000006, 75.72200000000001, 300, 50, -8]], [0, "8ba5DXv5FPj5Ib+fKVU1e4", 1, 0], [5, 200, 29.999999999999986], [198.063, 34.722, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbCn", 1, [[14, false, "等级145456", 26, 26, 1, 1, 1, -11, [6]], [3, 3, -12, [4, 4278190080]], [16, "等级145456", 22, 22, -13]], [0, "0cOSpYttVM8YmVApudj6RO", 1, 0], [5, 117.41, 27.72], [0, 0, 0.5], [-291.735, -19.048, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbLv", 1, [[2, "等级1", 26, 26, 1, 1, 1, -14, [2]], [3, 3, -15, [4, 4278190080]]], [0, "faw9K1zJJAW5/1K2cCwiV2", 1, 0], [5, 72.46, 38.76], [-259.892, 35.474, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbLock", 2, [[2, "8天21时后结束", 20, 20, 1, 1, 1, -16, [5]], [15, false, 3, -17, [4, 4278190080]]], [0, "c4nb53XVJK5KUAphA24uAs", 1, 0], [4, 4279571947], [5, 133.37, 25.2], [4.314999999999998, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_jj_txz", 1, [[17, 1, 0, -18, [0], 1]], [0, "50aw/yollE8beRovLrrAzp", 1, 0], [5, 614, 112]], [1, "icon_clock", 2, [[18, 0, -19, [3], 4]], [0, "d6TG55qxBEgoVLuGZJ6Hf5", 1, 0], [5, 24, 28], [88, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 6, 0, -2, 4, 0, -3, 2, 0, -4, 3, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 5, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 4, 1, 19], [0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1], [0, 2, 0, 0, 3, 0, 0]]]]