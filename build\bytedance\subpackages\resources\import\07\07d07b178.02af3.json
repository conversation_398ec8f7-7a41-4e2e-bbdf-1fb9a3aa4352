[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "f8npR6F8ZIZoCp3cKXjJQz", "4dMZmkcjlJEbhEI0rAtegJ", "a2MjXRFdtLlYQ5ouAFv/+R", "73jGpne/9JUI/171Zur5Pr", "dawGYxTP5Om4V62YNpzggJ", "64AOGbo2VPer6l9fxh2JnF", "aaykaTHB5NNrUWT8SIjQaa", "f3p84uzd5EVLXvLuhlyoRY", "b2AxAf3ZJHXYpmZKl+3U37", "dal4o32KpO75yexm9hALiM", "cdvDZRLPZKb70zilA0TpQ8", "79mtFK8phK6YVzwv29a4Qn", "e0NuoATJ9GvY6nd16nIiia"], ["node", "_spriteFrame", "_N$file", "_parent", "root", "_textureSetter", "buyButton", "content", "data", "asset"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint", "_color"], -1, 4, 9, 5, 1, 2, 7, 5, 5], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_styleFlags", "_lineHeight", "_fontSize", "_N$overflow", "_N$fontFamily", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$paddingBottom", "_N$spacingY", "_N$horizontalDirection", "node", "_layoutSize"], -6, 1, 5], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 2, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["0c79f+9MQdIdpl7MSHBspNX", ["node", "labelArr", "content", "buyButton"], 3, 1, 2, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["48d8djdjTJBH4k8s5IfoHgR", ["node"], 3, 1]], [[3, 0, 1, 2, 2], [3, 0, 1, 2], [0, 0, 7, 5, 4, 6, 9, 2], [15, 0, 1, 2, 2], [0, 0, 7, 8, 5, 4, 6, 9, 2], [2, 0, 1, 2, 3, 4, 3], [2, 1, 2, 3, 4, 2], [2, 2, 3, 4, 1], [0, 0, 8, 5, 4, 6, 2], [0, 0, 7, 5, 4, 6, 2], [0, 0, 1, 7, 5, 4, 6, 9, 3], [10, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4], [13, 0, 1, 2, 3], [8, 0, 2], [0, 0, 2, 8, 5, 4, 6, 3], [0, 0, 7, 8, 5, 4, 6, 2], [0, 0, 8, 5, 4, 6, 10, 9, 2], [0, 0, 1, 7, 8, 5, 4, 6, 3], [0, 0, 1, 7, 8, 5, 4, 6, 9, 3], [0, 0, 3, 7, 5, 4, 11, 6, 3], [0, 0, 7, 4, 9, 2], [9, 0, 1, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3, 1], [3, 1, 2, 1], [12, 0, 1, 2, 3, 3], [6, 0, 3, 2], [4, 0, 1, 2, 9, 10, 4], [4, 0, 1, 3, 4, 5, 6, 2, 7, 9, 10, 9], [4, 0, 1, 2, 8, 9, 10, 5], [2, 0, 2, 3, 4, 2], [7, 1, 2, 1], [7, 0, 1, 2, 2], [14, 0, 1, 2, 2], [1, 0, 3, 1, 2, 9, 10, 11, 5], [1, 0, 3, 4, 1, 2, 7, 9, 10, 11, 7], [1, 0, 6, 5, 3, 1, 2, 9, 10, 11, 7], [1, 0, 6, 5, 3, 4, 1, 2, 9, 10, 11, 8], [1, 0, 6, 5, 4, 1, 2, 8, 9, 10, 8], [1, 0, 6, 5, 3, 4, 1, 2, 9, 10, 8], [1, 0, 5, 3, 4, 1, 2, 9, 10, 7], [16, 0, 1], [17, 0, 1, 2, 3, 4, 5, 6, 6], [18, 0, 1]], [[[[14, "FundRewardView"], [15, "FundRewardView", 1, [-7, -8], [[23, -6, [-4, -5], -3, -2]], [24, -1, 0], [5, 750, 1334]], [22, "BuyButtonByType", [-11, -12], [-10], [1, "34kqw3Jw9Ngo3ganRCcd/O", -9], [5, 215, 96], [0, -364.737, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [16, "bg", 1, [-14, -15, -16, -17, -18, -19, -20, 2, -21, -22], [[12, 45, 642, 900, -13]], [0, "d5iMdkpqZNhaS+GSBTrslt", 1, 0], [5, 750, 1334]], [8, "layout", [-24, -25, -26], [[27, 1, 1, 8, -23, [5, 180, 40]]], [1, "d2xm1ADOJAlYmFk6uhm73x", 2], [5, 180, 40]], [17, "content", [-28], [[28, 1, 3, 15, 10, 10, 10, 20, 15, -27, [5, 550, 140]]], [0, "25ZUKELnxKBYuJv6WBfZui", 1, 0], [5, 550, 140], [0, 0.5, 1], [20.359, 143.748, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn", 2, [4], [[5, 1, 0, -29, [23], 24], [31, -30, [[13, "0c79f+9MQdIdpl7MSHBspNX", "onClickBtn", 1]]]], [1, "c3UP7LGPZPFpR5z+aREDlU", 2], [5, 215, 86], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "countTimeNode", false, 2, [-32, -33], [[29, 1, 1, 5, 1, -31, [5, 123.6, 50]]], [1, "d5tmMyJYxLu6B1hfEwQQqb", 2], [5, 123.6, 50]], [19, "button03", false, 3, [-36], [[5, 1, 0, -34, [29], 30], [32, 3, -35, [[13, "0c79f+9MQdIdpl7MSHBspNX", "onClickBtn", 1]]]], [0, "c6w8jIBU5Daoxa992Hqgji", 1, 0], [5, 274, 108], [0, -367.877, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "view", [5], [[33, 0, -37, [32]], [12, 45, 240, 250, -38]], [0, "9dbo7QRptI65vz2zRcniFY", 1, 0], [5, 585, 300]], [20, "maskbg", 200, 1, [[26, 45, -39], [6, 0, -40, [0], 1]], [0, "1cRiRztq9HK7NnkLOmpPd/", 1, 0], [4, 4278190080], [5, 750, 1334]], [2, "New Label", 3, [[34, "点击任意位置关闭", false, 1, 1, -41, [2], 3], [3, 2, -42, [4, 4278190080]]], [0, "aahy4y8edEFJLjnNiEPuwZ", 1, 0], [5, 324, 54.4], [0, -506.287, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_gmtxzd", 3, [[5, 1, 0, -43, [4], 5], [41, -44]], [0, "05f7svC/1KW7Kccp+uMfm1", 1, 0], [5, 642, 900]], [2, "Label_title", 3, [[35, "确认购买", false, 1, 1, 1, 2, -45, [8], 9], [3, 3, -46, [4, 4278190080]]], [0, "0391/UvH5M0K7rFqzuyNJw", 1, 0], [5, 285, 56.4], [0, 419.645, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_gmtxzjld", 3, [-48], [[6, 0, -47, [12], 13]], [0, "04mXS5OxVJj6w/aw2LNr9Z", 1, 0], [5, 619, 341], [-4, -101, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 14, [[36, "奖\n励", 32, 32, false, 1, 1, -49, [10], 11], [3, 2, -50, [4, 4278190080]]], [0, "4eeT9MvUxOZJanUVUxiPeE", 1, 0], [5, 36, 76.32], [-275.868, 55.988, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Label", 3, [[-51, [3, 2, -52, [4, 4278190080]]], 1, 4], [0, "c51+brs2NA079DdXRTLuJS", 1, 0], [5, 392.31, 44.32], [0, 102.657, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 4, [[37, "免费", 28, 28, false, 1, 1, 1, -53, [21], 22], [3, 2, -54, [4, 4278190080]]], [1, "bbAl/jK5BODYmKbvlhguNV", 2], [5, 60, 39.28], [60, 0.902, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbTime", 7, [[38, "00:00:00", 28, 31, 1, 1, 1, "Consola", -55, [25]], [3, 2, -56, [4, 4278190080]]], [1, "81VXyEtIBJ1ZbZwlasw6uH", 2], [5, 123.6, 43.06]], [11, "New Label", 8, [[-57, [3, 2, -58, [4, 4279374353]]], 1, 4], [0, "ba1MwrKjVKUaSFNHWGd3rH", 1, 0], [5, 90.6, 43.06], [0, 5.84, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New ScrollView", 3, [9], [[42, false, 0.75, 0.23, null, null, -59, 5]], [0, "c9eYB1H4FC7ruZ8X/siRJF", 1, 0], [5, 585, 300], [0, -101.866, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "title_ad_reward", false, 3, [[7, -60, [6], 7]], [0, "6dLPU5LxBEhbUBOKOHk3fg", 1, 0], [5, 472, 103], [0, 438.629, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_txz02", 3, [[7, -61, [14], 15]], [0, "997vjxBaRBsL6fmc9I3X+j", 1, 0], [5, 282, 216], [0, 249.106, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "购买后达到30级可获得奖励", 32, 32, false, 1, 1, 1, 16, [16]], [2, "ads", 4, [[7, -62, [17], 18]], [1, "22G6hi1OxJdqDdFtHZYCoA", 2], [5, 50, 52], [-65, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "zuan", 4, [[30, 2, -63, [19], 20]], [1, "f5DBw/UwhG/7I5ik6HdTFP", 2], [5, 54, 56], [-5, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [10, "icon_clock", false, 7, [[6, 0, -64, [26], 27]], [1, "72dxqfbOVOJpFxt+On9cme", 2], [5, 32, 38], [-64.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 2], [40, "68元", 31, false, 1, 1, 1, 19, [28]], [21, "RewardItem", 5, [25, "1c39X23AxDS5KHIWleBG8w", true, -65, 31], [-202, -70, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 6, 27, 0, 7, 5, 0, -1, 23, 0, -2, 28, 0, 0, 1, 0, -1, 10, 0, -2, 3, 0, 4, 2, 0, -1, 27, 0, -1, 6, 0, -2, 7, 0, 0, 3, 0, -1, 11, 0, -2, 12, 0, -3, 21, 0, -4, 13, 0, -5, 14, 0, -6, 22, 0, -7, 16, 0, -9, 8, 0, -10, 20, 0, 0, 4, 0, -1, 24, 0, -2, 25, 0, -3, 17, 0, 0, 5, 0, -1, 29, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 18, 0, -2, 26, 0, 0, 8, 0, 0, 8, 0, -1, 19, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, -1, 23, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, -1, 28, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 4, 29, 0, 8, 1, 2, 3, 3, 4, 3, 6, 5, 3, 9, 9, 3, 20, 65], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 28], [-1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 2, -1, 1, -1, -1, 1, -1, -1, 1, 9, -1, 2, 2], [0, 4, 0, 2, 0, 5, 0, 6, 0, 2, 0, 1, 0, 7, 0, 8, 0, 0, 9, 0, 10, 0, 1, 0, 3, 0, 0, 11, 0, 0, 3, 12, 0, 1, 1]], [[{"name": "juanzhou", "rect": [0, 0, 676, 429], "offset": [0, 0], "originalSize": [676, 429], "capInsets": [88, 250, 76, 158]}], [5], 0, [0], [5], [13]], [[{"name": "title_ad_reward", "rect": [0, 0, 472, 103], "offset": [0, 0], "originalSize": [472, 103], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [5], [14]]]]