[1, ["ecpdLyjvZBwrvm+cedCcQy", "deYeqBbrtAM7ABF8+EGpZQ", "7a/QZLET9IDreTiBfRn2PD", "72MWK9NMtGh6pgOQzKJPd/", "52GCdb17tOJ449TnwCah/W", "bcaf7hOWVJbaRLt2Z0PsfH", "0dlOwxdjZAlJ8WIfnYtpgs", "2ailQKmSdOAbUnFLEvV9hi", "92HrO05xhOdLiCyZDU9NzS", "d4dedEfz9L86su08SZssSg", "a71KIuoWhDsLK4sK5O1FZT", "cewv0svlpJSoxzn49pWsBw", "fafEQmSwxNYJkowlMLJ1yb", "2crNC+/GtHu6umjsfGD5Fk"], ["node", "_spriteFrame", "_textureSetter", "root", "asset", "ske", "btnLayout", "_N$barSprite", "data", "_parent"], [["cc.Node", ["_name", "_groupIndex", "_prefab", "_contentSize", "_parent", "_children", "_components", "_trs"], 1, 4, 5, 1, 2, 9, 7], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "node"], -1, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "premultipliedAlpha", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["4b13cNPKeVBj7LH+gCSvO3d", ["node", "btnLayout", "ske"], 3, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Label", ["_string", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1]], [[4, 0, 1, 2, 2], [0, 0, 4, 6, 2, 3, 7, 2], [0, 0, 4, 5, 6, 2, 3, 7, 2], [2, 2, 3, 4, 1], [2, 0, 1, 2, 3, 3], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [13, 0, 1, 2, 2], [0, 0, 4, 2, 7, 2], [9, 0, 1, 2, 3, 3], [0, 0, 4, 6, 2, 3, 2], [7, 0, 2], [0, 0, 1, 5, 6, 2, 3, 3], [0, 0, 5, 2, 3, 7, 2], [0, 0, 4, 5, 2, 2], [3, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 6, 5, 2], [8, 0, 1, 2, 1], [4, 1, 2, 1], [10, 0, 1, 2, 3, 4, 4], [5, 0, 1, 4, 3], [5, 0, 2, 3, 4, 4], [2, 0, 1, 2, 3, 4, 3], [11, 0, 1, 2, 3, 3], [6, 0, 1, 2, 4, 3, 5, 6, 6], [6, 0, 1, 2, 3, 5, 6, 5], [14, 0, 1, 2, 3, 4, 5]], [[[{"name": "icon_wenhao", "rect": [0, 0, 53, 53], "offset": [0, 0], "originalSize": [53, 53], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[[10, "Shop_DrawCards_Pet"], [11, "Shop_DrawCards_Pet", 1, [-5, -6, -7, -8, -9, -10], [[16, -4, -3, -2]], [17, -1, 0], [5, 750, 1334]], [2, "btnLayer", 1, [-13, -14, -15], [[18, 1, 1, 18, -11, [5, 669, 100]], [19, 4, 139.60800000000006, -12]], [0, "8374C8kdJHEKh/ApMchH4T", 1, 0], [5, 669, 100], [0, -477.39199999999994, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "box", 1, [-17, -18, -19], [[21, 1, 0, -16, [9], 10]], [0, "84tMiSYx9G8YTh8RZGD8xN", 1, 0], [5, 710, 84], [0, 385.602, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bar", 3, [-23, -24], [[3, -20, [5], 6], [22, 498, 0.4, -22, -21]], [0, "2f5c9A9jBEA5AhR71HS8pd", 1, 0], [5, 504, 33], [18.87, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "upgrade_btn", 2, [-26, -27], [[4, 1, 0, -25, [13]]], [0, "47TdskmQtNpaR4/7b3luMy", 1, 0], [5, 211, 104], [-229, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "upgrade_btn", 2, [-29, -30], [[4, 1, 0, -28, [16]]], [0, "61c8gCJQhJqKq2hJjTn9ji", 1, 0], [5, 211, 104], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "upgrade_btn", 2, [-32, -33], [[4, 1, 0, -31, [19]]], [0, "96x1jU4D1JlKZeO6VcWp1/", 1, 0], [5, 211, 104], [229, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "num", 4, [[5, "100", 20, false, false, 1, 1, 1, 1, -34, [4]], [6, 3, -35, [4, 4278190080]]], [0, "2cvK+RDEVNQoWINfjS7hj+", 1, 0], [5, 39.37, 56.4]], [1, "num", 5, [[5, "召唤10次", 30, false, false, 1, 1, 1, 1, -36, [11]], [6, 3, -37, [4, 4278190080]]], [0, "catt1b0khOz5qXw6d3JD6/", 1, 0], [5, 129.37, 56.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 6, [[5, "召唤10次", 30, false, false, 1, 1, 1, 1, -38, [14]], [6, 3, -39, [4, 4278190080]]], [0, "eawcEuMkRJR6pkeEnl0gFJ", 1, 0], [5, 129.37, 56.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 7, [[5, "召唤10次", 30, false, false, 1, 1, 1, 1, -40, [17]], [6, 3, -41, [4, 4278190080]]], [0, "5adu/RUmdLVIjKVavuAY62", 1, 0], [5, 129.37, 56.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "item", [-42, -43], [0, "730/ODCmJMMZ8/JOhtpfT7", 1, 0], [5, 160, 160], [-221.378, 70.148, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 1, [[20, 45, 750, 1334, -44]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [1, "wz_cwzh", 1, [[3, -45, [0], 1]], [0, "48juG/VLFM54MJwJExAjdl", 1, 0], [5, 324, 84], [0, 496.821, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "jian", 1, [-46], [0, "01FZG+X7ZIGpctVoLVmsEE", 1, 0], [5, 936.6920166015625, 1334], [0, -320, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "default", "idle", 0, false, "idle", 15, [2]], [1, "lv", 3, [[25, false, "Lv.<color=#ffc03b>5</color>", 34, 50, -47]], [0, "20bGg2rLRJqoS3g1Gc5HSb", 1, 0], [5, 61.739999999999995, 63], [-287.198, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bar", 4, [-48], [0, "6fX/b3MBdCep+FFaBasRlL", 1, 0], [5, 199.20000000000002, 27], [0, 0, 0.5], [-249.263, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 1, 0, 18, [3]], [1, "icon_wenhao", 3, [[3, -49, [7], 8]], [0, "6eo+rjrrNO27zLt2AlcL2M", 1, 0], [5, 53, 53], [306.168, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Item_NeedDisplay", 5, [8, "1ahssPpX1Az6msLfzYX8ky", true, -50, 12], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Item_NeedDisplay", 6, [8, "c0z2qbXi5KHZGDEwTwe0HL", true, -51, 15], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Item_NeedDisplay", 7, [8, "e2vbZz6vdHxq0+KfFh0qFH", true, -52, 18], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "popList", 1, [12], [0, "3dvNeWV+xEabU7fiy0M84Q", 1, 0]], [1, "sword", 12, [[24, "default", "idle", 0, "idle", -53, [20]]], [0, "a5Y2Cu+J5FwKOtaXHAvL+W", 1, 0], [5, 158.35684204101562, 160.63531494140625], [2.776, -45.314, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "img_qp", 12, [[3, -54, [21], 22]], [0, "99fVKEM2RGV7dLxj5GvIu+", 1, 0], [5, 160, 160], [-1.222, -0.001, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 5, 16, 0, 6, 2, 0, 0, 1, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 3, 0, -5, 2, 0, -6, 24, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, 0, 3, 0, -1, 17, 0, -2, 4, 0, -3, 20, 0, 0, 4, 0, 7, 19, 0, 0, 4, 0, -1, 18, 0, -2, 8, 0, 0, 5, 0, -1, 9, 0, -2, 21, 0, 0, 6, 0, -1, 10, 0, -2, 22, 0, 0, 7, 0, -1, 11, 0, -2, 23, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 25, 0, -2, 26, 0, 0, 13, 0, 0, 14, 0, -1, 16, 0, 0, 17, 0, -1, 19, 0, 0, 20, 0, 3, 21, 0, 3, 22, 0, 3, 23, 0, 0, 25, 0, 0, 26, 0, 8, 1, 12, 9, 24, 54], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19], [-1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 4, -1, -1, 4, -1, -1, 4, -1, -1, -1, 1, 1], [0, 4, 2, 0, 0, 0, 5, 0, 6, 0, 7, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 0, 8, 9]], [[{"name": "img_djd", "rect": [0, 0, 128, 92], "offset": [0, 0], "originalSize": [128, 92], "capInsets": [53, 0, 55, 0]}], [1], 0, [0], [2], [10]], [[{"name": "wz_cwzh", "rect": [2, 0, 324, 84], "offset": [1, 0], "originalSize": [326, 84], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [11]], [[{"name": "loading_jindutiao", "rect": [34, 16, 504, 33], "offset": [-0.5, 0.5], "originalSize": [573, 66], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [12]], [[{"name": "loading_jindutiao_01", "rect": [0, 8, 498, 27], "offset": [0, 0], "originalSize": [498, 43], "capInsets": [8, 0, 11, 0]}], [1], 0, [0], [2], [13]]]]