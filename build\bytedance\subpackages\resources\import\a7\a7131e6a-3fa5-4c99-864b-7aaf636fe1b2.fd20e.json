[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "f6NcoTBPNJ4qSyD40PNpRP", "c3+ruR7+FEnKfu8yo+WDeT", "b8QjkehNBLzL1WXbgyi99L", "b2AxAf3ZJHXYpmZKl+3U37", "c6e7Id/hNJv5R+BvIjjgmA", "dal4o32KpO75yexm9hALiM", "65OUoZ25pGHqftEDT5VTS4", "29FYIk+N1GYaeWH/q1NxQO"], ["node", "_spriteFrame", "_parent", "root", "_N$disabledSprite", "buyButton", "title", "desc", "data"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_children", "_parent", "_trs", "_color"], -2, 4, 5, 9, 2, 1, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_N$overflow", "_fontSize", "_lineHeight", "_styleFlags", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$horizontalDirection", "node", "_layoutSize"], -1, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 2, 4, 5, 7], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["48d8djdjTJBH4k8s5IfoHgR", ["node"], 3, 1], ["b3d93NBkxBBHLKnUijcF2AY", ["node", "desc", "title", "buyButton"], 3, 1, 1, 1, 1]], [[4, 0, 1, 2, 2], [4, 0, 1, 2], [0, 0, 9, 7, 5, 6, 10, 2], [1, 1, 0, 2, 3, 4, 3], [0, 0, 9, 8, 7, 5, 6, 10, 2], [11, 0, 1, 2, 2], [1, 0, 2, 3, 4, 2], [2, 0, 5, 6, 7, 1, 2, 10, 11, 7], [12, 0, 1, 2, 3], [8, 0, 2], [0, 0, 2, 8, 7, 5, 6, 3], [0, 0, 3, 9, 7, 5, 11, 6, 3], [0, 0, 9, 8, 7, 5, 6, 2], [0, 0, 8, 5, 6, 2], [0, 0, 9, 7, 5, 6, 2], [0, 0, 8, 7, 5, 6, 2], [0, 0, 1, 9, 8, 7, 5, 6, 3], [0, 0, 4, 8, 7, 5, 6, 10, 3], [0, 0, 1, 9, 7, 5, 11, 6, 10, 3], [5, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 5, 2], [3, 0, 8, 2], [3, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [3, 0, 1, 2, 8, 4], [1, 2, 3, 4, 1], [1, 1, 2, 3, 4, 2], [4, 1, 2, 1], [10, 0, 1], [2, 0, 3, 1, 2, 4, 10, 11, 6], [2, 0, 5, 3, 1, 2, 4, 10, 11, 7], [2, 0, 8, 3, 1, 2, 4, 9, 10, 11, 8], [6, 0, 1, 2, 4, 5, 4], [6, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [13, 0, 1], [14, 0, 1, 2, 3, 1]], [[9, "M20_Pop_PayShopBuyConfirm"], [10, "M20_Pop_PayShopBuyConfirm", 1, [-6, -7], [[37, -5, -4, -3, -2]], [27, -1, 0], [5, 750, 1334]], [21, "BuyButtonByType", [-10, -11], [-9], [1, "c7ZgoCR7xOsquSapm+vugx", -8], [5, 215, 96], [0, -176.304, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [13, "content", [-12, -13, -14, 2, -15], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 574, 505]], [15, "layout", [-17, -18, -19], [[32, 1, 1, 8, -16, [5, 182, 40]]], [1, "d2xm1ADOJAlYmFk6uhm73x", 2], [5, 182, 40]], [17, "Background", 512, [-22], [[3, 1, 0, -20, [21], 22], [23, 0, 45, 9.5, 9.5, 7.5, 7.5, 100, 40, -21]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 61, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn", 2, [4], [[3, 1, 0, -23, [15], 16], [34, 3, -24, [[8, "b3d93NBkxBBHLKnUijcF2AY", "buyIt", 1]]]], [1, "c3UP7LGPZPFpR5z+aREDlU", 2], [5, 215, 86], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "countTimeNode", false, 2, [-26, -27], [[33, 1, 1, 5, 1, -25, [5, 155.07999999999998, 50]]], [1, "d5tmMyJYxLu6B1hfEwQQqb", 2], [5, 155.07999999999998, 50]], [11, "maskbg", 230, 1, [[22, 45, -28], [6, 0, -29, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [12, "bg", 1, [3], [[24, 45, 750, 1334, -30]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [14, "bg", 3, [[3, 1, 0, -31, [2], 3], [28, -32]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 523, 485]], [4, "title_zhua<PERSON><PERSON>", 3, [-34], [[3, 1, 0, -33, [5], 6]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 523, 86], [0, 200.818, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Label_title", 11, [[-35, [5, 3, -36, [4, 4278190080]]], 1, 4], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 261, 56.4]], [4, "frame", 3, [-38], [[3, 1, 0, -37, [8], 9]], [0, "7f4sHf+nJLc41+MksV97Rf", 1, 0], [5, 475, 241], [0, 14.469, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Label_tips", 13, [[-39, [5, 3, -40, [4, 4278190080]]], 1, 4], [0, "d66p8c7WlN74vPxeKncQaG", 1, 0], [5, 469, 120.4], [0, 4.556, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbPrice", 4, [[7, "免费", 28, 28, 1, 1, 1, -41, [14]], [5, 3, -42, [4, 4280427042]]], [1, "bbAl/jK5BODYmKbvlhguNV", 2], [5, 62, 41.28], [60, 0.902, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTime", 7, [[7, "00:00:00", 28, 31, 1, 1, 1, -43, [17]], [5, 3, -44, [4, 4278190080]]], [1, "81VXyEtIBJ1ZbZwlasw6uH", 2], [5, 118.08, 45.06], [18.499999999999993, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnclose", 3, [5], [[35, 3, -45, [[8, "b3d93NBkxBBHLKnUijcF2AY", "close", 1]], [4, 4293322470], [4, 3363338360], 5, 23]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [192.876, 200.799, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "装备名", false, 1, 1, 2, 12, [4]], [30, "指套可缩短冷兵器的冷却时间", 30, false, 1, 1, 2, 14, [7]], [2, "ads", 4, [[25, -46, [10], 11]], [1, "22G6hi1OxJdqDdFtHZYCoA", 2], [5, 50, 36], [-66, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "zuan", 4, [[26, 2, -47, [12], 13]], [1, "f5DBw/UwhG/7I5ik6HdTFP", 2], [5, 54, 56], [-6, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [2, "icon_clock", 7, [[6, 0, -48, [18], 19]], [1, "72dxqfbOVOJpFxt+On9cme", 2], [5, 32, 38], [-61.540000000000006, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, 2], [18, "Label", false, 5, [[31, "返回", false, false, 1, 1, 1, 1, -49, [20]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 5, 23, 0, 6, 18, 0, 7, 19, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, 3, 2, 0, -1, 23, 0, -1, 6, 0, -2, 7, 0, -1, 10, 0, -2, 11, 0, -3, 13, 0, -5, 17, 0, 0, 4, 0, -1, 20, 0, -2, 21, 0, -3, 15, 0, 0, 5, 0, 0, 5, 0, -1, 24, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 16, 0, -2, 22, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -1, 12, 0, -1, 18, 0, 0, 12, 0, 0, 13, 0, -1, 14, 0, -1, 19, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 24, 0, 8, 1, 2, 2, 3, 3, 2, 9, 4, 2, 6, 5, 2, 17, 49], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 4], [0, 1, 0, 2, 0, 0, 3, 0, 0, 4, 0, 5, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 10]]