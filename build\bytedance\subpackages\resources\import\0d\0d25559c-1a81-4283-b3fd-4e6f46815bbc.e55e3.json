[1, ["ecpdLyjvZBwrvm+cedCcQy", "94EziLmzlAirmOfZrgDe1Z", "a2MjXRFdtLlYQ5ouAFv/+R", "58L+DSdUdF4pTKifGiWAVB"], ["node", "_spriteFrame", "root", "_parent", "asset", "skillRoot", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], 1, 4, 5, 9, 1, 2, 7, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_N$paddingLeft", "node", "_layoutSize"], -2, 1, 5], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_N$cacheMode", "node", "_materials"], -6, 1, 3], ["14a74nN4vRNQKpH3x3vB4Ab", ["node", "labelArr", "skillRoot"], 3, 1, 2, 1]], [[3, 0, 1, 2, 2], [0, 0, 6, 4, 2, 3, 7, 2], [0, 0, 5, 4, 2, 3, 7, 2], [2, 1, 2, 3, 1], [4, 0, 2], [0, 0, 6, 4, 2, 3, 2], [0, 0, 1, 5, 4, 2, 8, 3, 3], [0, 0, 5, 6, 2, 3, 2], [0, 0, 5, 6, 4, 2, 3, 7, 2], [0, 0, 5, 2, 2], [5, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 2], [6, 0, 1, 2, 3, 4], [3, 1, 2, 1], [7, 0, 1, 2, 3, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 10], [1, 0, 1, 2, 5, 6, 4], [1, 0, 1, 4, 2, 3, 5, 6, 6], [1, 0, 1, 3, 5, 6, 4], [9, 0, 1, 2, 1]], [[4, "M33_RoleSelect"], [5, "M33_RoleSelect", [-5, -6], [[19, -4, [-3], -2]], [13, -1, 0], [5, 750, 1334]], [1, "title", [-8, -9, -10], [[16, 1, 1, 27.999999999999996, -7, [5, 484, 120]]], [0, "5dMzaKAelOAJOpgYdVL4S+", 1, 0], [5, 484, 120], [0, 101, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "box", [2, -12], [[18, 1, 2, 40, -11, [5, 750, 322]]], [0, "cbh74iG2JNWYggDA+9Cz25", 1, 0], [5, 750, 322], [0, 121.485, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "skillroot", 3, [-14], [[17, 1, 2, 5, 39, 34, -13, [5, 750, 162]]], [0, "46KwqWUtNMdontI7Tm6diE", 1, 0], [5, 750, 162], [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "maskbg", 161, 1, [[11, 0, -15, [0], 1], [12, 45, 750, 1334, -16]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4278190080], [5, 750, 1334]], [7, "bg", 1, [3], [0, "81vg/003JNFJr3x8kM830u", 1, 0], [5, 750, 1334]], [2, "line", 2, [[3, -17, [2], 3]], [0, "17VcVYMEtBKoqKo8P+8gFZ", 1, 0], [5, 87, 30], [-198.5, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [10, "title", 2, [-18], [0, "8cXYiN4o5B7qlQz0sxbw/+", 1, 0], [5, 254, 54.18], [-3.552713678800501e-15, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "选择初始英雄", 35, 43, false, 1, 1, 1, 2, 1, 8, [4]], [2, "line", 2, [[3, -19, [5], 6]], [0, "1d302R389LPYYOQewVA6+j", 1, 0], [5, 87, 30], [198.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "BuffCardBar", 4, [14, "e0kepbzYVC9KRIOoNMTD9b", true, -20, 7]]], 0, [0, 2, 1, 0, 5, 4, 0, -1, 9, 0, 0, 1, 0, -1, 5, 0, -2, 6, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 10, 0, 0, 3, 0, -2, 4, 0, 0, 4, 0, -1, 11, 0, 0, 5, 0, 0, 5, 0, 0, 7, 0, -1, 9, 0, 0, 10, 0, 2, 11, 0, 6, 1, 2, 3, 3, 3, 3, 6, 20], [0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, 4], [0, 2, 0, 1, 0, 0, 1, 3]]