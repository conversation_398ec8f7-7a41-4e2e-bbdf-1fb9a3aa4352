[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "f6NcoTBPNJ4qSyD40PNpRP", "c7h0yt1cRAHpuEKoZh75Ds", "dbWI2+TgFO07YqQKqfCQ6o", "aa+qQcLiVOmYjL878NcTrO", "62UWtMapBA06+psmC4OZhB", "6aXgXT2P1G9IH99spq8SpJ"], ["node", "_spriteFrame", "root", "bar", "data", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_active", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color"], 0, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "_materials"], -5, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["13da4MUMuBESKtvUwVHPszR", ["node", "nodeArr", "lbArr", "bar"], 3, 1, 2, 2, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1]], [[4, 0, 1, 2, 2], [0, 0, 6, 4, 3, 5, 8, 2], [11, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 5, 2], [1, 1, 0, 2, 3, 4, 3], [1, 2, 3, 4, 1], [2, 0, 5, 6, 1, 2, 3, 8, 9, 7], [6, 0, 2], [0, 0, 1, 7, 4, 3, 5, 3], [0, 0, 2, 6, 7, 3, 3], [0, 0, 6, 4, 3, 9, 5, 2], [3, 0, 1, 6, 2, 3, 4, 5, 2], [7, 0, 1, 2, 3, 4, 5, 6, 7, 3], [8, 0, 1, 2, 3, 1], [4, 1, 2, 1], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 3], [9, 0, 1, 1], [10, 0, 1, 2, 3], [2, 0, 7, 1, 2, 3, 4, 8, 9, 7], [2, 0, 5, 6, 1, 2, 3, 4, 8, 9, 8], [12, 0, 1, 2, 3, 3]], [[[[7, "FundTaskExItem"], [8, "FundTaskExItem", 1, [-9], [[13, -8, [-6, -7], [-3, -4, -5], -2]], [14, -1, 0], [5, 750, 380]], [9, "New Node", false, 1, [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [0, "92ZCOHQ5lHzYBhJEGHllGp", 1, 0]], [11, "New ProgressBar", 2, [-22], [[[4, 1, 0, -20, [8], 9], -21], 4, 1], [0, "b3GinX6idBZYEIvmSfp+/i", 1, 0], [5, 600, 24], [0, -6.176, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_djd", 2, [[5, -23, [10], 11], [17, -24, [[18, "13da4MUMuBESKtvUwVHPszR", "onClickItem", 1]]]], [0, "82wqtk23pBrJELrdTLdgAg", 1, 0], [5, 189, 165], [278.901, 0.883, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [1, "Label_title", 2, [[19, "额外奖励", false, 1, 1, 1, 2, -25, [6]], [2, 4, -26, [4, 4278190080]]], [0, "adH4eyGxJAt5V1eUKL5IhN", 1, 0], [5, 285, 56.4], [0, 107.897, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Label", 2, [[-27, [2, 3, -28, [4, 4279571992]]], 1, 4], [0, "c12ww9kFZCGotBIDaBhw+g", 1, 0], [5, 63.47, 33.72], [279.808, -18.644, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Label", 2, [[-29, [2, 3, -30, [4, 4279571992]]], 1, 4], [0, "e9iqk7Dq1DT7a7DRwhuUwo", 1, 0], [5, 79.41, 36.239999999999995], [0, -5.957, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Label", 2, [[-31, [2, 3, -32, [4, 4279571992]]], 1, 4], [0, "3bqoc8W1VDkK7J2Fv0Epi2", 1, 0], [5, 600, 55.72], [0, -84.277, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg", 2, [[15, 0, -33, [0], 1]], [0, "00Noa0Gn9PerWFd0oqGJb+", 1, 0], [4, 4279440216], [5, 1125, 380]], [1, "bg", 2, [[4, 1, 0, -34, [2], 3]], [0, "6boprhC+ZJA59GeLtgSrp3", 1, 0], [5, 680, 281], [0, 4.919, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title_zhua<PERSON><PERSON>", 2, [[4, 1, 0, -35, [4], 5]], [0, "2eCtXHyiVCf4yEPV9BLrYn", 1, 0], [5, 680, 82], [0, 108.121, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bar", 512, 3, [-36], [0, "a0POpSuSNP/qJ7JKWdCBAS", 1, 0], [5, 60, 24], [0, 0, 0.5], [-300, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 1, 0, 12, [7]], [21, 600, 0.1, 3, 13], [6, "(0/20)", 22, 22, 1, 1, 1, 6, [12]], [1, "icon_jytb", 2, [[5, -37, [13], 14]], [0, "f0JGkd0rdH96NNeusDwvZk", 1, 0], [5, 71, 77], [-290.032, -8.936, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "20/100", 24, 24, 1, 1, 1, 7, [15]], [20, "解锁高级通行证并达到满级后，每获得100积分，可再次获得奖励，最多获得20次", 22, 22, 1, 1, 1, 3, 8, [16]]], 0, [0, 2, 1, 0, 3, 14, 0, -1, 17, 0, -2, 15, 0, -3, 18, 0, -1, 4, 0, -2, 2, 0, 0, 1, 0, -1, 2, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 5, 0, -5, 3, 0, -6, 4, 0, -7, 6, 0, -8, 16, 0, -9, 7, 0, -10, 8, 0, 0, 3, 0, -2, 14, 0, -1, 12, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, 0, 6, 0, -1, 17, 0, 0, 7, 0, -1, 18, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, 0, 16, 0, 4, 1, 37], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13], [-1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1], [0, 1, 0, 2, 0, 3, 0, 0, 0, 4, 0, 5, 0, 0, 6, 0, 0, 7]], [[{"name": "boxicon", "rect": [0, 0, 189, 165], "offset": [0, 0.5], "originalSize": [189, 166], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [5], [8]]]]