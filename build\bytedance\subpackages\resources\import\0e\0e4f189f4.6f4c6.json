[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "e2VHprIDlCQre6wlyQ42yl", "dfuKIDLZZLHKvjPmE1MzYJ", "12avzmtw1BXbVWsr5Q+8CZ", "c7h0yt1cRAHpuEKoZh75Ds", "e40u3avyZHWYWu7f8ivsQQ", "f8npR6F8ZIZoCp3cKXjJQz", "40OtPssnZP9antMAWsDtDT", "62UWtMapBA06+psmC4OZhB", "b69iDpEQlDSK/hyQIPZCgx"], ["node", "_spriteFrame", "_N$file", "root", "bar", "data", "_normalMaterial", "_textureSetter"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color"], 0, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "node", "_materials", "_N$file"], -4, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc314A1xixG2aPXVeVQ1uU0", ["node", "bar"], 3, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_enabled", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_normalMaterial"], 2, 1, 9, 5, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["_enabled", "node"], 2, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1]], [[2, 0, 1, 2, 2], [0, 0, 6, 3, 4, 5, 7, 2], [9, 0, 1, 2, 2], [1, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 6, 4, 5, 7, 8, 9, 8], [1, 2, 3, 4, 1], [5, 0, 2], [0, 0, 8, 3, 4, 5, 2], [0, 0, 6, 3, 4, 5, 9, 7, 2], [0, 0, 6, 8, 3, 4, 5, 7, 2], [0, 0, 1, 6, 8, 3, 4, 5, 7, 3], [0, 0, 2, 6, 3, 4, 10, 5, 7, 3], [0, 0, 6, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 1], [2, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 7, 8, 9, 7], [10, 0, 1, 2, 3, 4, 5], [1, 0, 1, 2, 3, 3], [11, 0, 1, 2, 3, 4, 2], [12, 0, 1, 2, 3], [13, 0, 1, 2], [14, 0, 1, 2, 3, 3]], [[[[6, "TaskItem"], [7, "TaskItem", [-4, -5, -6, -7, -8, -9, -10, -11, -12], [[15, -3, -2]], [16, -1, 0], [5, 660, 137]], [8, "lbCn", 1, [[4, "等级145456", 28, 30, false, 1, 1, 1, -13, [6], 7], [2, 2, -14, [4, 4278190080]], [18, false, "", 26, 26, -15]], [0, "faw9K1zJJAW5/1K2cCwiV2", 1, 0], [5, 153.24, 41.8], [0, 0, 0.5], [-184.524, 27.421, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "New ProgressBar", 1, [-18], [[[3, 1, 0, -16, [11], 12], -17], 4, 1], [0, "36FoAsBJhMv4moWksEWf4W", 1, 0], [5, 347, 24], [-12.727, -15.562, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "button", 1, [-21], [[3, 1, 0, -19, [21], 22], [20, 3, -20, [[21, "cc314A1xixG2aPXVeVQ1uU0", "onClickItem", 1]], [4, 4286348412], 23]], [0, "a8dz6tYGxGk6GOFu76im4B", 1, 0], [5, 146, 86], [246.469, 3.179, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "lbLvVal", 1, [[4, "+10", 28, 30, false, 1, 1, 1, -22, [8], 9], [2, 2, -23, [4, 4278190080]]], [0, "00aqIsz0tN2LujyXdAhMjV", 1, 0], [5, 50.62, 41.8], [-267, 1.514, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbPro", 1, [[4, "0/1", 24, 31, false, 1, 1, 1, -24, [13], 14], [2, 2, -25, [4, 4278190080]]], [0, "51ogEHsiFFN4IuyOAMmEEu", 1, 0], [5, 40.05, 43.06], [-3, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "icon_dagou", false, 1, [-27], [[5, -26, [17], 18]], [0, "02sNv4OPhMOo3L5BxBvdou", 1, 0], [5, 62, 49], [248, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Sprite(Splash)", 156, 7, [[3, 1, 0, -28, [15], 16], [22, false, -29]], [0, "2bwcAv5K9IS7adXyq95NEp", 1, 0], [4, 4278190080], [5, 660, 137], [-248, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnLb", 4, [[17, "领取", 36, 36, false, 1, 1, -30, [19], 20], [2, 2, -31, [4, 4278190080]]], [0, "9dttdUcDVFRJpc06KIARfO", 1, 0], [5, 76, 49.36], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "img_jj_txz", 1, [[3, 1, 0, -32, [0], 1]], [0, "50aw/yollE8beRovLrrAzp", 1, 0], [5, 660, 137]], [1, "frame_gmtl_00_bg_num", 1, [[5, -33, [2], 3]], [0, "b7ewQ1WtJJw79wwTdtbrPl", 1, 0], [5, 34, 35], [-266.475, 3.117, 0, 0, 0, 0, 1, 3, 3, 1]], [1, "icon_dj", 1, [[5, -34, [4], 5]], [0, "b0jOIMWXhHeZumOEIxnQud", 1, 0], [5, 68, 77], [-266, 0.582, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bar", 3, [-35], [0, "5b48QLTVVCYKjQ5FO501Nx", 1, 0], [5, 138.8, 24], [0, 0, 0.5], [-173.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 1, 0, 13, [10]], [23, 347, 0.4, 3, 14]], 0, [0, 3, 1, 0, 4, 15, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 2, 0, -5, 5, 0, -6, 3, 0, -7, 6, 0, -8, 7, 0, -9, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, -2, 15, 0, -1, 13, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, -1, 14, 0, 5, 1, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14], [-1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, 6, 1], [0, 2, 0, 3, 0, 4, 0, 1, 0, 1, 0, 0, 5, 0, 1, 0, 2, 0, 6, 0, 7, 0, 8, 0, 9]], [[{"name": "frame_gmtl_00_bg_num", "rect": [0, 0, 34, 35], "offset": [0, 0], "originalSize": [34, 35], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [7], [10]]]]