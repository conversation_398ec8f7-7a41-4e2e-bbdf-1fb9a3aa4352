[1, ["ecpdLyjvZBwrvm+cedCcQy", "94EziLmzlAirmOfZrgDe1Z", "4613FnL15GjKE6Ci5QeOnx", "c46cSnrYpELobhdnhwO2KF", "2f2eePlx1M9LE0GdmtDq1k", "60pNS9bqZIObqUpkGQ3p5Q", "fbDdLW1uRLCZEp7HFyQS7U", "5b0HySO0xNLI16cb6FYAR2"], ["node", "_spriteFrame", "_parent", "root", "numLabel", "cardBtn", "icon", "tag", "desc", "skillname", "target", "skillRoot", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_children", "_components", "_parent", "_trs", "_color"], 0, 4, 5, 2, 9, 1, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_N$overflow", "_N$cacheMode", "_spacingX", "_enableWrapText", "_styleFlags", "node", "_materials"], -8, 1, 3], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "_N$spacingY", "_N$paddingRight", "node", "_layoutSize"], -3, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -2, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["5c4f9jgVJpAr55NnFbZ1r/i", ["node", "skillname", "desc", "tag", "icon", "cardBtn", "numLabel", "videoList"], 3, 1, 1, 1, 1, 1, 1, 1, 2], ["cc.<PERSON><PERSON>", ["_enabled", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["14a74nN4vRNQKpH3x3vB4Ab", ["node", "labelArr", "skillRoot"], 3, 1, 2, 1]], [[4, 0, 1, 2], [0, 0, 7, 6, 3, 4, 8, 2], [4, 0, 1, 2, 2], [9, 0, 1, 2, 2], [1, 3, 4, 5, 1], [1, 1, 0, 3, 4, 5, 3], [0, 0, 5, 6, 3, 4, 8, 2], [5, 0, 1, 2, 3, 4, 5, 2], [0, 0, 2, 7, 6, 3, 9, 4, 3], [0, 0, 1, 7, 5, 6, 3, 4, 8, 3], [7, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 5, 2, 3, 4, 6, 11, 12, 8], [6, 0, 2], [0, 0, 5, 6, 3, 4, 2], [0, 0, 7, 5, 3, 4, 2], [0, 0, 7, 5, 6, 3, 4, 8, 2], [0, 0, 1, 7, 5, 3, 3], [5, 0, 1, 2, 3, 4, 6, 5, 2], [1, 0, 3, 4, 2], [1, 1, 0, 3, 4, 3], [1, 0, 2, 3, 4, 3], [1, 3, 4, 1], [8, 0, 1, 2, 3, 4], [4, 1, 2, 1], [2, 0, 1, 5, 2, 8, 3, 4, 6, 7, 11, 12, 10], [2, 0, 1, 5, 2, 3, 4, 11, 12, 7], [2, 0, 1, 2, 3, 4, 11, 12, 6], [2, 0, 1, 5, 9, 2, 10, 3, 4, 6, 7, 11, 12, 11], [3, 0, 1, 2, 6, 7, 4], [3, 0, 1, 3, 5, 2, 6, 7, 6], [3, 0, 1, 3, 2, 4, 6, 7, 6], [3, 0, 1, 4, 6, 7, 4], [10, 0, 1, 2, 3, 4, 5, 6], [11, 0, 1, 2], [12, 0, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [14, 0, 1, 2, 2], [15, 0, 1, 2, 3], [16, 0, 1, 2, 1]], [[12, "M33_BuffShow"], [6, "BuffCardBar", [-13, -14, -15, -16, -17, -18, -19, -20, -21, -22], [[35, -10, -9, -8, -7, -6, -5, -4, [-2, -3]], [36, false, -12, [[37, "5c4f9jgVJpAr55NnFbZ1r/i", "onClick", -11]]]], [0, "e0kepbzYVC9KRIOoNMTD9b", -1], [5, 613, 162], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "M33_BuffShow", [-27, -28], [[38, -26, [-25], -24]], [23, -23, 0], [5, 750, 1334]], [9, "CardBtn", false, 1, [-31, -32], [[5, 1, 0, -29, [15], 16], [29, 1, 1, 10, 10, 6, -30, [5, 137, 56]]], [0, "7eEX6l3NVEALh3Ll4CT8ln", 1], [5, 137, 56], [236.172, -77.305, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", [-34, -35, -36], [[28, 1, 1, 27.999999999999996, -33, [5, 484, 120]]], [2, "5dMzaKAelOAJOpgYdVL4S+", 2, 0], [5, 484, 120], [0, 101, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "box", [4, -38], [[31, 1, 2, 40, -37, [5, 750, 322]]], [2, "cbh74iG2JNWYggDA+9Cz25", 2, 0], [5, 750, 322], [0, 121.485, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "skillroot", 5, [1], [[30, 1, 2, 5, 39, 34, -39, [5, 750, 162]]], [2, "46KwqWUtNMdontI7Tm6diE", 2, 0], [5, 750, 162], [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoIcon", 3, [[4, -40, [12], 13], [33, 2, -41]], [0, "28UTnPyLpNUroh998MLke7", 1], [5, 53, 41], [-32, 1.586, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lock", false, 1, [-42, -43, -44], [0, "19bJuxHwlCTqQu98oTJdS3", 1]], [8, "maskbg", 161, 2, [[18, 0, -45, [0]], [22, 45, 750, 1334, -46]], [2, "1anP/8/h9OzYuvGncfIzgz", 2, 0], [4, 4278190080], [5, 750, 1334]], [10, "name", 1, [[-47, [3, 3, -48, [4, 4278190080]]], 1, 4], [0, "87DSaDvGVLLaamuqimxtMl", 1], [5, 145, 41.8], [-223.613, -48.843, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 3, [[25, "体验", 26, 30, false, 1, 1, -49, [14]], [3, 3, -50, [4, 3456106496]]], [0, "56fV+FstlH6IXzq37l3PYa", 1], [5, 58, 43.8], [29.5, 3.279, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "isExclusive", false, 1, [-52], [[5, 1, 0, -51, [18], 19]], [0, "d7gqln6IdAI4wrfUY7tSTO", 1], [5, 123, 40], [-223.615, -72.452, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 12, [[11, "专属", 20, 30, false, 1, 1, 2, -53, [17]], [3, 3, -54, [4, 4278190080]]], [0, "03H3DkRXFNvoB3U3pbwo3G", 1], [5, 96, 41.8], [-1.741, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "num", 1, [[-55, [3, 3, -56, [4, 4278190080]]], 1, 4], [0, "7bvCuAnxdAaLBjY/1/7puE", 1], [5, 26.02, 56.4], [277.96, -43.25, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lockbg", 88, 8, [[5, 1, 0, -57, [21], 22], [34, -58]], [0, "b3qZxUb41Ic7BjvihNNoED", 1], [4, 4278190080], [5, 613, 162]], [1, "Label", 8, [[27, "通关主线第4章解锁", 26, 50, false, false, 1, 1, 1, 2, 1, -59, [25]], [3, 3, -60, [4, 4278190080]]], [0, "78V5RNXrZKMpZveWw3tBiy", 1], [5, 340, 69], [0, -52.305, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg", 2, [5], [2, "81vg/003JNFJr3x8kM830u", 2, 0], [5, 750, 1334]], [1, "line", 4, [[4, -61, [1], 2]], [2, "17VcVYMEtBKoqKo8P+8gFZ", 2, 0], [5, 87, 30], [-198.5, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [7, "title", 4, [-62], [2, "8cXYiN4o5B7qlQz0sxbw/+", 2, 0], [5, 254, 54.18], [-3.552713678800501e-15, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "选择初始英雄", 35, 43, false, 1, 1, 1, 2, 1, 19, [3]], [1, "line", 4, [[4, -63, [4], 5]], [2, "1d302R389LPYYOQewVA6+j", 2, 0], [5, 87, 30], [198.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 1, [[19, 1, 0, -64, [6]]], [0, "8dQi1VzuhPd5s2mn3IFcek", 1], [5, 613, 162], [0, 1.579, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "frame", 1, [[5, 1, 0, -65, [7], 8]], [0, "684Z0rXdBIsaGHvawENKQb", 1], [5, 116, 120], [-222.694, 5.652, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "icon", 1, [-66], [0, "92ax78fO1Kkp8sRcxEyiNN", 1], [5, 105, 105], [-226.928, 10.146, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [20, 2, false, 24, [9]], [11, "Buff名称", 26, 30, false, 1, 1, 2, 10, [10]], [17, "desc", 1, [-67], [0, "83uet4KSxNA4jsMFzxCJkG", 1], [5, 420, 76.83999999999999], [0, 0, 0.5], [-150.338, 3.932, 0, 0, 0, 0, 1, 1, 1, 1]], [32, false, "背包中与魔法帽相邻的所有\n魔法武器攻击+20%", 32, 420, 34, 27], [7, "tag", 1, [-68], [0, "a2vYQG5NxHapIZRSXYHzoR", 1], [5, 67, 47], [-276.239, 62.031, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [21, 29, [11]], [26, "1", 36, false, 1, 1, 14, [20]], [1, "icon_lock_small", 8, [[4, -69, [23], 24]], [0, "43rOTmrOlH9pQ033ex5Qy1", 1], [5, 47, 56], [285, 65, 0, 0, 0, 0, 1, 1.2, 1.2, 1]]], 0, [0, 3, 1, 0, -1, 7, 0, -2, 3, 0, 4, 31, 0, 5, 3, 0, 6, 25, 0, 7, 30, 0, 8, 28, 0, 9, 26, 0, 0, 1, 0, 10, 1, 0, 0, 1, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, -4, 10, 0, -5, 27, 0, -6, 29, 0, -7, 3, 0, -8, 12, 0, -9, 14, 0, -10, 8, 0, 3, 2, 0, 11, 6, 0, -1, 20, 0, 0, 2, 0, -1, 9, 0, -2, 17, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, -2, 11, 0, 0, 4, 0, -1, 18, 0, -2, 19, 0, -3, 21, 0, 0, 5, 0, -2, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, -2, 32, 0, -3, 16, 0, 0, 9, 0, 0, 9, 0, -1, 26, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -1, 13, 0, 0, 13, 0, 0, 13, 0, -1, 31, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 18, 0, -1, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, -1, 25, 0, -1, 28, 0, -1, 30, 0, 0, 32, 0, 12, 2, 1, 2, 6, 4, 2, 5, 5, 2, 17, 69], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1], [0, 0, 1, 0, 0, 1, 0, 0, 2, 0, 0, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 7, 0]]