[1, ["ecpdLyjvZBwrvm+cedCcQy", "70mSmGpopJRZTu56xArjI+", "ebJXGx5cNJ+b1BpmgufQFT", "a8lPnHTihBmrRgij+r2Olk", "40n/qScIlL4Kf71vl+Qxf1", "485eGEfEJLOqZGExd6w/kd", "1fevf19ExMW4Dg3zbkgLzt", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "65OUoZ25pGHqftEDT5VTS4", "f6NcoTBPNJ4qSyD40PNpRP", "14Raa/lg9FwqBToQ13vc2z", "b3lq3zNIxNzZnGdJIoY9Sf", "35RFcv50lBVotRgr46UtiC", "f2jdUXMcJCTo47gEEH4vcu", "a6cBIJO6hO/rLHYx6am4oC"], ["node", "_spriteFrame", "_N$target", "_parent", "checkMark", "_textureSetter", "root", "destopnode", "cblnode", "commonPoint", "destopPoint", "btndesc", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], 0, 4, 5, 9, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_styleFlags", "_N$cacheMode", "_N$overflow", "_enableWrapText", "_lineHeight", "_spacingX", "node", "_materials"], -8, 1, 3], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize"], 1, 1, 2, 4, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Toggle", ["_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_left", "_right", "_top", "_originalHeight", "node"], -3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$target"], 1, 1, 9, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["ee746LQYg5HZ4REGtp9toMY", ["node", "nodeArr", "btndesc", "destopPoint", "commonPoint", "cblnode", "destopnode"], 3, 1, 2, 1, 1, 1, 1, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [0, 0, 6, 5, 3, 4, 8, 2], [1, 3, 4, 5, 1], [0, 0, 6, 7, 5, 3, 4, 8, 2], [16, 0, 1, 2, 2], [0, 0, 6, 5, 3, 4, 2], [2, 0, 1, 4, 2, 3, 11, 12, 6], [0, 0, 7, 5, 3, 4, 8, 2], [7, 0, 1, 2, 3, 4], [1, 2, 0, 3, 4, 5, 3], [0, 0, 7, 5, 3, 4, 2], [1, 2, 3, 4, 5, 2], [2, 0, 1, 4, 5, 2, 3, 11, 12, 7], [10, 0, 2], [0, 0, 1, 6, 5, 3, 9, 4, 3], [0, 0, 6, 7, 3, 4, 8, 2], [0, 0, 6, 7, 3, 4, 2], [0, 0, 2, 6, 5, 3, 9, 4, 3], [11, 0, 1, 2, 3, 4, 5, 2], [4, 0, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 3], [12, 0, 1, 2, 3, 4, 5, 6, 1], [5, 1, 2, 1], [13, 0, 1, 2], [6, 1, 2, 3, 4, 5, 1], [6, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 3, 3], [1, 0, 3, 4, 5, 2], [1, 1, 3, 4, 5, 2], [1, 0, 1, 3, 4, 5, 3], [1, 1, 3, 4, 2], [1, 0, 1, 3, 4, 3], [14, 0, 1], [15, 0, 1, 2, 3, 4, 4], [8, 0, 2, 3, 4, 1, 6, 6], [8, 0, 1, 5, 6, 4], [9, 1, 0, 2, 3, 4, 3], [9, 0, 2, 3, 4, 2], [2, 0, 1, 9, 4, 10, 5, 2, 3, 6, 11, 12, 10], [2, 0, 1, 4, 2, 3, 7, 11, 12, 7], [2, 0, 1, 8, 2, 3, 7, 6, 11, 12, 8], [2, 0, 1, 8, 4, 5, 2, 3, 7, 6, 11, 12, 10]], [[[[13, "PlatformKSSidebarRewardView"], [10, "PlatformKSSidebarRewardView", [-9, -10], [[21, -8, [-7], -6, -5, -4, -3, -2]], [22, -1, 0], [5, 750, 1334]], [10, "box", [-12, -13, -14, -15, -16, -17], [[23, 2, -11]], [0, "7eASr9U55JGLvOk+cSCJ3z", 1, 0], [5, 571, 930]], [7, "toggle1", [-21, -22, -23, -24], [[24, -20, [4, 4292269782], -19, -18, [[8, "ee746LQYg5HZ4REGtp9toMY", "changetoggle", "1", 1]]]], [0, "89XpJE07dPiarys0ONuse4", 1, 0], [5, 221, 82], [-125.5, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "toggle2", [-28, -29, -30, -31], [[25, false, -27, [4, 4292269782], -26, -25, [[8, "ee746LQYg5HZ4REGtp9toMY", "changetoggle", "2", 1]]]], [0, "5a1Lf2eulEzbMyrIZyBtuX", 1, 0], [5, 221, 82], [125.5, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "img_desk", 2, [-33, -34, -35], [[11, 1, -32, [33], 34]], [0, "38DCV4MYlBC56vWODGFHpX", 1, 0], [5, 539, 791], [0, 43.897, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "New ToggleContainer", 2, [3, 4], [[32, -36], [33, 1, 1, 30, -37, [5, 472, 61]]], [0, "78BaOxhcNLp71BvWgZjyin", 1, 0], [5, 472, 61], [0, -437.535, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "title", 2, [-40, -41], [[9, 1, 0, -38, [20], 21], [34, 41, 4.5, 4.5, -83.95100000000008, 700, -39]], [0, "a4bwkW2UhAWp4Tjhxf/OfH", 1, 0], [5, 562, 69], [0, 514.451, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Close", 7, [-45], [[2, -42, [18], 19], [36, 1.1, 3, -44, [[26, "ee746LQYg5HZ4REGtp9toMY", "close", 1]], -43]], [0, "7ckgfEWVJNtrszdeTcOYzN", 1, 0], [5, 52, 56], [238.875, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "img_cbl", 2, [-47, -48], [[11, 1, -46, [45], 46]], [0, "80g9VjMIxIO4rfTmwZ2P/i", 1, 0], [5, 539, 791], [0, 43.897, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "<PERSON><PERSON>", [-52], [[9, 1, 0, -49, [48], 49], [37, 3, -51, [[8, "ee746LQYg5HZ4REGtp9toMY", "onBtn", "Get", 1]], -50]], [0, "afxZnDR8VPtpHD/qGRJg2r", 1, 0], [5, 232, 82], [0, 0.464, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "bg_icon_04", 5, [-54, -55], [[2, -53, [25], 26]], [0, "83w27ikHxLMJVDJLgeN96B", 1, 0], [5, 100, 100], [-90, -205, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_icon_02", 5, [-57, -58], [[2, -56, [30], 31]], [0, "34IQZOGFdBzYwxamosO1W1", 1, 0], [5, 100, 100], [90, -205, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_icon_04", 9, [-60, -61], [[2, -59, [38], 39]], [0, "5e85Bo2hNOpa7joy+f2bhY", 1, 0], [5, 100, 100], [-90, -225, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_icon_02", 9, [-63, -64], [[2, -62, [43], 44]], [0, "05fUnvocNDxIJ/WKxS7gHQ", 1, 0], [5, 100, 100], [90, -225, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "maskbg", 177.98999999999998, 1, [[27, 0, -65, [0], 1], [35, 45, 750, 1334, -66]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4281542699], [5, 750, 1334]], [5, "Background", 3, [[28, false, -67, [2], 3]], [0, "7ey+W/jyFPcaTyS6ZJjlar", 1, 0], [5, 221, 82]], [1, "New Label", 3, [[12, "添加桌面", 35, false, 1, 1, 1, -68, [5]], [4, 2, -69, [4, 4278190080]]], [0, "a8gzaR9tFP7Ir14jv84vGf", 1, 0], [5, 144, 54.4], [0, 6.105, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 3, [[2, -70, [6], 7]], [0, "feucgUlfVMvKKrLTnV5XXH", 1, 0], [5, 65, 66], [104.521, 10.044, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "Background", 4, [[29, 2, false, -71, [8], 9]], [0, "cbSRv6ktlLi4ICalFi7sAH", 1, 0], [5, 221, 82]], [1, "New Label", 4, [[12, "设为常用", 35, false, 1, 1, 1, -72, [11]], [4, 2, -73, [4, 4278190080]]], [0, "caWpkxBlpHPY/5048N05+L", 1, 0], [5, 144, 54.4], [0, 6.105, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 4, [[2, -74, [12], 13]], [0, "c3+/kV29hDbbOYij5+tlRr", 1, 0], [5, 65, 66], [104.521, 10.044, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "title", 7, [[38, "快手福利", 35, 50, false, 1, 1, 1, 1, 1, -75, [16]], [4, 2, -76, [4, 4278190080]]], [0, "370F2pvTxNwrn8V3zy2/O+", 1, 0], [5, 144, 67], [2.5, -2.497, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 11, [[6, "灵币x100", 30, false, 1, 1, -77, [24]], [4, 3, -78, [4, 4289792905]]], [0, "20KgjjMuBGJ7+guLi1Uz5Y", 1, 0], [5, 131.05, 56.4], [0.62, -47.96, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 12, [[6, "灵石x500", 30, false, 1, 1, -79, [29]], [4, 3, -80, [4, 4278229260]]], [0, "06AdsTP1NHfK9LoqkA9W0A", 1, 0], [5, 131.05, 56.4], [0.62, -47.96, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tips", 5, [[39, "每天从桌面图标进入都可领取奖励!", 25, false, 1, 1, 2, -81, [32]], [4, 2, -82, [4, 4282729803]]], [0, "c6x9V5yq1EkJMGEYGPZAN9", 1, 0], [5, 407.82000000000005, 50.4], [0, -294.632, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 13, [[6, "灵币x100", 30, false, 1, 1, -83, [37]], [4, 3, -84, [4, 4289792905]]], [0, "71ZMIfqtlArIltr7QDEFTV", 1, 0], [5, 131.05, 56.4], [0.62, -47.96, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 14, [[6, "灵石x500", 30, false, 1, 1, -85, [42]], [4, 3, -86, [4, 4278229260]]], [0, "29HVmrwYlNUa0izjWE0vb4", 1, 0], [5, 131.05, 56.4], [0.62, -47.96, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "btn_go", 2, [10], [0, "143wJejzlCM5RscwFd8rCv", 1, 0], [5, 300, 132], [0, -340.542, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "Label", 10, [[-87, [4, 3, -88, [4, 4278204274]]], 1, 4], [0, "0aChafOKFOjo7x8kmUA/MC", 1, 0], [5, 164, 56.4], [0, 1.214, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "bg", 1, [2], [0, "3aQ5JCUrlBML/7PcXsKi9d", 1, 0], [5, 750, 1334]], [19, "checkmark", 3, [-89], [0, "2cSZDdiTNNcK28ncH51Noc", 1, 0], [5, 221, 96]], [30, false, 31, [4]], [20, "checkmark", false, 4, [-90], [0, "e7K8EIS6BBypICrqShLIaY", 1, 0], [5, 221, 96]], [31, 2, false, 33, [10]], [1, "bg", 2, [[9, 1, 0, -91, [14], 15]], [0, "9dT9GdZ6tF15IeKc5Lwgdn", 1, 0], [5, 570, 950], [0, 78.035, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Label", false, 8, [[40, "x", 20, false, 1, 1, 1, 1, -92, [17]]], [0, "76g32DstFHmLkHo+07mNia", 1, 0], [4, 4278190080], [5, 100, 40]], [5, "shuijing", 11, [[2, -93, [22], 23]], [0, "a4zKgYk+1A15jjskcQ4WnN", 1, 0], [5, 47, 55]], [5, "icon_gold", 12, [[2, -94, [27], 28]], [0, "4fF54a7/hA6o2M6YNLPLNx", 1, 0], [5, 48, 49]], [5, "shuijing", 13, [[2, -95, [35], 36]], [0, "2688dKVOZJtYTSEICJ8mU7", 1, 0], [5, 47, 55]], [5, "icon_gold", 14, [[2, -96, [40], 41]], [0, "b18jOAa5NMuKmEpnK4FLPH", 1, 0], [5, 48, 49]], [41, "前往", 33, false, false, 1, 1, 1, 2, 1, 29, [47]]], 0, [0, 6, 1, 0, 7, 5, 0, 8, 9, 0, 9, 21, 0, 10, 18, 0, 11, 41, 0, -1, 28, 0, 0, 1, 0, -1, 15, 0, -2, 30, 0, 0, 2, 0, -1, 6, 0, -2, 35, 0, -3, 7, 0, -4, 5, 0, -5, 9, 0, -6, 28, 0, 4, 32, 0, 2, 16, 0, 0, 3, 0, -1, 16, 0, -2, 31, 0, -3, 17, 0, -4, 18, 0, 4, 34, 0, 2, 19, 0, 0, 4, 0, -1, 19, 0, -2, 33, 0, -3, 20, 0, -4, 21, 0, 0, 5, 0, -1, 11, 0, -2, 12, 0, -3, 25, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 22, 0, -2, 8, 0, 0, 8, 0, 2, 8, 0, 0, 8, 0, -1, 36, 0, 0, 9, 0, -1, 13, 0, -2, 14, 0, 0, 10, 0, 2, 10, 0, 0, 10, 0, -1, 29, 0, 0, 11, 0, -1, 37, 0, -2, 23, 0, 0, 12, 0, -1, 38, 0, -2, 24, 0, 0, 13, 0, -1, 39, 0, -2, 26, 0, 0, 14, 0, -1, 40, 0, -2, 27, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 41, 0, 0, 29, 0, -1, 32, 0, -1, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 12, 1, 2, 3, 30, 3, 3, 6, 4, 3, 6, 10, 3, 28, 96], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1], [0, 7, 0, 1, 0, 0, 0, 2, 0, 1, 0, 0, 0, 2, 0, 8, 0, 0, 0, 9, 0, 10, 0, 3, 0, 0, 4, 0, 5, 0, 0, 6, 0, 0, 11, 0, 3, 0, 0, 4, 0, 5, 0, 0, 6, 0, 12, 0, 0, 13]], [[{"name": "img_kuaishou", "rect": [0, 0, 539, 791], "offset": [0, 0], "originalSize": [539, 791], "capInsets": [0, 604, 0, 173]}], [3], 0, [0], [5], [14]], [[{"name": "img_kuaishou_02", "rect": [0, 0, 539, 791], "offset": [0, 0], "originalSize": [539, 791], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [5], [15]]]]