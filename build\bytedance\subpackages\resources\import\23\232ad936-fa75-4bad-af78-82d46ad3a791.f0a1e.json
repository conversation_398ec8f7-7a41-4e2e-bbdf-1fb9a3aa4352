[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "f3p84uzd5EVLXvLuhlyoRY", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr", "29R34cGbFB7YQJIiPE06Fl", "22cjIamqZH/Lbdvp80pFLv"], ["node", "_spriteFrame", "_N$disabledSprite", "_N$skeletonData", "root", "data", "_parent"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], -1, 4, 5, 9, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_N$overflow", "_enabled", "_lineHeight", "node", "_materials"], -7, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 2, 4, 5, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 1, 1, 5], ["9a37aBYLOBIc7aKOebuZRy5", ["node", "nodeArr", "labelArr", "imgArr"], 3, 1, 12, 2, 2]], [[4, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 9, 2], [11, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 7, 5, 10, 11, 8], [5, 0, 1, 2, 3, 4], [0, 0, 1, 7, 8, 6, 4, 5, 9, 3], [1, 1, 0, 3, 4, 3], [2, 0, 6, 1, 2, 3, 4, 7, 5, 10, 11, 9], [6, 0, 1, 2, 3, 4, 5, 2], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 1, 7, 6, 4, 5, 9, 3], [3, 0, 1, 2, 3, 4], [1, 0, 3, 4, 5, 2], [7, 0, 2], [0, 0, 2, 8, 6, 4, 5, 3], [0, 0, 7, 6, 4, 5, 2], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 8, 6, 4, 5, 9, 2], [0, 0, 7, 8, 6, 4, 5, 9, 2], [0, 0, 7, 8, 4, 5, 9, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 2], [9, 0, 1, 2, 3, 4, 5, 2], [3, 0, 3, 2], [4, 1, 2, 1], [1, 1, 3, 4, 2], [1, 3, 4, 5, 1], [1, 1, 0, 3, 4, 5, 3], [1, 2, 1, 0, 3, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 6, 5], [2, 8, 0, 6, 9, 1, 2, 3, 4, 5, 10, 11, 10], [2, 0, 6, 1, 2, 3, 4, 5, 10, 11, 8], [12, 0, 1, 2, 2], [5, 0, 1, 3, 3], [6, 1, 2, 1], [13, 0, 1], [14, 0, 1, 2, 3, 3], [15, 0, 1, 2, 3, 1]], [[13, "M50_Pop_GameEnd"], [14, "M50_Pop_GameEnd", 1, [-6, -7], [[36, -5, [[null, null, null, null, -4], 0, 0, 0, 0, 1], [-3], [-2]]], [23, -1, 0], [5, 750, 1334]], [9, "bg", 1, [-10, -11, -12, -13], [[11, 45, 750, 1334, -8], [33, -9, [[4, "9a37aBYLOBIc7aKOebuZRy5", "onBtn", "BackToMain", 1]]]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [17, "New Layout", [-15, -16, -17, -18], [[35, 2, 20, -14, [5, 536, 100]]], [0, "af0PhPKIRLu7si1FMxkRrR", 1, 0], [5, 536, 100], [0, -172.378, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_adunlock", false, 3, [-21, -22, -23, -24], [[6, 1, 0, -19, [11]], [31, "ModeUnlock", -20, [[32, "1a86cvvdNNACLg1YZsPcj9R", "onVideoUnlock", 1]]]], [0, "2cvD77zsxI5aAz7i1O1NGH", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "btn_playagain", 3, [-27, -28], [[8, 3, -25, [[4, "9a37aBYLOBIc7aKOebuZRy5", "onBtn", "Replay", 1]], [4, 4293322470], [4, 3363338360], 18], [26, 1, 0, -26, [19], 20]], [0, "13YRsAbzpBU7NRucCRvu3D", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "tryPlayTips", false, 3, [-31, -32], [[8, 3, -29, [[4, "caf28dRhxJBw4KlhTdqs/XK", "onBtn", "BackToMain", 1]], [4, 4293322470], [4, 3363338360], 23], [6, 1, 0, -30, [24]]], [0, "71695rGRNKdJpFf6anJ/Vo", 1, 0], [5, 248, 100], [0, -60, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "content", 2, [-34, 3], [[27, false, 1, 0, -33, [25], 26]], [0, "cfPCIfUtVMApgW4f3jCFGW", 1, 0], [5, 540, 600]], [5, "btn_next", false, 3, [-37], [[8, 3, -35, [[4, "1a86cvvdNNACLg1YZsPcj9R", "onBtn", "NextLevel", 1]], [4, 4293322470], [4, 3363338360], 13], [6, 1, 0, -36, [14]]], [0, "72ILAahsFECZuKla08Qe/C", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "mask", 140, 2, [[12, 0, -38, [0], 1], [11, 45, 100, 100, -39]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [20, "title_zhua<PERSON><PERSON>", 7, [-41], [-40], [0, "7a6P9Sp61LoLoXSyszH2uD", 1, 0], [5, 577, 247], [0, 0.5, 0], [0, 164.049, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Label", 10, [[-42, [2, 6, -43, [4, 4278190080]]], 1, 4], [0, "06nHHxbKBK8KhcgpLJvlt0", 1, 0], [5, 332, 125.4], [0, 77.721, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[7, "提前解锁", 24, false, false, 1, 1, 2, 1, -44, [8]], [2, 3, -45, [4, 3573547008]]], [0, "90ZE0oH1NBnrp+oDkUlj8l", 1, 0], [5, 136, 62], [34.75, -14.885, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tips", 4, [[7, "主线通关第%d关可解锁下一关", 24, false, false, 1, 1, 2, 1, -46, [9]], [2, 3, -47, [4, 3573547008]]], [0, "6eH1GRdEdBG7E20+CVvru0", 1, 0], [5, 274, 62], [-5.791, -78.888, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "adcount", 4, [[7, "0/5", 32, false, false, 1, 1, 2, 1, -48, [10]], [2, 3, -49, [4, 3573547008]]], [0, "efSk83kCZJbZnNOCr4NxzB", 1, 0], [5, 136, 62], [34.75, 23.965, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 8, [[3, "下一关", false, false, 1, 1, 2, 1, -50, [12]], [2, 3, -51, [4, 3573547008]]], [0, "d8JigGxTFNLqWxREl01EQ5", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "icon_ads", false, 5, [[12, 0, -52, [15], 16], [34, -53]], [0, "59MsfeRb1N+Ic+4fq0HVQV", 1, 0], [5, 45, 35], [-70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 5, [[3, "再次挑战", false, false, 1, 1, 2, 1, -54, [17]], [2, 3, -55, [4, 3573547008]]], [0, "70mth7lkpD/YaaJt+TupoO", 1, 0], [5, 136, 62], [-3.166, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tips", 6, [[30, "通关主线第2关后可以继续", 32, false, false, 1, 1, 1, -56, [21]], [2, 3, -57, [4, 3573547008]]], [0, "21IV6GUvZAgbGYrKtH5Td1", 1, 0], [5, 375.56, 56.4], [0, -74.018, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 6, [[3, "开始主线", false, false, 1, 1, 2, 1, -58, [22]], [2, 3, -59, [4, 3573547008]]], [0, "67xswwqsFEwobHRMRKAIsS", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "maskbg", 1, [[22, 45, -60]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [10, "effect_ribbons", false, 2, [[28, "default", "animation", 0, "animation", -61, [2], 3]], [0, "c60DQJ3S5BYKleDWdDZmFO", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 646.266, 0, 0, 0, 0, 1, 1, 1, 1]], [29, false, "游戏失败", 80, 90, false, false, 1, 1, 1, 11, [4]], [24, 1, 10, [5]], [1, "icon_ads", 4, [[25, -62, [6], 7]], [0, "23CmJ2i05BPL6t8VIOYwdq", 1, 0], [5, 54, 43], [-71.43, 4.466, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btn_close", 2, [-63], [0, "06V3Drg69L5LT4opWReggT", 1, 0], [5, 248, 100], [0, -364.376, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 25, [[3, "点击关闭", false, false, 1, 1, 2, 1, -64, [27]]], [0, "18NirINrJDaLJ1yKda1SWR", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 23, 0, -1, 22, 0, -5, 3, 0, 0, 1, 0, -1, 20, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 21, 0, -3, 7, 0, -4, 25, 0, 0, 3, 0, -1, 4, 0, -2, 8, 0, -3, 5, 0, -4, 6, 0, 0, 4, 0, 0, 4, 0, -1, 24, 0, -2, 12, 0, -3, 13, 0, -4, 14, 0, 0, 5, 0, 0, 5, 0, -1, 16, 0, -2, 17, 0, 0, 6, 0, 0, 6, 0, -1, 18, 0, -2, 19, 0, 0, 7, 0, -1, 10, 0, 0, 8, 0, 0, 8, 0, -1, 15, 0, 0, 9, 0, 0, 9, 0, -1, 23, 0, -1, 11, 0, -1, 22, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 24, 0, -1, 26, 0, 0, 26, 0, 5, 1, 3, 6, 7, 64], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 3, -1, -1, -1, 1, -1, -1, -1, -1, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, -1, 2, -1, -1, 1, -1], [0, 3, 4, 5, 0, 0, 0, 2, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 1, 0, 6, 0, 0, 1, 0, 0, 7, 0]]