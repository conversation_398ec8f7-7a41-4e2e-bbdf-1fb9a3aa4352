[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "d7kp9q8qNN7Z27vh39fJSC"], ["node", "_spriteFrame", "root", "bulletNumLabel", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_anchorPoint", "_parent", "_children"], 1, 9, 4, 5, 5, 1, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.Sprite", ["_type", "_sizeMode", "_fillType", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_offset", "_size"], 3, 1, 5, 5], ["e7926IfEuRPIb8JUmqaiC2u", ["node", "bulletNumLabel"], 3, 1, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 1, 7, 2, 3, 4, 5, 3], [0, 0, 1, 6, 2, 3, 4, 3], [0, 0, 6, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 6, 6], [1, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 4], [6, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 1, 2, 2], [8, 0, 1, 2, 1], [9, 0, 1, 1]], [[1, "role"], [2, "role", 4, [-5, -6, -7], [[11, -2, [0, -65, 60], [5, 400, 120]], [12, -4, -3]], [7, -1, 0], [5, 200, 150], [0, 1, 0]], [5, "bulletNum", 1, [[-8, [10, 3, -9, [4, 4278190080]]], 1, 4], [0, "f9e5M7AzNIQbQA56OqJTLC", 1, 0], [5, 67.17, 56.4], [0, 95.843, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "role", 4, 1, [[6, "b", "idle", 0, false, "idle", -10, [0]]], [0, "bd51k+F4ZG4b5QHfXuLnIo", 1, 0], [5, 70.5, 105.30000305175781]], [4, "line", 1, [[8, 3, 2, 1, -11, [1], 2]], [0, "cftEFQ8dRB6JrZnwKFYNsX", 1, 0], [5, 26, 487], [0, 0.5, -0.1]], [9, "Label", 25, false, 1, 1, 2, [3]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 5, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 2, 0, -1, 5, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, 4, 1, 11], [0, 0, 0, 0], [-1, -1, 1, -1], [1, 0, 2, 0]]