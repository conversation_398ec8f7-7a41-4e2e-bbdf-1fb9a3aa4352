[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "94EziLmzlAirmOfZrgDe1Z", "f3p84uzd5EVLXvLuhlyoRY", "c6823ZZu5DO5uHXx3BV9h0", "0brSjhoQtN+rsQ2fw2goG+", "64lhsbu2dMkbeBBU1jOpWF", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr", "2aUHdEa6JMPrryt0+fJzGP", "5cgHcadmNDAJCkR0+WNY/Q", "e7okC76N5PdK86LhdukXa3", "cdvDZRLPZKb70zilA0TpQ8", "9bmIQyoihGpr3HFazyeJcu", "29R34cGbFB7YQJIiPE06Fl", "86ENVF701Aor6kzAkJ4FUp", "35wnlFNZNBPYbdGy22Co+h", "7f7PpeEnNDl7QCnDxbF648", "ebwdEqJvZAto6Ky9EUKZ7D"], ["node", "_spriteFrame", "_textureSetter", "_parent", "_N$disabledSprite", "root", "_N$barSprite", "data", "_N$skeletonData", "asset"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 1, 9, 7, 2, 5, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "_fontSize", "_N$overflow", "_styleFlags", "node", "_materials"], -6, 1, 3], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "_N$paddingLeft", "_N$paddingTop", "_N$paddingBottom", "_N$affectedByScale", "node", "_layoutSize"], -5, 1, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_top", "node"], -2, 1], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.Prefab", ["_name"], 2], ["b9c5bW9FcxD67RIRWChMRkk", ["node", "nodeArr", "labelArr", "imgArr"], 3, 1, 2, 12, 12], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["1cb88LbIMdCF67rE0bwqZML", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6]], [[8, 0, 1, 2, 2], [17, 0, 1, 2, 2], [0, 0, 6, 7, 4, 5, 8, 2], [0, 0, 6, 9, 7, 4, 5, 8, 2], [2, 2, 3, 4, 1], [2, 0, 1, 2, 3, 3], [9, 0, 1, 2, 3, 4], [1, 0, 6, 4, 1, 8, 2, 3, 7, 5, 9, 10, 10], [0, 0, 1, 6, 9, 7, 4, 5, 8, 3], [6, 0, 2, 3, 4, 5, 6, 2], [2, 0, 1, 2, 3, 4, 3], [0, 0, 9, 7, 4, 5, 8, 2], [0, 0, 6, 7, 4, 5, 2], [9, 0, 1, 3, 3], [10, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 2], [1, 0, 6, 4, 1, 2, 3, 7, 5, 9, 10, 9], [0, 0, 6, 9, 7, 4, 5, 2], [0, 0, 6, 9, 4, 8, 2], [0, 0, 6, 7, 4, 5, 11, 8, 2], [2, 2, 3, 1], [14, 0, 1, 2, 2], [5, 0, 1, 2, 5, 4], [1, 0, 6, 4, 1, 2, 3, 5, 9, 10, 8], [11, 0, 2], [0, 0, 2, 9, 7, 4, 5, 3], [0, 0, 3, 6, 7, 4, 10, 5, 3], [0, 0, 6, 9, 4, 5, 8, 2], [0, 0, 1, 2, 6, 7, 4, 5, 8, 4], [0, 0, 1, 6, 7, 4, 5, 8, 3], [0, 0, 6, 4, 8, 2], [6, 0, 1, 2, 3, 4, 5, 6, 3], [7, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 2, 3, 4, 6, 5, 2], [12, 0, 1, 2, 3, 1], [8, 1, 2, 1], [13, 0, 1, 2, 3, 3], [4, 0, 1, 2, 8, 9, 4], [4, 0, 1, 3, 2, 8, 9, 5], [4, 0, 1, 4, 5, 6, 3, 2, 7, 8, 9, 9], [2, 1, 2, 3, 4, 2], [2, 0, 2, 3, 4, 2], [15, 0, 1, 2, 2], [5, 3, 0, 4, 5, 4], [5, 0, 5, 2], [16, 0, 1], [1, 0, 6, 1, 2, 3, 9, 10, 6], [1, 0, 6, 4, 1, 8, 2, 3, 5, 9, 10, 9], [1, 0, 4, 1, 2, 3, 7, 5, 9, 10, 8], [1, 0, 4, 1, 2, 3, 5, 9, 10, 7], [1, 0, 4, 1, 8, 2, 3, 7, 5, 9, 10, 9], [18, 0, 1, 2, 3, 4, 5, 6, 6], [19, 0, 1, 2, 2], [20, 0, 1, 2, 3, 4, 5, 6, 5]], [[[{"name": "title_02", "rect": [0, 0, 181, 69], "offset": [0, 0], "originalSize": [181, 69], "capInsets": [33, 0, 30, 0]}], [3], 0, [0], [2], [5]], [[{"name": "img_slbt", "rect": [0, 2, 476, 287], "offset": [0, -1], "originalSize": [476, 289], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [6]], [[[24, "M31_Pop_GameEnd"], [25, "M31_Pop_GameEnd", 1, [-14, -15], [[34, -13, [-8, -9, -10, -11, -12], [[-3, -4, null, -5, -6, -7], 1, 1, 0, 1, 1, 1], [[null, -2], 0, 1]]], [35, -1, 0], [5, 750, 1334]], [11, "content", [-18, -19, -20, -21, -22, -23, -24], [[37, 1, 2, 40, -16, [5, 620, 1002]], [10, 1, 0, -17, [54], 55]], [0, "cfPCIfUtVMApgW4f3jCFGW", 1, 0], [5, 620, 1002], [0, -34.591, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Layout", 2, [-26, -27, -28, -29, -30, -31, -32], [[38, 1, 1, 20, 20, -25, [5, 516, 120]]], [0, "af0PhPKIRLu7si1FMxkRrR", 1, 0], [5, 516, 120], [0, -401, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn_adunlock", false, 3, [-35, -36, -37, -38], [[5, 1, 0, -33, [26]], [21, "ModeUnlock", -34, [[13, "b9c5bW9FcxD67RIRWChMRkk", "onVideoUnlock", 1]]]], [0, "2cvD77zsxI5aAz7i1O1NGH", 1, 0], [5, 248, 100], [-268, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "ProgressBar", 2, [-42, -43, -44], [[10, 1, 0, -39, [17], 18], [42, 298, -41, -40]], [0, "ccCfRmrcdP7pOlGrnFsLrb", 1, 0], [5, 300, 34], [29.714, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "bg", 1, [-47, -48, 2], [[22, 45, 750, 1334, -45], [14, -46, [[6, "b9c5bW9FcxD67RIRWChMRkk", "onBtn", "BackToMain", 1]]]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [11, "rewardList", [-51], [[39, 1, 3, 5, 35, 30, 15, 15, true, -49, [5, 534, 185]], [43, 0, 1, -21.5, -50]], [0, "82ONQDnlFM5LmdsCL2zWC1", 1, 0], [5, 534, 185], [0, 69, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btn_next", 3, [-54, -55], [[15, 3, -52, [[6, "b9c5bW9FcxD67RIRWChMRkk", "onBtn", "NextLevel", 1]], [4, 4293322470], [4, 3363338360], 39], [5, 1, 0, -53, [40]]], [0, "72ILAahsFECZuKla08Qe/C", 1, 0], [5, 248, 100], [-134, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "btn_adReward", false, 3, [-58, -59], [[5, 1, 0, -56, [30]], [21, "GameEndADReward", -57, [[13, "b9c5bW9FcxD67RIRWChMRkk", "onClickDoubleReward", 1]]]], [0, "19xjLY3mFEvKrzB1VZJwwY", 1, 0], [5, 248, 100], [-268, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "btn_Share", false, 3, [-62, -63], [[5, 1, 0, -60, [34]], [45, -61]], [0, "5b7YoakH9Cz5AJpXpgqD+L", 1, 0], [5, 248, 100], [-268, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btn_playagain", 3, [-66, -67], [[15, 3, -64, [[6, "b9c5bW9FcxD67RIRWChMRkk", "onBtn", "Replay", 1]], [4, 4293322470], [4, 3363338360], 45], [10, 1, 0, -65, [46], 47]], [0, "13YRsAbzpBU7NRucCRvu3D", 1, 0], [5, 248, 100], [134, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "BackToMain", false, 3, [-70, -71], [[15, 3, -68, [[6, "b9c5bW9FcxD67RIRWChMRkk", "onBtn", "BackToMain", 1]], [4, 4293322470], [4, 3363338360], 50], [5, 1, 0, -69, [51]]], [0, "71695rGRNKdJpFf6anJ/Vo", 1, 0], [5, 248, 100], [268, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "line", 2, [-72, -73, -74], [0, "f2LMkrXr9Jrb405qWeHpsn", 1, 0], [0, 93, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "mask", 140, 6, [[40, 0, -75, [0], 1], [22, 45, 100, 100, -76]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [27, "title", 2, [-77, -78], [0, "daOklf2K5Pn53tfDcOqukT", 1, 0], [5, 600, 30], [0, 486, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Label_tips", 13, [[46, "获得奖励", 30, false, 1, 1, -79, [11]], [1, 3, -80, [4, 4278190080]]], [0, "efxLhK9ihAcKdz4JSDQXXu", 1, 0], [5, 126, 56.4]], [2, "num", 5, [[23, "70%", 20, false, false, 1, 1, 1, -81, [15]], [1, 3, -82, [4, 3573547008]]], [0, "65IpxRMRJCWq2yxq4DyRMg", 1, 0], [5, 46.03, 56.4], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Label", 5, [[47, "关卡进度", 20, false, false, 1, 1, 1, 1, -83, [16]], [1, 3, -84, [4, 3573547008]]], [0, "edCW4KNQpE/7eeLTUe8cA9", 1, 0], [5, 86, 56.4], [0, 1, 0.5], [-160.096, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New ScrollView", 2, [-86], [[51, false, 0.75, 0.23, null, null, -85, 7]], [0, "0dM5pKLtlKuobBVEgjDSJr", 1, 0], [5, 620, 280], [0, -161, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "view", 19, [7], [[52, 0, -87, [20]]], [0, "02EmAt8cZNbpQ8etZpV5Hj", 1, 0], [5, 620, 280]], [2, "Label", 4, [[16, "提前解锁", 24, false, false, 1, 1, 2, 1, -88, [23]], [1, 3, -89, [4, 3573547008]]], [0, "90ZE0oH1NBnrp+oDkUlj8l", 1, 0], [5, 136, 62], [34.75, -14.885, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "tips", 4, [[-90, [1, 3, -91, [4, 3573547008]]], 1, 4], [0, "6eH1GRdEdBG7E20+CVvru0", 1, 0], [5, 274, 62], [-5.791, -78.888, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "adcount", 4, [[-92, [1, 3, -93, [4, 3573547008]]], 1, 4], [0, "efSk83kCZJbZnNOCr4NxzB", 1, 0], [5, 136, 62], [34.75, 23.965, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 9, [[7, "奖励翻倍", 32, false, false, 1, 1, 1, 2, 1, -94, [29]], [1, 3, -95, [4, 3573547008]]], [0, "d2zMNXM4xN4oyi2G+tGsH/", 1, 0], [5, 150, 62], [21.988, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 10, [[7, "炫耀一下", 32, false, false, 1, 1, 1, 2, 1, -96, [31]], [1, 3, -97, [4, 3573547008]]], [0, "edD+xDHZVG/bpoo9LZyQhj", 1, 0], [5, 150, 62], [21.988, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 8, [[7, "下一关", 35, false, false, 1, 1, 1, 2, 1, -98, [35]], [1, 3, -99, [4, 3573547008]]], [0, "d8JigGxTFNLqWxREl01EQ5", 1, 0], [5, 136, 62], [0, 17.26, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_lightning", 8, [-101], [[4, -100, [37], 38]], [0, "19dBGCp5VMbIiumu0TRzcl", 1, 0], [5, 54, 55], [-20.351, -19.54, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [9, "Label", 27, [[-102, [1, 4, -103, [4, 3573547008]]], 1, 4], [0, "66lOQJVK9NX7slhqUUCXLo", 1, 0], [5, 43.57, 58.4], [55.095, 0.37, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 11, [[7, "再次挑战", 35, false, false, 1, 1, 1, 2, 1, -104, [41]], [1, 3, -105, [4, 3573547008]]], [0, "70mth7lkpD/YaaJt+TupoO", 1, 0], [5, 212, 62], [0, 17.808, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_lightning", 11, [-107], [[4, -106, [43], 44]], [0, "c8/PChX4pAjpX1X2O60A4n", 1, 0], [5, 54, 55], [-19.715, -20.724, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [9, "Label", 30, [[-108, [1, 4, -109, [4, 3573547008]]], 1, 4], [0, "c7wmNi7FBGGo3ygDmhRBTu", 1, 0], [5, 67.47, 58.4], [57.94, 4.637, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "tips", false, 12, [[-110, [1, 3, -111, [4, 3573547008]]], 1, 4], [0, "21IV6GUvZAgbGYrKtH5Td1", 1, 0], [5, 375.56, 56.4], [0, -74.018, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 12, [[48, "返回主页", false, false, 1, 1, 2, 1, -112, [49]], [1, 3, -113, [4, 3573547008]]], [0, "67xswwqsFEwobHRMRKAIsS", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "icon", false, 1, 3, [[20, -114, [52]], [14, -115, [[13, "43e4aiRpN1IQqz9TMKv7zk+", "onClickDamagePanel", 1]]]], [0, "5an4tLkzFP34f7sbS0fXjJ", 1, 0], [5, 112, 95], [496, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [11, "btn_close", [-117], [[14, -116, [[6, "43e4aiRpN1IQqz9TMKv7zk+", "onBtn", "BackToMain", 1]]]], [0, "2eQHuqJiRNu6lk4pI2dlZ5", 1, 0], [5, 248, 100], [0, -43.134, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "maskbg", 1, [[44, 45, -118]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [29, "effect_ribbons", false, 6, [[53, "default", "animation", 0, "animation", -119, [2], 3]], [0, "c60DQJ3S5BYKleDWdDZmFO", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 646.266, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "title_02", 15, [[10, 1, 0, -120, [4], 5]], [0, "21huFGC8NLLLv/tQufqFsS", 1, 0], [5, 620, 69]], [19, "isWin", 15, [[41, 1, -121, [6], 7]], [0, "6biTrtGpBP8Y+CLpncmtXq", 1, 0], [5, 476, 287], [0, 0.5, 0], [0, -25.127, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "img", 2, [-122], [0, "87MVm37BtFSoqozBzSyIi2", 1, 0], [5, 575, 298], [0, 282, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 40, [8]], [2, "line", 13, [[4, -123, [9], 10]], [0, "73Pmp8uitJGJRQEcYQa5vp", 1, 0], [5, 87, 30], [-155, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [2, "line", 13, [[4, -124, [12], 13]], [0, "c7rG7Lh/FKMoJ/0spLGnmz", 1, 0], [5, 87, 30], [155, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "bar", 5, [-125], [0, "69hl+Cpa9OcYBVG8rUBCMs", 1, 0], [5, 298, 34], [0, 0, 0.5], [-149.213, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 1, 0, 44, [14]], [30, "RewardItem", 7, [36, "37hTL9bKpCDIOEu0ttyZ8W", true, -126, 19], [-204, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_ads", 4, [[4, -127, [21], 22]], [0, "23CmJ2i05BPL6t8VIOYwdq", 1, 0], [5, 54, 43], [-71.43, 4.466, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "主线通关第%d关可解锁下一关", 24, false, false, 1, 1, 2, 1, 22, [24]], [16, "0/5", 32, false, false, 1, 1, 2, 1, 23, [25]], [2, "videoicon", 9, [[4, -128, [27], 28]], [0, "04UekMd3VBPpjY/aePy5uB", 1, 0], [5, 54, 43], [-79.553, -1.627, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bt_share", 10, [[4, -129, [32], 33]], [0, "59TJuRTjNHf7BzrMt5YWn9", 1, 0], [5, 48, 46], [-76.112, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "-5", false, false, 1, 1, 1, 28, [36]], [7, "-5", 60, false, false, 1, 1, 1, 2, 1, 31, [42]], [23, "通关主线第2关后可以继续", 32, false, false, 1, 1, 1, 32, [48]], [18, "New Node", 2, [35], [0, "96njot3i9Kmb9A2HJWvY4L", 1, 0], [0, -501, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 35, [[50, "点击空白区域可关闭", false, false, 1, 1, 1, 2, 1, -130, [53]]], [0, "92j1mloLNJB4h494kddB3p", 1, 0], [5, 252, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, -2, 41, 0, -1, 52, 0, -2, 53, 0, -4, 54, 0, -5, 48, 0, -6, 49, 0, -1, 7, 0, -2, 4, 0, -3, 8, 0, -4, 5, 0, -5, 3, 0, 0, 1, 0, -1, 36, 0, -2, 6, 0, 0, 2, 0, 0, 2, 0, -1, 15, 0, -2, 40, 0, -3, 13, 0, -4, 5, 0, -5, 19, 0, -6, 3, 0, -7, 55, 0, 0, 3, 0, -1, 4, 0, -2, 9, 0, -3, 10, 0, -4, 8, 0, -5, 11, 0, -6, 12, 0, -7, 34, 0, 0, 4, 0, 0, 4, 0, -1, 47, 0, -2, 21, 0, -3, 22, 0, -4, 23, 0, 0, 5, 0, 6, 45, 0, 0, 5, 0, -1, 44, 0, -2, 17, 0, -3, 18, 0, 0, 6, 0, 0, 6, 0, -1, 14, 0, -2, 37, 0, 0, 7, 0, 0, 7, 0, -1, 46, 0, 0, 8, 0, 0, 8, 0, -1, 26, 0, -2, 27, 0, 0, 9, 0, 0, 9, 0, -1, 50, 0, -2, 24, 0, 0, 10, 0, 0, 10, 0, -1, 25, 0, -2, 51, 0, 0, 11, 0, 0, 11, 0, -1, 29, 0, -2, 30, 0, 0, 12, 0, 0, 12, 0, -1, 32, 0, -2, 33, 0, -1, 42, 0, -2, 16, 0, -3, 43, 0, 0, 14, 0, 0, 14, 0, -1, 38, 0, -2, 39, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, -1, 48, 0, 0, 22, 0, -1, 49, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, -1, 28, 0, -1, 52, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, -1, 31, 0, -1, 53, 0, 0, 31, 0, -1, 54, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, -1, 56, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, -1, 41, 0, 0, 42, 0, 0, 43, 0, -1, 45, 0, 5, 46, 0, 0, 47, 0, 0, 50, 0, 0, 51, 0, 0, 56, 0, 7, 1, 2, 3, 6, 7, 3, 20, 35, 3, 55, 130], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 45], [-1, 1, -1, 8, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, 9, -1, -1, 1, -1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, 4, -1, -1, -1, -1, 1, 4, -1, 1, -1, -1, 4, -1, -1, -1, -1, 1, 1, 1], [0, 7, 8, 9, 0, 10, 0, 11, 0, 0, 2, 0, 0, 2, 0, 0, 0, 0, 12, 13, 0, 0, 3, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 14, 0, 0, 0, 0, 4, 1, 0, 0, 0, 0, 4, 1, 0, 15, 0, 0, 1, 0, 0, 0, 0, 16, 17, 18]], [[{"name": "uibg01", "rect": [0, 0, 86, 90], "offset": [0, 0], "originalSize": [86, 90], "capInsets": [40, 40, 40, 40]}], [3], 0, [0], [2], [19]]]]