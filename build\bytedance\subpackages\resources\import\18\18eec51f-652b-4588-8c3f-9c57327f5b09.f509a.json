[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "f6NcoTBPNJ4qSyD40PNpRP", "c3+ruR7+FEnKfu8yo+WDeT", "65OUoZ25pGHqftEDT5VTS4", "485eGEfEJLOqZGExd6w/kd"], ["node", "_spriteFrame", "_parent", "_N$disabledSprite", "root", "title", "desc", "costT", "cost", "shownode", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_active", "_prefab", "_contentSize", "_components", "_children", "_parent", "_trs", "_color"], -2, 4, 5, 9, 2, 1, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_isSystemFontUsed", "_enableWrapText", "_N$cacheMode", "_fontSize", "_styleFlags", "node", "_materials"], -6, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["6a539cFIv1I9Ym9P7ivyOq0", ["node", "shownode", "cost", "costT", "desc", "title"], 3, 1, 1, 1, 1, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 9, 8, 7, 5, 6, 10, 2], [1, 1, 0, 2, 3, 4, 3], [10, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3, 4, 5, 6, 2], [7, 0, 2], [0, 0, 1, 8, 7, 5, 6, 3], [0, 0, 2, 9, 7, 5, 11, 6, 3], [0, 0, 9, 8, 7, 5, 6, 2], [0, 0, 8, 5, 6, 2], [0, 0, 9, 7, 5, 6, 2], [0, 0, 9, 5, 6, 10, 2], [0, 0, 8, 7, 5, 6, 10, 2], [0, 0, 3, 8, 7, 5, 6, 10, 3], [0, 0, 4, 9, 7, 5, 11, 6, 10, 3], [4, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 3, 4, 5, 2], [3, 0, 8, 2], [3, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [3, 0, 1, 2, 8, 4], [1, 0, 2, 3, 4, 2], [1, 2, 3, 1], [1, 1, 0, 2, 3, 3], [5, 1, 2, 1], [9, 0, 1], [2, 0, 4, 1, 2, 3, 9, 10, 6], [2, 0, 7, 4, 1, 2, 3, 9, 10, 7], [2, 0, 5, 8, 1, 2, 3, 6, 9, 10, 8], [2, 0, 5, 4, 1, 2, 3, 6, 9, 10, 8], [6, 0, 1, 2, 3, 4], [6, 0, 1, 3, 3], [12, 0, 1, 2, 3, 4, 5, 1]], [[6, "M20_Pop_ShopBuyConfirm"], [7, "M20_Pop_ShopBuyConfirm", 1, [-8, -9], [[32, -7, -6, -5, -4, -3, -2]], [24, -1, 0], [5, 750, 1334]], [10, "content", [-10, -11, -12, -13, -14], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 574, 505]], [13, "Background", [-16, -17], [[23, 1, 0, -15, [12]]], [0, "e6rmK5wa1A/4/YfXa7l+hW", 1, 0], [5, 220, 96], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "Background", 512, [-20], [[2, 1, 0, -18, [15], 16], [19, 0, 45, 9.5, 9.5, 7.5, 7.5, 100, 40, -19]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 61, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "frame", 2, [-22, -23], [[2, 1, 0, -21, [8], 9]], [0, "7f4sHf+nJLc41+MksV97Rf", 1, 0], [5, 475, 241], [0, 14.469, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "maskbg", 230, 1, [[18, 45, -24], [21, 0, -25, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [9, "bg", 1, [2], [[20, 45, 750, 1334, -26]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [11, "bg", 2, [[2, 1, 0, -27, [2], 3], [25, -28]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 523, 485]], [1, "title_zhua<PERSON><PERSON>", 2, [-30], [[2, 1, 0, -29, [5], 6]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 523, 86], [0, 200.818, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Label_title", 9, [[-31, [3, 3, -32, [4, 4278190080]]], 1, 4], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 261, 56.4]], [4, "Label_tips", 5, [[-33, [3, 3, -34, [4, 4278190080]]], 1, 4], [0, "d66p8c7WlN74vPxeKncQaG", 1, 0], [5, 469, 120.4], [0, -62.608, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_buy", 2, [3], [[5, 3, -35, [[30, "6a539cFIv1I9Ym9P7ivyOq0", "buyIt", "11", 1]], [4, 4293322470], [4, 3363338360], 3, 13]], [0, "74tgDtIW5O2oFaTIiZUHPe", 1, 0], [5, 220, 90], [0, -165.812, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label", 3, [[-36, [3, 3, -37, [4, 4281824289]]], 1, 4], [0, "9bNuXmMdlKCrtWtIDP5e2Z", 1, 0], [5, 89, 65], [22.409, 4.111, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnclose", 2, [4], [[5, 3, -38, [[31, "6a539cFIv1I9Ym9P7ivyOq0", "close", 1]], [4, 4293322470], [4, 3363338360], 4, 17]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [192.876, 200.799, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "装备名", false, 1, 1, 2, 10, [4]], [12, "sprite_equip", 5, [0, "c5C+cEulxJO5Ww8v+dQZcw", 1, 0], [5, 100, 100], [0, 41.907, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "指套可缩短冷兵器的冷却时间", 30, false, 1, 1, 2, 11, [7]], [17, "icon_ads", 3, [-39], [0, "d9TJy/GFxE1rxg2FblNfZ+", 1, 0], [5, 48, 49], [-51.594, 4.111, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 18, [10]], [28, "11111", false, 1, 1, 1, 2, 1, 13, [11]], [15, "Label", false, 4, [[29, "返回", false, false, 1, 1, 1, 1, -40, [14]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 5, 15, 0, 6, 17, 0, 7, 19, 0, 8, 20, 0, 9, 16, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -1, 8, 0, -2, 9, 0, -3, 5, 0, -4, 12, 0, -5, 14, 0, 0, 3, 0, -1, 18, 0, -2, 13, 0, 0, 4, 0, 0, 4, 0, -1, 21, 0, 0, 5, 0, -1, 16, 0, -2, 11, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, -1, 15, 0, 0, 10, 0, -1, 17, 0, 0, 11, 0, 0, 12, 0, -1, 20, 0, 0, 13, 0, 0, 14, 0, -1, 19, 0, 0, 21, 0, 10, 1, 2, 2, 7, 3, 2, 12, 4, 2, 14, 40], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19], [-1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 3, -1, -1, 1, 3, 1], [0, 2, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 0, 1, 0, 0, 6, 1, 7]]