[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "66pEqh4J5GSqoB0lCmas/G", "164ddVrz5PdKqjAtT69J0j", "cb1VFt7lZM5bK3HPiU1WUY", "a2MjXRFdtLlYQ5ouAFv/+R", "73jGpne/9JUI/171Zur5Pr", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "f6NcoTBPNJ4qSyD40PNpRP", "23Tw89umhO5pVi85QDxfi5"], ["node", "_spriteFrame", "_N$file", "_N$font", "_parent", "_N$disabledSprite", "root", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_styleFlags", "_N$overflow", "_lineHeight", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node", "_N$font"], -2, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["8f3a60PkW5AOJOqYaW4MO6Z", ["node"], 3, 1]], [[4, 0, 1, 2, 2], [1, 1, 0, 3, 4, 5, 3], [0, 0, 7, 6, 4, 5, 8, 2], [8, 0, 1, 2, 2], [0, 0, 7, 6, 4, 10, 5, 11, 8, 2], [1, 3, 4, 5, 1], [9, 0, 1, 2, 3, 4, 5, 6, 6], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 7, 9, 6, 4, 5, 8, 2], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 7, 6, 4, 10, 5, 8, 2], [0, 0, 7, 9, 4, 8, 2], [2, 0, 1, 2, 3, 4, 5, 10, 11, 12, 7], [5, 1, 0, 2, 3, 3], [6, 0, 1, 2, 3, 4], [0, 0, 7, 9, 4, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [2, 0, 6, 9, 1, 2, 3, 4, 5, 10, 11, 12, 9], [10, 0, 1], [7, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 9, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 3, 7, 6, 4, 10, 5, 8, 3], [0, 0, 1, 7, 6, 4, 10, 5, 8, 3], [3, 0, 3, 2], [3, 0, 1, 2, 3, 4], [1, 0, 3, 4, 5, 2], [1, 1, 3, 4, 5, 2], [1, 2, 1, 0, 3, 4, 5, 4], [4, 1, 2, 1], [2, 0, 6, 1, 2, 7, 3, 4, 10, 11, 12, 8], [2, 0, 1, 2, 3, 4, 8, 5, 10, 11, 8], [5, 0, 2, 3, 4, 5, 6, 7, 2], [6, 0, 1, 3, 3], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1]], [[19, "M33_Pop_DiffSelectGeneral"], [20, "M33_Pop_DiffSelectGeneral", 1, [-3, -4], [[37, -2]], [31, -1, 0], [5, 750, 1334]], [9, "btn", [-7, -8, -9, -10], [[1, 1, 0, -5, [47], 48], [13, 0.9, 3, -6, [[14, "8f3a60PkW5AOJOqYaW4MO6Z", "onBtn", "3", 1]]]], [0, "01JrhPo/BLDqjmhyS/eepG", 1, 0], [5, 613, 175], [0, -205, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "btn", [-13, -14, -15], [[1, 1, 0, -11, [30], 31], [13, 0.9, 3, -12, [[14, "8f3a60PkW5AOJOqYaW4MO6Z", "onBtn", "2", 1]]]], [0, "25XbyARz1Cephe8wl1Aw25", 1, 0], [5, 613, 175], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [22, "content", [-16, -17, -18, -19], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [9, "Background", [-23], [[29, 1, -20, [7], 8], [34, 3, -22, [[35, "8f3a60PkW5AOJOqYaW4MO6Z", "close", 1]], [4, 4293322470], [4, 3363338360], -21, 9]], [0, "8adN9uZ5dNFauTFm5YJOWU", 1, 0], [5, 64, 65], [269.386, -0.668, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "Layout", 4, [-25, 3, 2], [[36, 1, 2, 30, -24, [5, 660, 585]]], [0, "c3JkdSM9pFpZa33JYsUVKA", 1, 0], [5, 660, 585], [0, -29.729, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn", 6, [-28, -29], [[1, 1, 0, -26, [17], 18], [13, 0.9, 3, -27, [[14, "8f3a60PkW5AOJOqYaW4MO6Z", "onBtn", "1", 1]]]], [0, "a0r6yGgShI1qCmS0+ok2ZE", 1, 0], [5, 613, 175], [0, 205, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "title_zhua<PERSON><PERSON>", 4, [-31, 5], [[30, false, 1, 0, -30, [10], 11]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 680, 82], [0, 348.214, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lock", 3, [-32, -33, -34], [0, "5b1++pcCNI2IuTD94N+Sg7", 1, 0]], [11, "New Node", 2, [-35, -36, -37], [0, "eeREPoxIpIDbBRuuXMalUH", 1, 0], [-13.726, -26.32, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lock", 2, [-38, -39, -40], [0, "afvDx3s2FOH5cKuLklScWg", 1, 0]], [7, "maskbg", 230, 1, [[26, 45, -41], [28, 0, -42, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [21, "bg", 1, [4], [[27, 45, 750, 1334, -43]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [2, "Label_title", 8, [[32, "难度选择", 48, false, false, 1, 1, 1, -44, [4], 5], [3, 4, -45, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 200, 58.4], [0, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label", 7, [[12, "普通", false, false, 1, 1, 1, -46, [14], 15], [3, 3, -47, [4, 4278190080]]], [0, "265OpWr1hFTKb7TPwcx/5z", 1, 0], [4, 4278255406], [5, 86, 56.4], [-214.183, 3.202, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label", 3, [[12, "困难", false, false, 1, 1, 1, -48, [19], 20], [3, 3, -49, [4, 4278190080]]], [0, "064lfd5DNPtrm5aHpLFBr0", 1, 0], [4, 4294927081], [5, 86, 56.4], [-214.183, 3.202, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Node", 3, [-50, -51], [0, "39IC6FPNhGOYtTx3rxs9cI", 1, 0], [-12.582, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lockbg", 125, 9, [[1, 1, 0, -52, [24], 25], [18, -53]], [0, "05qOc4K5NE77kZzWS9FW3j", 1, 0], [4, 4278190080], [5, 613, 175]], [16, "Label", 9, [[17, "通关主线第6章解锁", 30, 50, false, false, 1, 1, 1, -54, [28], 29], [3, 2, -55, [4, 4278190080]]], [0, "0bi4dWq/tBR6EsIAFy9+q+", 1, 0], [5, 256.51, 67], [0, 0, 0.5], [-90, 65, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label", 2, [[12, "地狱", false, false, 1, 1, 1, -56, [32], 33], [3, 3, -57, [4, 4278190080]]], [0, "e2uuj7ldhFpKC+Gd2zhfg9", 1, 0], [4, 4278190335], [5, 86, 56.4], [-214.183, 3.202, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Node", 2, [-58, -59], [0, "7bTd/W/FFB07bkIGhMmkSb", 1, 0], [-13.726, 26.389, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lockbg", 125, 11, [[1, 1, 0, -60, [41], 42], [18, -61]], [0, "e9JlleQrVJlonedlkqhYni", 1, 0], [4, 4278190080], [5, 613, 175]], [16, "Label", 11, [[17, "通关主线第8章解锁", 30, 50, false, false, 1, 1, 1, -62, [45], 46], [3, 2, -63, [4, 4278190080]]], [0, "e702m2WSJOD4QFkghLV9rn", 1, 0], [5, 257.44, 67], [0, 0, 0.5], [-90, 65, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "bg", 4, [[1, 1, 0, -64, [2], 3]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 680, 782]], [24, "Label", false, 5, [[33, "返回", false, false, 1, 1, 1, 1, -65, [6]]], [0, "60hmbYqdZCRaezrdS0zth8", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "fkbg_02", 83, 4, [[1, 1, 0, -66, [12], 13]], [0, "34KmHY6WVPQrkmRfBD9n28", 1, 0], [4, 4294962402], [5, 640, 650], [0, -31.684, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New RichText", 7, [[6, false, "<b>初见巨龙</b>", 24, 340, 50, -67, 16]], [0, "27uY+7ZXRCA4tp5F7Kr7gk", 1, 0], [4, 4278190080], [5, 340, 63], [0, 0, 0.5], [-54.145, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_star", 17, [[5, -68, [21], 22]], [0, "7eBUXzTAZLXKDebVkF5hMK", 1, 0], [5, 33, 32], [-82.197, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New RichText", 17, [[6, false, "<b>开局可抽取全局增益</b>", 24, 350, 26, -69, 23]], [0, "69N6uTqXBHJrE7LmT1NgJn", 1, 0], [4, 4278190080], [5, 350, 32.76], [0, 0, 0.5], [-48.205, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "icon_lock_small", 9, [[5, -70, [26], 27]], [0, "2bXbLRGOhLLaUOdH3icRoB", 1, 0], [5, 34, 41], [285, 70, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [2, "img_star", 21, [[5, -71, [34], 35]], [0, "ddG5fQcQ1FSZThtWSc2nGX", 1, 0], [5, 33, 32], [-82.197, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New RichText", 21, [[6, false, "<b>开局可抽取全局增益</b>", 24, 350, 50, -72, 36]], [0, "6eDPU0iLtD+5Smf564NZuP", 1, 0], [4, 4278190080], [5, 350, 63], [0, 0, 0.5], [-48.205, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "img_star", 10, [[5, -73, [37], 38]], [0, "e5J6mkCHhPzISgTVqxRBJW", 1, 0], [5, 33, 32], [-82.197, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New RichText", 10, [[6, false, "<b>龙身会出现金宝箱必出S词条</b>", 24, 350, 35, -74, 39]], [0, "98NxVAW0NLZqzqFzGZrZlT", 1, 0], [4, 4278190080], [5, 350, 44.1], [0, 0, 0.5], [-48.205, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "New RichText", 10, [[6, false, "<b>(需升级装备解锁S词条)</b>", 24, 350, 35, -75, 40]], [0, "74QlIzed5MfotYipjT6Hz6", 1, 0], [4, 4278190335], [5, 350, 44.1], [0, 0, 0.5], [-48.205, -30.344, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "icon_lock_small", 11, [[5, -76, [43], 44]], [0, "7bj2REMjVEv41I47JXuaK2", 1, 0], [5, 34, 41], [285, 70, 0, 0, 0, 0, 1, 1.2, 1.2, 1]]], 0, [0, 6, 1, 0, 0, 1, 0, -1, 12, 0, -2, 13, 0, 0, 2, 0, 0, 2, 0, -1, 20, 0, -2, 21, 0, -3, 10, 0, -4, 11, 0, 0, 3, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, -3, 9, 0, -1, 24, 0, -2, 8, 0, -3, 26, 0, -4, 6, 0, 0, 5, 0, 7, 5, 0, 0, 5, 0, -1, 25, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, -2, 27, 0, 0, 8, 0, -1, 14, 0, -1, 18, 0, -2, 30, 0, -3, 19, 0, -1, 33, 0, -2, 34, 0, -3, 35, 0, -1, 22, 0, -2, 36, 0, -3, 23, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 28, 0, -2, 29, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 31, 0, -2, 32, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 8, 1, 2, 4, 6, 3, 4, 6, 4, 4, 13, 5, 4, 8, 76], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 2, -1, -1, 1, 5, -1, 1, -1, 1, -1, 2, 3, -1, 1, -1, 2, -1, 1, 3, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 3, -1, 1, 3, 3, -1, 1, -1, 1, -1, 2, -1, 1], [0, 6, 0, 7, 0, 1, 0, 0, 8, 9, 0, 10, 0, 11, 0, 1, 2, 0, 3, 0, 1, 0, 4, 2, 0, 3, 0, 5, 0, 1, 0, 3, 0, 1, 0, 4, 2, 0, 4, 2, 2, 0, 3, 0, 5, 0, 1, 0, 3]]