[1, ["ecpdLyjvZBwrvm+cedCcQy", "faCRlmMwlE1bRS1xQmqm/5", "495tvycHtBVp9ik6BeVp2Z", "a0rvROFd5IVr9UoEF7ZIum", "f8npR6F8ZIZoCp3cKXjJQz", "5fl1LlOX9G3rWAYVuvMHjs", "088BpBOEhL7JJsqORCo2KC", "f7293wEF9JhIuMEsrLuqng", "7aSL72M71OCKrrZfvV9xtf", "b8ZZ86JvdEIbD/fzZDFJ1s"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "root", "boxnode", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children"], 1, 4, 5, 9, 1, 7, 2], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_lineHeight", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["1dc08rHS31JxKuMCqBpXppx", ["node", "boxnode"], 3, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[2, 0, 1, 2, 2], [3, 2, 3, 4, 1], [0, 0, 1, 5, 4, 2, 3, 6, 3], [0, 0, 5, 4, 2, 3, 6, 2], [8, 0, 1, 2, 2], [0, 0, 5, 7, 2, 3, 6, 2], [0, 0, 5, 4, 2, 3, 2], [5, 0, 2], [0, 0, 7, 4, 2, 3, 2], [0, 0, 5, 7, 4, 2, 3, 6, 2], [6, 0, 1, 1], [2, 1, 2, 1], [7, 0, 1, 2, 3, 4, 4], [1, 0, 1, 6, 2, 3, 4, 7, 8, 9, 7], [1, 0, 1, 2, 5, 3, 4, 7, 8, 7], [1, 0, 1, 2, 5, 3, 4, 7, 8, 9, 7], [3, 0, 1, 2, 3, 4, 3]], [[[{"name": "processing", "rect": [4, 4, 364, 11], "offset": [-0.5, 0], "originalSize": [373, 19], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [3], [2]], [[[7, "M20_Prepare_AwardItem"], [8, "M20_Prepare_AwardItem", [-4, -5, -6, -7, -8], [[10, -3, -2]], [11, -1, 0], [5, 606, 190]], [5, "point", 1, [-9, -10, -11, -12, -13], [0, "fbVuwCxchFbY1LOu1vcgHi", 1, 0], [5, 750, 110], [52.28, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "reward", 1, [-15], [[12, 2, 1, -50, -14, [5, 520, 110]]], [0, "a0Lk/sqUxFAoAIme6+AMQ5", 1, 0], [5, 520, 110], [52.28, -32.266, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "box", 3, [-16, -17, -18], [0, "39PpVvg8ZAnZsuWzGGmC5r", 1, 0], [5, 520, 78], [0, 27.074, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "lbTitle", 1, [[13, "第一章", 30, 30, false, 1, 1, -19, [2], 3], [4, 2, -20, [4, 4278190080]]], [0, "357ebNL0BENK8ZgonEaD1L", 1, 0], [5, 94, 41.8], [52.28, 48.187, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", false, 2, [[14, "关卡进度奖励", 30, false, 1, 1, 1, -21, [12]], [4, 3, -22, [4, 4278190080]]], [0, "31mOiUskJNLIKkd+2vOHAL", 1, 0], [5, 186, 56.4], [0, -84.328, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "wave", 4, [[15, "第%d波", 24, false, 1, 1, 1, -23, [15], 16], [4, 2, -24, [4, 4278190080]]], [0, "fcUE5yEu5Mq5pph0ns6mnj", 1, 0], [5, 88.98, 54.4], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_zjjld", 1, [[16, 1, 0, -25, [0], 1]], [0, "9fXlW/ObtG54HKHLezfWaM", 1, 0], [5, 474, 150], [52.28, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Sprite", false, 2, [[1, -26, [4], 5]], [0, "a8Y4qP78tGophaT6MCssBj", 1, 0], [5, 52, 14], [-225, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Sprite copy", 2, [[1, -27, [6], 7]], [0, "57Q5Xv1sdLZrStSPoO+X24", 1, 0], [5, 364, 11]], [2, "New Sprite copy", false, 2, [[1, -28, [8], 9]], [0, "df4xlOg0BBdrHbeMgAndXJ", 1, 0], [5, 52, 14], [76.271, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Sprite copy", false, 2, [[1, -29, [10], 11]], [0, "a7ue8YT0tFXYignx2dPOcT", 1, 0], [5, 52, 14], [225, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "icon", 4, [[1, -30, [13], 14]], [0, "59ZwoiywRGwZRzbd4Y7NsV", 1, 0], [5, 71, 86]], [2, "redpoint", false, 4, [[1, -31, [17], 18]], [0, "0567FnwihNy5WwGBH7iIiL", 1, 0], [5, 44, 43], [32, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_zj01", 1, [[1, -32, [19], 20]], [0, "094JUjD8lLVo8Hn5U3Sa1C", 1, 0], [5, 558, 566], [-207.023, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]]], 0, [0, 4, 1, 0, 5, 3, 0, 0, 1, 0, -1, 8, 0, -2, 5, 0, -3, 2, 0, -4, 3, 0, -5, 15, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 6, 0, 0, 3, 0, -1, 4, 0, -1, 13, 0, -2, 7, 0, -3, 14, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 6, 1, 32], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 2, -1, 1, -1, 1], [0, 3, 0, 4, 0, 1, 0, 5, 0, 1, 0, 1, 0, 0, 6, 0, 7, 0, 8, 0, 9]]]]