
fantianyin_ground1.png
size: 1006,508
format: RGBA8888
filter: Linear,Linear
repeat: none
b1
  rotate: false
  xy: 653, 2
  size: 113, 119
  orig: 128, 128
  offset: 6, 6
  index: -1
b1s
  rotate: false
  xy: 2, 274
  size: 232, 232
  orig: 232, 232
  offset: 0, 0
  index: -1
b2
  rotate: false
  xy: 2, 64
  size: 215, 208
  orig: 232, 232
  offset: 10, 9
  index: -1
b2ss
  rotate: false
  xy: 833, 4
  size: 128, 128
  orig: 128, 128
  offset: 0, 0
  index: -1
bao_00001
  rotate: false
  xy: 479, 12
  size: 107, 108
  orig: 242, 221
  offset: 64, 56
  index: -1
bao_00003
  rotate: false
  xy: 219, 20
  size: 151, 151
  orig: 242, 221
  offset: 46, 35
  index: -1
bao_00005
  rotate: false
  xy: 643, 320
  size: 200, 186
  orig: 242, 221
  offset: 21, 23
  index: -1
bao_00007
  rotate: false
  xy: 439, 319
  size: 202, 187
  orig: 242, 221
  offset: 20, 20
  index: -1
bao_00009
  rotate: false
  xy: 236, 314
  size: 201, 192
  orig: 242, 221
  offset: 21, 14
  index: -1
bao_00011
  rotate: false
  xy: 454, 122
  size: 197, 195
  orig: 242, 221
  offset: 23, 14
  index: -1
bao_00013
  rotate: false
  xy: 653, 123
  size: 178, 195
  orig: 242, 221
  offset: 27, 9
  index: -1
bao_00015
  rotate: true
  xy: 833, 134
  size: 182, 171
  orig: 242, 221
  offset: 32, 27
  index: -1
bao_00017
  rotate: true
  xy: 845, 318
  size: 188, 145
  orig: 242, 221
  offset: 26, 29
  index: -1
bao_00019
  rotate: true
  xy: 422, 2
  size: 118, 55
  orig: 242, 221
  offset: 89, 35
  index: -1
ll1
  rotate: true
  xy: 2, 21
  size: 41, 173
  orig: 60, 179
  offset: 10, 3
  index: -1
ll2
  rotate: false
  xy: 372, 2
  size: 48, 169
  orig: 84, 197
  offset: 16, 15
  index: -1
ll3
  rotate: true
  xy: 236, 173
  size: 139, 216
  orig: 151, 221
  offset: 4, 3
  index: -1
