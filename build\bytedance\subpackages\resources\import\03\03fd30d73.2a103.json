[1, ["ecpdLyjvZBwrvm+cedCcQy", "013O7a2M5EIogkOhZa3qz6", "3azwFq32JFRYwIVmgenb8z", "36fO0/2jRJBKecsJBpzgXC", "a2MjXRFdtLlYQ5ouAFv/+R", "2cZhvLfvREUKZZ5DhMmy6M", "32V4vm+ihHkqH9bVdQ5VuS", "5esB9LxCNIpozu/DuPYufl", "4dfV6o0QNPCYCY2qmDdU2C", "dal4o32KpO75yexm9hALiM", "50ValcSENI1KkbkvsYM3kY", "14Xla+BIpOHK2kow81xEhI", "e0qbAdelpG24ww9ch50icp", "ddyJk7wHBHVZW6zrjIfzo5", "a8lPnHTihBmrRgij+r2Olk", "7diaWBnXJEFrwL3pxjGYYj", "07X15va9lNQJ/UYqNOC6/c", "3cZSovisFLVZqdLE9n2iw3"], ["node", "_spriteFrame", "_textureSetter", "root", "my<PERSON>rid<PERSON>iew", "_N$content", "data", "_parent", "templete"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_contentSize", "_parent", "_components", "_children", "_trs", "_anchorPoint", "_color"], -1, 4, 5, 1, 9, 2, 7, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "node"], -1, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["e7b979rcw9Lw5D9ujJbEEso", ["node", "labelArr", "sprArr", "my<PERSON>rid<PERSON>iew"], 3, 1, 2, 2, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "content"], 3, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 6, 7, 4, 5, 9, 2], [14, 0, 1, 2, 2], [6, 0, 4, 5, 1, 2, 3, 6, 7, 7], [2, 1, 0, 2, 3, 4, 3], [0, 0, 6, 8, 7, 4, 5, 9, 2], [2, 0, 2, 3, 4, 2], [0, 0, 6, 7, 4, 5, 10, 9, 2], [0, 0, 6, 7, 4, 11, 5, 9, 2], [2, 2, 3, 4, 1], [10, 0, 1, 1], [11, 0, 1, 2, 3], [7, 0, 2], [0, 0, 1, 8, 7, 4, 5, 3], [0, 0, 8, 4, 5, 2], [0, 0, 6, 8, 4, 2], [0, 0, 2, 6, 8, 4, 3], [0, 0, 6, 8, 7, 4, 5, 10, 9, 2], [0, 0, 3, 6, 7, 4, 11, 5, 3], [0, 0, 6, 8, 4, 5, 2], [0, 0, 6, 7, 4, 5, 2], [4, 0, 1, 6, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 1], [5, 1, 2, 1], [2, 1, 0, 2, 3, 3], [2, 0, 2, 3, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 2], [3, 0, 1, 2, 4, 4], [3, 3, 0, 1, 4, 4], [3, 0, 4, 2], [6, 0, 1, 2, 3, 6, 7, 5], [15, 0, 1, 1]], [[[{"name": "img_txzmb", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [10, 15, 10, 16]}], [1], 0, [0], [2], [1]], [[{"name": "img_yltxzdb", "rect": [0, 0, 658, 241], "offset": [0, 0], "originalSize": [658, 241], "capInsets": [206, 170, 171, 43]}], [1], 0, [0], [2], [2]], [[{"name": "img_yltxztt", "rect": [0, 0, 750, 437], "offset": [0, 0], "originalSize": [750, 437], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[[12, "FundIdleTaskView"], [13, "FundIdleTaskView", 1, [-6, -7], [[24, -5, [-4], [-3], -2]], [25, -1, 0], [5, 750, 1334]], [14, "top", [-8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18], [0, "35kv/ZeItD8o0kjwWnWXhT", 1, 0], [5, 650, 780]], [5, "button03", 2, [-21, -22], [[26, 1, 0, -19, [13]], [10, -20, [[11, "e7b979rcw9Lw5D9ujJbEEso", "onOpenBuyFund", 1]]]], [0, "08gLZJknxHCaJk/ylxdZCH", 1, 0], [5, 260, 86], [150.973, 251.545, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_jdt01", 2, [-24, -25], [[4, 1, 0, -23, [21], 22]], [0, "6bST0KezZEj4yUlNc5Os9e", 1, 0], [5, 276, 79], [-147.905, 253.09, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New Node", 4, [-26, -27, -28], [0, "890xGRS0lMqZNfm45ec7Z+", 1, 0]], [16, "New Node", false, 4, [-29, -30, -31], [0, "d5g9JRjWFOuaeRh+5355wS", 1, 0]], [21, "New ScrollView", 2, [-35], [[[28, false, 0.9, 0.23, null, null, -33, -32], -34], 4, 1], [0, "d3QP5vKcVNiYFpQroo6E1d", 1, 0], [5, 605, 535], [0, -122.649, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "view", 7, [-38], [[29, 0, -36, [25]], [30, 45, 240, 250, -37]], [0, "aagRL+ZcVBV7eaDmuX0sAi", 1, 0], [5, 605, 535], [0, 0.5, 1], [0, 267.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "content", 8, [[31, 0, 41, 614, -39]], [0, "16afk4fNxDo76TAvHDosrq", 1, 0], [5, 605, 274], [0, 0, 1], [-302.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "maskbg", 200, 1, [[32, 45, -40], [6, 0, -41, [0], 1]], [0, "38BN9FY3ZGlpjA8xdiIyFc", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "lbTitle", 2, [[33, "魔法生物奇妙夜", 1, 1, 1, -42, [6]], [2, 4, -43, [4, 4279374353]]], [0, "29WF8pO7dIHrej+nYjWgXz", 1, 0], [5, 288, 58.4], [1.179, 365.312, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 2, [[3, "游历时开启更高倍数获得更大奖励！", 26, 31, 1, 1, 1, -44, [7]], [2, 4, -45, [4, 4279374353]]], [0, "acMs6nN45FY4dbOJUhXE5d", 1, 0], [5, 424, 47.06], [1.179, -429.369, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_guanbi", 2, [[9, -46, [8], 9], [10, -47, [[11, "e7b979rcw9Lw5D9ujJbEEso", "close", 1]]]], [0, "34B7oREFtKY6TISnDG53vW", 1, 0], [5, 52, 56], [251.836, 351.164, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnNe", 3, [[3, "未激活", 30, 30, 1, 1, 1, -48, [10]], [2, 3, -49, [4, 4279374353]]], [0, "caq9dXaUJO56MkyAhb88/p", 1, 0], [5, 96, 43.8], [38.283, 3.025, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 5, [[3, "结束时间：", 26, 26, 1, 1, 1, -50, [16]], [2, 2, -51, [4, 4278190080]]], [0, "5dE2sIZBlGvb4rWc/XARpT", 1, 0], [5, 134, 36.76], [25.702, 16.45, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "New Label", 5, [[-52, [2, 2, -53, [4, 4278190080]]], 1, 4], [0, "6b0PhBpbRKPJjwD+aEOs0o", 1, 0], [5, 206.78, 31.72], [26.872, -14.907, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 6, [[3, "购买后累计获得", 30, 30, 1, 1, 1, -54, [18]], [2, 3, -55, [4, 4278190080]]], [0, "c1h0C2ea1JNJf0Pn/yjqu2", 1, 0], [5, 216, 43.8], [0, 19.776, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Label", 6, [[3, "240", 30, 30, 1, 1, 1, -56, [19]], [2, 3, -57, [4, 4278190080]]], [0, "bc2l5d3ORLeZuT2LVLelxP", 1, 0], [5, 56.05, 43.8], [0, 0, 0.5], [-31.55, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_txzbt01", 2, [-59], [[4, 1, 0, -58, [27], 28]], [0, "f6/8yq/TRPMbvcpv1HWEf0", 1, 0], [5, 310, 73], [-154.332, 167.09, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Label", 19, [[3, "免费奖励", 32, 30, 1, 1, 1, -60, [26]], [2, 3, -61, [4, 4278190080]]], [0, "7f7dHI+UdHwKFlj6mAO7cG", 1, 0], [4, 4287035647], [5, 134, 43.8], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_txzbt01", 2, [-63], [[4, 1, 0, -62, [30], 31]], [0, "2ekeGz1VhDjItyDmPw49Nw", 1, 0], [5, 310, 73], [152.475, 167.09, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Label", 21, [[3, "基金奖励", 32, 30, 1, 1, 1, -64, [29]], [2, 3, -65, [4, 4278190080]]], [0, "95nATSma5EmqJ5wVrE5TJI", 1, 0], [4, 4287035647], [5, 134, 43.8], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "bg", 1, [2], [0, "99b1Qdgu5GApqcw6DScSX1", 1, 0], [5, 650, 950]], [20, "bg_txz_hyd", 2, [[4, 1, 0, -66, [2], 3]], [0, "bbw8dWXo1HI6V9sj+tiB4R", 1, 0], [5, 658, 950]], [1, "bg_tzxbg", 2, [[9, -67, [4], 5]], [0, "65RqOVpvZK3rHjvM0A0e2n", 1, 0], [5, 750, 437], [0, 449.204, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_txz", 3, [[6, 0, -68, [11], 12]], [0, "7aXXHzpehPdZCn1BgTK2P0", 1, 0], [5, 73, 56], [-60, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_clock", 5, [[6, 0, -69, [14], 15]], [0, "f3PQXiGQhJVZQK3/uZ0PHI", 1, 0], [5, 44, 52], [-106.044, -0.675, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "18天17时10分25秒", 24, 22, 1, 1, 1, 16, [17]], [23, "shuijing", 6, [-70], [0, "1bLqz8dBxOTZRY7lvclLNO", 1, 0], [5, 38, 35], [-55.984, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [27, 0, 29, [20]], [1, "img_txzmb", 2, [[4, 1, 0, -71, [23], 24]], [0, "7e+AN54A1I0LSXP5eFH3hR", 1, 0], [5, 610, 580], [0, -103.903, 0, 0, 0, 0, 1, 1, 1, 1]], [34, 7, 9]], 0, [0, 3, 1, 0, 4, 32, 0, -1, 30, 0, -1, 28, 0, 0, 1, 0, -1, 10, 0, -2, 23, 0, -1, 24, 0, -2, 25, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, -6, 3, 0, -7, 4, 0, -8, 31, 0, -9, 7, 0, -10, 19, 0, -11, 21, 0, 0, 3, 0, 0, 3, 0, -1, 14, 0, -2, 26, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, -1, 27, 0, -2, 15, 0, -3, 16, 0, -1, 17, 0, -2, 18, 0, -3, 29, 0, 5, 9, 0, 0, 7, 0, -2, 32, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -1, 28, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, -1, 30, 0, 0, 31, 0, 6, 1, 2, 7, 23, 71], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 32], [-1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, 1, 8], [0, 4, 0, 5, 0, 6, 0, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 0, 0, 0, 0, 10, 0, 11, 0, 0, 0, 12, 0, 0, 13, 14, 15]], [[{"name": "img_txzbt02", "rect": [0, 0, 114, 95], "offset": [0, 0], "originalSize": [114, 95], "capInsets": [19, 17, 23, 63]}], [1], 0, [0], [2], [16]], [[{"name": "img_txzbt01", "rect": [0, 0, 114, 95], "offset": [0, 0], "originalSize": [114, 95], "capInsets": [24, 20, 25, 62]}], [1], 0, [0], [2], [17]]]]