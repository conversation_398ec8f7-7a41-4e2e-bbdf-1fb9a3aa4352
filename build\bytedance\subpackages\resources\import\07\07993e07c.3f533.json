[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "baHW41841PF5Yr61qWY9O/", "ebJXGx5cNJ+b1BpmgufQFT", "fcPFXLQfZAELFjCzHKtkbB", "29R34cGbFB7YQJIiPE06Fl", "cfJJ9Xu6ZHkqRHdNnvEHo7", "70sgc9GipJubQaGYBcQf65", "afAeqwhB9AHo1gfdDvnPqU", "a3x5UTYH5GXpMFJQanguqm", "abc8cgqi1BLIwD2EUWicsn", "05U5Pxb/BBTZHwRiiAEowD", "bffmYw4n9ApJlVfnHriS0Y", "92GoDTaSpGMLrGkOtSoos7", "dbNkclouNG/oj/cNi+uAJ8", "a2MjXRFdtLlYQ5ouAFv/+R", "eanDMqoepBgqHXzkkZEmu+", "36mcGF0ftGZ7D2ACiEAhA0", "4dxW7MUspAhLZvcQQeasOQ", "7bzvgwNy5FyZa9eKIR81Vn", "77eSmMWr5A5L1a346LZ0jh"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "petIcon", "gridView", "attrLayout", "equipSlotNode", "ske", "data", "asset", "templete"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_right", "_bottom", "_left", "alignMode", "node"], -5, 1], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "_N$spacingY", "_N$paddingLeft", "_N$paddingRight", "node", "_layoutSize"], -3, 1, 5], ["cc.Label", ["_string", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_lineHeight", "node", "_materials"], -5, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "premultipliedAlpha", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["1e3f6XATUJJwIwQyyhwvhJu", ["UItype", "nodeArr", "node", "labelArr", "ske", "equipSlotNode", "attrLayout", "gridView", "petIcon"], 1, 1, 12, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["1563dBtNsFCp7Et7MyFQ3Sd", ["SwitchID", "node"], 2, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["00798YhOSNDMq8AyX+/Wp5o", ["my<PERSON>ey", "node"], 2, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["key_count", "node", "space", "padding", "scrollview", "content"], 2, 1, 5, 5, 1, 1]], [[7, 0, 1, 2, 2], [0, 0, 5, 3, 2, 4, 7, 2], [3, 2, 3, 4, 1], [17, 0, 1, 2, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [3, 1, 0, 2, 3, 4, 3], [14, 0, 1, 2, 3, 3], [0, 0, 6, 3, 2, 4, 7, 2], [0, 0, 5, 3, 2, 4, 2], [5, 0, 1, 2, 3, 4, 5, 8, 9, 7], [8, 0, 1, 2, 3, 4], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 6, 3, 2, 4, 9, 7, 2], [1, 0, 1, 2, 8, 4], [1, 0, 6, 4, 3, 1, 2, 8, 7], [3, 2, 3, 1], [8, 0, 1, 3, 3], [15, 0, 1, 2], [5, 0, 1, 2, 3, 4, 5, 6, 8, 9, 8], [18, 0, 1, 2], [9, 0, 1, 2, 3, 5, 6, 5], [10, 0, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 5, 6, 3, 2, 8, 4, 7, 2], [0, 0, 5, 3, 2, 4, 9, 7, 2], [0, 0, 1, 5, 3, 2, 8, 4, 3], [0, 0, 5, 2, 2], [6, 0, 1, 6, 2, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4, 7, 5, 2], [11, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [7, 1, 2, 1], [13, 0, 1, 2, 3, 3], [1, 0, 3, 8, 3], [1, 0, 1, 8, 3], [1, 0, 5, 1, 2, 8, 5], [1, 7, 0, 4, 5, 1, 2, 8, 7], [1, 0, 8, 2], [3, 0, 2, 3, 4, 2], [3, 0, 2, 3, 2], [4, 2, 0, 4, 5, 1, 6, 7, 6], [4, 0, 1, 3, 6, 7, 4], [4, 2, 0, 1, 6, 7, 4], [4, 2, 0, 1, 3, 6, 7, 5], [16, 0, 1, 2, 2], [5, 0, 1, 7, 2, 3, 4, 5, 8, 9, 8], [9, 0, 1, 2, 4, 3, 5, 6, 6], [19, 0, 1, 2, 3, 4, 5, 6, 6], [20, 0, 1, 2, 3, 4, 5, 2]], [[[[21, "Role<PERSON>iew"], [22, "Role<PERSON>iew", [-9, -10], [[30, 0, [null], -8, [[-7, null], 1, 0], -6, -5, -4, -3, -2]], [31, -1, 0], [5, 750, 1334]], [7, "top", [-12, -13, -14, -15, -16, -17, -18, -19], [[33, 1, 135.5, -11]], [0, "e3w4oxlkZEn7fm5IANSH+d", 1, 0], [5, 750, 788], [0, 137.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "item", [-23, -24, -25], [[2, -20, [21], 22], [6, 0.9, 3, -21, [[10, "1e3f6XATUJJwIwQyyhwvhJu", "openView", "ui/Pet/PetView", 1]]], [17, 11, -22]], [0, "75qu9pjuZEbYnognjxJSuM", 1, 0], [5, 78, 78], [49, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "item", [-29, -30, -31], [[2, -26, [14], 15], [17, 12, -27], [6, 0.9, 3, -28, [[10, "1e3f6XATUJJwIwQyyhwvhJu", "openView", "ui/role/Role_Select", 1]]]], [0, "79GjwkEyFNIoGIh4rQ7jZR", 1, 0], [5, 78, 78], [-49, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "bg_zhuangbei_sz", [-34, -35, -36], [[5, 1, 0, -32, [42], 43], [34, 41, 20, -33]], [0, "95Ph+bv4ZNcYGEtVxqp2+H", 1, 0], [5, 750, 88], [0, 229, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "bg", 1, [-38, 2, -39], [[13, 45, 750, 1334, -37]], [0, "81vg/003JNFJr3x8kM830u", 1, 0], [5, 750, 1334]], [4, "img_zld", 2, [-42, -43], [[5, 1, 0, -40, [33], 34], [40, 1, 1, -30, 60, 40, -41, [5, 147.8, 46]]], [0, "d4F3BTv4JEeYiToTicQByO", 1, 0], [5, 147.8, 46], [0, -56.608, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "bottom", 6, [5, -46], [[38, 0, -44, [45], 46], [14, 5, 325, 325, 788, 100, 100, -45]], [0, "b6H/RVj6FK/ImxBgdNgZSi", 1, 0], [4, 4280821800], [5, 750, 546], [0, -394, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "ScrollView", 8, [-50], [[-47, [14, 45, 15, 15, 88, 240, 250, -48], -49], 1, 4, 1], [0, "b4HU6/2XlOnKQVaQrZfIfU", 1, 0], [5, 720, 458], [0, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "equipSlotNode", 2, [-52], [[41, 3, 435, 36, -51, [5, 684, 200]]], [0, "e7WBYO079JZJ8McedsFpZo", 1, 0], [5, 684, 200], [0, 0.5, 1], [0, 364.774, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "box", 10, [-54, -55], [[2, -53, [5], 6]], [0, "ccPhrEYgdG87fQ1XMmShjk", 1, 0], [5, 122, 122], [-281, -61, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnLayer", 2, [4, 3], [[42, 1, 1, 20, -56, [5, 176, 80]]], [0, "f0hMAV5ltM45yxDe6Tn3be", 1, 0], [5, 176, 80], [0, -182.56, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "attrLayout", 2, [-58], [[43, 1, 1, 20, 16, -57, [5, 178, 54]]], [0, "8f21kaW1FHebSiVHly/GgT", 1, 0], [5, 178, 54], [-42.319, -112.797, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "item", 13, [-60, -61], [[5, 1, 0, -59, [28], 29]], [0, "f6nsp7MOBMuKGaxoROtbOI", 1, 0], [5, 178, 33]], [4, "btn", 5, [-64], [[5, 1, 0, -62, [37], 38], [6, 0.9, 3, -63, [[10, "1e3f6XATUJJwIwQyyhwvhJu", "openView", "ui/role/Role_EquipForge", 1]]]], [0, "ed/PdLENJF/rFCLRmue/Py", 1, 0], [5, 160, 67], [277.729, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn", 5, [-67], [[5, 1, 0, -65, [40], 41], [6, 0.9, 3, -66, [[16, "1e3f6XATUJJwIwQyyhwvhJu", "resetSortType", 1]]]], [0, "7bMAFk0qlMa7znd5lkGkTl", 1, 0], [5, 160, 67], [-276.641, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "view", 9, [-70], [[44, 0, -68, [44]], [35, 45, 130, 240, 250, -69]], [0, "33LeCfL6ZBor55kY8CdnV9", 1, 0], [5, 720, 328], [0, 0.5, 1], [0, 229, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "content", 17, [[36, 0, 9, 510, 120, 240, 250, -71]], [0, "5dy9eaK0ZFO4ScRv27Hjnf", 1, 0], [5, 750, 250], [0, 0, 1], [-360, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "maskbg", 161, 1, [[39, 0, -72, [0]], [13, 45, 750, 1334, -73]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "img", 6, [[2, -74, [1], 2], [37, 1, -75]], [0, "d7aue//TdEkYACL74VULRT", 1, 0], [5, 1125, 788], [0, 273, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_xqtb", 2, [[2, -76, [7], 8], [6, 0.9, 3, -77, [[16, "1e3f6XATUJJwIwQyyhwvhJu", "onClickPropertyDisplay", 1]]]], [0, "715yBe1AhG844IKJQ8pc+P", 1, 0], [5, 54, 56], [298.383, -114.3, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "txt", 4, [[9, "角色", 22, false, false, 1, 1, -78, [11]], [3, 4, -79, [4, 4278190080]]], [0, "b2LH5zVhhPJ58GrpQGAR2W", 1, 0], [5, 52, 58.4], [0, -30.254, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ggtipicon", 4, [[2, -80, [12], 13], [19, 3000, -81]], [0, "06+WHUuZlOSJmUr+XxIotb", 1, 0], [5, 65, 66], [28.883, 23.497, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [1, "txt", 3, [[9, "宠物", 22, false, false, 1, 1, -82, [18]], [3, 4, -83, [4, 4278190080]]], [0, "3e5JUHhHtEEKvdFnexTjRD", 1, 0], [5, 52, 58.4], [0, -30.254, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ggtipicon", 3, [[2, -84, [19], 20], [19, 1027, -85]], [0, "cbAj3Q3WhPW65Sf5P2w6zR", 1, 0], [5, 65, 66], [28.883, 23.497, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [8, "val", 14, [[18, "0", 24, false, false, 1, 1, 1, -86, [27]], [3, 3, -87, [4, 4278190080]]], [0, "6cBjVZtwhA0qMhUrxgDWSk", 1, 0], [5, 19.35, 56.4]], [28, "val", 7, [[-88, [3, 3, -89, [4, 4278190080]]], 1, 4], [0, "bc8XHaDwZD3LHvbRjCdxGa", 1, 0], [5, 23.8, 56.4], [0, 0, 0.5], [-9.900000000000006, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Label", 5, [[45, "我的装备", 32, 50, false, false, 1, 1, -90, [35]], [3, 4, -91, [4, 4278190080]]], [0, "82w5IkKc5IoaTQOFOi2W+0", 1, 0], [5, 136, 71]], [1, "txt", 15, [[9, "合成", 28, false, false, 1, 1, -92, [36]], [3, 4, -93, [4, 4278190080]]], [0, "3b2O9xBodD2pAlc1k2/KR3", 1, 0], [5, 64, 58.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "txt", 16, [[9, "按品质排序", 24, false, false, 1, 1, -94, [39]], [3, 4, -95, [4, 4278190080]]], [0, "7aNaofRplM/5ymbhfNPLXX", 1, 0], [5, 128, 58.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 11, [[15, -96, [3]]], [0, "42IWYJwYRLE5STvdmVHtpz", 1, 0], [5, 66, 65]], [26, "RoleEquipItem", 11, [32, "fdPc9ufyNPK4tFszjqpG7z", true, -97, 4]], [1, "icon", 4, [[2, -98, [9], 10]], [0, "5euu8/z1VML5NP3CTdD2fb", 1, 0], [5, 69, 71], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", 3, [[2, -99, [16], 17]], [0, "87U7LATwRLgLc6S3e4HU5n", 1, 0], [5, 58, 70]], [1, "pet_l", 2, [[20, "default", "idle", 0, "idle", -100, [23]]], [0, "331mPR/MtJkY73k1NNNjtI", 1, 0], [5, 72, 85.11897277832031], [-120, -20, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "pet_r", 2, [[20, "default", "idle", 0, "idle", -101, [24]]], [0, "6didjN2LlPZIYgp8boQJZk", 1, 0], [5, 72, 85.11897277832031], [120, -20, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [29, "ske", 2, [-102], [0, "ceMYQdHclFHqRdoOdGdSkI", 1, 0], [5, 402.000244140625, 383.9200134277344], [0, -21.921, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "f", "idle", 0, false, "idle", 37, [25]], [1, "icon_dj", 14, [[15, -103, [26]]], [0, "6aVTYZddhBE6a5FxMWOq/k", 1, 0], [5, 62, 62], [-85.198, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "icon_cp", 7, [[2, -104, [30], 31]], [0, "9e65MEXbxM66Pisd0a3oTc", 1, 0], [5, 54, 65], [-76.9, 5.817, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "0", 32, false, false, 1, 1, 1, 27, [32]], [47, false, 0.75, 0.23, null, null, 9, 18], [48, 5, 9, [0, 20, 20], [0, 20, 20], 42, 18]], 0, [0, 4, 1, 0, 5, 3, 0, 6, 43, 0, 7, 13, 0, 8, 10, 0, 9, 38, 0, -1, 41, 0, 0, 1, 0, -1, 19, 0, -2, 6, 0, 0, 2, 0, -1, 10, 0, -2, 21, 0, -3, 12, 0, -4, 35, 0, -5, 36, 0, -6, 37, 0, -7, 13, 0, -8, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 34, 0, -2, 24, 0, -3, 25, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 33, 0, -2, 22, 0, -3, 23, 0, 0, 5, 0, 0, 5, 0, -1, 28, 0, -2, 15, 0, -3, 16, 0, 0, 6, 0, -1, 20, 0, -3, 8, 0, 0, 7, 0, 0, 7, 0, -1, 40, 0, -2, 27, 0, 0, 8, 0, 0, 8, 0, -2, 9, 0, -1, 42, 0, 0, 9, 0, -3, 43, 0, -1, 17, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, -1, 31, 0, -2, 32, 0, 0, 12, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, -1, 39, 0, -2, 26, 0, 0, 15, 0, 0, 15, 0, -1, 29, 0, 0, 16, 0, 0, 16, 0, -1, 30, 0, 0, 17, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 41, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 4, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, -1, 38, 0, 0, 39, 0, 0, 40, 0, 10, 1, 2, 3, 6, 3, 3, 12, 4, 3, 12, 5, 3, 8, 104], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43], [-1, -1, 1, -1, 11, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, 12], [0, 0, 6, 0, 2, 0, 7, 0, 8, 0, 9, 0, 0, 3, 0, 4, 0, 10, 0, 0, 3, 0, 4, 1, 1, 1, 0, 0, 0, 11, 0, 12, 0, 0, 13, 0, 0, 0, 5, 0, 0, 5, 0, 14, 0, 0, 15, 2]], [[{"name": "img_zld3", "rect": [0, 0, 33, 46], "offset": [0, 0], "originalSize": [33, 46], "capInsets": [0, 0, 16, 0]}], [2], 0, [0], [2], [16]], [[{"name": "icon_js", "rect": [0, 0, 69, 71], "offset": [0, 0], "originalSize": [69, 71], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [17]], [[{"name": "icon_cw", "rect": [0, 0, 58, 70], "offset": [0, 0], "originalSize": [58, 70], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [18]], [[{"name": "img_jsbj1", "rect": [0, 0, 1125, 788], "offset": [0, 0], "originalSize": [1125, 788], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [19]], [[{"name": "img_jsrkd", "rect": [0, 0, 78, 78], "offset": [0, 0], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [20]]]]