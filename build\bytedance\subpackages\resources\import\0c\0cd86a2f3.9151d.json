[1, ["ecpdLyjvZBwrvm+cedCcQy", "cdvDZRLPZKb70zilA0TpQ8", "8aRJRt8edARbXE6r6sVPlz", "12avzmtw1BXbVWsr5Q+8CZ", "f4q/y8V1JOua5kDQElQEvj", "e40u3avyZHWYWu7f8ivsQQ", "42rYiASb5Lra+b+76Ynk1T", "16FFf6Jd5H3Jp0anTpzZjT"], ["node", "_spriteFrame", "root", "asset", "_textureSetter", "data", "_normalMaterial"], [["cc.Node", ["_name", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children"], 1, 4, 9, 5, 1, 7, 2], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["65c963D+sNPTaWG7QIj/Eyr", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_normalMaterial"], 2, 1, 9, 5, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[2, 0, 1, 2, 2], [0, 0, 5, 3, 2, 4, 6, 2], [0, 0, 5, 7, 3, 2, 4, 6, 2], [0, 0, 5, 2, 6, 2], [6, 0, 1, 2, 3, 3], [3, 0, 1, 2, 3, 4, 3], [3, 2, 3, 4, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 7], [11, 0, 1, 2, 2], [4, 0, 2], [0, 0, 7, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 2], [0, 0, 1, 5, 3, 2, 4, 6, 3], [5, 0, 1], [2, 1, 2, 1], [7, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4, 4]], [[[[9, "StartChallangeTaskItem"], [10, "StartChallangeTaskItem", [-3, -4, -5, -6, -7, -8], [[13, -2]], [14, -1, 0], [5, 660, 137]], [2, "button", 1, [-11], [[5, 1, 0, -9, [6], 7], [15, 3, -10, [[16, "cc314A1xixG2aPXVeVQ1uU0", "onClickItem", 1]], [4, 4286348412], 8]], [0, "a8dz6tYGxGk6GOFu76im4B", 1, 0], [5, 146, 86], [246.469, 5.179, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "rewardNode", 1, [-13, -14], [[17, 1, 1, 30, -12, [5, 262, 128]]], [0, "cfz+sid9xFCqshiH0xaZnW", 1, 0], [5, 262, 128], [0, 4.336, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "lbLvVal", 1, [[7, "+10", 26, 31, 1, 1, 1, -15, [4]], [8, 3, -16, [4, 4279374353]]], [0, "00aqIsz0tN2LujyXdAhMjV", 1, 0], [5, 50.1, 45.06], [-267, 1.514, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnLb", 2, [[7, "领取", 26, 31, 1, 1, 1, -17, [5]], [8, 3, -18, [4, 4279374353]]], [0, "9dttdUcDVFRJpc06KIARfO", 1, 0], [5, 58, 45.06], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "img_jj_txz", 1, [[5, 1, 0, -19, [0], 1]], [0, "50aw/yollE8beRovLrrAzp", 1, 0], [5, 660, 137]], [1, "icon_dj", 1, [[6, -20, [2], 3]], [0, "b0jOIMWXhHeZumOEIxnQud", 1, 0], [5, 68, 77], [-266, 0.582, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "icon_dagou", false, 1, [[6, -21, [9], 10]], [0, "33sxqP+glHnp0xsGUIyv+F", 1, 0], [5, 62, 49], [253, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "RewardItem", 3, [4, "2bASLwK1NDL6pZvMKugC5l", true, -22, 11], [-73, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "RewardItem", 3, [4, "03f05kNalMO6UJBfs8i9hV", true, -23, 12], [73, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 4, 0, -4, 2, 0, -5, 8, 0, -6, 3, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, 0, 3, 0, -1, 9, 0, -2, 10, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 2, 9, 0, 2, 10, 0, 5, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, -1, 1, 6, -1, 1, 3, 3], [0, 2, 0, 3, 0, 0, 0, 4, 0, 0, 5, 1, 1]], [[{"name": "img_rw_liebiao", "rect": [0, 0, 341, 137], "offset": [0, 0], "originalSize": [341, 137], "capInsets": [147, 0, 182, 0]}], [1], 0, [0], [4], [6]], [[{"name": "button03", "rect": [0, 0, 91, 86], "offset": [0, 0], "originalSize": [91, 86], "capInsets": [32, 0, 35, 0]}], [1], 0, [0], [4], [7]]]]