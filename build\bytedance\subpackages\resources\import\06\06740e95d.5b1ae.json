[1, ["ecpdLyjvZBwrvm+cedCcQy", "94EziLmzlAirmOfZrgDe1Z", "38grv9cKxKa6gWNZwnDIl5", "a2MjXRFdtLlYQ5ouAFv/+R", "9aMIpYX4NKVYCa/143BXoz", "4613FnL15GjKE6Ci5QeOnx", "00NIGuVtBNMpyt0XH28Xmd", "f3p84uzd5EVLXvLuhlyoRY", "29R34cGbFB7YQJIiPE06Fl", "c46cSnrYpELobhdnhwO2KF", "7a/QZLET9IDreTiBfRn2PD", "ab6In+2FxIm48/mlM9oFKo"], ["node", "_spriteFrame", "_parent", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_N$overflow", "_enableWrapText", "_spacingX", "_styleFlags", "_N$cacheMode", "node", "_materials"], -8, 1, 3], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "_N$paddingTop", "node", "_layoutSize"], -2, 1, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$horizontalAlign", "node"], -3, 1], ["cc.Prefab", ["_name"], 2], ["74438RbUVBP6Jp5z1BLOvtf", ["node", "root"], 3, 1, 1], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "desc", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], -1, 1, 3]], [[5, 0, 1, 2, 2], [0, 0, 6, 5, 3, 4, 7, 2], [1, 1, 0, 3, 4, 5, 3], [1, 3, 4, 5, 1], [12, 0, 1, 2, 2], [0, 0, 8, 5, 3, 4, 2], [0, 0, 6, 8, 5, 3, 4, 7, 2], [0, 0, 8, 5, 3, 4, 7, 2], [3, 0, 1, 3, 5, 6, 4], [9, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3], [2, 0, 1, 2, 3, 4, 11, 12, 6], [2, 0, 1, 7, 2, 3, 4, 6, 11, 12, 8], [13, 0, 1], [7, 0, 2], [0, 0, 2, 6, 5, 3, 9, 4, 3], [0, 0, 6, 8, 3, 4, 2], [0, 0, 6, 8, 3, 7, 2], [0, 0, 6, 5, 3, 4, 10, 7, 2], [0, 0, 1, 6, 5, 3, 9, 4, 7, 3], [0, 0, 6, 5, 3, 9, 4, 10, 7, 2], [0, 0, 1, 6, 5, 3, 4, 7, 3], [0, 0, 1, 6, 5, 3, 4, 3], [8, 0, 1, 1], [5, 1, 2, 1], [1, 1, 0, 3, 4, 3], [1, 0, 3, 4, 5, 2], [1, 3, 4, 1], [1, 0, 2, 3, 4, 3], [3, 0, 1, 4, 2, 5, 6, 5], [3, 0, 1, 3, 2, 5, 6, 5], [11, 0, 1, 2, 3, 4], [2, 0, 1, 5, 2, 3, 4, 11, 12, 7], [2, 0, 1, 5, 2, 8, 9, 3, 4, 6, 10, 11, 12, 11], [6, 0, 1, 5, 2, 3, 4, 6, 7], [6, 0, 1, 2, 3, 4, 6, 6], [14, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "img_jlxzd", "rect": [0, 0, 308, 257], "offset": [0, 0], "originalSize": [308, 257], "capInsets": [148, 117, 149, 133]}], [4], 0, [0], [3], [2]], [[[14, "Item_Reward2C1View"], [5, "Item_Reward2C1View", [-4, -5], [[23, -3, -2]], [24, -1, 0], [5, 750, 1334]], [5, "item", [-7, -8, -9, -10, -11, -12], [[2, 1, 0, -6, [16], 17]], [0, "78vp4QEUZI5qont7gAswwT", 1, 0], [5, 308, 423]], [5, "box", [-14, -15, -16, -17, -18], [[29, 1, 2, -40, 40, -13, [5, 750, 883]]], [0, "cbh74iG2JNWYggDA+9Cz25", 1, 0], [5, 750, 883]], [7, "btn", [-21, -22, -23], [[9, "BagAdRefreshBuff", "是否观看广告刷新属性", -19, [[10, "74438RbUVBP6Jp5z1BLOvtf", "adResetAllBuff", 1]]], [2, 1, 0, -20, [21], 22]], [0, "57d+AgNfNALK3TJYYBSz6Z", 1, 0], [5, 246, 108], [-143, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", 3, [-25, -26, -27], [[8, 1, 1, 27.999999999999996, -24, [5, 484, 120]]], [0, "5dMzaKAelOAJOpgYdVL4S+", 1, 0], [5, 484, 120], [0, 381.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btn", [-30, -31], [[9, "BagAdGetAllBuff", "是否观看广告获得所有属性", -28, [[10, "74438RbUVBP6Jp5z1BLOvtf", "adGetAllSkill", 1]]], [25, 1, 0, -29, [26]]], [0, "82OV+8XgRKJrkYTwbOuIlx", 1, 0], [5, 246, 108], [143, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "root", 3, [2], [[30, 1, 1, 39, 34, -32, [5, 308, 423]]], [0, "46KwqWUtNMdontI7Tm6diE", 1, 0], [5, 308, 423], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Layout", 3, [4, 6], [[8, 1, 1, 40, -33, [5, 532, 260]]], [0, "ddwmWeCCZECaLHLX1UI7xn", 1, 0], [5, 532, 260], [0, -311.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "maskbg", 161, 1, [[26, 0, -34, [0], 1], [31, 45, 750, 1334, -35]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4278190080], [5, 750, 1334]], [16, "bg", 1, [3, -36], [0, "81vg/003JNFJr3x8kM830u", 1, 0], [5, 750, 1334]], [1, "name", 2, [[32, "Buff名称", 26, 30, false, 1, 1, -37, [14]], [4, 3, -38, [4, 4278190080]]], [0, "f2cBPu4N9CO6bQRHcn/izb", 1, 0], [5, 103.78, 43.8], [0, 8.963, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 2, [[11, "x1", 36, false, 1, 1, -39, [15]], [4, 3, -40, [4, 4278190080]]], [0, "d8lwtogH5Fspw2l1gLtG/Q", 1, 0], [5, 44.02, 56.4], [44.899, 61.429, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoicon", 4, [[3, -41, [18], 19], [13, -42]], [0, "2bMYh0McdLl5OJwuVe4e01", 1, 0], [5, 50, 52], [-72.522, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[12, "刷新", 35, false, false, 1, 1, 2, -43, [20]], [4, 3, -44, [4, 4278190080]]], [0, "faggS4ZjpPa5KnNntdpVBX", 1, 0], [5, 140, 50.4], [30.198, 2.371, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 6, [[12, "全都要", 35, false, false, 1, 1, 2, -45, [23]], [4, 3, -46, [4, 4278190080]]], [0, "668sJpPHxOf6EOqNOk8S32", 1, 0], [5, 166, 50.4], [31.847, 2.371, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoicon", 6, [[3, -47, [24], 25], [13, -48]], [0, "1cSWDXKk1BfKRW4AzlM3Bh", 1, 0], [5, 53, 41], [-76.776, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "titleBg", 3, [-49], [0, "49GkTZRoBJaanU0fQqnAVE", 1, 0], [0, 481.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "img_btembx", 17, [[27, -50, [2]]], [0, "97eeEopGNPn4I+eHaf/UbK", 1, 0], [5, 531, 334], [0, 0.5, 0], [0, -156.801, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 5, [[3, -51, [3], 4]], [0, "17VcVYMEtBKoqKo8P+8gFZ", 1, 0], [5, 87, 30], [-198.5, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "title", 5, [[33, "选择宝箱奖励", 35, 43, false, 1, 1, 1, 1, 2, 1, -52, [5]]], [0, "8cXYiN4o5B7qlQz0sxbw/+", 1, 0], [5, 254, 54.18], [-3.552713678800501e-15, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 5, [[3, -53, [6], 7]], [0, "1d302R389LPYYOQewVA6+j", 1, 0], [5, 87, 30], [198.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "getinfo", false, 3, [[11, "选择1个宝物", 32, false, 1, 1, -54, [8]]], [0, "2c0uXyodtBBIqa2In1n3N6", 1, 0], [4, 4282298874], [5, 174.5, 50.4], [0, 340.00000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "namebg", 2, [[2, 1, 0, -55, [9], 10]], [0, "2dYjYtYaZPqp8D5P3n7Ci/", 1, 0], [5, 218, 44], [0, 9.141, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "frame", 2, [[2, 1, 0, -56, [11], 12]], [0, "cf3L0WIBVPRYYmNckbCyFz", 1, 0], [5, 116, 120], [0, 105.838, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon", 2, [[28, 2, false, -57, [13]]], [0, "82OLVW1hVIHZHFEIMVTsK3", 1, 0], [5, 105, 105], [0, 110.332, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [20, "desc", 2, [[34, false, "<outline color=black width=2>火尖枪释放可造成<color=#FFDD42>1.5倍</color>伤害连续红色枪刺</outline>", 1, 24, 240, 36, -58]], [0, "2beSjvN/xJZJX/vYWoJyey", 1, 0], [4, 4278190080], [5, 240, 81.35999999999999], [0, 0.5, 1], [0, -26.461, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "richtext_tips", false, 4, [[35, false, "高概率刷出<color=#0fffff>稀有</color>或以上属性", 20, 240, 30, -59]], [0, "62UxwxW71C0JNaX3WvVquu", 1, 0], [5, 240, 37.8], [0, -69.93, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "kbx", false, 10, [[36, "bx01", "idle", 0, "idle", -60, [27]]], [0, "38ZkTjkx5MIoGC4VYrEWv7", 1, 0], [5, 872, 1334]]], 0, [0, 4, 1, 0, 4, 7, 0, 0, 1, 0, -1, 9, 0, -2, 10, 0, 0, 2, 0, -1, 23, 0, -2, 24, 0, -3, 25, 0, -4, 11, 0, -5, 12, 0, -6, 26, 0, 0, 3, 0, -1, 17, 0, -2, 5, 0, -3, 22, 0, -4, 7, 0, -5, 8, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, -2, 27, 0, -3, 14, 0, 0, 5, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, 0, 6, 0, 0, 6, 0, -1, 15, 0, -2, 16, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -2, 28, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 18, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 5, 1, 2, 2, 7, 3, 2, 10, 4, 2, 8, 6, 2, 8, 60], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1], [0, 3, 0, 0, 1, 0, 0, 1, 0, 0, 4, 0, 5, 0, 0, 0, 0, 6, 0, 7, 0, 0, 8, 0, 0, 9, 0, 10]], [[{"name": "img_jlxzbt", "rect": [0, 0, 44, 44], "offset": [0, 0], "originalSize": [44, 44], "capInsets": [15, 0, 14, 0]}], [4], 0, [0], [3], [11]]]]