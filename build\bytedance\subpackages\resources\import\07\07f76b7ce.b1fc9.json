[1, ["ecpdLyjvZBwrvm+cedCcQy", "3cCshaTXxG46zNxE9+oVOu", "b4n0uanjhLsI1TEaOiQRoU", "f7293wEF9JhIuMEsrLuqng"], ["node", "_textureSetter", "root", "tag", "bg", "icon", "val", "data", "_spriteFrame", "_N$file"], [["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials"], 1, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_children", "_components", "_prefab", "_contentSize"], 1, 2, 9, 4, 5], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 12, 4, 5, 5, 7], ["87bed3IyxNFlq8CyuFedRT0", ["node", "val", "icon", "bg", "tag"], 3, 1, 1, 1, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Label", ["_fontSize", "_lineHeight", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3]], [[1, 0, 1, 2, 2], [0, 0, 1, 2, 3, 4, 5, 3], [2, 2, 3, 1], [4, 0, 2], [5, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 2, 3, 4, 5, 6, 7, 3], [0, 0, 1, 2, 3, 4, 5, 6, 3], [7, 0, 1, 2, 3, 4, 1], [1, 1, 2, 1], [8, 0, 1, 2, 2], [2, 0, 1, 2, 3, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "bg_icon_00", "rect": [1, 1, 100, 100], "offset": [0, 0], "originalSize": [102, 102], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [1], [1]], [[[3, "RewardItem"], [4, "RewardItem", 1, [-7, -8, -9, -10], [[7, -6, -5, -4, -3, -2]], [8, -1, 0], [5, 116, 120]], [5, "val", 1, 1, [[-11, [9, 2, -12, [4, 4278190080]]], 1, 4], [0, "e5J6wr5jNMgbXxWtPqgyAx", 1, 0], [5, 4, 41.8], [0, 1, 0.5], [48.395, -33.869, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 1, 1, [-13], [0, "c0PNkrYNhGKKfnBMRIeCKX", 1, 0], [5, 100, 100]], [2, 3, [0]], [1, "icon", 1, 1, [-14], [0, "bdbdts/cdCX6DWFKTsgAm5", 1, 0], [5, 80, 80]], [10, 0, false, 5, [1]], [6, "tag", 1, 1, [-15], [0, "91sTlwcfxEKqcCGENXj3HV", 1, 0], [5, 33, 33], [43.854, 38.615, 0, 0, 0, 0, 1, 1.3, 1.3, 1.3]], [2, 7, [2]], [11, 28, 30, false, 1, 2, 1, 2, [3]]], 0, [0, 2, 1, 0, 3, 8, 0, 4, 4, 0, 5, 6, 0, 6, 9, 0, 0, 1, 0, -1, 3, 0, -2, 5, 0, -3, 7, 0, -4, 2, 0, -1, 9, 0, 0, 2, 0, -1, 4, 0, -1, 6, 0, -1, 8, 0, 7, 1, 15], [0, 0, 0, 0, 4, 9], [-1, -1, -1, -1, 8, 9], [0, 0, 0, 0, 2, 3]]]]