{"1": {"id": 1, "lv": 1, "element": "[[7,45],[20,100],[8,180],[21,260],[998,360],[998,420]]", "unlockActive": [1000, 1020, 1040, 1060, 1080, 1100, 1120, 1140, 1200, 1220, 1240, 1260, 1300], "unlockPassive": [1000, 1020, 1040, 1100, 1120, 1140, 1200, 1220, 1240, 1260, 1280, 1300], "lvupPool": [1000, 1020, 1040, 1060, 1080, 1100, 1120, 1140, 1200, 1220, 1240, 1260, 1300], "acteventPool": [1000, 1020, 1040, 1060, 1080, 1100, 1120, 1140, 1200, 1220, 1240, 1260, 1300], "maxWeight": [10, 100], "maxLv": 3, "maxLimit": 2, "norPassPool": [11000, 11002, 11004, 11020, 11022, 11040, 11042, 11044, 11060, 11062, 11080, 11082, 11084, 11100, 11102, 11120, 11122, 11140, 11142, 11200, 11202, 11204, 11220, 11222, 11224, 11240, 11242, 11260, 11262, 11300, 11302, 1, 2, 11, 12, 31, 41, 51, 71], "advPassPool": [10000, 10001, 10002, 11001, 11003, 11005, 11021, 11023, 11041, 11043, 11045, 11061, 11063, 11081, 11083, 11085, 11101, 11103, 11104, 11121, 11123, 11124, 11141, 11143, 11201, 11203, 11205, 11221, 11223, 11225, 11241, 11243, 11261, 11263, 11301, 11303, 11304, 21, 32, 42, 52, 72, 81], "norBuffPool": [1, 11, 31, 41, 51, 71], "advBuffPool": [2, 12, 21, 32, 42, 52, 72, 81], "tdPool1": [[11, 12, 51, 52, 61, 62, 10000, 10001, 10002, 20003, 20005, 20007, 20011, 20014, 20015, 20016, 20020, 20021, 20022, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20034, 20035, 20036, 20037, 20038, 20041, 20042, 20043, 20044, 20045, 20046, 20047, 20048, 20049, 20050, 20051, 11140, 11141, 11142, 11143], [80, 11, 11, 8, 80, 11, 8, 8, 8, 11, 11, 15, 8, 15, 11, 80, 80, 11, 8, 11, 80, 15, 8, 80, 11, 15, 80, 11, 8, 80, 11, 15, 80, 11, 15, 3, 80, 15, 11, 8, 80, 11, 15, 80, 11, 15, 11, 8, 15, 8]], "tdPool2": [[11, 12, 51, 52, 61, 62, 10000, 10001, 10002, 20003, 20005, 20007, 20011, 20014, 20015, 20016, 20020, 20021, 20022, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20034, 20035, 20036, 20037, 20038, 20041, 20042, 20043, 20044, 20045, 20046, 20047, 20048, 20049, 20050, 20051, 11140, 11141, 11142, 11143, 11040, 11041, 11042, 11043, 11044, 11045], [80, 11, 11, 8, 80, 11, 8, 8, 8, 11, 11, 15, 8, 15, 11, 80, 80, 11, 8, 11, 80, 15, 8, 80, 11, 15, 80, 11, 8, 80, 11, 15, 80, 11, 15, 3, 80, 15, 11, 8, 80, 11, 15, 80, 11, 15, 11, 8, 15, 8, 11, 8, 15, 8, 11, 8]], "tdPool3": [[11, 12, 51, 52, 61, 62, 10000, 10001, 10002, 20003, 20005, 20007, 20011, 20014, 20015, 20016, 20020, 20021, 20022, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20034, 20035, 20036, 20037, 20038, 20041, 20042, 20043, 20044, 20045, 20046, 20047, 20048, 20049, 20050, 20051, 11140, 11141, 11142, 11143, 11040, 11041, 11042, 11043, 11044, 11045, 11080, 11081, 11082, 11083, 11084, 11085], [80, 11, 11, 8, 80, 11, 8, 8, 8, 11, 11, 15, 8, 15, 11, 80, 80, 11, 8, 11, 80, 15, 8, 80, 11, 15, 80, 11, 8, 80, 11, 15, 80, 11, 15, 3, 80, 15, 11, 8, 80, 11, 15, 80, 11, 15, 11, 8, 15, 8, 11, 8, 15, 8, 11, 8, 11, 8, 15, 8, 15, 8]], "tdPool4": [[11, 12, 51, 52, 61, 62, 10000, 10001, 10002, 20003, 20005, 20007, 20011, 20014, 20015, 20016, 20020, 20021, 20022, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20034, 20035, 20036, 20037, 20038, 20041, 20042, 20043, 20044, 20045, 20046, 20047, 20048, 20049, 20050, 20051, 11140, 11141, 11142, 11143, 11040, 11041, 11042, 11043, 11044, 11045, 11080, 11081, 11082, 11083, 11084, 11085, 11200, 11201, 11202, 11203, 11204, 11205], [80, 11, 11, 8, 80, 11, 8, 8, 8, 11, 11, 15, 8, 15, 11, 80, 80, 11, 8, 11, 80, 15, 8, 80, 11, 15, 80, 11, 8, 80, 11, 15, 80, 11, 15, 3, 80, 15, 11, 8, 80, 11, 15, 80, 11, 15, 11, 8, 15, 8, 11, 8, 15, 8, 11, 8, 11, 8, 15, 8, 15, 8, 11, 8, 15, 8, 15, 8]], "tdPool5": [[11, 12, 51, 52, 61, 62, 10000, 10001, 10002, 20003, 20005, 20007, 20011, 20014, 20015, 20016, 20020, 20021, 20022, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20034, 20035, 20036, 20037, 20038, 20041, 20042, 20043, 20044, 20045, 20046, 20047, 20048, 20049, 20050, 20051, 11140, 11141, 11142, 11143, 11040, 11041, 11042, 11043, 11044, 11045, 11080, 11081, 11082, 11083, 11084, 11085, 11200, 11201, 11202, 11203, 11204, 11205, 11700, 11701, 11702, 11703, 11704], [80, 11, 11, 8, 80, 11, 8, 8, 8, 11, 11, 15, 8, 15, 11, 80, 80, 11, 8, 11, 80, 15, 8, 80, 11, 15, 80, 11, 8, 80, 11, 15, 80, 11, 15, 3, 80, 15, 11, 8, 80, 11, 15, 80, 11, 15, 11, 8, 15, 8, 11, 8, 15, 8, 11, 8, 11, 8, 15, 8, 15, 8, 11, 8, 15, 8, 15, 8, 11, 8, 15, 8, 8]]}}