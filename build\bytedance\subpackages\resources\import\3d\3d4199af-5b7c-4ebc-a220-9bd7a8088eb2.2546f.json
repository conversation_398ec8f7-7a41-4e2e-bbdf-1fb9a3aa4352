[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "de9P2St5JLZIV0Yv22GYlt"], ["node", "_spriteFrame", "_N$normalSprite", "_N$disabledSprite", "_parent", "root", "richText", "toggle", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 2, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -4, 1, 3], ["cc.RichText", ["_isSystemFontUsed", "_N$fontSize", "_N$lineHeight", "_N$string", "_N$horizontalAlign", "_N$maxWidth", "node"], -3, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark"], 1, 1, 5, 1, 1], ["60b18AjVIFJW55nE1wRLSDL", ["node", "toggle", "richText"], 3, 1, 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 6, 3, 4, 5, 7, 2], [1, 1, 0, 3, 4, 3], [4, 0, 1, 2, 3, 4, 7, 8, 6], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3], [0, 0, 6, 8, 3, 4, 5, 7, 2], [0, 0, 8, 3, 4, 5, 7, 2], [0, 0, 6, 3, 4, 9, 5, 7, 2], [4, 0, 1, 5, 2, 3, 4, 6, 7, 8, 8], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [7, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 2, 6, 3, 4, 9, 5, 3], [2, 0, 2, 3, 4, 5, 6, 2], [2, 0, 2, 7, 3, 4, 5, 6, 2], [2, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 5, 3], [1, 3, 4, 1], [1, 0, 3, 4, 2], [1, 0, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4], [3, 1, 2, 1], [9, 0, 1], [5, 0, 3, 4, 1, 5, 2, 6, 7], [5, 0, 1, 2, 6, 4], [6, 0, 1, 2, 3, 3], [12, 0, 1, 2, 3, 4, 5, 3], [13, 0, 1, 2, 1]], [[11, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [12, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 1, [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16], [[29, -4, -3, -2]], [23, -1, 0], [5, 750, 1335]], [7, "Background", [-18], [[2, 1, 0, -17, [7]]], [0, "21eOjg9aJJ4riuEExnKg+q", 1, 0], [5, 220, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "Background", [-20], [[2, 1, 0, -19, [11]]], [0, "b1Dq/fwklOTKK8LFbyft+n", 1, 0], [5, 220, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [15, "New Toggle", 1, [-22, -23], [-21], [0, "ccLJTpGj9CYbYcHUwLohbb", 1, 0], [5, 40, 40], [83.013, -192.869, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "bg", 178.5, 1, [[17, 0, -24, [0], 1], [22, 45, 750, 1334, -25]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4281542699], [5, 750, 1335]], [1, "gg_db_blue2", 1, [[18, 1, 0, -26, [2], 3], [24, -27]], [0, "036kHne0tBhbiiPxElLEUH", 1, 0], [5, 586, 414], [5.477, -26.657, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 1, [[3, "提 示", 32, false, 1, 1, -28, [5]], [4, 2, -29, [4, 4284432169]]], [0, "76e4OPXTtE64fLOqUlawMo", 1, 0], [5, 76.89, 54.4], [9.125, 151.277, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnCancel", 1, [2], [[10, 0.9, 3, -30, [[5, "60b18AjVIFJW55nE1wRLSDL", "onCancel", 1]], [4, 4293322470], [4, 3363338360], 2, 8, 9]], [0, "ban1YayWhEVZ/Kqn5NUvxP", 1, 0], [5, 220, 80], [-137.442, -129.315, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 2, [[9, "取  消", 28, false, false, 1, 1, 1, -31, [6]], [4, 2, -32, [4, 4286139662]]], [0, "cefI7WRPFITZzZPYcYH7SL", 1, 0], [5, 75.56, 54.4], [0.259, 5.742, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnConfirm", 1, [3], [[10, 0.9, 3, -33, [[5, "60b18AjVIFJW55nE1wRLSDL", "onConfirm", 1]], [4, 4293322470], [4, 3363338360], 3, 12, 13]], [0, "23yXtv+UJKHpx/bIBId5BK", 1, 0], [5, 220, 80], [150.571, -130.318, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 3, [[9, "确  认", 28, false, false, 1, 1, 1, -34, [10]], [4, 2, -35, [4, 4280041602]]], [0, "21RW4iBapED4xtSnI8N2gd", 1, 0], [5, 75.56, 54.4], [-1.297, 5.038, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "close", 1, [[19, -36, [14]], [27, 0.9, 3, -37, [[5, "60b18AjVIFJW55nE1wRLSDL", "onCancel", 1]]]], [0, "83DD3WZLxBKpjexeaRGEMC", 1, 0], [5, 78, 46], [259.784, 156.005, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 4, [[20, 0, -38, [16]]], [0, "07ZNYLVZdAtLiyvyYc8dAH", 1, 0], [5, 38, 38], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "gg_db", 1, [[2, 1, 0, -39, [4]]], [0, "7bgN+6N4ZMZYI8MM+fEsSm", 1, 0], [5, 534, 147], [6.391, 16.777, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc", 1, [[25, false, "", 1, 26, 350, 31, -40]], [0, "e1q9d6IvlBDbupScIgwXM6", 1, 0], [5, 350, 39.06], [10.217, 20.816, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "New RichText", 1, [-41], [0, "1dWq2Wn3hEOIYP8dC9d8MN", 1, 0], [5, 92.03, 63], [0, 16.353, 0, 0, 0, 0, 1, 1, 1, 1]], [26, false, 24, 50, 16], [8, "New Label", 1, [[3, "点击空白区域关闭", 22, false, 1, 1, -42, [15]]], [0, "0bCjp6jd5D7bu+L2lQu2eL", 1, 0], [4, 4294037950], [5, 176, 50.4], [6.954, -316.968, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "checkmark", false, 4, [-43], [0, "02442V+9FFWKeiCnEkqJfy", 1, 0], [5, 50, 43], [0.186, 5.952, 0, 0, 0, 0, 1, 1, 1, 1]], [21, 2, false, 19, [17]], [28, 3, false, 4, [4, 4292269782], 13, 20], [8, "New Label", 1, [[3, "今日不再询问", 20, false, 1, 1, -44, [18]]], [0, "5amRQZ95pFf6rq60nOdMpP", 1, 0], [4, 4288646786], [5, 120, 50.4], [168.007, -193.688, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 6, 17, 0, 7, 21, 0, 0, 1, 0, -1, 5, 0, -2, 6, 0, -3, 14, 0, -4, 7, 0, -5, 15, 0, -6, 8, 0, -7, 10, 0, -8, 16, 0, -9, 12, 0, -10, 18, 0, -11, 4, 0, -12, 22, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, -1, 11, 0, -1, 21, 0, -1, 13, 0, -2, 19, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, -1, 17, 0, 0, 18, 0, -1, 20, 0, 0, 22, 0, 8, 1, 2, 4, 8, 3, 4, 10, 44], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20], [-1, 1, -1, 1, -1, -1, -1, -1, 2, 3, -1, -1, 2, 3, -1, -1, -1, -1, -1, 1], [0, 1, 0, 1, 0, 0, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 0, 0, 0, 4]]