[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "f6NcoTBPNJ4qSyD40PNpRP", "c3+ruR7+FEnKfu8yo+WDeT", "f3p84uzd5EVLXvLuhlyoRY", "7bvouSjDBJkayaYUJwyZdY", "65OUoZ25pGHqftEDT5VTS4"], ["node", "_spriteFrame", "_N$disabledSprite", "_parent", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], -2, 4, 5, 9, 1, 2, 7, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_enabled", "_top", "_bottom", "node"], -6, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite", "_N$target"], 1, 1, 9, 5, 5, 6, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 5, 7], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["8b94fVpwcpFmL8EbZiAnUx6", ["node", "labelArr"], 3, 1, 2]], [[4, 0, 1, 2, 2], [0, 0, 8, 9, 7, 5, 6, 10, 2], [3, 1, 0, 2, 3, 4, 3], [9, 0, 1, 2, 2], [0, 0, 8, 7, 5, 6, 2], [0, 0, 8, 7, 5, 6, 10, 2], [3, 0, 2, 3, 4, 2], [2, 0, 5, 1, 2, 3, 4, 8, 9, 7], [2, 0, 5, 6, 1, 2, 3, 4, 7, 8, 9, 9], [11, 0, 1, 2, 3], [6, 0, 2], [0, 0, 1, 9, 7, 5, 6, 3], [0, 0, 2, 8, 7, 5, 11, 6, 3], [0, 0, 8, 9, 7, 5, 6, 2], [0, 0, 9, 5, 6, 2], [0, 0, 3, 9, 7, 5, 6, 10, 3], [0, 0, 4, 8, 7, 5, 11, 6, 10, 3], [7, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 9, 2], [1, 6, 3, 0, 4, 5, 1, 2, 9, 8], [1, 3, 0, 4, 5, 7, 8, 1, 2, 9, 9], [1, 0, 1, 2, 9, 4], [3, 1, 0, 2, 3, 3], [4, 1, 2, 1], [8, 0, 1], [2, 0, 1, 2, 3, 8, 9, 5], [2, 0, 6, 1, 2, 3, 4, 7, 8, 9, 8], [10, 0, 1], [5, 1, 0, 2, 3, 4, 5, 6, 3], [5, 0, 2, 3, 4, 5, 7, 6, 2], [12, 0, 1, 1]], [[10, "M38_Pop_Ad"], [11, "M38_Pop_Ad", 1, [-4, -5], [[30, -3, [-2]]], [23, -1, 0], [5, 750, 1334]], [14, "content", [-6, -7, -8, -9, -10], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 574, 505]], [1, "btn_coutinue", 2, [-13, -14], [[28, 0.9, 3, -11, [[9, "ff733bBkoZAHL6KHq12ja0K", "onClickCoutinueFight", 1]], [4, 4293322470], [4, 3363338360], 17], [22, 1, 0, -12, [18]]], [0, "baZwZnr4BFeahDVKhdqBXc", 1, 0], [5, 230, 90], [0, -152.322, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 3, [-17, -18], [[2, 1, 0, -15, [15], 16], [19, false, 0, 45, -248, -248, 100, 40, -16]], [0, "7eCutlVDFKS5Svm2cGpnF7", 1, 0], [5, 150, 65], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1.5]], [15, "Background", 512, [-21], [[2, 1, 0, -19, [20], 21], [20, 0, 45, 9.5, 9.5, 7.5, 7.5, 100, 40, -20]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 61, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "bg_shuxing", 2, [-23, -24], [[2, 1, 0, -22, [9], 10]], [0, "0ddPuPQoZK0q9X2MFdRMol", 1, 0], [5, 448, 198], [0, 6.958, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "maskbg", 70, 1, [[18, 45, -25], [6, 0, -26, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [13, "bg", 1, [2], [[21, 45, 750, 1334, -27]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [4, "bg", 2, [[2, 1, 0, -28, [2], 3], [24, -29]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 488, 448]], [1, "title_zhua<PERSON><PERSON>", 2, [-31], [[2, 1, 0, -30, [5], 6]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 488, 86], [0, 177.348, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label_title", 10, [[25, "提示", false, 1, 1, -32, [4]], [3, 3, -33, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 86, 56.4]], [4, "Label", 6, [[7, "战斗准备中，   分钟后开启。\n是否观看视频直接进入?", 30, false, 1, 1, 2, -34, [7]], [3, 3, -35, [4, 4278190080]]], [0, "65vJ/N4a1KVpjxYocZGve1", 1, 0], [5, 410, 138.4]], [17, "Label_tips", 6, [[-36, [3, 3, -37, [4, 4278190080]]], 1, 4], [0, "52GPNNLQZNDpvhNAFDQfG4", 1, 0], [4, 4282072023], [5, 399, 138.4], [-10.106, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Label", 3, [[8, "继续", 35, false, false, 1, 1, 2, 1, -38, [11]], [3, 3, -39, [4, 4278215712]]], [0, "99+SJAXOBM15DIFNxsP80M", 1, 0], [5, 220, 60], [0, 3.08, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Label", 4, [[8, "刷新", 25, false, false, 1, 1, 2, 1, -40, [12]], [3, 2, -41, [4, 4291003648]]], [0, "4aQxE6oDdNqLv2lpZ9zmtg", 1, 0], [5, 83, 40], [22.824, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "icon_ads", 4, [[6, 0, -42, [13], 14], [27, -43]], [0, "4fjcTNhkRKcYMci6Nm3cEg", 1, 0], [5, 39, 31], [-40.664, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnclose", 2, [5], [[29, 3, -44, [[9, "ff733bBkoZAHL6KHq12ja0K", "onClickGiveUp", 1]], [4, 4293322470], [4, 3363338360], 5, 22]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [192.876, 175.006, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "30", 30, false, 1, 1, 2, 13, [8]], [16, "Label", false, 5, [[26, "返回", false, false, 1, 1, 1, 1, -45, [19]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 18, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, -1, 9, 0, -2, 10, 0, -3, 6, 0, -4, 3, 0, -5, 17, 0, 0, 3, 0, 0, 3, 0, -1, 14, 0, -2, 4, 0, 0, 4, 0, 0, 4, 0, -1, 15, 0, -2, 16, 0, 0, 5, 0, 0, 5, 0, -1, 19, 0, 0, 6, 0, -1, 12, 0, -2, 13, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 18, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 19, 0, 5, 1, 2, 3, 8, 5, 3, 17, 45], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, 2, -1, -1, -1, 1, 2], [0, 2, 0, 3, 0, 0, 4, 0, 0, 0, 5, 0, 0, 0, 6, 0, 7, 1, 0, 0, 0, 8, 1]]