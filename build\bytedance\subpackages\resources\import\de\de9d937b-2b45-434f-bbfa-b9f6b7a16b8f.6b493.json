[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "03W+ZBfDNJwreQxWi3jfXh", "4em49P+vdBkoQ550ODpPYy"], ["node", "_spriteFrame", "_parent", "_N$disabledSprite", "templete", "root", "_N$target", "_N$content", "content", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "node"], -1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_styleFlags", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content", "templete"], 3, 1, 5, 1, 6], ["533fdYVCYdKvYTYHA8Kr+G2", ["node"], 3, 1]], [[3, 0, 1, 2, 2], [0, 0, 8, 6, 4, 5, 9, 2], [1, 0, 1, 3, 4, 4], [5, 0, 2], [0, 0, 1, 8, 6, 4, 5, 3], [0, 0, 2, 7, 6, 4, 10, 5, 3], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 7, 8, 4, 5, 2], [0, 0, 8, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 9, 2], [0, 0, 3, 7, 6, 4, 10, 5, 9, 3], [0, 0, 7, 8, 6, 4, 5, 11, 9, 2], [0, 0, 7, 6, 4, 5, 11, 9, 2], [1, 0, 4, 2], [1, 2, 0, 1, 4, 4], [2, 0, 2, 3, 4, 2], [2, 1, 2, 3, 4, 2], [2, 1, 0, 2, 3, 4, 3], [3, 1, 2, 1], [4, 0, 1, 5, 2, 3, 4, 8, 9, 7], [4, 0, 6, 1, 2, 3, 4, 7, 8, 9, 8], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 6, 6], [11, 0, 1, 2, 3, 1], [12, 0, 1]], [[3, "StartChallangeTaskView"], [4, "StartChallangeTaskView", 1, [-3, -4], [[27, -2]], [18, -1, 0], [5, 750, 1334]], [1, "Background", [-8], [[16, 1, -5, [4], 5], [22, 3, -7, [[23, "52385PSj7pK14qhQyEdFxQP", "close", 1]], [4, 4293322470], [4, 3363338360], -6, 6]], [0, "90OlI7eu5N6YuKmHM/sieV", 1, 0], [5, 52, 56], [300, 403.66, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "title_zhua<PERSON><PERSON>", [-10, 2], [[17, 1, 0, -9, [7], 8]], [0, "45NuY+WQ9L5J6TOpjXxAQa", 1, 0], [5, 710, 900]], [1, "New ScrollView", [-15], [[25, false, 0.75, 0.23, null, null, -12, -11], [26, -14, [0, 0, 20], -13, 10]], [0, "c16wooUcRFu7atWQTNsgll", 1, 0], [5, 660, 770], [0, -38.957, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 4, [-18], [[24, 0, -16, [9]], [2, 45, 240, 250, -17]], [0, "f6rJOdci5LpqR2itdXzuYz", 1, 0], [5, 660, 770], [0, 0.5, 1], [0, 385, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "content", 5, [[14, 0, 41, 614, -19]], [0, "ecKprdHUBMRoHqCj7C6P4V", 1, 0], [5, 660, 274], [0, 0, 1], [-330, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "maskbg", 230, 1, [[13, 45, -20], [15, 0, -21, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [6, "bg", 1, [-23], [[2, 45, 750, 1334, -22]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [7, "content", 8, [3, 4], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 710, 1080]], [9, "Label_title", 3, [[19, "星级挑战奖励", false, 1, 1, 1, 2, -24, [2]], [21, 4, -25, [4, 4278190080]]], [0, "6aRvpyzjtLSqbRuVrwAnNW", 1, 0], [5, 285, 56.4], [0, 404.104, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label", false, 2, [[20, "返回", false, false, 1, 1, 1, 1, -26, [3]]], [0, "32xj1jPPxLkrFw1riXxUhd", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, 0, 2, 0, 6, 2, 0, 0, 2, 0, -1, 11, 0, 0, 3, 0, -1, 10, 0, 7, 6, 0, 0, 4, 0, 8, 6, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 9, 1, 2, 2, 3, 3, 2, 9, 4, 2, 9, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, -1, 1, 3, -1, 1, -1, 4], [0, 1, 0, 0, 0, 2, 3, 0, 4, 0, 5]]