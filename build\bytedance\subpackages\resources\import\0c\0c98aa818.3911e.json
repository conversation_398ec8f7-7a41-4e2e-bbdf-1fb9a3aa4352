[1, ["ecpdLyjvZBwrvm+cedCcQy", "13R5ozr9pLoosTknLwN8kR", "e40u3avyZHWYWu7f8ivsQQ", "49p0wPDo1LbZh0qz19LmGy", "541JFhmHBKjJYh6xeJUq42", "a2MjXRFdtLlYQ5ouAFv/+R", "60tk6aaV9I7o0sdkHbwlOS", "e14bTA5z5AAb5h76T/03bw", "23Tw89umhO5pVi85QDxfi5"], ["node", "_spriteFrame", "_parent", "_textureSetter", "root", "suggestStr", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 5, 1, 9, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_isSystemFontUsed", "_string", "_N$overflow", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -6, 1, 3], ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], 0, 2, 4, 5, 1, 2, 7, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_color", "_anchorPoint", "_trs"], 0, 1, 12, 4, 5, 5, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_top", "node"], -3, 1], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target"], 2, 1, 9, 5, 5, 1], ["cc.Prefab", ["_name"], 2], ["3dcc4CSj6BLb408Ot2wmJMr", ["node", "suggestStr", "grouplist"], 3, 1, 1, 2], ["cc.Layout", ["_N$layoutType", "_N$paddingLeft", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.LabelOutline", ["_enabled", "_width", "node", "_color"], 1, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 2, 1, 1, 1, 1], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "_N$target", "checkMark"], 1, 1, 1, 1]], [[7, 0, 1, 2, 2], [1, 0, 3, 4, 5, 2], [0, 0, 5, 8, 3, 4, 7, 2], [0, 0, 5, 6, 3, 9, 4, 10, 7, 2], [3, 0, 6, 7, 3, 4, 5, 8, 2], [1, 0, 2, 3, 4, 3], [2, 1, 3, 0, 4, 5, 9, 10, 6], [15, 0, 1, 2, 3, 4, 3], [0, 0, 5, 6, 3, 4, 7, 2], [0, 0, 2, 5, 6, 3, 4, 7, 3], [3, 0, 2, 1, 6, 3, 4, 5, 4], [0, 0, 8, 6, 3, 4, 2], [0, 0, 8, 6, 3, 4, 7, 2], [0, 0, 5, 8, 6, 3, 4, 7, 2], [1, 1, 0, 3, 4, 5, 3], [8, 0, 1, 2, 3, 4, 5, 2], [5, 3, 0, 4, 5, 1, 2, 6, 7], [13, 0, 1, 2, 3], [9, 0, 2], [0, 0, 5, 8, 3, 9, 4, 7, 2], [0, 0, 1, 5, 6, 3, 9, 4, 3], [0, 0, 5, 6, 3, 9, 4, 7, 2], [0, 0, 5, 8, 3, 4, 2], [3, 0, 7, 3, 4, 9, 5, 8, 2], [3, 0, 1, 6, 3, 4, 5, 3], [4, 0, 1, 3, 4, 5, 6, 3], [4, 0, 1, 2, 3, 4, 5, 7, 6, 8, 9, 4], [4, 0, 1, 3, 4, 5, 7, 6, 8, 9, 3], [10, 0, 1, 2, 1], [7, 1, 2, 1], [1, 1, 3, 4, 2], [1, 1, 0, 3, 4, 3], [8, 1, 1], [11, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 6, 4], [5, 3, 0, 1, 2, 6, 5], [2, 1, 0, 4, 5, 2, 9, 10, 6], [2, 3, 6, 0, 2, 9, 10, 5], [2, 1, 3, 6, 0, 2, 9, 10, 6], [2, 1, 3, 7, 0, 4, 5, 2, 8, 9, 10, 9], [12, 0, 1, 2, 3, 3], [14, 0, 1, 2, 3, 4, 2]], [[[{"name": "fk_grey", "rect": [0, 0, 30, 30], "offset": [0, 0], "originalSize": [30, 30], "capInsets": [11, 12, 12, 12]}], [6], 0, [0], [3], [3]], [[{"name": "fk_btn", "rect": [0, 0, 32, 66], "offset": [0, 0], "originalSize": [32, 66], "capInsets": [12, 27, 12, 28]}], [6], 0, [0], [3], [4]], [[[18, "M20_Pop_FeedBack"], [11, "M20_Pop_FeedBack", [-8, -9], [[28, -7, -6, [-2, -3, -4, -5]]], [29, -1, 0], [5, 750, 1334]], [11, "box", [-12, -13, -14], [[14, 1, 0, -10, [26], 27], [32, -11]], [0, "d9DSXCIgVPqr8ZpxrZVWvk", 1, 0], [5, 600, 800]], [12, "checkList", [-16, -17, -18, -19], [[33, 3, -50.1, 25, 25, -15, [5, 600, 160]]], [0, "e9VpfvzjxCd535eETi8O0y", 1, 0], [5, 600, 160], [0, 125.546, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "New EditBox", [-21, -22, -23], [-20], [0, "77Ob0iONZGrbMkwevns8Vx", 1, 0], [4, 4285822068], [5, 480, 250], [0, -159.475, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "游戏卡死", 3, [-25, -26], [-24], [0, "4c+T7Cv7RB24QsgtvmGZpw", 1, 0], [5, 250, 80], [-225.10000000000002, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "数据丢失", 3, [-28, -29], [-27], [0, "50Q6G3ws9OF4PU4T7ZqGe4", 1, 0], [5, 250, 80], [49.89999999999998, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "游戏无法看广告", 3, [-31, -32], [-30], [0, "a2bfli2PBGgbMTkBvRX5pm", 1, 0], [5, 250, 80], [-225.1, -65, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "玩法与广告不同", 3, [-34, -35], [-33], [0, "f4Tl0sUXVAvYLA2XGXy502", 1, 0], [5, 250, 80], [49.900000000000006, -65, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "gg_db", 2, [-36, 4, 3], [0, "01q/dkKRlAw5N8FNe/+wqg", 1, 0], [4, 4293515731], [5, 500, 224], [0, 45.986, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Background", [-38], [[14, 1, 0, -37, [24], 25]], [0, "2fLFcrf/dKqrtyOjgrQiLc", 1, 0], [5, 435, 85], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [20, "maskbg", 185, 1, [[1, 0, -39, [0], 1], [34, 45, 750, 1334, -40]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4281542699], [5, 750, 1334]], [21, "New Label", 9, [[36, "反馈与建议", false, 1, 1, 2, -41, [2]], [40, false, 3, -42, [4, 4278190080]]], [0, "8eePfuVXtG6JxBsA2D4ikj", 1, 0], [4, 4283848278], [5, 350, 50.4], [0, 281.506, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "BACKGROUND_SPRITE", 512, 4, [[-43, [35, 0, 45, 160, 40, -44]], 1, 4], [0, "c6GeCbR79KwbWa0ojuSSvD", 1, 0], [5, 480, 250]], [26, "TEXT_LABEL", 512, false, 4, [[-45, [16, 0, 45, 5, 10, 158, 40, -46]], 1, 4], [0, "b8PbItrqFOwaSbkRCxplu1", 1, 0], [4, 4285822068], [5, 475, 240], [0, 0, 1], [-235, 115, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "PLACEHOLDER_LABEL", 512, 4, [[-47, [16, 0, 45, 5, 10, 158, 40, -48]], 1, 4], [0, "42AzUT19lAXZo1Mh44Aalw", 1, 0], [4, 4285822068], [5, 475, 240], [0, 0, 1], [-235, 115, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "游戏卡死", 5, [-49, -50], [0, "4f6FcR+ddBaLx6buxz0wTI", 1, 0], [5, 45, 45], [-8.782, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "数据丢失", 6, [-51, -52], [0, "cfX9XqYSJJU7T3HwGyojx9", 1, 0], [5, 45, 45], [-8.782, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "游戏无法看广告", 7, [-53, -54], [0, "c5mwdf6WhBgaygYkDH+Mg4", 1, 0], [5, 45, 45], [-8.782, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "玩法与广告不同", 8, [-55, -56], [0, "28s508JiBMCLdpe/LvJDdr", 1, 0], [5, 45, 45], [-8.782, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "btnExit", 2, [-59], [[15, 3, -58, [[17, "26fb6wQ5SBOEZMigCtCsTOj", "close", 1]], [4, 4293322470], [4, 3363338360], -57]], [0, "7bl/oIfX1JgLe/L10i2NYa", 1, 0], [5, 80, 80], [261.037, 323.443, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 20, [[30, 1, -60, [22]]], [0, "a5llsiVvBIGrvrGzXrgwy7", 1, 0], [5, 32, 32], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "btnContinue", 2, [10], [[15, 3, -61, [[17, "3dcc4CSj6BLb408Ot2wmJMr", "Submit", 1]], [4, 4293322470], [4, 3363338360], 10]], [0, "c99By6DvtNeLVe8mph9E7W", 1, 0], [5, 200, 85], [0, -320.116, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bg", 1, [2], [0, "81vg/003JNFJr3x8kM830u", 1, 0], [5, 750, 1334]], [31, 1, 0, 13, [3]], [37, 30, 35, false, 1, 14, [4]], [38, "您的建议对我们很重要", 30, 35, false, 1, 15, [5]], [41, 100, 4, 25, 26, 24], [8, "Background", 16, [[1, 0, -62, [6], 7]], [0, "85V54+AnlBjIQYpBhgymtX", 1, 0], [5, 45, 45], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [24, "checkmark", false, 16, [-63], [0, "0aYuqPbPpOG4mD75mYq2E/", 1, 0], [5, 45, 45]], [5, 0, false, 29, [8]], [3, "New Label", 5, [[6, "游戏卡死", 30, false, 1, 1, -64, [9]]], [0, "86JxRYhlNFbJ2IzUCxGsT9", 1, 0], [4, 4284374622], [5, 120, 50.4], [0, 0, 0.5], [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, false, 5, 5, 30], [9, "Background", 512, 17, [[1, 0, -65, [10], 11]], [0, "2c9HwzCqZEaaxg0L5Edpnz", 1, 0], [5, 45, 45], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "checkmark", 512, false, 17, [-66], [0, "1cYZ37LAlMrLhH1YAm4Biq", 1, 0], [5, 45, 45]], [5, 0, false, 34, [12]], [3, "New Label", 6, [[6, "数据丢失", 30, false, 1, 1, -67, [13]]], [0, "59FkVngtBAmbtajlq5Si1O", 1, 0], [4, 4284374622], [5, 120, 50.4], [0, 0, 0.5], [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, false, 6, 6, 35], [9, "Background", 512, 18, [[1, 0, -68, [14], 15]], [0, "0bqLK6bRtCWZlpRSbBeZLb", 1, 0], [5, 45, 45], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "checkmark", 512, false, 18, [-69], [0, "40KNxgmwJHnYdyQ9PolbFf", 1, 0], [5, 45, 45]], [5, 0, false, 39, [16]], [3, "New Label", 7, [[6, "游戏无法看广告", 30, false, 1, 1, -70, [17]]], [0, "8fBKKTL9JI7aPNwf7QRnIz", 1, 0], [4, 4284374622], [5, 210, 50.4], [0, 0, 0.5], [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, false, 7, 7, 40], [9, "Background", 512, 19, [[1, 0, -71, [18], 19]], [0, "6cB7W3J9pCdq69AbHY0V0V", 1, 0], [5, 45, 45], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "checkmark", 512, false, 19, [-72], [0, "cfDibUOn5MJ5ov2GSJvZWd", 1, 0], [5, 45, 45]], [5, 0, false, 44, [20]], [3, "New Label", 8, [[6, "玩法与广告不同", 30, false, 1, 1, -73, [21]]], [0, "67tjY2v4xKlqg5wL4wO0hl", 1, 0], [4, 4284374622], [5, 210, 50.4], [0, 0, 0.5], [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, false, 8, 8, 45], [8, "Label copy", 10, [[39, "提交", 32, false, false, 1, 1, 3, 1, -74, [23]]], [0, "ffhdDBvz1K0pxstgZ9AAFp", 1, 0], [5, 155, 50.4], [2.164, 1.716, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 32, 0, -2, 37, 0, -3, 42, 0, -4, 47, 0, 5, 27, 0, 0, 1, 0, -1, 11, 0, -2, 23, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 20, 0, -3, 22, 0, 0, 3, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -1, 27, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -1, 32, 0, -1, 16, 0, -2, 31, 0, -1, 37, 0, -1, 17, 0, -2, 36, 0, -1, 42, 0, -1, 18, 0, -2, 41, 0, -1, 47, 0, -1, 19, 0, -2, 46, 0, -1, 12, 0, 0, 10, 0, -1, 48, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 24, 0, 0, 13, 0, -1, 25, 0, 0, 14, 0, -1, 26, 0, 0, 15, 0, -1, 28, 0, -2, 29, 0, -1, 33, 0, -2, 34, 0, -1, 38, 0, -2, 39, 0, -1, 43, 0, -2, 44, 0, 6, 21, 0, 0, 20, 0, -1, 21, 0, 0, 21, 0, 0, 22, 0, 0, 28, 0, -1, 30, 0, 0, 31, 0, 0, 33, 0, -1, 35, 0, 0, 36, 0, 0, 38, 0, -1, 40, 0, 0, 41, 0, 0, 43, 0, -1, 45, 0, 0, 46, 0, 0, 48, 0, 7, 1, 2, 2, 23, 3, 2, 9, 4, 2, 9, 10, 2, 22, 74], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 30, 35, 40, 45], [-1, 1, -1, -1, -1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1], [0, 5, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 6, 0, 7, 8, 2, 2, 2, 2]]]]