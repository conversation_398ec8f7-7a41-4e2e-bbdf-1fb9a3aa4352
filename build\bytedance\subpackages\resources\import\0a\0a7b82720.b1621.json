[1, ["ecpdLyjvZBwrvm+cedCcQy", "b8mzpWSVNGlpHgsV0Ke5R/", "67qpQ4UQRJPpgLpkY7tGmN", "7a/QZLET9IDreTiBfRn2PD", "3cqZ2v/B9EjIGbbWDR6Maq", "f8npR6F8ZIZoCp3cKXjJQz", "c5MCld3zJOr6q+9g+OP5LF"], ["node", "_spriteFrame", "root", "data", "_N$skeletonData", "_N$file", "_textureSetter"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_position", "_scale", "_color"], 0, 9, 4, 5, 1, 2, 7, 8, 8, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["4dc22g311dAKpK9xqbJeXej", ["node"], 3, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Animation", ["playOnLoad", "_clips", "node"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6]], [[2, 0, 1, 2, 2], [0, 0, 6, 7, 3, 4, 5, 8, 2], [0, 0, 6, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4], [1, 3, 4, 1], [9, 0, 1, 2, 3], [4, 0, 2], [0, 0, 7, 3, 4, 5, 9, 10, 2], [0, 0, 6, 7, 3, 4, 5, 2], [0, 0, 2, 6, 3, 4, 11, 5, 3], [0, 0, 1, 6, 3, 4, 5, 3], [0, 0, 6, 3, 4, 5, 8, 2], [0, 0, 1, 6, 3, 4, 5, 8, 3], [5, 0, 1], [2, 1, 2, 1], [7, 0, 1], [1, 1, 0, 3, 4, 5, 3], [1, 0, 2, 3, 4, 3], [1, 3, 4, 5, 1], [8, 0, 1, 2, 3, 4, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 8], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 6]], [[[[6, "LoadingView"], [7, "LoadingView", [-3], [[13, -2]], [14, -1, 0], [5, 750, 1334], [1, 0, 0, 0], [1, 1, 1, 1]], [8, "gg_db", 1, [-6, -7, -8], [[3, 45, 750, 1334, -4], [15, -5]], [0, "22rQvGxnFNDJPUznP5U2DW", 1, 0], [5, 750, 1334]], [1, "load", 2, [-10, -11, -12, -13], [[4, -9, [10]]], [0, "deQxKroyVOg6UgRj8ouIQZ", 1, 0], [5, 135, 153], [0, 60.724, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "loadtext", 3, [-16], [[19, 1, 1, -4, -14, [5, 147.96, 25.2]], [5, true, [null], -15]], [0, "1fW2O0eFhGfqwHLKSJKb2Y", 1, 0], [5, 147.96, 25.2], [15.879, -157.402, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "gg_db", 101.235, 2, [[16, 1, 0, -17, [0], 1], [3, 45, 750, 1334, -18]], [0, "dcnkofTsRBt5ZV9Cfo65tT", 1, 0], [4, 4278190080], [5, 750, 1334]], [10, "icon_load", false, 3, [[17, 2, false, -19, [6]], [5, true, [null], -20]], [0, "57AXKyxSBHaJPrWhi36J7+", 1, 0], [5, 135, 153]], [2, "load", 4, [[20, "加载中...", 36, 36, false, 1, 1, 1, -21, [8], 9], [21, 2, -22, [4, 4278190080]]], [0, "6dnP7gGAxGCa0mTneoSI5U", 1, 0], [5, 147.96, 49.36]], [11, "td_swcz", 2, [[18, -23, [2], 3]], [0, "8cAD6/2bNAc7Os+xP846N1", 1, 0], [5, 172, 99], [0, 2.75, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "preload", 3, [[22, "default", "play", 0, false, "play", -24, [4], 5]], [0, "bathadLXlPl7wLfNpN57w9", 1, 0], [5, 300.10626220703125, 300.10626220703125]], [12, "<PERSON><PERSON><PERSON><PERSON>", false, 3, [[4, -25, [7]]], [0, "c8K9ZLnGhNL5+NR93OuU6I", 1, 0], [5, 82, 110], [-11.342, 2.835, 0, 0, 0, 0, 1, 0.5, 0.5, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 8, 0, -3, 3, 0, 0, 3, 0, -1, 9, 0, -2, 6, 0, -3, 10, 0, -4, 4, 0, 0, 4, 0, 0, 4, 0, -1, 7, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 3, 1, 25], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 4, -1, -1, -1, 5, -1], [0, 1, 0, 2, 3, 4, 0, 0, 0, 5, 0]], [[{"name": "scenebg", "rect": [0, 0, 750, 1334], "offset": [0, 0], "originalSize": [750, 1334], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [6], [6]]]]