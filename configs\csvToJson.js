import fs from 'fs';
import path from 'path';
import iconv from 'iconv-lite';

// 设置输入和输出目录
const csvDir = './csv';
const jsonDir = './json';

// 确保输出目录存在
if (!fs.existsSync(jsonDir)) {
    fs.mkdirSync(jsonDir, { recursive: true });
}

// 检测文件编码
function detectEncoding(buffer, fileName = '') {
    // 检查BOM
    if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
        return 'utf8';
    }

    // 取样本进行检测
    const sample = buffer.slice(0, Math.min(4000, buffer.length));

    // 检查是否有高位字节（非ASCII字符）
    let hasHighBytes = false;
    for (let i = 0; i < sample.length; i++) {
        if (sample[i] > 127) {
            hasHighBytes = true;
            break;
        }
    }

    // 如果没有高位字节，说明是纯ASCII，使用UTF-8
    if (!hasHighBytes) {
        return 'utf8';
    }

    // 尝试UTF-8解码
    let utf8Valid = true;
    let utf8ChineseCount = 0;
    try {
        const utf8Text = iconv.decode(sample, 'utf8');
        // 检查是否有替换字符（乱码标志）
        if (utf8Text.includes('�')) {
            utf8Valid = false;
        } else {
            // 计算中文字符数量
            utf8ChineseCount = (utf8Text.match(/[\u4e00-\u9fff]/g) || []).length;
        }
    } catch (e) {
        utf8Valid = false;
    }

    // 尝试GBK解码
    let gbkValid = true;
    let gbkChineseCount = 0;
    try {
        const gbkText = iconv.decode(sample, 'gbk');
        // 检查是否有替换字符
        if (gbkText.includes('�')) {
            gbkValid = false;
        } else {
            // 计算中文字符数量
            gbkChineseCount = (gbkText.match(/[\u4e00-\u9fff]/g) || []).length;
        }
    } catch (e) {
        gbkValid = false;
    }

    // 决策逻辑
    if (utf8Valid && !gbkValid) {
        return 'utf8';
    } else if (!utf8Valid && gbkValid) {
        return 'gbk';
    } else if (utf8Valid && gbkValid) {
        // 两种编码都有效，比较中文字符数量
        // 如果GBK解码出的中文字符明显更多，可能是GBK编码
        if (gbkChineseCount > utf8ChineseCount * 1.5) {
            return 'gbk';
        } else {
            return 'utf8';
        }
    } else {
        // 两种都无效，默认使用UTF-8
        return 'utf8';
    }
}

// 读取并转换文件内容
function readFileWithEncoding(filePath) {
    const buffer = fs.readFileSync(filePath);
    const fileName = path.basename(filePath);
    const encoding = detectEncoding(buffer, fileName);

    console.log(`Detected encoding for ${fileName}: ${encoding}`);

    let content;
    if (encoding === 'gbk') {
        content = iconv.decode(buffer, 'gbk');
    } else {
        content = iconv.decode(buffer, 'utf8');
    }

    // 统一换行符为 \n
    content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    return content;
}

// 获取所有CSV文件
function getCsvFiles() {
    try {
        const files = fs.readdirSync(csvDir);
        return files.filter(file => file.endsWith('.csv'));
    } catch (err) {
        console.error(`Error reading csv directory: ${err.message}`);
        return [];
    }
}

// 解析CSV行，处理引号和逗号
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    let i = 0;
    
    while (i < line.length) {
        const char = line[i];
        
        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                // 转义的引号
                current += '"';
                i += 2;
            } else {
                // 切换引号状态
                inQuotes = !inQuotes;
                i++;
            }
        } else if (char === ',' && !inQuotes) {
            // 字段分隔符
            result.push(current);
            current = '';
            i++;
        } else {
            current += char;
            i++;
        }
    }
    
    // 添加最后一个字段
    result.push(current);
    return result;
}

// 处理单个CSV文件
function processCsvFile(csvFile) {
    const csvPath = path.join(csvDir, csvFile);
    console.log(`Processing file: ${csvFile}`);

    try {
        // 读取CSV内容（自动检测编码）
        const csvContent = readFileWithEncoding(csvPath);
        
        // 更智能的行分割，考虑引号内的换行符
        const lines = [];
        let currentLine = '';
        let inQuotes = false;
        
        for (let i = 0; i < csvContent.length; i++) {
            const char = csvContent[i];
            
            if (char === '"') {
                if (inQuotes && csvContent[i + 1] === '"') {
                    // 转义的引号
                    currentLine += '""';
                    i++;
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                    currentLine += char;
                }
            } else if (char === '\n' && !inQuotes) {
                // 行结束（不在引号内）
                if (currentLine.trim() !== '') {
                    lines.push(currentLine);
                }
                currentLine = '';
            } else {
                currentLine += char;
            }
        }
        
        // 添加最后一行
        if (currentLine.trim() !== '') {
            lines.push(currentLine);
        }
        
        if (lines.length < 2) {
            console.log(`Warning: ${csvFile} does not contain enough data`);
            return;
        }
        
        // 解析标题行
        const headers = parseCSVLine(lines[0]);
        const idColumn = headers[0]; // 第一列是ID列
        const dataColumns = headers.slice(1); // 其余列是数据列
        
        // 创建JSON对象
        const jsonData = {};
        
        // 处理每一行数据
        for (let i = 1; i < lines.length; i++) {
            const values = parseCSVLine(lines[i]);
            
            if (values.length !== headers.length) {
                console.log(`Warning: Line ${i + 1} in ${csvFile} has incorrect number of columns`);
                continue;
            }
            
            const id = values[0];
            const dataObject = {};
            
            // 构建数据对象
            for (let j = 1; j < values.length; j++) {
                const columnName = dataColumns[j - 1];
                let value = values[j];
                
                // 处理空值
                if (value === '' || value === null || value === undefined) {
                    continue; // 跳过空值，不添加到对象中
                }
                
                // 尝试转换数值、布尔值和数组
                if (value.toLowerCase() === 'true') {
                    value = true;
                } else if (value.toLowerCase() === 'false') {
                    value = false;
                } else if (!isNaN(value) && !isNaN(parseFloat(value)) && value.trim() !== '') {
                    const numValue = parseFloat(value);
                    // 检查是否为整数
                    if (Number.isInteger(numValue)) {
                        value = parseInt(value);
                    } else {
                        value = numValue;
                    }
                } else if (value.startsWith('[') && value.endsWith(']')) {
                    // 解析JSON数组格式（统一的数组格式）
                    try {
                        const parsed = JSON.parse(value);
                        if (Array.isArray(parsed)) {
                            value = parsed;
                        }
                    } catch (e) {
                        // 保持原值
                    }
                }
                
                dataObject[columnName] = value;
            }
            
            // 只有当数据对象不为空时才添加到JSON中
            if (Object.keys(dataObject).length > 0) {
                jsonData[id] = dataObject;
            }
        }
        
        // 创建JSON文件名
        const jsonFileName = path.basename(csvFile, '.csv') + '.json';
        const jsonPath = path.join(jsonDir, jsonFileName);
        
        // 写入JSON文件（UTF-8编码，CRLF换行符）
        const jsonContent = JSON.stringify(jsonData, null, 2);
        // 将LF换行符转换为CRLF
        const jsonContentWithCRLF = jsonContent.replace(/\n/g, '\r\n');
        fs.writeFileSync(jsonPath, jsonContentWithCRLF, 'utf8');
        console.log(`Created JSON file: ${jsonFileName}`);
        
    } catch (err) {
        console.error(`Error: Failed to process ${csvFile}: ${err.message}`);
    }
}

// 主函数
function main() {
    const csvFiles = getCsvFiles();
    
    if (csvFiles.length === 0) {
        console.log('No CSV files found in the csv directory');
        return;
    }
    
    csvFiles.forEach(processCsvFile);
    console.log('All CSV files have been converted!');
}

// 执行主函数
main();