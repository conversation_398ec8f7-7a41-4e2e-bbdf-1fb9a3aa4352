[1, ["ecpdLyjvZBwrvm+cedCcQy", "baHW41841PF5Yr61qWY9O/", "70sgc9GipJubQaGYBcQf65", "a2MjXRFdtLlYQ5ouAFv/+R", "29R34cGbFB7YQJIiPE06Fl", "dbNkclouNG/oj/cNi+uAJ8", "c4DTFoEjBGzbnGaQxFEcFI", "8eLvGKIpNE9afrKCo2x9f9", "0a9Z6F9SZC7rVKOiuWsae7", "c1XJNDUv9JsZpUTnI2I4kb", "7a/QZLET9IDreTiBfRn2PD", "18nOvW6CdKz6pIMkEx7p7R", "3fO8rcb+9Fyr650dZJQa35", "93mzWMnFFFW5SAjJ7Xf9Ay", "92cxXlkzxFKY8E3C9W43lm", "f1oLpJwq1EEYtLpd1sEGaF"], ["node", "_spriteFrame", "root", "_textureSetter", "asset", "_parent", "nextRichText", "boxLayer", "gridView", "data", "templete"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint", "_color"], -1, 4, 9, 5, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_top", "_originalHeight", "_bottom", "_right", "_left", "alignMode", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents"], 1, 1, 9], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 2, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "node", "_materials"], -4, 1, 3], ["cc.Prefab", ["_name"], 2], ["36d05jcgrhBE7VbRd5iYmqM", ["node", "nodeArr", "labelArr", "gridView", "boxLayer", "nextRichText"], 3, 1, 2, 2, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "loop", "premultipliedAlpha", "_animationName", "_playTimes", "node", "_materials"], -4, 1, 3], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["key_count", "node", "space", "padding", "scrollview", "content"], 2, 1, 5, 5, 1, 1]], [[7, 0, 1, 2, 2], [0, 0, 7, 5, 4, 6, 9, 2], [2, 2, 3, 4, 1], [0, 0, 7, 8, 5, 4, 6, 9, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 7, 5, 4, 6, 2], [16, 0, 1, 2, 2], [0, 0, 7, 4, 2], [12, 0, 1, 2, 3, 3], [2, 0, 2, 3, 4, 2], [2, 2, 3, 1], [9, 0, 1, 6, 2, 3, 4, 5, 7, 8, 8], [0, 0, 7, 8, 5, 4, 6, 10, 9, 2], [0, 0, 2, 7, 8, 5, 4, 6, 9, 3], [1, 0, 1, 3, 8, 4], [1, 0, 8, 2], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4, 5, 7, 8, 7], [10, 0, 2], [0, 0, 1, 8, 5, 4, 6, 3], [0, 0, 8, 5, 4, 6, 10, 9, 2], [0, 0, 8, 5, 4, 11, 6, 9, 2], [0, 0, 7, 8, 5, 4, 6, 2], [0, 0, 7, 5, 4, 6, 10, 9, 2], [0, 0, 3, 7, 5, 4, 11, 6, 3], [5, 0, 1, 6, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 7, 5, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [6, 0, 1, 2, 3, 4, 5, 7, 6, 2], [11, 0, 1, 2, 3, 4, 5, 1], [7, 1, 2, 1], [1, 0, 2, 8, 3], [1, 0, 6, 5, 2, 1, 3, 8, 7], [1, 0, 1, 8, 3], [1, 0, 2, 4, 1, 3, 8, 6], [1, 7, 0, 5, 4, 1, 3, 8, 7], [1, 0, 2, 4, 1, 8, 5], [13, 0, 1, 2, 3, 4, 4], [14, 0, 1, 2], [4, 1, 0, 2, 3, 3], [4, 2, 3, 1], [4, 0, 2, 3, 2], [8, 0, 1, 3], [15, 0, 1, 2, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [18, 0, 1, 2, 3, 4, 5], [19, 0, 1, 2, 3, 4, 5, 6, 6], [20, 0, 1, 2, 3, 4, 5, 2]], [[[{"name": "img_qipao", "rect": [0, 0, 155, 87], "offset": [0, 0], "originalSize": [155, 87], "capInsets": [25, 25, 96, 39]}], [3], 0, [0], [3], [6]], [[{"name": "img_fgx", "rect": [0, 0, 16, 3], "offset": [0, 0], "originalSize": [16, 3], "capInsets": [4, 0, 4, 0]}], [3], 0, [0], [3], [7]], [[{"name": "hcbj", "rect": [0, 0, 1125, 540], "offset": [0, 0], "originalSize": [1125, 540], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [8]], [[[18, "Role_EquipForge"], [19, "Role_EquipForge", 1, [-10, -11], [[29, -9, [-7, -8], [-5, -6], -4, -3, -2]], [30, -1, 0], [5, 750, 1334]], [20, "top", [-13, -14, -15, -16, -17, -18], [[31, 1, 135.5, -12]], [0, "d9Xg1OZkxMaIK6jD9wq//8", 1, 0], [5, 750, 402], [0, 0.5, 1], [0, 531.5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "layer", 2, [-20, -21, -22], [[37, 1, 1, 10, -19, [5, 303, 122]]], [0, "84pqNh3GxGNKWQlo9eEjks", 1, 0], [5, 303, 122], [0, 0, 0.5], [-319.415, -263.834, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [13, "img_qipao", false, 2, [-25, -26, -27], [[4, 1, 0, -23, [24], 25], [38, 2, -24]], [0, "eaxUxcghBJbLDPnEtTI70w", 1, 0], [5, 457, 194], [112.73, -9.018, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "bottom", [-30, -31, -32], [[9, 0, -28, [41], 42], [32, 5, 325, 325, 540, 100, 100, -29]], [0, "22HLaDIO5NeKI+vrZ8/+Ki", 1, 0], [4, 4280821800], [5, 750, 794], [0, -270, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_zhuangbei_sz", 5, [-35, -36, -37], [[4, 1, 0, -33, [34], 35], [33, 41, 20, -34]], [0, "c9JCvibUdLpoVQsAQKLf+y", 1, 0], [5, 750, 88], [0, 353, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bg", 1, [-39, 2, 5], [[14, 45, 750, 1334, -38]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [3, "box", 2, [-41, -42], [[2, -40, [19], 20]], [0, "a3+HM8dRdDKLD9Xi82cRmt", 1, 0], [5, 122, 122], [-267.221, -76.071, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btn", 6, [-45], [[4, 1, 0, -43, [29], 30], [39, 0.9, 3, -44, [[16, "36d05jcgrhBE7VbRd5iYmqM", "onCkickCompound", 1]]]], [0, "62UjXW4/hAAZvHAkkJXjoc", 1, 0], [5, 160, 67], [277.729, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [25, "ScrollView", 5, [-49], [[-46, [34, 45, 88, 86, 240, 250, -47], -48], 1, 4, 1], [0, "15MHpYYEtBf6lG3HK+xoxx", 1, 0], [5, 750, 620], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "box", 3, [-51, -52], [[2, -50, [7], 8]], [0, "c4cLGDrE5KjZjhgtMGGoCH", 1, 0], [5, 122, 122], [61, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "box", 3, [-54, -55], [[2, -53, [13], 14]], [0, "9bdxDCJLJK0YNQbwHcCDFW", 1, 0], [5, 122, 122], [242, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "btn", false, 6, [-58], [[4, 1, 0, -56, [32], 33], [40, -57, [[42, "1e3f6XATUJJwIwQyyhwvhJu", "resetSortType"]]]], [0, "99NChxyJdMzYy8A2xnOY3C", 1, 0], [5, 160, 67], [-276.641, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "view", 10, [-61], [[43, 0, -59, [36]], [14, 45, 240, 250, -60]], [0, "a5ytvCvYBNQ7uWRhUt9U0q", 1, 0], [5, 750, 620], [0, 0.5, 1], [0, 310, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "content", 14, [[35, 0, 9, 510, 120, 240, 250, -62]], [0, "09qVo8x8BKl6Ktsi8YKzbx", 1, 0], [5, 750, 250], [0, 0, 1], [-375, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_zhuangbei_sz", 5, [-65], [[4, 1, 0, -63, [39], 40], [36, 44, 714.12, -10.754999999999995, 20, -64]], [0, "b4Rg89sXRFBborwCJZljWC", 1, 0], [5, 750, 100], [0, -357.755, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "maskbg", 140, 1, [[15, 45, -66], [9, 0, -67, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "img", 7, [[2, -68, [2], 3], [15, 1, -69]], [0, "27eaVsH1dLa6JrrF21EYPD", 1, 0], [5, 1125, 540], [0, 397, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "msg", 2, [[-70, [6, 4, -71, [4, 4278190080]]], 1, 4], [0, "c7DyZPxkZAZZ6z4LFRXPWo", 1, 0], [5, 112, 71], [0, 0, 0.5], [-320.82, -351.611, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Label", 6, [[11, "我的装备", 32, 50, false, false, 1, 1, -72, [27]], [6, 4, -73, [4, 4278190080]]], [0, "2bZUsK22ZIspKxYZCiRIzF", 1, 0], [5, 136, 71]], [1, "txt", 9, [[17, "合成", 28, false, false, 1, 1, -74, [28]], [6, 4, -75, [4, 4278190080]]], [0, "e2A0MDpBdDibWOd3Ey6WtX", 1, 0], [5, 64, 58.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "txt", 13, [[17, "按品质排序", 24, false, false, 1, 1, -76, [31]], [6, 4, -77, [4, 4278190080]]], [0, "9aRUhPSadOApkb6YqzkSCJ", 1, 0], [5, 128, 58.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button_return", 16, [[2, -78, [37], 38], [41, 3, -79, [[16, "36d05jcgrhBE7VbRd5iYmqM", "close", 1]]]], [0, "16SJN4VRZMh7B/xxexW5CN", 1, 0], [5, 66, 69], [-322.549, 3.946, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "chuizi", 2, [[44, "default", "animation", 0, false, false, "animation", 1, -80, [4]]], [0, "94vISAcY5K7IPP83pIiF6x", 1, 0], [5, 297, 279.**************], [227.927, -404.126, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 11, [[10, -81, [5]]], [0, "314r4aNHJLFIcDkQMkQIXT", 1, 0], [5, 66, 65]], [7, "RoleEquipItem", 11, [8, "fdPc9ufyNPK4tFszjqpG7z", true, -82, 6]], [1, "icon_jiahao", 3, [[2, -83, [9], 10]], [0, "f4lcS7MeVO6rhEVEmf8mVq", 1, 0], [5, 39, 39], [151.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 12, [[10, -84, [11]]], [0, "a3JIxtbm5JZK+/WU0l/sK8", 1, 0], [5, 66, 65]], [7, "RoleEquipItem", 12, [8, "fdPc9ufyNPK4tFszjqpG7z", true, -85, 12]], [1, "icon_jts", 2, [[2, -86, [15], 16]], [0, "543NSMQERFXL4Aol+qtc3N", 1, 0], [5, 46, 51], [-264.25, -176.167, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 8, [[10, -87, [17]]], [0, "12I38M+/FCsIoA3+r9hA53", 1, 0], [5, 66, 65]], [7, "RoleEquipItem", 8, [8, "fdPc9ufyNPK4tFszjqpG7z", true, -88, 18]], [1, "img_fgx", 4, [[9, 0, -89, [21], 22]], [0, "93oB0qh/lP8pHoMuyc1wzp", 1, 0], [5, 400, 3], [0, 48.087, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "Label", 4, [-90], [0, "c0pT1DMclIcoXtyIgJr3HB", 1, 0], [4, 4278190335], [5, 112, 63], [0, 68.609, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "我的装备", 28, 50, false, false, 1, 1, 34, [23]], [28, "msg", 4, [-91], [0, "795+9r2K1FjpMej0owQa+O", 1, 0], [4, 4281940021], [5, 256.51, 110.83999999999999], [0, 0, 1], [-200.458, 43.91, 0, 0, 0, 0, 1, 1, 1, 1]], [45, false, "攻击率                 +35%\n攻击率                 +35%\n装备技能升至2级", 24, 34, 36], [11, "材料需求", 26, 50, false, false, 1, 1, 19, [26]], [46, false, 0.75, 0.23, null, null, 10, 15], [47, 5, 10, [0, 20, 20], [0, 20, 20], 39, 15]], 0, [0, 2, 1, 0, 6, 37, 0, 7, 3, 0, 8, 40, 0, -1, 35, 0, -2, 38, 0, -1, 8, 0, -2, 9, 0, 0, 1, 0, -1, 17, 0, -2, 7, 0, 0, 2, 0, -1, 24, 0, -2, 3, 0, -3, 30, 0, -4, 8, 0, -5, 4, 0, -6, 19, 0, 0, 3, 0, -1, 11, 0, -2, 27, 0, -3, 12, 0, 0, 4, 0, 0, 4, 0, -1, 33, 0, -2, 34, 0, -3, 36, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, -2, 10, 0, -3, 16, 0, 0, 6, 0, 0, 6, 0, -1, 20, 0, -2, 9, 0, -3, 13, 0, 0, 7, 0, -1, 18, 0, 0, 8, 0, -1, 31, 0, -2, 32, 0, 0, 9, 0, 0, 9, 0, -1, 21, 0, -1, 39, 0, 0, 10, 0, -3, 40, 0, -1, 14, 0, 0, 11, 0, -1, 25, 0, -2, 26, 0, 0, 12, 0, -1, 28, 0, -2, 29, 0, 0, 13, 0, 0, 13, 0, -1, 22, 0, 0, 14, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 23, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, -1, 38, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 2, 26, 0, 0, 27, 0, 0, 28, 0, 2, 29, 0, 0, 30, 0, 0, 31, 0, 2, 32, 0, 0, 33, 0, -1, 35, 0, -1, 37, 0, 9, 1, 2, 5, 7, 5, 5, 7, 91], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40], [-1, 1, -1, 1, -1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, 10], [0, 3, 0, 9, 10, 0, 1, 0, 2, 0, 11, 0, 1, 0, 2, 0, 12, 0, 1, 0, 2, 0, 13, 0, 0, 14, 0, 0, 0, 0, 4, 0, 0, 4, 0, 5, 0, 0, 15, 0, 5, 0, 3, 1]]]]