[1, ["ecpdLyjvZBwrvm+cedCcQy", "22cjIamqZH/Lbdvp80pFLv", "18fU8MiRhAvacPMzvkRWSp", "95l8LoKk9Bw79Ixjc0Lhel", "f3p84uzd5EVLXvLuhlyoRY", "3ae7efMv1CLq2ilvUY/tQi"], ["node", "_spriteFrame", "root", "icon", "lbl_lv", "lbl_name", "data", "_parent"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent", "_anchorPoint", "_color"], 1, 9, 4, 5, 7, 2, 1, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_enableWrapText", "_N$cacheMode", "_styleFlags", "node", "_materials"], -6, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 12, 4, 5, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "node"], -2, 1], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["65c107D+EdDHr71wNqPczVr", ["node", "lbl_name", "lbl_lv", "icon"], 3, 1, 1, 1, 1]], [[4, 0, 1, 2, 2], [0, 0, 7, 2, 3, 4, 5, 2], [7, 0, 1, 2, 2], [1, 1, 0, 2, 3, 3], [2, 0, 1, 2, 3, 5, 9, 10, 6], [9, 0, 1, 2, 3], [0, 0, 1, 7, 6, 2, 3, 4, 5, 3], [1, 1, 0, 2, 3, 4, 3], [1, 2, 3, 4, 1], [2, 0, 1, 6, 2, 4, 3, 7, 9, 10, 8], [8, 0, 1, 2, 3, 3], [5, 0, 2], [0, 0, 6, 2, 3, 4, 5, 2], [0, 0, 7, 2, 3, 9, 4, 8, 5, 2], [0, 0, 7, 6, 2, 3, 4, 8, 5, 2], [0, 0, 7, 6, 2, 3, 4, 5, 2], [0, 0, 1, 6, 2, 3, 4, 5, 3], [3, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 6, 4, 7, 5, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 2, 3, 2], [4, 1, 2, 1], [2, 0, 1, 2, 8, 4, 3, 9, 10, 7], [2, 0, 1, 6, 2, 4, 3, 5, 7, 9, 10, 9], [10, 0, 1], [11, 0, 1, 2, 3, 4, 5, 6], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 4], [14, 0, 1, 2, 3, 1]], [[11, "MoreGamesMiniItem"], [12, "MoreGamesMiniItem", [-6, -7, -8, -9, -10, -11, -12, -13], [[28, -5, -4, -3, -2]], [21, -1, 0], [5, 630, 180], [0, -140, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "video", false, [-16, -17, -18], [[3, 1, 0, -14, [16]], [26, "ModeUnlock", -15, [[5, "a5defhqfHlNCq/7Qbl+BNL0", "onVideoUnlock", 1]]]], [0, "0fFe0rYDhDiauCGhAkC0v4", 1, 0], [5, 211, 112], [356.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "Layout", 1, [-20, 2, -21], [[27, 1, 1, 40, -19, [5, 211, 150]]], [0, "11iRE1vANJDI0BljuvSlzl", 1, 0], [5, 211, 150], [0, 0, 0.5], [87.426, -8.881, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "openGame", 3, [-24], [[3, 1, 0, -22, [12]], [10, 0.9, 3, -23, [[5, "65c107D+EdDHr71wNqPczVr", "onClickOpenGame", 1]]]], [0, "dekfj+g5pPJ7GCDcJ1y9aG", 1, 0], [5, 211, 112], [105.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "notUnlock", false, 3, [-27], [[3, 1, 0, -25, [18]], [10, 0.9, 3, -26, [[5, "a5defhqfHlNCq/7Qbl+BNL0", "onClickNotUnlock", 1]]]], [0, "8dvxJAbx1NjJ6wMSDTuvBJ", 1, 0], [5, 211, 112], [356.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "title", 1, [[-28, [2, 3, -29, [4, 4278190080]]], 1, 4], [0, "95UKSMyRdN4KQ/DsefUpVm", 1, 0], [5, 279, 56.4], [0, 0, 0.5], [-157.795, 34.173, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "zbTips", false, 1, [-31], [[7, 1, 0, -30, [9], 10]], [0, "bfSadlDVRBQ6W3H9W5pinp", 1, 0], [5, 120, 44], [-277.111, 64.106, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 7, [[22, "主播同款", 22, false, 1, 1, 1, -32, [8]], [2, 3, -33, [4, 4278190277]]], [0, "a3MIy6dotD44SCSEFPRjQe", 1, 0], [5, 94, 56.4], [-2.578, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[9, "开始", 32, false, false, 1, 1, 1, -34, [11]], [2, 3, -35, [4, 4278190080]]], [0, "09jRzZWP9LV6zHOdhjmgrA", 1, 0], [5, 70, 56.4], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 2, [[23, "提前解锁", 28, false, false, 1, 1, 2, 1, -36, [13]], [2, 3, -37, [4, 4278190080]]], [0, "78LxUPWApAT5u9dHv7XZjA", 1, 0], [5, 186, 56.4], [0, -16.553, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 2, [[8, -38, [14], 15], [24, -39]], [0, "bbxt6fM9RPKos7OQygDWbY", 1, 0], [5, 54, 43], [-59.389, 25.431, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 5, [[9, "未解锁", 32, false, false, 1, 1, 1, -40, [17]], [2, 3, -41, [4, 4278190080]]], [0, "276DdNw9VCdJWVNQrtpVYP", 1, 0], [5, 102, 56.4], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 1, [[7, 1, 0, -42, [0], 1]], [0, "3djLRR99VOZoFW/RkqTpsR", 1, 0], [5, 630, 160], [0, -6.981, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tagbg", 1, [[8, -43, [2], 3]], [0, "8025YC8oRDUKQtAE7Q8b8T", 1, 0], [5, 375, 48], [-90.54, 33.077, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "icon", 1, [-44], [0, "2d0ABMc1VOiKgD1rEitrYn", 1, 0], [5, 116, 116], [-232.324, -6.981, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 2, 15, [4]], [4, "更多玩法", 30, false, 1, 2, 6, [5]], [18, "level_id", 1, [-45], [0, "04RrzlUgFCJ4MoXc1xi3eW", 1, 0], [4, 4281940021], [5, 163.23000000000002, 50.4], [0, 0, 0.5], [-157.795, -11.956, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "第1关", 27, false, 1, 2, 18, [6]], [13, "lockTips", 1, [[4, "通关第2章解锁", 26, false, 1, 2, -46, [7]]], [0, "42B9cMzDlOJ4grdzQaiuy+", 1, 0], [4, 4281940021], [5, 283.27, 50.4], [0, 0, 0.5], [-157.795, -46.254, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 2, [[25, false, "<outline color=black width=4><color=#ffe064>1</c>/ 5</outline>", 1, 28, 100, -47]], [0, "b7pj7KZ4xLHoFpLXlKE6Ee", 1, 0], [5, 100, 50.4], [28.691, 27.997, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 3, 16, 0, 4, 19, 0, 5, 17, 0, 0, 1, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 6, 0, -5, 18, 0, -6, 20, 0, -7, 7, 0, -8, 3, 0, 0, 2, 0, 0, 2, 0, -1, 10, 0, -2, 11, 0, -3, 21, 0, 0, 3, 0, -1, 4, 0, -3, 5, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, -1, 17, 0, 0, 6, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, -1, 16, 0, -1, 19, 0, 0, 20, 0, 0, 21, 0, 6, 1, 2, 7, 3, 47], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1], [0, 1, 0, 2, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 4, 0, 0, 5]]