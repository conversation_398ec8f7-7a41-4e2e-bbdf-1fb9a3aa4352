"use strict";
cc._RF.push(module, '5b59eOsFG5FwJQQDX+d+/Zn', 'Launcher');
// start/scripts/Launcher.js

"use strict";

var i;
var $callID = require("./CallID");
var $cfg = require("./Cfg");
var $mVC = require("./MVC");
var $notifier = require("./Notifier");
var $listenID = require("./ListenID");
var $manager = require("./Manager");
var $time = require("./Time");
var $uIManager = require("./UIManager");
var $alertManager = require("./AlertManager");
var $eventController = require("./EventController");
var $game = require("./Game");
var $sdkConfig = require("./SdkConfig");
var $wonderSdk = require("./WonderSdk");
var $gameUtil = require("./GameUtil");
var $moduleLauncher = require("./ModuleLauncher");
var $sdkLauncher = require("./SdkLauncher");
var $uILauncher = require("./UILauncher");
var k = cc._decorator;
var O = k.ccclass;
var I = k.property;
var A = function (e) {
  function t() {
    var t;
    var o = null !== e && e.apply(this, arguments) || this;
    o.testMode = !1;
    o.CustomPlatform = $sdkConfig.EPlatform.WEB_DEV;
    o.bmsVersion = "";
    o.progress = null;
    o.progressText = null;
    o.wonderlogo = null;
    o.gamelogo = null;
    o.logos = [];
    o.scenebg = null;
    o.softRightText = null;
    o.softICPText = null;
    o.logomap = ((t = {}).tc = 0, t.en = 1, t.th = 2, t.vn = 3, t.cn = 4, t);
    o._saveOffset = 0;
    return o;
  }
  __extends(t, e);
  t.prototype.onLoad = function () {
    var e = this;
    console.log("[Launcher][onLoad]");
    cc._gameManager = $manager.Manager;
    cc.game.addPersistRootNode(this.node);
    cc.macro.ENABLE_MULTI_TOUCH = !1;
    if (cc.sys.isBrowser) {
      cc.view.enableAutoFullScreen(!1);
      this.scheduleOnce(function () {
        e.fit();
      });
    }
    cc.sys.hasFont = !1;
    $notifier.Notifier.send($listenID.ListenID.Event_SendEvent, "View", {
      Type: "show",
      Scene: "loading"
    });
    $manager.Manager.setPhysics(!0);
    $manager.Manager.setPhysics(!1);
  };
  t.prototype.fit = function () {
    var e = cc.view.getVisibleSize();
    var t = e.width / e.height;
    var o = Math.round(100 * t);
    if (o > 57) {
      if (o >= 100) {
        cc.Canvas.instance.fitHeight = !0, cc.Canvas.instance.fitWidth = !0;
      } else {
        cc.Canvas.instance.fitHeight = !0, cc.Canvas.instance.fitWidth = !1;
      }
    }
    cc.debug.setDisplayStats(!1);
  };
  t.prototype.onEnable = function () {
    this.changeListener(!0);
  };
  t.prototype.onDisable = function () {
    this.changeListener(!1);
  };
  t.prototype.changeListener = function (e) {
    $notifier.Notifier.changeListener(e, $listenID.ListenID.Login_Finish, this.onLogin_Finish, this, $notifier.PriorLowest);
    $notifier.Notifier.changeListener(e, $listenID.ListenID.Game_Load, this.onOpenGame, this, -200);
    $notifier.Notifier.changeListener(e, $listenID.ListenID.Fight_BackToMain, this.backToMain, this, 200);
  };
  t.prototype.lateUpdate = function () {};
  t.prototype.onLogin_Finish = function () {
    this.wonderlogo.parent.destroy();
    this.gameStart();
  };
  t.prototype.onOpenGame = function () {
    this.scenebg.setActive(!1);
  };
  t.prototype.backToMain = function () {
    this.scenebg.setActive(!0);
  };
  t.prototype.responsive = function () {
    var e = cc.view.getDesignResolutionSize();
    var t = cc.view.getFrameSize();
    var o = function o() {
      cc.Canvas.instance.fitHeight = !0;
      cc.Canvas.instance.fitWidth = !0;
    };
    var i = e.width / e.height;
    var n = t.width / t.height;
    if (i < 1) {
      if (n < 1) {
        if (n > i) {
          o();
        } else {
          cc.Canvas.instance.fitHeight = !1, cc.Canvas.instance.fitWidth = !0;
        }
      } else {
        o();
      }
    } else {
      if (n > 1) {
        if (n < i) {
          o();
        } else {
          cc.Canvas.instance.fitHeight = !0, cc.Canvas.instance.fitWidth = !1;
        }
      } else {
        o();
      }
    }
  };
  t.prototype.start = function () {
    var e;
    return __awaiter(this, void 0, void 0, function () {
      return __generator(this, function (t) {
        switch (t.label) {
          case 0:
            this.initWonderFrameWork();
            if (wonderSdk.isNative) {
              //
            } else {
              new $eventController.EventController();
            }
            console.log("[Launcher][BMS_APP_NAME]", wonderSdk.BMS_APP_NAME);
            console.log("[Launcher][BMS_VERSION]", wonderSdk.BMS_VERSION);
            this.checkPlatformInfo();
            return [4, this.loadConfig()];
          case 1:
            t.sent();
            window.tpdg = $cfg.Cfg.language.getAll();
            window.initlang("tpdg");
            this.initLanguageInfo();
            this.gamelogo.spriteFrame = this.logos[null !== (e = this.logomap[cc.sys.language]) && void 0 !== e ? e : 4];
            if (wonderSdk.isIOS) {
              this.wonderlogo.setActive(!1);
            }
            new $uILauncher.UILauncher();
            this.progressText.string = cc.js.formatStr("%d%", 0);
            this.progress.progress = 0;
            new $moduleLauncher.ModuleLauncher();
            new $sdkLauncher.SdkLauncher(this.progressText, this.progress);
            return [2];
        }
      });
    });
  };
  t.prototype.checkPlatformInfo = function () {
    if (wonderSdk.isNative) {
      this.wonderlogo.active = !1;
    }
    this.softRightText.string = $sdkConfig.SoftRightHodler[this.CustomPlatform];
    this.softICPText.string = $sdkConfig.SoftICP[this.CustomPlatform];
  };
  t.prototype.update = function (e) {
    $time.Time.update(e);
    $uIManager.UIManager.update(e);
  };
  t.prototype.initWonderFrameWork = function () {
    $wonderSdk.WonderSdk.init(this.CustomPlatform, this.testMode);
    if (this.bmsVersion.length > 0) {
      $sdkConfig.BMSInfoList[this.CustomPlatform].BMS_VERSION = this.bmsVersion;
      console.log("已修改Bms版本号");
    }
  };
  t.prototype.loadConfig = function () {
    var e = [];
    var t = wonderSdk.isNative;
    for (var o in $cfg.Cfg.keyJson) if (t || "language" != o) {
      e.push($cfg.Cfg.initByBaseConfig(o, this.progressText, this.progress));
      // e.push($cfg.Cfg.initLocalJson(o, this.progressText, this.progress));
    }

    return Promise.all(e);
  };
  t.prototype.initLanguageInfo = function () {
    var e = this.initLangCode();
    $gameUtil.CCTool.Language.init(e, function () {
      console.log("[languageFun][init]语言包初始化完成", e);
    });
  };
  t.prototype.initLangCode = function () {
    var e = cc.sys.LANGUAGE_ENGLISH;
    try {
      var t = cc.sys.language;
      var o = cc.sys.languageCode;
      cc.log("[lType]", t, o);
      if ("zh" === t) {
        if (-1 != o.indexOf("hant") || -1 != o.indexOf("tw") || -1 != o.indexOf("hk") || -1 != o.indexOf("mo")) {
          e = "tc", cc.sys.hasFont = !1;
        } else {
          e = "zh";
        }
      } else {
        if ("ja" == t) {
          e = "jp", cc.sys.hasFont = !1;
        } else {
          if ("ko" == t) {
            e = "kr", cc.sys.hasFont = !1;
          } else {
            if (-1 != o.indexOf("vi") || -1 != o.indexOf("vn")) {
              e = "vn", cc.sys.hasFont = !1;
            } else {
              e = -1 != o.indexOf("th") || -1 != t.indexOf("th") ? "en" : -1 != o.indexOf("id") || -1 != o.indexOf("in") ? "ina" : "en";
            }
          }
        }
      }
      console.log("[Language] --> 初始化语言:  lan: " + e + " systype: " + t + " syscode: " + cc.sys.languageCode);
      return e;
    } catch (e) {}
  };
  t.prototype.gameStart = function () {
    var e;
    console.log("[进入游戏 gameStart] 1.00", this.progress.progress);
    $manager.Manager.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);
    Object.defineProperty(cc.game, "collisionMatrix", JSON.parse($manager.Manager.oldGroupMatrix));
    if (!this.testMode && wonderSdk.isLive) {
      if ($manager.Manager.vo.userVo.code) {
        wonderSdk.requestLoginCode($manager.Manager.vo.userVo.code).then(function () {
          $alertManager.AlertManager.showNormalTips("验证成功");
        })["catch"](function () {
          // $manager.Manager.vo.userVo.code = null;
          // $manager.Manager.vo.userVo.ca_code = null;
          $alertManager.AlertManager.showNormalTips("验证码过期");
          $uIManager.UIManager.Open("ui/setting/H5CodeView");
          $manager.Manager.vo.saveUserData();
        });
      } else {
        $uIManager.UIManager.Open("ui/setting/H5CodeView");
      }
    }
    var t = null;
    if (null === (e = $notifier.Notifier.call($callID.CallID.Platform_Query)) || void 0 === e) {
      t = void 0;
    } else {
      t = e.mode;
    }
    if (t) {
      $notifier.Notifier.send($listenID.ListenID.Is_Back_From_Try_Play);
      var o = $cfg.Cfg.MiniGameLv.get(t);
      var i = $game.Game.getMouth(o.type);
      $notifier.Notifier.send(i.mouth, o.type, $mVC.MVC.openArgs().setParam({
        id: o.id,
        isTryPaly: !0
      }));
    } else {
      $notifier.Notifier.send($listenID.ListenID.BottomBar_OpenView, 1);
    }
    if (wonderSdk.isByteDance) {
      var n = $notifier.Notifier.call($callID.CallID.Platform_CdKey);
      $notifier.Notifier.send($listenID.ListenID.ByteDance_Check_Gift, n, !0);
    } else {
      if (wonderSdk.isBLMicro) {
        $notifier.Notifier.send($listenID.ListenID.Platform_CheckScene, $notifier.Notifier.call($callID.CallID.Platform_GetScene));
      }
    }
    $notifier.Notifier.send($listenID.ListenID.Event_SendEvent, "View", {
      Type: "hide",
      Scene: "loading"
    });
  };
  t.isBreak = !1;
  __decorate([I({
    displayName: "测试模式"
  })], t.prototype, "testMode", void 0);
  __decorate([I({
    type: $sdkConfig.EPlatform,
    displayName: "自定义平台"
  })], t.prototype, "CustomPlatform", void 0);
  __decorate([I({
    displayName: "BMS版本号"
  })], t.prototype, "bmsVersion", void 0);
  __decorate([I(cc.ProgressBar)], t.prototype, "progress", void 0);
  __decorate([I(cc.Label)], t.prototype, "progressText", void 0);
  __decorate([I(cc.Node)], t.prototype, "wonderlogo", void 0);
  __decorate([I(cc.Sprite)], t.prototype, "gamelogo", void 0);
  __decorate([I([cc.SpriteFrame])], t.prototype, "logos", void 0);
  __decorate([I(cc.Node)], t.prototype, "scenebg", void 0);
  __decorate([I(cc.Label)], t.prototype, "softRightText", void 0);
  __decorate([I(cc.Label)], t.prototype, "softICPText", void 0);
  return __decorate([O], t);
}(cc.Component);
exports["default"] = A;

cc._RF.pop();