[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "3ae55zevNLro44lk9vad+V", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "f6NcoTBPNJ4qSyD40PNpRP", "ddFhwuPitMK6rcBkC3ddyJ", "e3q84hOUtFEo1aiRNVD8I7", "65OUoZ25pGHqftEDT5VTS4"], ["node", "_spriteFrame", "_parent", "_N$disabledSprite", "root", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_groupIndex", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -2, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_N$overflow", "_string", "_fontSize", "_N$horizontalAlign", "_styleFlags", "_enableWrapText", "_N$cacheMode", "_lineHeight", "node", "_materials"], -7, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "_enabled", "node", "_color"], 1, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["ebe0chtY9FE6oEftFfPqcZ8", ["node", "nodeArr", "labelArr"], 3, 1, 2, 2]], [[5, 0, 1, 2, 2], [0, 0, 8, 10, 7, 5, 6, 9, 2], [0, 0, 8, 7, 5, 6, 9, 2], [1, 1, 0, 2, 3, 4, 3], [6, 0, 2, 3, 2], [6, 1, 0, 2, 3, 3], [0, 0, 4, 10, 7, 5, 6, 9, 3], [9, 0, 1], [2, 3, 4, 0, 6, 1, 2, 10, 11, 7], [10, 0, 1, 2, 3, 4, 5, 6, 2], [0, 0, 1, 8, 7, 5, 11, 6, 12, 9, 3], [3, 3, 0, 1, 2, 8, 5], [1, 1, 0, 2, 3, 3], [2, 3, 4, 7, 0, 6, 5, 1, 2, 8, 10, 11, 10], [7, 0, 1, 2, 3, 4], [0, 0, 2, 8, 10, 7, 5, 6, 9, 3], [1, 0, 2, 3, 4, 2], [8, 0, 2], [0, 0, 3, 10, 7, 5, 6, 3], [0, 0, 1, 8, 7, 5, 11, 6, 3], [0, 0, 8, 10, 7, 5, 6, 2], [0, 0, 10, 5, 6, 2], [0, 0, 8, 7, 5, 6, 12, 9, 2], [0, 0, 8, 10, 7, 5, 6, 12, 9, 2], [0, 0, 2, 8, 7, 5, 11, 6, 9, 3], [4, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 6, 4, 5, 2], [4, 0, 1, 2, 3, 6, 4, 7, 5, 2], [3, 0, 8, 2], [3, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [3, 0, 1, 2, 8, 4], [1, 2, 3, 1], [1, 2, 3, 4, 1], [1, 1, 2, 3, 4, 2], [5, 1, 2, 1], [2, 3, 0, 5, 1, 2, 10, 11, 6], [2, 4, 9, 7, 0, 6, 1, 2, 10, 11, 8], [2, 3, 4, 0, 1, 2, 10, 11, 6], [2, 3, 7, 0, 5, 1, 2, 8, 10, 11, 8], [7, 0, 1, 3, 3], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1, 2, 1]], [[17, "M20_Pop_Insufficient_Props_Tips"], [18, "M20_Pop_Insufficient_Props_Tips", 1, [-14, -15], [[41, -13, [-5, -6, -7, -8, -9, -10, -11, -12], [-2, -3, -4]]], [34, -1, 0], [5, 750, 1334]], [21, "content", [-16, -17, -18, -19, -20, -21, -22, -23], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 574, 800]], [23, "list", 2, [-25, -26, -27, -28], [[40, 1, 2, 10, -24, [5, 560, 120]]], [0, "e0skCoiT5NcZeoThdOrOrM", 1, 0], [5, 560, 120], [0, 0.5, 1], [0, 49.944, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "item_shop", 3, [-31, -32], [[3, 1, 0, -29, [19], 20], [7, -30]], [0, "f2XpTBdlNPypM+Vn/AW+zK", 1, 0], [5, 544, 120], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "item_sweep", false, 3, [-35, -36], [[3, 1, 0, -33, [25], 26], [7, -34]], [0, "1fm7PlJD5CjI1fYOgvrshB", 1, 0], [5, 544, 120], [0, -190, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "item_coin_challenge", false, 3, [-39, -40], [[3, 1, 0, -37, [31], 32], [7, -38]], [0, "941ZPDmfVDEYjxSXoKAkJX", 1, 0], [5, 544, 120], [0, -320, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "item_challenge", false, 3, [-43, -44], [[3, 1, 0, -41, [37], 38], [7, -42]], [0, "f2F7hGw91KVbGJ9WZ2tF9+", 1, 0], [5, 544, 120], [0, -450, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "prop_icon_bg", 2, [-46, -47], [[16, 0, -45, [10], 11]], [0, "121WHtw0VILbndLfsAUg0r", 1, 0], [5, 140, 150], [-200.486, 192.95, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Background", 512, [-50], [[12, 1, 0, -48, [17]], [11, 0, 45, 100, 40, -49]], [0, "69IE7OkVZMHLfdd59Oapjv", 1, 0], [5, 150, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "Background", 512, [-53], [[12, 1, 0, -51, [23]], [11, 0, 45, 100, 40, -52]], [0, "ad3NWQ+9ZCJI9S4ip1+ZKe", 1, 0], [5, 150, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "Background", 512, [-56], [[12, 1, 0, -54, [29]], [11, 0, 45, 100, 40, -55]], [0, "279uCpQVdKzpTNITqDvARO", 1, 0], [5, 150, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "Background", 512, [-59], [[12, 1, 0, -57, [35]], [11, 0, 45, 100, 40, -58]], [0, "11qyR62y9HPpyizXnTLC8R", 1, 0], [5, 150, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "Background", 512, [-62], [[33, 1, -60, [40], 41], [29, 0, 45, 14, 14, 12, 12, 100, 40, -61]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 52, 56], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [19, "maskbg", 140, 1, [[28, 45, -63], [16, 0, -64, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [20, "bg", 1, [2], [[30, 45, 750, 1334, -65]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [2, "bg", 2, [[3, 1, 0, -66, [2], 3], [7, -67]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 590, 800], [0, -27.877, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title_prop_tips", 2, [-69], [[3, 1, 0, -68, [5], 6]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 590, 82], [0, 328.408, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Label_title", 17, [[-70, [4, 3, -71, [4, 4278190080]]], 1, 4], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 320, 70], [0, -1.142, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "prop_icon", 8, [[31, -72, [7]]], [0, "1aCqcfelJFTqCE4VVGZ5h/", 1, 0], [5, 64, 59], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [2, "icon_pintu", 8, [[32, -73, [8], 9]], [0, "ddP5evBpBMoYTcPeORMJ7t", 1, 0], [5, 33, 33], [61.186, 67.078, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "prop_name", 2, [[-74, [5, false, 3, -75, [4, 4278190080]]], 1, 4], [0, "05fgzXOCdGLpuNd/fL+qUE", 1, 0], [4, 4278190080], [5, 400, 56.4], [81.516, 238.744, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "prop_desc", 2, [[-76, [5, false, 3, -77, [4, 4278190080]]], 1, 4], [0, "9c8SEYTJdCW45Te9jL3raT", 1, 0], [4, 4278190080], [5, 400, 37.8], [0, 0.5, 1], [81.516, 210.104, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "prop_get_ways", 2, [[37, "获取路径", 22, false, 1, 2, -78, [14]], [4, 3, -79, [4, 4278190080]]], [0, "d4nBPIEXRBDJOEPyghG+Nv", 1, 0], [5, 154, 56.4], [0, 0, 0.5], [-260.487, 78.633, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label_way", 144, 4, [[8, "商城获取灵石", 28, false, 1, 1, 2, -80, [15]], [5, false, 3, -81, [4, 4278190080]]], [0, "22uyzM54ZEELykwHDor8Wa", 1, 0], [4, 4278190080], [5, 350, 56.4], [0, 0, 0.5], [-252.467, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_go", 4, [9], [[9, 3, -82, [[14, "ebe0chtY9FE6oEftFfPqcZ8", "onBtn", "shop", 1]], [4, 4293322470], [4, 3363338360], 9, 18]], [0, "c5DpV/jw9LA6BNfrVd8uSw", 1, 0], [5, 150, 80], [178.721, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 9, [[13, "前往", 30, false, false, 1, 1, 1, 2, 1, -83, [16]], [4, 3, -84, [4, 3573589800]]], [0, "fbtKxjOQFDQJWZo7WV/Fcn", 1, 0], [5, 120, 62], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label_way", 144, 5, [[8, "扫荡", 28, false, 1, 1, 2, -85, [21]], [5, false, 3, -86, [4, 4278190080]]], [0, "e0LPoa35RLyaoWKFApKf01", 1, 0], [4, 4278190080], [5, 350, 56.4], [0, 0, 0.5], [-252.467, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_go", 5, [10], [[9, 3, -87, [[14, "ebe0chtY9FE6oEftFfPqcZ8", "onBtn", "sweep", 1]], [4, 4293322470], [4, 3363338360], 10, 24]], [0, "ccg/Cov01GaoWHLOwzl5DQ", 1, 0], [5, 150, 80], [178, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 10, [[13, "前往", 30, false, false, 1, 1, 1, 2, 1, -88, [22]], [4, 3, -89, [4, 3573589800]]], [0, "318FbzcD5GVK+M0qwWBj7A", 1, 0], [5, 120, 62], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label_way", 144, 6, [[8, "灵石大作战", 28, false, 1, 1, 2, -90, [27]], [5, false, 3, -91, [4, 4278190080]]], [0, "aeXifwAkpG4bvNgWeDZKlt", 1, 0], [4, 4278190080], [5, 350, 56.4], [0, 0, 0.5], [-252.467, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_go", 6, [11], [[9, 3, -92, [[14, "ebe0chtY9FE6oEftFfPqcZ8", "onBtn", "coin_challenge", 1]], [4, 4293322470], [4, 3363338360], 11, 30]], [0, "07z9lBJodDHovAwi9scS5y", 1, 0], [5, 150, 80], [178, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 11, [[13, "前往", 30, false, false, 1, 1, 1, 2, 1, -93, [28]], [4, 3, -94, [4, 3573589800]]], [0, "f7JyPzaGNC565sM96ZVTm5", 1, 0], [5, 120, 62], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "Label_way", 144, 7, [[8, "僵尸乱斗", 28, false, 1, 1, 2, -95, [33]], [5, false, 3, -96, [4, 4278190080]]], [0, "7dHEJXRN5Pu4UsGz0Cj1h4", 1, 0], [4, 4278190080], [5, 350, 56.4], [0, 0, 0.5], [-252.467, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_go", 7, [12], [[9, 3, -97, [[14, "ebe0chtY9FE6oEftFfPqcZ8", "onBtn", "challenge", 1]], [4, 4293322470], [4, 3363338360], 12, 36]], [0, "220d2YWX1DVI2apVUdi/cq", 1, 0], [5, 150, 80], [178, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 12, [[13, "前往", 30, false, false, 1, 1, 1, 2, 1, -98, [34]], [4, 3, -99, [4, 3573589800]]], [0, "dcz3GgYwtFIq+w5k/1cfJF", 1, 0], [5, 120, 62], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnclose", 2, [13], [[9, 3, -100, [[39, "ebe0chtY9FE6oEftFfPqcZ8", "close", 1]], [4, 4293322470], [4, 3363338360], 13, 42]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [239.152, 325.285, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "资源不足", false, 1, 1, 2, 18, [4]], [8, "灵石", 35, false, 1, 1, 2, 21, [12]], [36, 25, 30, false, false, 1, 1, 3, 22, [13]], [24, "Label", false, 13, [[38, "返回", false, false, 1, 1, 1, 1, -101, [39]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 38, 0, -2, 39, 0, -3, 37, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 3, 0, -6, 19, 0, -7, 20, 0, -8, 8, 0, 0, 1, 0, -1, 14, 0, -2, 15, 0, -1, 16, 0, -2, 17, 0, -3, 8, 0, -4, 21, 0, -5, 22, 0, -6, 23, 0, -7, 3, 0, -8, 36, 0, 0, 3, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, 0, 4, 0, 0, 4, 0, -1, 24, 0, -2, 25, 0, 0, 5, 0, 0, 5, 0, -1, 27, 0, -2, 28, 0, 0, 6, 0, 0, 6, 0, -1, 30, 0, -2, 31, 0, 0, 7, 0, 0, 7, 0, -1, 33, 0, -2, 34, 0, 0, 8, 0, -1, 19, 0, -2, 20, 0, 0, 9, 0, 0, 9, 0, -1, 26, 0, 0, 10, 0, 0, 10, 0, -1, 29, 0, 0, 11, 0, 0, 11, 0, -1, 32, 0, 0, 12, 0, 0, 12, 0, -1, 35, 0, 0, 13, 0, 0, 13, 0, -1, 40, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -1, 18, 0, -1, 37, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, -1, 38, 0, 0, 21, 0, -1, 39, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 40, 0, 5, 1, 2, 2, 15, 9, 2, 25, 10, 2, 28, 11, 2, 31, 12, 2, 34, 13, 2, 36, 101], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 3, -1, 1, -1, -1, -1, 3, -1, 1, -1, -1, -1, 3, -1, 1, -1, -1, -1, 3, -1, 1, -1, -1, 1, 3], [0, 3, 0, 4, 0, 0, 5, 0, 0, 6, 0, 7, 0, 0, 0, 0, 0, 0, 1, 0, 2, 0, 0, 0, 1, 0, 2, 0, 0, 0, 1, 0, 2, 0, 0, 0, 1, 0, 2, 0, 0, 8, 1]]