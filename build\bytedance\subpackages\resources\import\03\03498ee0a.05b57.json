[1, ["ecpdLyjvZBwrvm+cedCcQy", "18nOvW6CdKz6pIMkEx7p7R", "36Cem8ATFKiZ97R3wH1eRk", "6dIu0y3qVCn4X/P4Czkx/b", "91UV3fT0pIP7/dm/ASli1S", "55YJmpry5KabM2vFna9B7k", "5b0HySO0xNLI16cb6FYAR2", "7a/QZLET9IDreTiBfRn2PD", "0bNy06XqNOgKsDrtBlOZTu", "05U5Pxb/BBTZHwRiiAEowD", "fdCzkk+E9Bi6NPgLxQX7Ke", "80qo2FidtLeJtFUhLGJ/S7", "33WpvXATtDJqsHi2fIodO6"], ["node", "_spriteFrame", "_textureSetter", "root", "mySkeleton", "target", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color"], 0, 4, 9, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_fontSize", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 5, 7], ["17bdblch/RLzbM2zwUci0bd", ["node", "sprArr", "nodeArr", "lbArr", "mySkeleton"], 3, 1, 2, 2, 2, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "timeScale", "_animationName", "node", "_materials"], -3, 1, 3]], [[5, 0, 1, 2, 2], [0, 0, 6, 4, 3, 5, 7, 2], [2, 2, 3, 4, 1], [11, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 3], [0, 0, 6, 8, 3, 2], [0, 0, 6, 4, 3, 9, 5, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 1], [10, 0, 1, 2, 3], [3, 4, 0, 1, 2, 3, 6, 7, 6], [3, 4, 0, 5, 1, 2, 3, 6, 7, 7], [6, 0, 2], [0, 0, 1, 8, 4, 3, 5, 3], [0, 0, 6, 8, 4, 3, 5, 7, 2], [0, 0, 6, 4, 3, 5, 2], [0, 0, 2, 6, 4, 3, 5, 7, 3], [4, 0, 1, 2, 3, 4, 5, 6, 3], [4, 0, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3, 4, 1], [5, 1, 2, 1], [2, 2, 3, 1], [3, 0, 5, 1, 2, 3, 6, 7, 6], [12, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "img_cw_tzxz", "rect": [0, 0, 273, 122], "offset": [0, 0], "originalSize": [273, 122], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [2]], [[{"name": "img_cw_tz", "rect": [0, 0, 290, 122], "offset": [0, 0], "originalSize": [290, 122], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[[12, "PetFingtInfo"], [13, "PetFingtInfo", 1, [-10, -11, -12, -13], [[19, -7, [-6], [-5], [-3, -4], -2], [8, -9, [[9, "17bdblch/RLzbM2zwUci0bd", "onClickItem", -8]]]], [20, -1, 0], [5, 128, 128]], [14, "skillNode", 1, [-16, -17, -18, -19, -20], [[4, 1, 0, -14, [21], 22], [8, -15, [[9, "17bdblch/RLzbM2zwUci0bd", "onClickSkill", 1]]]], [0, "4aDUspUZ1Ag4aHtRgRSFuL", 1, 0], [5, 94, 125], [131.698, 108.703, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "petNode", 1, [-21, -22, -23, -24, -25, -26], [0, "11YRQrLP5Mp5ITYMrFP6/G", 1, 0]], [7, "lbLock", 3, [[-27, [3, 2, -28, [4, 4278190080]]], 1, 4], [0, "cesFyKelJMOLlkgap0w8io", 1, 0], [4, 4294967294], [5, 96.02, 26.68], [0, 0.866, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbPetNe", 3, [[10, "灵猫", 32, 1, 1, 1, -29, [10]], [3, 3, -30, [4, 4278190080]]], [0, "bao3EiNPNHBZ2X0ftRoSa6", 1, 0], [4, 4294957639], [5, 70, 56.4], [0, 159.05, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nodeDesc", 3, [-31, -32], [0, "a2uQFO1IdM14w1gAMK5rfR", 1, 0]], [1, "lbPri", 6, [[11, "普通", 25, 25, 1, 1, 1, -33, [13]], [3, 2, -34, [4, 4278190080]]], [0, "0cU0GR2kZMOK9bzyRgTzo7", 1, 0], [5, 54, 35.5], [0, -39, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbSkNe", 2, [[10, "参战技能", 20, 1, 1, 1, -35, [18]], [3, 2, -36, [4, 4278190080]]], [0, "fe36DPmN5C34UN5TiMZros", 1, 0], [4, 4294967294], [5, 84, 54.4], [0, 47.789, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbLv", 2, [[-37, [3, 2, -38, [4, 4278190080]]], 1, 4], [0, "bbRne7sJhCgrBhdYUhhuXJ", 1, 0], [4, 4294967294], [5, 60.02, 26.68], [0, -47.134, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bg", 1, [[2, -39, [0], 1]], [0, "8b9OT/N8VG6aN2/8FByt7b", 1, 0], [5, 290, 122]], [1, "img_cw_tzxz", 1, [[2, -40, [2], 3]], [0, "d5FX3KFdBCKbKsjvUBnbWN", 1, 0], [5, 273, 122], [-2, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_lock_small", 3, [[2, -41, [4], 5]], [0, "64YDckjwRIF6XJE6pxRFBG", 1, 0], [5, 47, 56], [0, 41, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_jiahao", 3, [[2, -42, [6], 7]], [0, "d20KyYZn5LGoF9ndv8z3sF", 1, 0], [5, 39, 39], [0, 41, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 18, 18, 1, 1, 1, 4, [8]], [17, "bird1", false, 3, [-43], [0, "60fXxJdHdKIYa/+4Y5MtpZ", 1, 0], [5, 72, 85.11897277832031], [0, 13.697, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "default", "idle", 0, false, 0.6, "idle", 15, [9]], [1, "img_qz03", 6, [[4, 1, 0, -44, [11], 12]], [0, "fbo2W6FnNHwYqoOHfISdDp", 1, 0], [5, 163, 42], [0, -39, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_djd", 2, [[4, 1, 0, -45, [14], 15]], [0, "54UMrVrlJPzIHsnK3ITIGF", 1, 0], [5, 68, 68], [0, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "icon_jiahao", false, 2, [[2, -46, [16], 17]], [0, "7c1psVQylKOovia2U/0O7U", 1, 0], [5, 39, 39], [0, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "imgSkill", 2, [-47], [0, "a4wn/lE5FBBb4UAliemE0j", 1, 0], [5, 90, 73], [0, -10, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [21, 20, [19]], [11, "等级99", 18, 18, 1, 1, 1, 9, [20]]], 0, [0, 3, 1, 0, 4, 16, 0, -1, 22, 0, -2, 14, 0, -1, 2, 0, -1, 21, 0, 0, 1, 0, 5, 1, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -3, 3, 0, -4, 2, 0, 0, 2, 0, 0, 2, 0, -1, 18, 0, -2, 19, 0, -3, 8, 0, -4, 20, 0, -5, 9, 0, -1, 12, 0, -2, 13, 0, -3, 4, 0, -4, 15, 0, -5, 5, 0, -6, 6, 0, -1, 14, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 17, 0, -2, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 22, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, -1, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, -1, 21, 0, 6, 1, 47], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, 1], [0, 4, 0, 5, 0, 6, 0, 1, 0, 7, 0, 0, 8, 0, 0, 9, 0, 1, 0, 0, 0, 0, 10, 11]], [[{"name": "img_cw_czjnd", "rect": [0, 0, 38, 58], "offset": [0, 0], "originalSize": [38, 58], "capInsets": [7, 38, 5, 9]}], [1], 0, [0], [2], [12]]]]