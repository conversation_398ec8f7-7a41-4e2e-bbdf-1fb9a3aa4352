[1, ["ecpdLyjvZBwrvm+cedCcQy", "92QHEm/1FB+bEACGmgjUyk", "54F9Hwcp5MxKvwbJ6CPQ4f"], ["node", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_is3DNode", "_prefab", "_components", "_parent", "_trs", "_children", "_contentSize", "_anchorPoint", "_eulerAngles"], 0, 4, 9, 1, 7, 2, 5, 5, 5], ["cc.CurveRange", ["mode", "constantMax", "constant", "constantMin"], -1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.GradientRange", ["_mode", "color", "colorMin", "gradient"], 2, 5, 5, 4], ["cc.<PERSON><PERSON><PERSON>", ["time", "alpha"], 1], ["cc.ParticleSystem3D", ["scaleSpace", "_capacity", "node", "_materials", "startDelay", "startLifetime", "startColor", "startSize", "startSpeed", "startRotation", "gravityModifier", "rateOverTime", "rateOverDistance", "bursts", "_shapeModule", "_colorOverLifetimeModule", "_velocityOvertimeModule", "_limitVelocityOvertimeModule"], 1, 1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 4, 4, 4, 4], ["cc.<PERSON><PERSON>", ["time"], 2], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["_sizeMode", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", ["count"], 3, 4], ["cc.ShapeModule", ["enable", "emitFrom", "radius", "_angle", "arcSpeed", "_scale"], -1, 4, 5], ["cc.ColorOvertimeModule", ["enable", "color"], 2, 4], ["cc.Gradient", ["colorKeys", "alphaKeys"], 3, 9, 9], ["cc.VelocityOvertimeModule", ["enable", "x", "y", "z", "speedModifier"], 2, 4, 4, 4, 4], ["cc.LimitVelocityOvertimeModule", ["enable", "dampen", "limit", "limitX", "limitY", "limitZ"], 1, 4, 4, 4, 4], ["90417mcft9EuLa4kj5jrJVQ", ["node"], 3, 1]], [[1, 1], [1, 0, 3, 1, 4], [1, 2, 2], [2, 0, 1, 2], [6, 0, 2], [7, 0, 2], [0, 0, 1, 7, 4, 3, 8, 3], [0, 0, 1, 5, 4, 3, 8, 3], [0, 0, 5, 7, 3, 6, 2], [0, 0, 2, 5, 4, 3, 9, 6, 3], [0, 0, 2, 5, 4, 3, 6, 10, 3], [8, 0, 1, 2, 2], [2, 0, 1, 2, 2], [2, 1, 2, 1], [5, 1, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 3], [5, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 2], [1, 0, 1, 3], [3, 1, 1], [3, 0, 2, 2], [3, 0, 3, 2], [9, 0, 1], [10, 0, 1, 2, 3, 4, 5, 5], [11, 0, 1, 2], [12, 0, 1, 1], [6, 1], [4, 1], [4, 1, 0, 3], [4, 0, 2], [13, 0, 1, 2, 3, 4, 2], [14, 0, 1, 2, 3, 4, 5, 3], [15, 0, 1]], [[5, "MagnetGoods"], [6, "MagnetGoods", 7, [-3, -4], [[30, -2]], [13, -1, 0], [5, 64, 64]], [8, "FX_cieitdaoju", 1, [-6, -7], [3, "c00XS/c09Noppb9BmCx0EF", -5], [0, 250, 0, 0, 0, 0, 1, 8, 25, 1]], [7, "icon", 7, 1, [[11, 0, -8, [0]]], [12, "d78d0qyh1LmrUU2o4FIhBY", 1, 0], [5, 43, 45]], [9, "gaung<PERSON>hu", true, 2, [[14, 1002, 0, -9, [1], [0], [2, 5], [17, [4, 4294939681]], [2, 5], [0], [0], [0], [0], [0], [[20, [2, 1]]]]], [3, "b2crpDDnBB3ZwZlQu1W5oJ", 2], [0, 0, 0.5], [0, 0, 0, 0, 0, 0, 1, 1, 4, 1]], [10, "LIZI", true, 2, [[15, 0, -10, [2], [0], [1, 3, 0.8, 1.5], [18, 2, [4, 4294946655]], [1, 3, 0.2, 0.7], [1, 3, 5, 18], [16, 3, 360], [0], [2, 10], [0], [21, true, 0, 0.6, 0, [0], [1, 1.2, 0, 1]], [22, true, [19, 1, [23, [[24], [4, 0.6409090909090909], [4, 0.6409090909090909], [4, 0.6409090909090909]], [[25], [26, 255, 0.37727272727272726], [27, 0.9954545454545454]]]]], [28, true, [1, 3, 0.3, -0.3], [1, 3, 0.3, -0.3], [1, 3, 0.3, -0.3], [2, 1]], [29, true, 0.2, [0], [0], [0], [0]]]], [3, "achF9QyGxDUL1x5HCgFEr9", 2], [0, -9.717, 0, 0.7071067811865475, 0, 0, 0.7071067811865476, 1, 1, 1], [1, 90, 0, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 1, 2, 0, -1, 4, 0, -2, 5, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 2, 1, 10], [0, 0, 0], [-1, -1, -1], [0, 1, 2]]