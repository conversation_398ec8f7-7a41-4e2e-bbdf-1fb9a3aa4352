[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "f7293wEF9JhIuMEsrLuqng", "e7n7V2vMBFQ5v7eF5oYrkv", "f0BIwQ8D5Ml7nTNQbh1YlS", "f8npR6F8ZIZoCp3cKXjJQz", "5fdNKB8blK7KpDNcwy/hDI", "a016jB2DhKs6pPW0sX8w55", "9e8rlz/d1PdKQJHJhQCmy/", "b5h/7+C<PERSON>J<PERSON>Obfan4lScQJ0", "a2MjXRFdtLlYQ5ouAFv/+R", "c1f4UDWZJNUJmmvN1IN/rK", "d20mwc8GpOOo6svEcfztOV", "ddFhwuPitMK6rcBkC3ddyJ", "83PZ0CH0xOfaHSB0mMiipU", "69OoKTp+5H0KIChcprfKMo", "73jGpne/9JUI/171Zur5Pr", "7bhcZjDqBJlYNxeY0rnkei", "ccpu60o3FKFY+668VHRM+l", "49eKzQe6BJaqCh+WvNVDew"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_parent", "_N$disabledSprite", "root", "_N$normalSprite", "goodimg", "goodsbg", "_N$target", "data"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_right", "_top", "_bottom", "alignMode", "node"], -5, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_lineHeight", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "node", "_layoutSize"], -1, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite", "_N$normalSprite"], 2, 1, 9, 5, 5, 1, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["d8ed9pjFDZM0pN4KgT0nByS", ["UItype", "node", "nodeArr", "labelArr"], 2, 1, 2, 2], ["588e9YJTR5JAoebMnGix+fw", ["node", "goodsbg", "goodimg"], 3, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [5, 0, 1, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [1, 3, 4, 5, 1], [15, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 6, 4, 5, 8, 2], [6, 0, 1, 2, 3, 4, 2], [1, 1, 0, 3, 4, 5, 3], [1, 1, 0, 3, 4, 3], [3, 7, 0, 3, 4, 5, 6, 1, 2, 8, 9], [8, 0, 1, 2, 3, 4, 5, 7, 6, 2], [9, 0, 1, 2, 3, 4], [4, 0, 1, 2, 5, 3, 4, 8, 9, 10, 7], [10, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 9, 6, 4, 5, 2], [0, 0, 7, 9, 4, 5, 8, 2], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 7, 9, 6, 4, 10, 5, 8, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [0, 0, 7, 6, 4, 10, 5, 11, 8, 2], [0, 0, 1, 7, 6, 4, 5, 8, 3], [0, 0, 1, 7, 6, 4, 5, 3], [6, 0, 1, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3, 2], [5, 1, 2, 1], [13, 0, 1, 2, 1], [1, 0, 3, 4, 5, 2], [1, 3, 4, 1], [1, 2, 3, 4, 5, 2], [14, 0, 1], [7, 0, 1, 2, 4, 5, 4], [7, 0, 1, 3, 2, 4, 5, 5], [3, 0, 8, 2], [3, 0, 1, 2, 8, 4], [3, 0, 3, 4, 5, 6, 1, 2, 8, 8], [8, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 3, 3], [4, 0, 1, 6, 7, 2, 3, 4, 8, 9, 10, 8], [4, 0, 1, 6, 2, 5, 3, 4, 8, 9, 8], [4, 0, 1, 7, 2, 5, 3, 4, 8, 9, 8]], [[[{"name": "bg_dp_coin", "rect": [0, 0, 161, 59], "offset": [0, 0], "originalSize": [161, 59], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [6]], [[{"name": "img_sddb03", "rect": [0, 0, 593, 153], "offset": [0, 0], "originalSize": [593, 153], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [7]], [[{"name": "icon_card_02", "rect": [0, 1, 49, 50], "offset": [0, -0.5], "originalSize": [49, 51], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [8]], [[{"name": "icon_jns01", "rect": [10, 15, 172, 126], "offset": [-1.5, 2], "originalSize": [195, 160], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [9]], [[[15, "Shop_SkillBoxInfo"], [16, "Shop_SkillBoxInfo", 1, [-7, -8], [[28, 0, -6, [-4, -5], [-2, -3]]], [29, -1, 0], [5, 750, 1334]], [17, "goodItem", [-13, -14, -15, -16, -17], [[30, -12, -11, -10]], [1, "86GWyAoENDMo3QFOJr7Wx4", -9], [5, 80, 50], [-90, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [18, "content", [-20, -21, -22, -23, -24], [[9, 1, 0, -18, [32], 33], [34, -19]], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 664, 750]], [19, "lrbtn", 3, [-25, -26, -27, -28], [0, "48n/6ky5hE+Z+YunJIYWLx", 1, 0], [5, 275, 0], [0, 250.004, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "infocontent", 3, [-30], [[35, 1, 2, 20, -29, [5, 530, 230]]], [0, "2bsmcRGppCHJazgatGT3z9", 1, 0], [5, 530, 230], [0, -40.717, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "frame", 5, [-32, -33], [[9, 1, 0, -31, [17], 18]], [0, "15EkFfVZJL8LkgvIHjR9S1", 1, 0], [5, 610, 230]], [7, "Background", [[3, -34, [21], 22], [11, 0, 45, 3.5, 3.5, 2.5, 2.5, 100, 40, -35]], [0, "10GEDvTnhBTraOyk5uaskm", 1, 0], [5, 43, 35], [0, 0, 0, 0, 0, 0, 1, -1, 1, 0]], [7, "Background", [[3, -36, [25], 26], [11, 0, 45, 3.5, 3.5, 2.5, 2.5, 100, 40, -37]], [0, "1fTWZbuqVOVZ0NmW2vCfIe", 1, 0], [5, 43, 35], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [20, "maskbg", 230, 1, [[37, 45, -38], [31, 0, -39, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [6, "bg", 1, [3], [[38, 45, 750, 1334, -40]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [2, "title_zhua<PERSON><PERSON>", 3, [-42], [[10, 1, 0, -41, [3]]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 664, 86], [0, 330.563, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Label_title", 11, [[-43, [4, 3, -44, [4, 4278190080]]], 1, 4], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 198, 66.47999999999999]], [2, "btnclose", 3, [-47], [[40, 3, -46, [[41, "d8ed9pjFDZM0pN4KgT0nByS", "close", 1]], [4, 4293322470], [4, 3363338360], -45, 6]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [275.89, 324.903, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 13, [[3, -48, [4], 5]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 64, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [21, "content", 6, [2], [[36, 1, 3, 70, 30, -49, [5, 260, 50]]], [0, "4dal2KkFZBgp2mqSzrUhOd", 1, 0], [4, 4278190080], [5, 260, 50], [118.971, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 2, [[-50, [39, 45, -47.5, -47.5, -12.5, -12.5, 116, 120, -51]], 1, 4], [1, "a3lph4JQ9Kc7Wv2gjJpFZT", 2], [5, 175, 75]], [22, "num", 2, [[14, "x10", 36, false, 1, 1, 1, -52, [13], 14], [4, 2, -53, [4, 4278190080]]], [1, "dcrYPy5OVMv6CcmFaAFU4U", 2], [5, 60.85, 54.4], [0, 1, 0.5], [62.19, 1.487, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "add", 2, [[14, "+10", 26, false, 1, 1, 1, -54, [15], 16], [4, 2, -55, [4, 4278190080]]], [1, "36bU9m1DhForjir0U1PdsG", 2], [4, 4280090492], [5, 45.59, 54.4], [0, 1, 0.5], [77.064, -26.774, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnleft", 4, [7], [[12, 3, -56, [[13, "d8ed9pjFDZM0pN4KgT0nByS", "pageChange", "-1", 1]], [4, 4293322470], [4, 3363338360], 7, 23, 24]], [0, "726ze2pEFOc6hz9CJ3xJWm", 1, 0], [5, 50, 40], [-130, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnright", 4, [8], [[12, 3, -57, [[13, "d8ed9pjFDZM0pN4KgT0nByS", "pageChange", "1", 1]], [4, 4293322470], [4, 3363338360], 8, 27, 28]], [0, "b7WGw0IkNCo6/AN75JEkaT", 1, 0], [5, 50, 40], [130, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "lv", 4, [[-58, [4, 2, -59, [4, 4278190080]]], 1, 4], [0, "1cty/KDvJDa5s8C4Z3SgC2", 1, 0], [5, 67.74000000000001, 54.4], [0, 1.627, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Label_title", 3, [[42, "每次抽取都可增加经验，提升等级", 30, 30, false, false, 1, 1, -60, [30], 31], [4, 2, -61, [4, 4278190080]]], [0, "4bjy3wUXRIbLfc29Y+Y563", 1, 0], [5, 454, 41.8], [0, -318.025, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "宝箱等级", 48, 48, false, 1, 1, 1, 12, [2]], [5, "icon", 6, [[3, -62, [7], 8]], [0, "d8gxPXB49C86KvAj3wpUUj", 1, 0], [5, 172, 126], [-174.547, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, 1, 0, 16, [9]], [27, "icon", 2, [-63], [1, "c8TQPIne1PvJf+EhL/y/tz", 2], [5, 49, 50], [-48.983, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [32, 26, [10]], [24, "icon_pintu", false, 2, [[3, -64, [11], 12]], [1, "81jE12cbtAJaj7tyX8R5Ym", 2], [5, 35, 35], [51.182, 48.061, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "bg_shuxing", false, 4, [[33, false, -65, [19], 20]], [0, "ack0DGJ65DC7J7v4XXBsp7", 1, 0], [5, 75, 32]], [44, "Lv.1", 36, false, false, 1, 2, 1, 21, [29]]], 0, [0, 6, 1, 0, -1, 23, 0, -2, 30, 0, -1, 4, 0, -2, 5, 0, 0, 1, 0, -1, 9, 0, -2, 10, 0, 6, 2, 0, 8, 27, 0, 9, 25, 0, 0, 2, 0, -1, 16, 0, -2, 26, 0, -3, 28, 0, -4, 17, 0, -5, 18, 0, 0, 3, 0, 0, 3, 0, -1, 11, 0, -2, 13, 0, -3, 5, 0, -4, 4, 0, -5, 22, 0, -1, 29, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 24, 0, -2, 15, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 12, 0, -1, 23, 0, 0, 12, 0, 10, 14, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 0, 15, 0, -1, 25, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, -1, 30, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 24, 0, -1, 27, 0, 0, 28, 0, 0, 29, 0, 11, 1, 2, 4, 15, 3, 4, 10, 7, 4, 19, 8, 4, 20, 65], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 25, 27, 30], [-1, 1, -1, -1, -1, 1, 5, -1, 1, -1, -1, -1, 1, -1, 3, -1, 3, -1, 1, -1, 1, -1, 1, 7, 5, -1, 1, 7, 5, -1, -1, 3, -1, 1, 3, 1, 1, 3], [0, 10, 0, 0, 0, 11, 1, 0, 12, 0, 0, 0, 13, 0, 2, 0, 2, 0, 14, 0, 15, 0, 3, 4, 1, 0, 3, 4, 1, 0, 0, 5, 0, 16, 5, 17, 18, 2]], [[{"name": "arrow_dp", "rect": [0, 0, 43, 35], "offset": [0, 0], "originalSize": [43, 35], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [19]]]]