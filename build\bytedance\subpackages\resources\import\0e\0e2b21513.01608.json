[1, ["ecpdLyjvZBwrvm+cedCcQy", "4ffbVQYohHoLfkb2ormYfg", "f9+VMCSutDhYoE/prGmfcB", "a2MjXRFdtLlYQ5ouAFv/+R", "73jGpne/9JUI/171Zur5Pr", "f8npR6F8ZIZoCp3cKXjJQz", "cbAEkvb+1HSrkEhP8WzNiN", "63lYttOWBAHb/h2xyHkhxg", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "f6NcoTBPNJ4qSyD40PNpRP", "586MyaYRZBYbdgSvAl61dB", "4b/I/MuHRAYL8UkPXgwwYX"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "my<PERSON>rid<PERSON>iew", "_N$target", "_N$content", "data", "_N$file", "_N$disabledSprite", "templete"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_bottom", "alignMode", "node"], -3, 1], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "_styleFlags", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["ea2edXCI+NGI5+Zqm9xkWmX", ["node", "my<PERSON>rid<PERSON>iew"], 3, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_enabled", "_N$alphaThreshold", "node", "_materials"], 1, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content"], 3, 1, 5, 1]], [[4, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 8, 2], [1, 3, 4, 5, 1], [1, 1, 0, 3, 4, 5, 3], [6, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 9, 4, 5, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 11, 8, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 3, 7, 6, 4, 10, 5, 8, 3], [0, 0, 1, 7, 6, 4, 10, 5, 8, 3], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 1], [4, 1, 2, 1], [1, 2, 1, 0, 3, 4, 5, 4], [1, 0, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 3, 3], [2, 0, 3, 4, 1, 2, 6, 6], [2, 5, 0, 1, 6, 4], [2, 0, 6, 2], [2, 0, 1, 2, 6, 4], [5, 0, 4, 5, 1, 6, 2, 3, 10, 11, 12, 8], [5, 0, 7, 1, 2, 3, 8, 9, 10, 11, 8], [13, 0, 1, 2, 2], [14, 0, 1, 2, 1]], [[[{"name": "dn-1", "rect": [0, 0, 576, 333], "offset": [0, 0], "originalSize": [576, 333], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [1]], [[{"name": "dn-2", "rect": [0, 0, 175, 88], "offset": [0, 0], "originalSize": [175, 88], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [2]], [[[4, "FundListView"], [5, "FundListView", 1, [-4, -5], [[16, -3, -2]], [17, -1, 0], [5, 750, 1334]], [6, "title_zhua<PERSON><PERSON>", [-7, -8, -9, -10], [[18, false, 1, 0, -6, [14], 15]], [0, "45NuY+WQ9L5J6TOpjXxAQa", 1, 0], [5, 680, 82], [0, 348.214, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "content", [-11, 2, -12, -13], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 680, 1126]], [8, "Background", 2, [-17], [[2, -14, [11], 12], [20, 3, -16, [[21, "ea2edXCI+NGI5+Zqm9xkWmX", "close", 1]], [4, 4293322470], [4, 3363338360], -15, 13]], [0, "90OlI7eu5N6YuKmHM/sieV", 1, 0], [5, 64, 65], [269.386, -0.668, 0, 0, 0, 0, 1, 1, 1, 0]], [15, "New ScrollView", 3, [-21], [[[22, false, 0.75, 0.23, null, null, -19, -18], -20], 4, 1], [0, "c16wooUcRFu7atWQTNsgll", 1, 0], [5, 620, 800], [0, -112.848, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 5, [-24], [[23, false, 0, -22, [18]], [24, 45, 20, 20, 240, 250, -23]], [0, "f6rJOdci5LpqR2itdXzuYz", 1, 0], [5, 620, 760], [0, 0.5, 1], [0, 380, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "content", 6, [[25, 0, 41, 614, -25]], [0, "ecKprdHUBMRoHqCj7C6P4V", 1, 0], [5, 620, 274], [0, 0, 1], [-310, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "maskbg", 230, 1, [[26, 45, -26], [19, 0, -27, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [12, "bg", 1, [3], [[27, 45, 750, 1334, -28]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [1, "Label_title", 2, [[28, "基金", 48, 48, false, 1, 1, 1, -29, [4], 5], [30, 3, -30, [4, 4278190080]]], [0, "6aRvpyzjtLSqbRuVrwAnNW", 1, 0], [5, 102, 66.47999999999999], [0, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 3, [[3, 1, 0, -31, [2], 3]], [0, "32XWbHc3VCJoxAH6kEEwjT", 1, 0], [5, 680, 914], [0, -72.512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dn-2", 2, [[2, -32, [6], 7]], [0, "c92db/UP5BUptb0pmJyjdJ", 1, 0], [5, 175, 88], [-146.241, -6.779, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dn-1", 2, [[2, -33, [8], 9]], [0, "c9PBBaYtlJpIcuwzTdHFWh", 1, 0], [5, 576, 333], [0, 203.576, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "Label", false, 4, [[29, "返回", false, false, 1, 1, 1, 1, -34, [10]]], [0, "32xj1jPPxLkrFw1riXxUhd", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "fkbg_02", 83, 3, [[3, 1, 0, -35, [16], 17]], [0, "ba3dgY0OJGsr6W/o7C1onV", 1, 0], [4, 4294962402], [5, 640, 800], [0, -109.369, 0, 0, 0, 0, 1, 1, 1, 1]], [31, 5, [0, 0, 20], 7]], 0, [0, 4, 1, 0, 5, 16, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, 0, 2, 0, -1, 10, 0, -2, 12, 0, -3, 13, 0, -4, 4, 0, -1, 11, 0, -3, 15, 0, -4, 5, 0, 0, 4, 0, 6, 4, 0, 0, 4, 0, -1, 14, 0, 7, 7, 0, 0, 5, 0, -2, 16, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 8, 1, 2, 3, 3, 3, 3, 9, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16], [-1, 1, -1, 1, -1, 9, -1, 1, -1, 1, -1, -1, 1, 10, -1, 1, -1, 1, -1, 11], [0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 0, 8, 9, 0, 10, 0, 11, 0, 12]]]]