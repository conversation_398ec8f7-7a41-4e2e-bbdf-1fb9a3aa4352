{"10001": {"id": 10001, "type": 4, "taskDes": "活跃度达到20", "itemNum": 20, "otherValue": 23}, "10002": {"id": 10002, "type": 4, "taskDes": "活跃度达到40", "itemNum": 40, "otherValue": 23}, "10003": {"id": 10003, "type": 4, "taskDes": "活跃度达到60", "itemNum": 60, "otherValue": 23}, "10004": {"id": 10004, "type": 4, "taskDes": "活跃度达到80", "itemNum": 80, "otherValue": 23}, "10005": {"id": 10005, "type": 4, "taskDes": "活跃度达到100", "itemNum": 100, "otherValue": 23}, "10006": {"id": 10006, "type": 4, "taskDes": "活跃度达到140", "itemNum": 140, "otherValue": 23}, "10007": {"id": 10007, "type": 4, "taskDes": "活跃度达到280", "itemNum": 280, "otherValue": 23}, "10008": {"id": 10008, "type": 4, "taskDes": "活跃度达到420", "itemNum": 420, "otherValue": 23}, "10009": {"id": 10009, "type": 4, "taskDes": "活跃度达到560", "itemNum": 560, "otherValue": 23}, "10010": {"id": 10010, "type": 4, "taskDes": "活跃度达到700", "itemNum": 700, "otherValue": 23}, "10011": {"id": 10011, "type": 3, "taskDes": "消耗体力10", "itemNum": 10, "otherValue": 1}, "10012": {"id": 10012, "type": 2, "taskDes": "购买体力1次", "itemNum": 1}, "10013": {"id": 10013, "type": 12, "taskDes": "挑战主线章节1次", "itemNum": 1, "otherValue": 1}, "10014": {"id": 10014, "type": 12, "taskDes": "参加挑战：熔岩蛇龙", "itemNum": 1, "otherValue": 12}, "10015": {"id": 10015, "type": 12, "taskDes": "参加挑战：蓝眼白龙", "itemNum": 1, "otherValue": 14}, "10016": {"id": 10016, "type": 12, "taskDes": "参加挑战：神龙之战", "itemNum": 1, "otherValue": 10}, "10017": {"id": 10017, "type": 12, "taskDes": "参加挑战：屠龙前传", "itemNum": 1, "otherValue": 11}, "10018": {"id": 10018, "type": 12, "taskDes": "参加挑战：浮空之龙", "itemNum": 1, "otherValue": 15}, "10019": {"id": 10019, "type": 10, "taskDes": "开启宝箱2次", "itemNum": 2}, "10020": {"id": 10020, "type": 17, "taskDes": "进行宠物召唤1次", "itemNum": 1}, "10021": {"id": 10021, "type": 16, "taskDes": "进行神器召唤1次", "itemNum": 1}, "10022": {"id": 10022, "type": 6, "taskDes": "宠物升级1次", "itemNum": 1}, "10023": {"id": 10023, "type": 7, "taskDes": "角色升级1次", "itemNum": 1}, "10024": {"id": 10024, "type": 8, "taskDes": "装备强化1次", "itemNum": 1}, "10025": {"id": 10025, "type": 11, "taskDes": "商店购买1次", "itemNum": 1}, "10026": {"id": 10026, "type": 9, "taskDes": "法宝升级1次", "itemNum": 1}, "10027": {"id": 10027, "type": 15, "taskDes": "进行法宝召唤1次", "itemNum": 1}, "10100": {"id": 10100, "type": 1, "taskDes": "登录游戏1次", "itemNum": 1}, "10101": {"id": 10101, "type": 1, "taskDes": "登录游戏2次", "itemNum": 2}, "10102": {"id": 10102, "type": 1, "taskDes": "登录游戏3次", "itemNum": 3}, "10103": {"id": 10103, "type": 1, "taskDes": "登录游戏4次", "itemNum": 4}, "10104": {"id": 10104, "type": 1, "taskDes": "登录游戏5次", "itemNum": 5}, "10105": {"id": 10105, "type": 1, "taskDes": "登录游戏6次", "itemNum": 6}, "10106": {"id": 10106, "type": 1, "taskDes": "登录游戏7次", "itemNum": 7}, "20001": {"id": 20001, "type": 4, "taskDes": "活跃度达到50", "itemNum": 50, "otherValue": 24}, "20002": {"id": 20002, "type": 4, "taskDes": "活跃度达到100", "itemNum": 100, "otherValue": 24}, "20003": {"id": 20003, "type": 4, "taskDes": "活跃度达到150", "itemNum": 150, "otherValue": 24}, "20004": {"id": 20004, "type": 4, "taskDes": "活跃度达到200", "itemNum": 200, "otherValue": 24}, "20005": {"id": 20005, "type": 4, "taskDes": "活跃度达到250", "itemNum": 250, "otherValue": 24}, "20006": {"id": 20006, "type": 4, "taskDes": "活跃度达到300", "itemNum": 300, "otherValue": 24}, "20010": {"id": 20010, "type": 14, "taskDes": "通关主线第3章", "itemNum": 1, "otherValue": 3}, "20011": {"id": 20011, "type": 14, "taskDes": "通关主线第6章", "itemNum": 1, "otherValue": 6}, "20012": {"id": 20012, "type": 14, "taskDes": "通关主线第9章", "itemNum": 1, "otherValue": 9}, "20013": {"id": 20013, "type": 14, "taskDes": "通关主线第12章", "itemNum": 1, "otherValue": 12}, "20014": {"id": 20014, "type": 14, "taskDes": "通关主线第15章", "itemNum": 1, "otherValue": 15}, "20015": {"id": 20015, "type": 14, "taskDes": "通关主线第18章", "itemNum": 1, "otherValue": 18}, "20016": {"id": 20016, "type": 14, "taskDes": "通关主线第21章", "itemNum": 1, "otherValue": 21}, "20017": {"id": 20017, "type": 14, "taskDes": "通关主线第24章", "itemNum": 1, "otherValue": 24}, "20018": {"id": 20018, "type": 14, "taskDes": "通关主线第27章", "itemNum": 1, "otherValue": 27}, "20019": {"id": 20019, "type": 14, "taskDes": "通关主线第30章", "itemNum": 1, "otherValue": 30}, "20020": {"id": 20020, "type": 14, "taskDes": "通关主线第33章", "itemNum": 1, "otherValue": 33}, "20021": {"id": 20021, "type": 14, "taskDes": "通关主线第36章", "itemNum": 1, "otherValue": 36}, "20022": {"id": 20022, "type": 14, "taskDes": "通关主线第39章", "itemNum": 1, "otherValue": 39}, "20023": {"id": 20023, "type": 14, "taskDes": "通关主线第42章", "itemNum": 1, "otherValue": 42}, "20024": {"id": 20024, "type": 14, "taskDes": "通关主线第45章", "itemNum": 1, "otherValue": 45}, "20100": {"id": 20100, "type": 13, "taskDes": "通关挑战：熔岩蛇龙", "itemNum": 1, "otherValue": 12}, "20101": {"id": 20101, "type": 13, "taskDes": "通关挑战：蓝眼白龙", "itemNum": 1, "otherValue": 14}, "20102": {"id": 20102, "type": 13, "taskDes": "通关挑战：神龙之战", "itemNum": 1, "otherValue": 10}, "20103": {"id": 20103, "type": 13, "taskDes": "通关挑战：屠龙前传", "itemNum": 1, "otherValue": 11}, "20104": {"id": 20104, "type": 13, "taskDes": "通关挑战：浮空之龙", "itemNum": 1, "otherValue": 15}, "20200": {"id": 20200, "type": 10, "taskDes": "开启宝箱5次", "itemNum": 5}, "20201": {"id": 20201, "type": 10, "taskDes": "开启宝箱10次", "itemNum": 10}, "20202": {"id": 20202, "type": 10, "taskDes": "开启宝箱15次", "itemNum": 15}, "20203": {"id": 20203, "type": 10, "taskDes": "开启宝箱20次", "itemNum": 20}, "20204": {"id": 20204, "type": 10, "taskDes": "开启宝箱25次", "itemNum": 25}, "20205": {"id": 20205, "type": 10, "taskDes": "开启宝箱30次", "itemNum": 30}, "20206": {"id": 20206, "type": 10, "taskDes": "开启宝箱35次", "itemNum": 35}, "20300": {"id": 20300, "type": 16, "taskDes": "进行神器召唤2次", "itemNum": 2}, "20301": {"id": 20301, "type": 16, "taskDes": "进行神器召唤4次", "itemNum": 4}, "20302": {"id": 20302, "type": 16, "taskDes": "进行神器召唤6次", "itemNum": 6}, "20303": {"id": 20303, "type": 16, "taskDes": "进行神器召唤8次", "itemNum": 8}, "20304": {"id": 20304, "type": 16, "taskDes": "进行神器召唤10次", "itemNum": 10}, "20305": {"id": 20305, "type": 16, "taskDes": "进行神器召唤12次", "itemNum": 12}, "20306": {"id": 20306, "type": 16, "taskDes": "进行神器召唤14次", "itemNum": 14}, "20400": {"id": 20400, "type": 17, "taskDes": "进行宠物召唤10次", "itemNum": 10}, "20401": {"id": 20401, "type": 17, "taskDes": "进行宠物召唤20次", "itemNum": 20}, "20402": {"id": 20402, "type": 17, "taskDes": "进行宠物召唤30次", "itemNum": 30}, "20403": {"id": 20403, "type": 17, "taskDes": "进行宠物召唤40次", "itemNum": 40}, "20404": {"id": 20404, "type": 17, "taskDes": "进行宠物召唤50次", "itemNum": 50}, "20405": {"id": 20405, "type": 17, "taskDes": "进行宠物召唤60次", "itemNum": 60}, "20406": {"id": 20406, "type": 17, "taskDes": "进行宠物召唤70次", "itemNum": 70}, "20500": {"id": 20500, "type": 16, "taskDes": "进行法宝召唤5次", "itemNum": 5}, "20501": {"id": 20501, "type": 16, "taskDes": "进行法宝召唤10次", "itemNum": 10}, "20502": {"id": 20502, "type": 16, "taskDes": "进行法宝召唤15次", "itemNum": 15}, "20503": {"id": 20503, "type": 16, "taskDes": "进行法宝召唤20次", "itemNum": 20}, "20504": {"id": 20504, "type": 16, "taskDes": "进行法宝召唤30次", "itemNum": 30}, "20505": {"id": 20505, "type": 16, "taskDes": "进行法宝召唤35次", "itemNum": 35}, "20506": {"id": 20506, "type": 16, "taskDes": "进行法宝召唤5次", "itemNum": 5}, "20600": {"id": 20600, "type": 11, "taskDes": "商店购买道具5次", "itemNum": 5}, "20601": {"id": 20601, "type": 11, "taskDes": "商店购买道具5次", "itemNum": 5}, "20602": {"id": 20602, "type": 11, "taskDes": "商店购买道具5次", "itemNum": 5}, "20603": {"id": 20603, "type": 11, "taskDes": "商店购买道具10次", "itemNum": 10}, "20604": {"id": 20604, "type": 11, "taskDes": "商店购买道具10次", "itemNum": 10}, "20605": {"id": 20605, "type": 11, "taskDes": "商店购买道具15次", "itemNum": 15}, "20606": {"id": 20606, "type": 11, "taskDes": "商店购买道具15次", "itemNum": 15}, "20700": {"id": 20700, "type": 3, "taskDes": "消耗体力20", "itemNum": 20, "otherValue": 1}, "20701": {"id": 20701, "type": 3, "taskDes": "消耗体力30", "itemNum": 30, "otherValue": 1}, "20702": {"id": 20702, "type": 3, "taskDes": "消耗体力40", "itemNum": 40, "otherValue": 1}, "20703": {"id": 20703, "type": 3, "taskDes": "消耗体力50", "itemNum": 50, "otherValue": 1}, "20704": {"id": 20704, "type": 3, "taskDes": "消耗体力60", "itemNum": 60, "otherValue": 1}, "20705": {"id": 20705, "type": 3, "taskDes": "消耗体力70", "itemNum": 70, "otherValue": 1}, "20706": {"id": 20706, "type": 3, "taskDes": "消耗体力80", "itemNum": 80, "otherValue": 1}, "20800": {"id": 20800, "type": 19, "taskDes": "战力达到300", "itemNum": 300}, "20801": {"id": 20801, "type": 19, "taskDes": "战力达到600", "itemNum": 600}, "20802": {"id": 20802, "type": 19, "taskDes": "战力达到900", "itemNum": 900}, "20803": {"id": 20803, "type": 19, "taskDes": "战力达到1200", "itemNum": 1200}, "20804": {"id": 20804, "type": 19, "taskDes": "战力达到1500", "itemNum": 1500}, "20805": {"id": 20805, "type": 19, "taskDes": "战力达到1800", "itemNum": 1800}, "20806": {"id": 20806, "type": 19, "taskDes": "战力达到2100", "itemNum": 2100}, "20900": {"id": 20900, "type": 5, "taskDes": "观看广告10次", "itemNum": 10}, "20901": {"id": 20901, "type": 5, "taskDes": "观看广告10次", "itemNum": 10}, "20902": {"id": 20902, "type": 5, "taskDes": "观看广告10次", "itemNum": 10}, "20903": {"id": 20903, "type": 5, "taskDes": "观看广告10次", "itemNum": 10}, "20904": {"id": 20904, "type": 5, "taskDes": "观看广告15次", "itemNum": 15}, "20905": {"id": 20905, "type": 5, "taskDes": "观看广告15次", "itemNum": 15}, "20906": {"id": 20906, "type": 5, "taskDes": "观看广告15次", "itemNum": 15}, "21000": {"id": 21000, "type": 4, "taskDes": "获得灵石1000", "itemNum": 1000, "otherValue": 2}, "21001": {"id": 21001, "type": 4, "taskDes": "获得灵石2000", "itemNum": 2000, "otherValue": 2}, "21002": {"id": 21002, "type": 4, "taskDes": "获得灵石3000", "itemNum": 3000, "otherValue": 2}, "21003": {"id": 21003, "type": 4, "taskDes": "获得灵石4000", "itemNum": 4000, "otherValue": 2}, "21004": {"id": 21004, "type": 4, "taskDes": "获得灵石5000", "itemNum": 5000, "otherValue": 2}, "21005": {"id": 21005, "type": 4, "taskDes": "获得灵石6000", "itemNum": 6000, "otherValue": 2}, "21006": {"id": 21006, "type": 4, "taskDes": "获得灵石7000", "itemNum": 7000, "otherValue": 2}, "21100": {"id": 21100, "type": 4, "taskDes": "获得灵币500", "itemNum": 1000, "otherValue": 2}, "21101": {"id": 21101, "type": 4, "taskDes": "获得灵币1000", "itemNum": 2000, "otherValue": 2}, "21102": {"id": 21102, "type": 4, "taskDes": "获得灵币1500", "itemNum": 3000, "otherValue": 2}, "21103": {"id": 21103, "type": 4, "taskDes": "获得灵币2000", "itemNum": 4000, "otherValue": 2}, "21104": {"id": 21104, "type": 4, "taskDes": "获得灵币2500", "itemNum": 5000, "otherValue": 2}, "21105": {"id": 21105, "type": 4, "taskDes": "获得灵币3000", "itemNum": 6000, "otherValue": 2}, "21106": {"id": 21106, "type": 4, "taskDes": "获得灵币3500", "itemNum": 7000, "otherValue": 2}, "21200": {"id": 21200, "type": 1, "taskDes": "登录游戏8次", "itemNum": 8}, "21201": {"id": 21201, "type": 1, "taskDes": "登录游戏9次", "itemNum": 9}, "21202": {"id": 21202, "type": 1, "taskDes": "登录游戏10次", "itemNum": 10}, "21203": {"id": 21203, "type": 1, "taskDes": "登录游戏11次", "itemNum": 11}, "21204": {"id": 21204, "type": 1, "taskDes": "登录游戏12次", "itemNum": 12}, "21205": {"id": 21205, "type": 1, "taskDes": "登录游戏13次", "itemNum": 13}, "21206": {"id": 21206, "type": 1, "taskDes": "登录游戏14次", "itemNum": 14}, "99999": {"id": 99999, "type": 20, "taskDes": "充值1次", "itemNum": 1}}