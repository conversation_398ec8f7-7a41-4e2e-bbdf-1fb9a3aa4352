[1, ["7a/QZLET9IDreTiBfRn2PD", "c3GERtfJhAWJErqRXJREpY", "ecpdLyjvZBwrvm+cedCcQy", "b9xnpG2zlEsoMMy3GMQBRQ"], ["node", "root", "data", "_N$skeletonData"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 2, 9, 4, 5, 2, 1, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials"], 1, 1, 3], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[2, 0, 2], [0, 0, 4, 1, 2, 3, 2], [0, 0, 5, 1, 2, 3, 6, 2], [3, 0, 1, 2, 3, 3], [1, 1, 2, 1], [1, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 6, 5], [5, 0, 1, 2, 3, 4, 5]], [[[[0, "<PERSON>"], [1, "<PERSON>", [-3], [[3, 1, 0, -2, [2]]], [4, -1, 0], [5, 70, 70]], [2, "ball1", 1, [[6, "default", "animation", 0, "animation", -4, [0], 1]], [5, "114ba9ob9LoJH3hjVRGPGR", 1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]]], 0, [0, 1, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 2, 1, 4], [0, 0, 0], [-1, 3, -1], [0, 1, 2]], [[[7, "ball1", ".bin", "\nball1.png\nsize: 716,104\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nw1\n  rotate: false\n  xy: 104, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nw2\n  rotate: false\n  xy: 206, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nw3\n  rotate: false\n  xy: 308, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nw4\n  rotate: false\n  xy: 410, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nw5\n  rotate: false\n  xy: 512, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nw6\n  rotate: false\n  xy: 614, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nw7\n  rotate: false\n  xy: 2, 2\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\n", ["ball1.png"], [0]], -1], 0, 0, [0], [-1], [3]]]]