[1, ["ecpdLyjvZBwrvm+cedCcQy", "65OUoZ25pGHqftEDT5VTS4", "f6NcoTBPNJ4qSyD40PNpRP", "35RFcv50lBVotRgr46UtiC", "22cjIamqZH/Lbdvp80pFLv", "70mSmGpopJRZTu56xArjI+", "ebJXGx5cNJ+b1BpmgufQFT", "a8lPnHTihBmrRgij+r2Olk", "40n/qScIlL4Kf71vl+Qxf1", "51xgzaHXJNpLw7wyr07UMc", "38leaXDFhCJIBJ/NFyGYc2", "a2MjXRFdtLlYQ5ouAFv/+R", "6dS6kDXHxHtZYN4vxmJnhL", "c6823ZZu5DO5uHXx3BV9h0", "e3q84hOUtFEo1aiRNVD8I7", "02zDU9uudJXIP1temsdBeO", "485eGEfEJLOqZGExd6w/kd", "1fevf19ExMW4Dg3zbkgLzt", "cfS22Jg79NIpmfgxdEr7L6"], ["node", "_spriteFrame", "_parent", "_N$target", "_textureSetter", "checkMark", "root", "navRp", "adddestopRp", "adddestopN", "addNavN", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_parent", "_components", "_children", "_trs", "_color"], 0, 4, 5, 1, 9, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_N$cacheMode", "_styleFlags", "_enableWrapText", "_N$overflow", "_lineHeight", "_spacingX", "node", "_materials"], -8, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$target"], 1, 1, 9, 1], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize"], 1, 1, 2, 4, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Toggle", ["_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_left", "_right", "_top", "_originalHeight", "node"], -3, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["84a28AxKhdB/7gAvtOJCU4p", ["node", "labelArr", "addNavN", "adddestopN", "adddestopRp", "navRp", "infonode"], 3, 1, 2, 1, 1, 1, 1, 2], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[6, 0, 1, 2, 2], [0, 0, 5, 6, 3, 4, 8, 2], [16, 0, 1, 2, 2], [1, 3, 4, 5, 1], [0, 0, 5, 7, 6, 3, 4, 8, 2], [1, 2, 0, 3, 4, 5, 3], [2, 0, 1, 4, 2, 3, 11, 12, 6], [0, 0, 7, 6, 3, 4, 8, 2], [0, 0, 5, 6, 3, 4, 2], [8, 0, 1, 2, 3, 4], [1, 2, 3, 4, 5, 2], [0, 0, 1, 5, 7, 6, 3, 4, 8, 3], [0, 0, 1, 5, 6, 3, 9, 4, 3], [0, 0, 5, 7, 3, 4, 8, 2], [13, 0, 1, 2], [8, 0, 1, 3, 3], [9, 0, 2, 3, 4, 1, 6, 6], [3, 0, 2, 3, 4, 2], [2, 0, 1, 9, 4, 10, 6, 2, 3, 5, 11, 12, 10], [2, 0, 1, 7, 2, 3, 8, 5, 11, 12, 8], [10, 0, 2], [0, 0, 7, 6, 3, 4, 2], [0, 0, 2, 5, 6, 3, 9, 4, 3], [0, 0, 5, 6, 3, 9, 4, 8, 2], [0, 0, 1, 5, 7, 3, 4, 3], [0, 0, 5, 7, 3, 4, 2], [11, 0, 1, 2, 3, 4, 5, 2], [5, 0, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 3], [12, 0, 1, 2, 3, 4, 5, 6, 1], [6, 1, 2, 1], [7, 1, 2, 3, 4, 5, 1], [7, 0, 1, 2, 3, 4, 5, 2], [1, 0, 3, 4, 5, 2], [1, 1, 3, 4, 5, 2], [1, 0, 1, 3, 4, 5, 3], [1, 1, 3, 4, 2], [1, 0, 1, 3, 4, 3], [9, 0, 1, 5, 6, 4], [3, 1, 0, 2, 3, 4, 3], [3, 0, 2, 3, 2], [14, 0, 1], [15, 0, 1, 2, 3, 4, 4], [2, 0, 1, 7, 4, 6, 2, 3, 5, 11, 12, 9], [2, 0, 1, 4, 2, 3, 8, 11, 12, 7], [2, 0, 1, 7, 4, 6, 2, 3, 8, 5, 11, 12, 10]], [[[{"name": "img_cebian<PERSON>_bili", "rect": [0, 0, 539, 791], "offset": [0, 0], "originalSize": [539, 791], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [9]], [[{"name": "img_bili_zmcbl", "rect": [0, 0, 516, 758], "offset": [0, 0], "originalSize": [516, 758], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [10]], [[[20, "PlatformBLSidebarRewardView"], [21, "PlatformBLSidebarRewardView", [-10, -11, -12], [[29, -9, [-8], -7, -6, -5, -4, [-2, -3]]], [30, -1, 0], [5, 750, 1334]], [7, "box", [-14, -15, -16, -17, -18, -19], [[14, 2, -13]], [0, "c5SDgW4b9PmaxvEq7X4pPZ", 1, 0], [5, 571, 930], [0, -70.977, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "toggle1", [-23, -24, -25, -26], [[31, -22, [4, 4292269782], -21, -20, [[9, "6b3a0+oGFdFjJeL2/WiG0xH", "changleToggle", "1", 1]]]], [0, "13bvZbLj9KiZSyUsEqdpCf", 1, 0], [5, 221, 82], [-120.5, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "toggle2", [-30, -31, -32, -33], [[32, false, -29, [4, 4292269782], -28, -27, [[9, "6b3a0+oGFdFjJeL2/WiG0xH", "changleToggle", "2", 1]]]], [0, "4251xLAFZIyKEAObAUN4Rp", 1, 0], [5, 221, 82], [120.5, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "box", [-36, -37, -38], [[5, 1, 0, -34, [14], 15], [14, 2, -35]], [0, "7eASr9U55JGLvOk+cSCJ3z", 1, 0], [5, 530, 900], [0, -44.943, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_cel", 2, [-40, -41, -42], [[10, 1, -39, [41], 42]], [0, "9bvqHjGdNFkZLLuUlrit9x", 1, 0], [5, 539, 791], [0, 107.425, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "tpdg_ty_dk_shang", 5, [-45, -46], [[5, 1, 0, -43, [6], 7], [16, 41, 20.5, 20.5, 8.463000000000022, 700, -44]], [0, "a4bwkW2UhAWp4Tjhxf/OfH", 1, 0], [5, 489, 69], [0, 407.037, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "<PERSON><PERSON>", [-50], [[5, 1, 0, -47, [12], 13], [17, 3, -49, [[9, "6b3a0+oGFdFjJeL2/WiG0xH", "onBtn", "Get", 1]], -48]], [0, "b9cV2ytnVBDKZap8oWWXRx", 1, 0], [5, 232, 82], [0, 0.464, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "New ToggleContainer", false, 2, [3, 4], [[41, -51], [42, 1, 1, 20, -52, [5, 462, 61]]], [0, "d7fXPWysRNo5ocf6sQoxJ9", 1, 0], [5, 462, 61], [0, -469.48, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "img_desk", false, 2, [-54, -55], [[10, 1, -53, [53], 54]], [0, "d4XWkKyqVEnqZKCX2eiJBr", 1, 0], [5, 516, 758], [0, 86.795, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "title", 2, [-58, -59], [[5, 1, 0, -56, [59], 60], [16, 41, 3, 3, -115.30499999999995, 700, -57]], [0, "42dpxmUQlP540YcDWAzzpS", 1, 0], [5, 565, 69], [0, 545.805, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Close", 11, [-63], [[10, 1, -60, [57], 58], [39, 1.1, 3, -62, [[15, "84a28AxKhdB/7gAvtOJCU4p", "close", 1]], -61]], [0, "12qncuothML6rYJPaUZKWe", 1, 0], [5, 52, 56], [223.346, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "<PERSON><PERSON>", [-67], [[5, 1, 0, -64, [62], 63], [17, 3, -66, [[9, "84a28AxKhdB/7gAvtOJCU4p", "onBtn", "Get", 1]], -65]], [0, "b4+ZjttGJP77xS801Lva3J", 1, 0], [5, 232, 82], [0, 0.464, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "Close", 7, [-70], [[5, 1, 0, -68, [4], 5], [40, 3, -69, [[15, "6b3a0+oGFdFjJeL2/WiG0xH", "close", 1]]]], [0, "7ckgfEWVJNtrszdeTcOYzN", 1, 0], [5, 61.99999999999999, 65], [223.346, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "bg_icon_04", 6, [-72, -73], [[3, -71, [33], 34]], [0, "b6DHDl8cFMaplN9f2Q9gTw", 1, 0], [5, 100, 100], [-90, -290.466, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg_icon_02", 6, [-75, -76], [[3, -74, [38], 39]], [0, "60xwTG4L5HUJwHyuiPG1HN", 1, 0], [5, 100, 100], [90, -290.466, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg_icon_04", 10, [-78, -79], [[3, -77, [46], 47]], [0, "balgxEMJtB+IuPSSinIJOX", 1, 0], [5, 116, 116], [-90, -180.991, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg_icon_02", 10, [-81, -82], [[3, -80, [51], 52]], [0, "908ORH+klD26Xd2ez71LHl", 1, 0], [5, 116, 120], [90, -180.991, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "maskbg", 177.98999999999998, 1, [[33, 0, -83, [0], 1], [38, 45, 750, 1334, -84]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4281542699], [5, 750, 1334]], [1, "title", 7, [[18, "侧边栏奖励", 35, 50, false, 1, 1, 1, 1, 1, -85, [2]], [2, 2, -86, [4, **********]]], [0, "370F2pvTxNwrn8V3zy2/O+", 1, 0], [5, 179, 67], [2.5, 2.191, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_zhengti", 5, [-88], [[3, -87, [9], 10]], [0, "e9u+6jN39Dfod7Lb7WTVoK", 1, 0], [5, 539, 791], [0, -32.189, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "New Label", 21, [[6, "x300", 55, false, 1, 1, -89, [8]], [2, 4, -90, [4, 4278354322]]], [0, "a45fU+SsxIZKrU72NzIuVB", 1, 0], [4, 4285064447], [5, 127.96, 58.4], [117.128, -319.529, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 8, [[43, "进入侧边栏", 33, false, false, 1, 1, 1, 1, -91, [11]], [2, 3, -92, [4, **********]]], [0, "a0s6afQ6VNqodLCOwSHMvo", 1, 0], [5, 171, 56.4], [0, 1.214, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 3, [[34, false, -93, [16], 17]], [0, "3f5nLhigJAeK29P6fnDH5x", 1, 0], [5, 221, 82]], [1, "New Label", 3, [[6, "侧边栏", 35, false, 1, 1, -94, [19]], [2, 2, -95, [4, **********]]], [0, "2f1VY2a5xEopJ7SIxdvyeQ", 1, 0], [5, 109, 54.4], [0, 6.105, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 3, [[3, -96, [20], 21]], [0, "80qj18UGxNPIgjzsD4naiY", 1, 0], [5, 65, 66], [104.521, 10.044, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [8, "Background", 4, [[35, 2, false, -97, [22], 23]], [0, "9cCwlE8dNLprTBVslsZREy", 1, 0], [5, 221, 82]], [1, "New Label", 4, [[6, "添加桌面", 35, false, 1, 1, -98, [25]], [2, 2, -99, [4, **********]]], [0, "28COaWIk1Pc6646xEMGTWT", 1, 0], [5, 144, 54.4], [0, 6.105, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 4, [[3, -100, [26], 27]], [0, "f2kXF/tI1Ee76ehn2Jpn9k", 1, 0], [5, 65, 66], [104.521, 10.044, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "New Label", 15, [[6, "灵币x100", 30, false, 1, 1, -101, [32]], [2, 3, -102, [4, **********]]], [0, "ea8O3j+wRBJpH0SlL9eQGY", 1, 0], [5, 131.05, 56.4], [0.62, -47.96, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 16, [[6, "体力x10", 30, false, 1, 1, -103, [37]], [2, 3, -104, [4, **********]]], [0, "a9QN1ImNFKNZKsjPRSzup0", 1, 0], [5, 114.37, 56.4], [0.62, -47.96, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tips", 6, [[44, "每天从侧边栏进入都可领取奖励!", 25, false, 1, 1, 2, -105, [40]], [2, 3, -106, [4, 4286019447]]], [0, "50t+CVrwtAfo2nuNBjxlts", 1, 0], [5, 407.82000000000005, 50.4], [0, -380.982, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 17, [[6, "灵币x100", 30, false, 1, 1, -107, [45]], [2, 3, -108, [4, **********]]], [0, "7fH8f5cV5KjIny6Mk3OyIP", 1, 0], [5, 128.26, 56.4], [0.62, -89.457, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 18, [[6, "灵石x500", 30, false, 1, 1, -109, [50]], [2, 3, -110, [4, 4278229260]]], [0, "78MnEhSPJIzK086GzC7uz0", 1, 0], [5, 131.57, 56.4], [0.62, -89.457, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 11, [[18, "哔哩哔哩福利", 35, 50, false, 1, 1, 1, 1, 1, -111, [55]], [2, 2, -112, [4, **********]]], [0, "d1xkdfWl1OI7FrjO4sPWTa", 1, 0], [5, 214, 67], [2.5, -2.576, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "Label", 13, [[-113, [2, 3, -114, [4, **********]]], 1, 4], [0, "84s847TBRC06GYBQrg1vT8", 1, 0], [5, 164, 56.4], [0, 1.214, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "bg", false, 1, [5], [0, "3aQ5JCUrlBML/7PcXsKi9d", 1, 0], [5, 750, 1334]], [12, "Label", false, 14, [[19, "x", 20, false, 1, 1, 1, 1, -115, [3]]], [0, "76g32DstFHmLkHo+07mNia", 1, 0], [4, **********], [5, 100, 40]], [13, "tpdg_ty_dk_yinying", 5, [8], [0, "d8mQFywFNIr4Qgksi54uvm", 1, 0], [5, 300, 132], [0, -437.518, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "bg", 1, [2], [0, "bcwHa+N2pO5ZqGQRGvcbOT", 1, 0], [5, 750, 1334]], [27, "checkmark", 3, [-116], [0, "bdZ0iLnAJCcJ/EZAxqgWfl", 1, 0], [5, 221, 96]], [36, false, 41, [18]], [28, "checkmark", false, 4, [-117], [0, "6ehL/FMCtO/oeftmNcl32Q", 1, 0], [5, 221, 96]], [37, 2, false, 43, [24]], [1, "bg", 2, [[5, 1, 0, -118, [28], 29]], [0, "83jJj4i+FAY5b5gMqZwzp9", 1, 0], [5, 570, 1000], [0, 78.035, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "shuijing", 15, [[3, -119, [30], 31]], [0, "82qSwyiP1Dc7Gz1/wYhTbG", 1, 0], [5, 47, 55]], [8, "icon_gold", 16, [[3, -120, [35], 36]], [0, "b2M7cTUH9BmJiOnr894c6L", 1, 0], [5, 54, 55]], [8, "shuijing", 17, [[3, -121, [43], 44]], [0, "d6MUkO/+hM6YjyygxotO0U", 1, 0], [5, 69, 62]], [8, "icon_gold", 18, [[3, -122, [48], 49]], [0, "68Ls+ewcVL05Rw5eqPpFOf", 1, 0], [5, 64, 59]], [12, "Label", false, 12, [[19, "x", 20, false, 1, 1, 1, 1, -123, [56]]], [0, "90H7+Y9h5E07miF0JBcTkD", 1, 0], [4, **********], [5, 100, 40]], [13, "btn_go", 2, [13], [0, "60moYTRM9L9L0mX2esoQzC", 1, 0], [5, 300, 132], [0, -352.828, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "前往", 33, false, false, 1, 1, 1, 2, 1, 36, [61]]], 0, [0, 6, 1, 0, -1, 6, 0, -2, 10, 0, 7, 26, 0, 8, 29, 0, 9, 4, 0, 10, 3, 0, -1, 52, 0, 0, 1, 0, -1, 19, 0, -2, 37, 0, -3, 40, 0, 0, 2, 0, -1, 9, 0, -2, 45, 0, -3, 6, 0, -4, 10, 0, -5, 11, 0, -6, 51, 0, 5, 42, 0, 3, 24, 0, 0, 3, 0, -1, 24, 0, -2, 41, 0, -3, 25, 0, -4, 26, 0, 5, 44, 0, 3, 27, 0, 0, 4, 0, -1, 27, 0, -2, 43, 0, -3, 28, 0, -4, 29, 0, 0, 5, 0, 0, 5, 0, -1, 7, 0, -2, 21, 0, -3, 39, 0, 0, 6, 0, -1, 15, 0, -2, 16, 0, -3, 32, 0, 0, 7, 0, 0, 7, 0, -1, 20, 0, -2, 14, 0, 0, 8, 0, 3, 8, 0, 0, 8, 0, -1, 23, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -1, 17, 0, -2, 18, 0, 0, 11, 0, 0, 11, 0, -1, 35, 0, -2, 12, 0, 0, 12, 0, 3, 12, 0, 0, 12, 0, -1, 50, 0, 0, 13, 0, 3, 13, 0, 0, 13, 0, -1, 36, 0, 0, 14, 0, 0, 14, 0, -1, 38, 0, 0, 15, 0, -1, 46, 0, -2, 30, 0, 0, 16, 0, -1, 47, 0, -2, 31, 0, 0, 17, 0, -1, 48, 0, -2, 33, 0, 0, 18, 0, -1, 49, 0, -2, 34, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, -1, 52, 0, 0, 36, 0, 0, 38, 0, -1, 42, 0, -1, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 11, 1, 2, 2, 40, 3, 2, 9, 4, 2, 9, 5, 2, 37, 8, 2, 39, 13, 2, 51, 123], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1], [0, 11, 0, 0, 0, 1, 0, 2, 0, 0, 12, 0, 0, 3, 0, 4, 0, 5, 0, 0, 0, 6, 0, 5, 0, 0, 0, 6, 0, 4, 0, 7, 0, 0, 8, 0, 13, 0, 0, 14, 0, 0, 15, 0, 7, 0, 0, 8, 0, 16, 0, 0, 17, 0, 18, 0, 0, 0, 1, 0, 2, 0, 0, 3]]]]