{"1": {"id": 1, "define": "Energy", "type": 1, "rarity": 2, "name": "体力", "desc": "有体力才能打龙", "icon": "yy/images/icon/icon_lightning"}, "2": {"id": 2, "define": "Coin", "type": 1, "rarity": 2, "name": "灵石", "desc": "升级法宝必需的资源", "icon": "yy/images/icon/icon_gold"}, "3": {"id": 3, "define": "Crystal", "type": 1, "rarity": 1, "name": "木头", "icon": "ui/lobby/shuijing"}, "4": {"id": 4, "define": "GOLD", "type": 1, "rarity": 1, "name": "灵石", "icon": "ui/lobby/icon_gold"}, "5": {"id": 5, "define": "Ved<PERSON>", "type": 1, "rarity": 2, "name": "视频", "icon": "yy/images/icon/icon_ads"}, "6": {"id": 6, "define": "<PERSON><PERSON>", "type": 1, "rarity": 1, "name": "法力值", "icon": "yy/images/icon/mana"}, "7": {"id": 7, "define": "Amethyst", "type": 1, "rarity": 2, "name": "紫水晶", "icon": "yy/images/icon/icon_duanzao"}, "8": {"id": 8, "define": "Amethyst_In", "type": 1, "rarity": 2, "name": "紫水晶", "icon": "yy/images/icon/icon_duanzao"}, "9": {"id": 9, "define": "Box", "type": 2, "rarity": 3, "name": "宝箱", "icon": "yy/images/icon/icon_duanzao"}, "10": {"id": 10, "define": "Equipfragments", "type": 2, "rarity": 2, "name": "普通法宝碎片", "desc": "随机普通法宝碎片", "icon": "yy/images/icon/icon_card_02"}, "11": {"id": 11, "define": "Diamond", "type": 1, "rarity": 4, "name": "灵币", "desc": "用于在商店购买法宝宝箱金币", "icon": "yy/images/icon/icon_diamond"}, "12": {"id": 12, "define": "BEquipfragments", "type": 2, "rarity": 3, "name": "稀有法宝碎片", "desc": "随机稀有法宝碎片", "icon": "yy/images/icon/icon_card_03"}, "13": {"id": 13, "define": "AEquipfragments", "type": 2, "rarity": 4, "name": "史诗法宝碎片", "desc": "随机史诗法宝碎片", "icon": "yy/images/icon/icon_card_04"}, "14": {"id": 14, "define": "SEquipfragments", "type": 2, "rarity": 5, "name": "传说法宝碎片", "desc": "随机传说法宝碎片", "icon": "yy/images/icon/icon_card_05"}, "15": {"id": 15, "define": "silver", "type": 1, "rarity": 1, "name": "银币", "desc": "背包里刷新装备必备", "icon": "yy/images/icon/icon_gold_02"}, "16": {"id": 16, "define": "adcoupons_in", "type": 1, "rarity": 1, "name": "局内广告券", "desc": "免一次广告", "icon": "yy/images/icon/icon_gold_02"}, "17": {"id": 17, "define": "adcoupons_out", "type": 1, "rarity": 5, "name": "免广券", "desc": "免一次广告", "icon": "yy/images/icon/icon_adcard"}, "18": {"id": 18, "define": "score", "type": 1, "rarity": 1, "name": "积分", "icon": "yy/images/icon/icon_trophy"}, "19": {"id": 19, "define": "boxkey", "type": 1, "rarity": 1, "name": "普通宝箱钥匙", "desc": "开启普通箱子的钥匙", "icon": "yy/images/icon/icon_key01"}, "20": {"id": 20, "define": "advboxkey", "type": 1, "rarity": 1, "name": "高级宝箱钥匙", "desc": "开启高级箱子的钥匙", "icon": "yy/images/icon/icon_key02"}, "21": {"id": 21, "define": "purchases", "type": 1, "rarity": 1, "name": "内购", "icon": "yy/images/icon/icon_key02"}, "22": {"id": 22, "define": "adcoupons_gift", "type": 2, "rarity": 3, "name": "免广告礼包", "desc": "开启后获得2~10张免广券", "icon": "yy/images/icon/icon_newgift"}, "23": {"id": 23, "define": "DayTaskCoin", "type": 1, "rarity": 4, "name": "每日任务活跃度", "desc": "每日任务活跃度", "icon": "yy/images/icon/icon_rw_rhy"}, "24": {"id": 24, "define": "WeekTaskCoin", "type": 1, "rarity": 5, "name": "七日嘉年华活跃度", "desc": "七日嘉年华活跃度", "icon": "yy/images/icon/icon_hyd"}, "25": {"id": 25, "define": "High_Box", "type": 2, "rarity": 3, "name": "高级宝箱", "icon": "yy/images/icon/icon_duanzao"}, "26": {"id": 26, "define": "Share", "type": 1, "rarity": 2, "name": "分享", "icon": "yy/images/icon/bt_share"}, "27": {"id": 27, "define": "buffSelect", "type": 96, "rarity": 2, "name": "弹出buff3选1界面", "icon": "yy/images/icon/bt_share"}, "28": {"id": 28, "define": "dragonBall1", "type": 100, "rarity": 2, "name": "龙珠1", "icon": "img/ModeChains/ui/seven01"}, "29": {"id": 29, "define": "dragonBall2", "type": 100, "rarity": 2, "name": "龙珠2", "icon": "img/ModeChains/ui/seven02"}, "30": {"id": 30, "define": "dragonBall3", "type": 100, "rarity": 2, "name": "龙珠3", "icon": "img/ModeChains/ui/seven03"}, "31": {"id": 31, "define": "dragonBall4", "type": 100, "rarity": 2, "name": "龙珠4", "icon": "img/ModeChains/ui/seven04"}, "32": {"id": 32, "define": "dragonBall5", "type": 100, "rarity": 2, "name": "龙珠5", "icon": "img/ModeChains/ui/seven05"}, "33": {"id": 33, "define": "dragonBall6", "type": 100, "rarity": 2, "name": "龙珠6", "icon": "img/ModeChains/ui/seven06"}, "34": {"id": 34, "define": "dragonBall7", "type": 100, "rarity": 2, "name": "龙珠7", "icon": "img/ModeChains/ui/seven07"}, "35": {"id": 35, "define": "buffDrop", "type": 99, "rarity": 2, "name": "Buff直接掉落", "icon": "yy/images/icon/bt_share"}, "36": {"id": 36, "define": "ranBuffDrop", "type": 98, "rarity": 2, "name": "随机buff直接掉落", "icon": "img/buff/img_buff_hong"}, "37": {"id": 37, "define": "boxBuffDrop", "type": 97, "rarity": 2, "name": "宝箱buff直接掉落", "icon": "yy/images/fight/icon_tcs_bx03"}, "38": {"id": 38, "define": "sbuffSelect", "type": 96, "rarity": 2, "name": "弹出金色buff3选1界面", "icon": "yy/images/fight/icon_tcs_bx03"}, "40": {"id": 40, "define": "dynamite", "type": 95, "rarity": 2, "name": "炸药桶", "icon": "img/ModeChains/icon_mt"}, "41": {"id": 41, "define": "evilBox", "type": 96, "rarity": 2, "name": "恶魔宝箱", "icon": "img/ModeChains/icon_embx"}, "42": {"id": 42, "define": "goldMine", "type": 1, "rarity": 2, "name": "挖矿龙关卡金矿石", "icon": "yy/images/icon/icon_ks"}, "43": {"id": 43, "define": "buffIdDrop", "type": 99, "rarity": 2, "name": "BuffId直接掉落", "icon": "yy/images/icon/bt_share"}, "44": {"id": 44, "define": "buffSelect21", "type": 96, "rarity": 2, "name": "弹出buff2选1界面", "icon": "img/Mode<PERSON>hai<PERSON>/img_baoshi"}, "45": {"id": 45, "define": "lvp", "type": 999, "rarity": 2, "name": "增加关卡", "icon": "yy/images/icon/icon_lvp"}, "46": {"id": 46, "define": "RMB", "type": 1, "rarity": 3, "name": "RMB充值"}, "47": {"id": 47, "type": 96, "rarity": 2, "name": "弹出buff2选1界面", "icon": "yy/images/fight/icon_tcs_bx05"}, "48": {"id": 48, "define": "drawGold", "type": 1, "rarity": 2, "name": "画线关卡金币", "icon": "yy/images/icon/icon_gold_02", "spine": "bones/ui/xxjb"}, "49": {"id": 49, "define": "lvpass", "type": 999, "rarity": 2, "name": "通关关卡", "icon": "yy/images/icon/icon_lvp"}, "50": {"id": 50, "define": "anyVideo", "type": 999, "rarity": 2, "name": "累计广告次数", "icon": "yy/images/icon/icon_ads"}, "51": {"id": 51, "define": "freeBuy", "type": 1, "rarity": 1, "name": "免费购买", "icon": "yy/images/icon/icon_free"}, "52": {"id": 52, "define": "stayTuned", "type": 999, "rarity": 1, "name": "敬请期待", "icon": "yy/images/icon/icon_rwjf"}, "53": {"id": 53, "define": "adCoin", "type": 1, "rarity": 1, "name": "广告代币", "desc": "观看广告后获得,可购买角色通行证月卡", "icon": "yy/images/icon/icon_gghb"}, "54": {"id": 54, "define": "coupon", "type": 1, "rarity": 1, "name": "挑战入场券", "desc": "可增加挑战玩法的次数", "icon": "yy/images/icon/icon_tzq"}, "55": {"id": 55, "define": "SSEquipfragments", "type": 2, "rarity": 6, "name": "SS级法宝碎片", "desc": "随机神话法宝碎片", "icon": "yy/images/icon/icon_card_05"}, "56": {"id": 56, "define": "superStar", "type": 3, "rarity": 6, "name": "星星", "desc": "星级挑战获得的道具", "icon": "yy/images/icon/icon_xxh"}, "501": {"id": 501, "type": 9, "rarity": 5, "name": "霸王龙", "desc": "7天签到获得", "icon": "img/Pet/icon/icon_cwtb_501"}, "1000": {"id": 1000, "type": 2, "rarity": 2, "name": "降魔杵碎片", "desc": "升级法宝降魔杵", "icon": "yy/images/equip/item_20011"}, "1001": {"id": 1001, "type": 2, "rarity": 2, "name": "引雷符碎片", "desc": "升级法宝引雷符", "icon": "yy/images/equip/item_20016"}, "1002": {"id": 1002, "type": 2, "rarity": 2, "name": "阴阳镜碎片", "desc": "升级法宝阴阳镜", "icon": "yy/images/equip/item_20054"}, "1003": {"id": 1003, "type": 2, "rarity": 3, "name": "闪雷鞭碎片", "desc": "升级法宝闪雷鞭", "icon": "yy/images/equip/item_20034"}, "1004": {"id": 1004, "type": 2, "rarity": 3, "name": "五毒炼心葫碎片", "desc": "升级法宝五毒炼心葫", "icon": "yy/images/equip/item_20145"}, "1005": {"id": 1005, "type": 2, "rarity": 3, "name": "暴雨梨花针碎片", "desc": "升级法宝暴雨梨花针", "icon": "yy/images/equip/item_20026"}, "1006": {"id": 1006, "type": 2, "rarity": 3, "name": "引龙幡碎片", "desc": "升级法宝引龙幡", "icon": "yy/images/equip/item_20045"}, "1007": {"id": 1007, "type": 2, "rarity": 3, "name": "业火红莲碎片", "desc": "升级法宝业火红莲", "icon": "yy/images/equip/item_20154"}, "1008": {"id": 1008, "type": 2, "rarity": 3, "name": "雷楔碎片", "desc": "升级法宝雷楔", "icon": "yy/images/equip/item_20153"}, "1009": {"id": 1009, "type": 2, "rarity": 4, "name": "玄天剑碎片", "desc": "升级法宝玄天剑", "icon": "yy/images/equip/item_20143"}, "1010": {"id": 1010, "type": 2, "rarity": 4, "name": "玉虚昆仑扇碎片", "desc": "升级法宝玉虚昆仑扇", "icon": "yy/images/equip/item_20065"}, "1011": {"id": 1011, "type": 2, "rarity": 4, "name": "裂空刃碎片", "desc": "升级法宝裂空刃", "icon": "yy/images/equip/item_20146"}, "1012": {"id": 1012, "type": 2, "rarity": 4, "name": "三光琉璃瓶碎片", "desc": "升级法宝三光琉璃瓶", "icon": "yy/images/equip/item_20084"}, "1013": {"id": 1013, "type": 2, "rarity": 4, "name": "定海神针碎片", "desc": "升级法宝定海神针", "icon": "yy/images/equip/item_20173"}, "1014": {"id": 1014, "type": 2, "rarity": 4, "name": "万灵旗碎片", "desc": "升级法宝万灵旗", "icon": "yy/images/equip/item_20046"}, "1015": {"id": 1015, "type": 2, "rarity": 4, "name": "女娲石碎片", "desc": "升级法宝女娲石", "icon": "yy/images/equip/item_20104"}, "1016": {"id": 1016, "type": 2, "rarity": 5, "name": "翻天印碎片", "desc": "升级法宝翻天印", "icon": "yy/images/equip/item_20164"}, "1017": {"id": 1017, "type": 2, "rarity": 4, "name": "风火轮碎片", "desc": "升级法宝风火轮", "icon": "yy/images/equip/item_20025"}, "1018": {"id": 1018, "type": 2, "rarity": 5, "name": "破军槊碎片", "desc": "升级法宝破军槊", "icon": "yy/images/equip/item_20174"}, "1019": {"id": 1019, "type": 2, "rarity": 5, "name": "紫金葫芦碎片", "desc": "升级法宝紫金葫芦", "icon": "yy/images/equip/item_20023"}, "1022": {"id": 1022, "type": 2, "rarity": 4, "name": "冰霜剑碎片", "desc": "升级法宝冰霜剑", "icon": "yy/images/equip/item_20062"}, "1023": {"id": 1023, "type": 2, "rarity": 5, "name": "芭蕉扇碎片", "desc": "升级法宝芭蕉扇", "icon": "yy/images/equip/item_20022"}, "1024": {"id": 1024, "type": 2, "rarity": 6, "name": "七宝琉璃树碎片", "desc": "升级法宝七宝琉璃树", "icon": "yy/images/equip/item_20074"}, "4000": {"id": 4000, "define": "<PERSON><PERSON>ard", "type": 30, "rarity": 1, "name": "局内奖励(宝石装备)", "icon": "yy/images/icon/icon_duanzao"}, "8000": {"id": 8000, "define": "passExpCard", "type": 1, "rarity": 2, "name": "普通通行证经验卡", "desc": "用于解锁普通通行证等级", "icon": "yy/images/icon/icon_jytb", "show": 0}, "8001": {"id": 8001, "define": "mainPassExpCard", "type": 1, "rarity": 2, "name": "主线通行证经验卡", "desc": "用于主线通行证升级", "icon": "yy/images/icon/icon_jytb", "show": 0}, "8002": {"id": 8002, "define": "idleExpCard", "type": 1, "rarity": 3, "name": "挂机通行证经验卡", "desc": "用于挂机通行证升级", "icon": "yy/images/icon/icon_jytb"}, "8030": {"id": 8030, "define": "expCard_petPool", "type": 1, "rarity": 2, "name": "宠物池经验卡", "desc": "用于提升宠物卡池等级", "icon": "yy/images/icon/icon_duanzao"}, "8031": {"id": 8031, "define": "petUp_fodder", "type": 1, "rarity": 2, "name": "宠物饲料", "desc": "用于提升宠物等级", "icon": "yy/images/icon/icon_cw_cwsl", "system": 11}, "8032": {"id": 8032, "define": "expCard_petUpPool", "type": 1, "rarity": 2, "name": "宠物培育池经验卡", "desc": "用于提升宠物培育池的等级", "icon": "yy/images/icon/icon_duanzao"}, "8033": {"id": 8033, "define": "pet<PERSON>gg", "type": 1, "rarity": 4, "name": "宠物蛋", "desc": "用于抽取宠物", "icon": "yy/images/icon/icon_egg1", "system": 11}, "8060": {"id": 8060, "define": "equipLvCard", "type": 1, "rarity": 2, "name": "装备升级石", "desc": "用于装备升级", "icon": "yy/images/icon/icon_st", "system": 10}, "8061": {"id": 8061, "define": "equipPhaseCard", "type": 1, "rarity": 3, "name": "装备升阶卷", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsjtz", "system": 10}, "8062": {"id": 8062, "define": "equipGradeWeaponEpic", "type": 1, "rarity": 4, "name": "装备品级材料武器紫", "desc": "用于装备升级", "icon": "yy/images/icon/icon_zbsp001", "system": 10}, "8063": {"id": 8063, "define": "equipGradeArmorEpic", "type": 1, "rarity": 4, "name": "装备品级材料防具紫", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsp002", "system": 10}, "8064": {"id": 8064, "define": "equipGradeRingEpic", "type": 1, "rarity": 4, "name": "装备品级材料戒指紫", "desc": "用于装备升级", "icon": "yy/images/icon/icon_zbsp003", "system": 10}, "8065": {"id": 8065, "define": "equipGradeNecklaceEpic", "type": 1, "rarity": 4, "name": "装备品级材料项链紫", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsp004", "system": 10}, "8066": {"id": 8066, "define": "equipGradeWeaponLegend", "type": 1, "rarity": 5, "name": "装备品级材料武器金", "desc": "用于装备升级", "icon": "yy/images/icon/icon_zbsp005", "system": 10}, "8067": {"id": 8067, "define": "equipGradeArmorLegend", "type": 1, "rarity": 5, "name": "装备品级材料防具金", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsp006", "system": 10}, "8068": {"id": 8068, "define": "equipGradeRingLegend", "type": 1, "rarity": 5, "name": "装备品级材料戒指金", "desc": "用于装备升级", "icon": "yy/images/icon/icon_zbsp007", "system": 10}, "8069": {"id": 8069, "define": "equipGradeNecklaceLegend", "type": 1, "rarity": 5, "name": "装备品级材料项链金", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsp008", "system": 10}, "8070": {"id": 8070, "define": "equipGradeWeaponMyth", "type": 1, "rarity": 6, "name": "装备品级材料武器红", "desc": "用于装备升级", "icon": "yy/images/icon/icon_zbsp009", "system": 10}, "8071": {"id": 8071, "define": "equipGradeArmorMyth", "type": 1, "rarity": 6, "name": "装备品级材料防具红", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsp010", "system": 10}, "8072": {"id": 8072, "define": "equipGradeRingMyth", "type": 1, "rarity": 6, "name": "装备品级材料戒指红", "desc": "用于装备升级", "icon": "yy/images/icon/icon_zbsp011", "system": 10}, "8073": {"id": 8073, "define": "equipGradeNecklaceMyth", "type": 1, "rarity": 6, "name": "装备品级材料项链红", "desc": "用于装备升阶", "icon": "yy/images/icon/icon_zbsp012", "system": 10}, "8074": {"id": 8074, "define": "dEquipRan", "type": 1, "rarity": 1, "name": "随机白色装备", "desc": "随机获得1件普通品质装备", "icon": "yy/images/icon/icon_zbsp013", "system": 10}, "8075": {"id": 8075, "define": "cEquipRan", "type": 1, "rarity": 2, "name": "随机绿色装备", "desc": "随机获得1件精良品质装备", "icon": "yy/images/icon/icon_zbsp014", "system": 10}, "8076": {"id": 8076, "define": "bEquipRan", "type": 1, "rarity": 3, "name": "随机蓝色装备", "desc": "随机获得1件稀有品质装备", "icon": "yy/images/icon/icon_zbsp015", "system": 10}, "8077": {"id": 8077, "define": "aEquipRan", "type": 1, "rarity": 4, "name": "随机紫色装备", "desc": "随机获得1件史诗品质装备", "icon": "yy/images/icon/icon_zbsp011", "system": 10}, "8078": {"id": 8078, "define": "asEquipRan", "type": 1, "rarity": 4, "name": "随机s紫色装备", "desc": "随机获得1件s史诗品质装备", "icon": "yy/images/icon/icon_zbsp016", "system": 10}, "8079": {"id": 8079, "define": "equipBox", "type": 1, "rarity": 2, "name": "商店宝箱", "desc": "商店宝箱", "icon": "yy/images/icon/icon_zbsp009", "system": 10}, "8080": {"id": 8080, "define": "asEquipBox", "type": 1, "rarity": 4, "name": "紫色s装备宝箱", "desc": "s史诗品质装备自选宝箱", "icon": "yy/images/icon/icon_zbsp012", "system": 10}, "8081": {"id": 8081, "define": "c<PERSON><PERSON>", "type": 1, "rarity": 2, "name": "木钥匙", "desc": "可开启木质装备宝箱", "icon": "yy/images/icon/icon_ys01", "system": 10}, "8082": {"id": 8082, "define": "b<PERSON><PERSON>", "type": 1, "rarity": 3, "name": "银钥匙", "desc": "可开启银质装备宝箱", "icon": "yy/images/icon/icon_ys02", "system": 10}, "8083": {"id": 8083, "define": "a<PERSON><PERSON>", "type": 1, "rarity": 4, "name": "金钥匙", "desc": "可开启黄金装备宝箱", "icon": "yy/images/icon/icon_ys03", "system": 10}, "8084": {"id": 8084, "define": "s<PERSON>ey", "type": 1, "rarity": 5, "name": "幻彩钥匙", "desc": "可用于神器召唤", "icon": "yy/images/icon/icon_ys04", "system": 10}, "8090": {"id": 8090, "define": "roleUp_dust", "type": 1, "rarity": 2, "name": "升级卷", "desc": "可升级角色等级", "icon": "yy/images/icon/juan", "system": 12}, "10000": {"id": 10000, "define": "item", "type": 999, "rarity": 1, "name": "道具"}, "10001": {"id": 10001, "define": "roleEquip", "type": 999, "rarity": 1, "name": "装备"}, "10002": {"id": 10002, "define": "pet", "type": 999, "rarity": 1, "name": "宠物"}, "10003": {"id": 10003, "define": "role", "type": 999, "rarity": 1, "name": "角色"}, "10004": {"id": 10004, "define": "skill", "type": 999, "rarity": 1, "name": "法宝"}, "10005": {"id": 10005, "define": "newplayergift", "type": 999, "rarity": 1, "name": "新手礼包"}, "40000": {"id": 40000, "define": "rF_xs", "type": 2, "rarity": 2, "name": "小仙碎片", "desc": "小仙升级碎片", "icon": "yy/images/icon/item_1021", "system": 12}, "40001": {"id": 40001, "define": "rF_nvs", "type": 2, "rarity": 3, "name": "女武神碎片", "desc": "女武神升级碎片", "icon": "yy/images/icon/icon_jssp03", "system": 12}, "40002": {"id": 40002, "define": "rF_zss", "type": 2, "rarity": 4, "name": "咒术师碎片", "desc": "咒术师升级碎片", "icon": "yy/images/icon/icon_jssp05", "system": 12}, "40003": {"id": 40003, "define": "rF_mwk", "type": 2, "rarity": 5, "name": "魔悟空碎片", "desc": "魔悟空升级碎片", "icon": "yy/images/icon/icon_jssp01", "system": 12}, "40004": {"id": 40004, "define": "rF_mnz", "type": 2, "rarity": 5, "name": "魔哪吒碎片", "desc": "魔哪吒升级碎片", "icon": "yy/images/icon/icon_jssp02", "system": 12}, "40020": {"id": 40020, "define": "rF_ran", "type": 2, "rarity": 4, "name": "随机角色碎片", "desc": "获得随机角色碎片", "icon": "yy/images/icon/icon_jswh", "system": 12}}