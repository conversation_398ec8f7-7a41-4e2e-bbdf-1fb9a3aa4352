[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "1ayaeT0/dLgbJVItl0dXFT", "30DcQwi35MSKNlPeS11GtI", "27RTpTPjZN9KOAJJnUPPk1"], ["node", "_spriteFrame", "root", "finger", "clickRange", "desc", "myLight", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], -1, 4, 5, 9, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "_target"], 0, 1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_opacity", "_groupIndex", "_parent", "_components", "_prefab", "_color", "_contentSize"], 0, 1, 12, 4, 5, 5], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_color", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 5, 7], ["95d2aM4Sd5GOLwR8DXZyd0i", ["node"], 3, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["_N$inverted", "node", "_materials"], 2, 1, 3], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "node"], -1, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["d7f6eC1831ALq1TXI56RVED", ["node", "myLight", "desc", "clickRange", "finger"], 3, 1, 1, 1, 1, 1]], [[3, 0, 1, 2, 2], [1, 0, 2, 3, 4, 2], [4, 0, 2], [0, 0, 1, 8, 6, 4, 5, 3], [0, 0, 7, 8, 6, 4, 10, 5, 2], [0, 0, 2, 7, 6, 4, 10, 5, 3], [0, 0, 1, 7, 8, 4, 5, 9, 3], [0, 0, 7, 6, 4, 5, 2], [0, 0, 3, 2, 7, 6, 4, 5, 9, 4], [0, 0, 1, 7, 6, 4, 5, 9, 3], [5, 0, 1, 2, 3, 4, 5, 6, 7, 4], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [1, 1, 0, 2, 3, 4, 3], [1, 2, 3, 4, 1], [7, 0, 1], [2, 0, 1, 2, 3, 4], [2, 0, 1, 2, 3, 4, 4], [3, 1, 2, 1], [8, 0, 1], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5], [11, 0, 1, 1], [12, 0, 1, 2, 3, 4], [13, 0, 1, 2, 3, 4, 1]], [[2, "GuideView"], [3, "GuideView", 1, [-7, -8, -9, -10, -11], [[23, -6, -5, -4, -3, -2]], [17, -1, 0], [5, 750, 1334]], [10, "mask", 200, 1, 1, [[[1, 0, -12, [0], 1], -13, [15, 45, 750, 1334, -14]], 4, 1, 4], [0, "49bCmI4bVPtIKi03u5LCss", 1, 0], [4, 4278190080], [5, 750, 1334]], [4, "clickRange", 1, [-16], [[19, true, -15, [4]]], [0, "01iTRUJ4NMK6NFBJbbblyT", 1, 0], [4, 4278190080], [5, 100, 100]], [5, "New Node", 120, 3, [[18, -17], [1, 0, -18, [2], 3], [16, 45, 750, 1334, -19, 1]], [0, "ffQUEw4plPu7K7Tb0O4pto", 1, 0], [4, 4278190080], [5, 750, 1334]], [6, "box", 1, 1, [-20, -21], [0, "54gCbPKrhDF7Ouy/pCbfub", 1, 0], [5, 600, 160], [0, -688.458, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button", false, 150, 1, [[1, 0, -22, [7], 8], [21, -23, [[22, "d7f6eC1831ALq1TXI56RVED", "onBtn", "onButtonEvent", 1]]]], [0, "8bAn2nM2pGq4TP/82BvG7I", 1, 0], [5, 100, 100], [982.022, -387.787, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "finger", 1, 1, [[13, -24, [9], 10]], [0, "d08sGN0AJD04H4VtExLKMQ", 1, 0], [5, 85, 83], [687.258, -326.068, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 2], [7, "bg", 5, [[12, 1, 0, -25, [5], 6]], [0, "e6zq/uE1BITIWovNXATaGL", 1, 0], [5, 600, 160]], [11, "New Label", 1, 5, [-26], [0, "8cXMguXTBNlZcaEF9CkPFd", 1, 0], [4, 4280953386], [5, 550, 90.39999999999999], [0, 0, 0.5], [-278.044, 3.522, 0, 0, 0, 0, 1, 1, 1, 1]], [20, false, "1111111111111111111111111111111111111", 34, 550, 10]], 0, [0, 2, 1, 0, 3, 7, 0, 4, 3, 0, 5, 11, 0, 6, 8, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, 0, 2, 0, -2, 8, 0, 0, 2, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, -2, 10, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 9, 0, -1, 11, 0, 7, 1, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1], [0, 2, 0, 1, 0, 0, 3, 0, 1, 0, 4]]