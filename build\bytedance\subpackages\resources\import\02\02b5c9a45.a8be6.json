[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "a0rvROFd5IVr9UoEF7ZIum", "f8npR6F8ZIZoCp3cKXjJQz", "29FYIk+N1GYaeWH/q1NxQO", "6b5LN99vlF67Zb1lpBR5r9", "bcbsbRY4hPnLpbMfLXFE2W", "a2MjXRFdtLlYQ5ouAFv/+R", "3ae55zevNLro44lk9vad+V", "c1f4UDWZJNUJmmvN1IN/rK", "90fsDA8khGAITvfMGnM0E5", "f3p84uzd5EVLXvLuhlyoRY", "42QVRm3a1H/pA7ZY/1EuYY", "a8lPnHTihBmrRgij+r2Olk", "a6kz4txaVEkZUKUBQoucZO", "73jGpne/9JUI/171Zur5Pr", "6fgBCSDDdPMInvyNlggls2", "3ae7efMv1CLq2ilvUY/tQi", "7fb1UFcOFF4JjCil9b/Ker", "33NszIJ/lEloRKC7SNEu19", "e8b1aIrxdA/pJaKw9C8qpe"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_parent", "_N$disabledSprite", "root", "costgray", "ad<PERSON>y", "adcount", "adget", "coincount", "coinget", "coincost", "data", "_normalMaterial", "_grayMaterial"], [["cc.Node", ["_name", "_active", "_opacity", "_groupIndex", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], -2, 4, 5, 9, 1, 2, 7, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_enableWrapText", "_N$cacheMode", "_styleFlags", "_lineHeight", "_N$overflow", "node", "_materials", "_N$file"], -7, 1, 3, 6], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_right", "alignMode", "_top", "_bottom", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["8b8d1cDbpRMdK3Tq1f8qmZ6", ["node", "coincost", "coinget", "coincount", "adget", "adcount", "ad<PERSON>y", "costgray"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1]], [[5, 0, 1, 2, 2], [0, 0, 8, 7, 5, 6, 10, 2], [4, 2, 3, 4, 1], [13, 0, 1, 2, 2], [9, 0, 1, 2, 3, 4, 5, 2], [4, 1, 0, 2, 3, 4, 3], [0, 0, 8, 9, 7, 5, 6, 10, 2], [1, 0, 3, 5, 1, 7, 4, 2, 6, 10, 11, 9], [12, 0, 1, 2, 3], [0, 0, 1, 2, 8, 7, 5, 11, 6, 4], [4, 0, 2, 3, 4, 2], [3, 0, 1, 2, 8, 4], [7, 0, 2], [0, 0, 3, 9, 7, 5, 6, 3], [0, 0, 9, 7, 5, 6, 2], [0, 0, 4, 9, 7, 5, 6, 10, 3], [0, 0, 2, 8, 7, 5, 11, 6, 3], [0, 0, 8, 9, 7, 5, 6, 2], [0, 0, 7, 5, 6, 2], [0, 0, 1, 8, 7, 5, 6, 10, 3], [0, 0, 8, 9, 5, 6, 10, 2], [0, 0, 1, 8, 7, 5, 11, 6, 10, 3], [8, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 1], [5, 1, 2, 1], [11, 0, 1, 2, 2], [3, 5, 0, 3, 4, 6, 7, 1, 2, 8, 9], [3, 0, 8, 2], [3, 0, 3, 4, 1, 2, 8, 6], [1, 0, 3, 8, 1, 7, 4, 2, 10, 11, 12, 8], [1, 0, 3, 5, 1, 4, 2, 6, 10, 11, 12, 8], [1, 0, 5, 1, 4, 2, 9, 6, 10, 11, 8], [1, 0, 3, 5, 1, 7, 2, 6, 10, 11, 8], [6, 0, 2, 3, 4, 5, 6, 7, 2], [6, 1, 0, 2, 3, 4, 5, 6, 3], [14, 0, 1]], [[[{"name": "icon_gmtl_item_02", "rect": [0, 3, 116, 100], "offset": [0, 0], "originalSize": [116, 106], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [6]], [[[12, "Shop_GetEnergy"], [13, "Shop_GetEnergy", 1, [-10, -11], [[23, -9, -8, -7, -6, -5, -4, -3, -2]], [24, -1, 0], [5, 750, 1334]], [14, "content", [-13, -14, -15, -16, -17], [[5, 1, 0, -12, [37], 38]], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 590, 520]], [6, "btn_adgetengry", 2, [-19, -20, -21, -22], [[25, "BagAdGetEnergy", -18, [[8, "8b8d1cDbpRMdK3Tq1f8qmZ6", "adGet", 1]]]], [0, "bbvLaANW5OUoKOhBbl29ED", 1, 0], [5, 225, 344], [130, -34.195, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "btn_buyengry", 2, [-24, -25, -26, -27], [-23], [0, "0ezMEeuotAtrdydJtxVu0y", 1, 0], [5, 226, 344], [-130, -34.195, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "Background", 3, [-29, -30, -31, -32], [[5, 1, 0, -28, [18], 19]], [0, "2caTtajRVOvaQanF3kftdf", 1, 0], [5, 226, 344], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "Background", 4, [-34, -35, -36, -37], [[5, 1, 0, -33, [31], 32]], [0, "36hZ4mmWhEcrIsgt0kJB9q", 1, 0], [5, 226, 344], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [15, "Background", 512, [-40], [[2, -38, [7], 8], [26, 0, 45, 8, 8, 7.5, 7.5, 100, 40, -39]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 64, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "maskbg", 140, 1, [[27, 45, -41], [10, 0, -42, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [17, "bg", 1, [2], [[11, 45, 750, 1334, -43]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [18, "Label_title", [[29, "购买体力", 48, 48, false, 1, 1, 1, -44, [4], 5], [3, 3, -45, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 198, 66.47999999999999]], [6, "btnclose", 2, [7], [[33, 3, -46, [[8, "8b8d1cDbpRMdK3Tq1f8qmZ6", "close", 1]], [4, 4293322470], [4, 3363338360], 7, 9]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [236.654, 217.04399999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 5, [[2, -47, [14], 15], [35, -48]], [0, "efZiOO5qRNJJ1lD14OEwuv", 1, 0], [5, 50, 52], [-61.789, -128.251, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 5, [[30, "获得", 36, false, false, 1, 1, 1, -49, [16], 17], [3, 2, -50, [4, 4278190080]]], [0, "0bcdU1BtdPhqtxCoMMU63/", 1, 0], [5, 76, 54.4], [26.957, -125.972, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "val", 3, [[-51, [3, 2, -52, [4, 4278190080]]], 1, 4], [0, "d3ipOPRp5B7LWe1CmhuQge", 1, 0], [5, 61.89, 54.4], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "dailycount", 3, [[-53, [3, 2, -54, [4, 4278190080]]], 1, 4], [0, "5fwhl+Gq5EB6WG9Sjso9YY", 1, 0], [5, 213.31, 54.4], [0, 130.904, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "gray", false, 97, 3, [[5, 1, 0, -55, [22], 23], [28, 45, -0.5, -0.5, 100, 100, -56]], [0, "8aABbg2KhO6YW/2vi+27mm", 1, 0], [4, 4278190080], [5, 226, 344]], [4, "Label", 6, [[-57, [3, 2, -58, [4, 4278190080]]], 1, 4], [0, "5aKrebbFZIN5HO3OrCjLlw", 1, 0], [5, 57.28, 54.4], [32.675, -127.002, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "val", 4, [[-59, [3, 2, -60, [4, 4278190080]]], 1, 4], [0, "b4NcLExiVJRKzgQCvxRCNv", 1, 0], [5, 61.89, 54.4], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "dailycount", 4, [[-61, [3, 2, -62, [4, 4278190080]]], 1, 4], [0, "e3JTbH+klBxJoWnRaEzITL", 1, 0], [5, 213.31, 54.4], [0, 130, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "gray", false, 97, 4, [[10, 0, -63, [35], 36], [11, 45, 100, 100, -64]], [0, "caU8WsEQ1IKpiEbV9eiEvX", 1, 0], [4, 4278190080], [5, 226, 344]], [19, "bg copy", false, 2, [[5, 1, 0, -65, [2], 3]], [0, "99+xIi+WdFnI61zwjB7qZe", 1, 0], [5, 525, 380], [0, -35.31099999999992, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "title_zhua<PERSON><PERSON>", 2, [10], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 590, 82], [0, 218.461, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Label", false, 7, [[31, "返回", false, false, 1, 1, 1, 1, -66, [6]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "frame_gmtl_00", 5, [[2, -67, [10], 11]], [0, "c4KwDNTrVJuKLHhyrQ/wcl", 1, 0], [5, 138, 135], [0, 38.823, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_gmtl_item_01", 5, [[2, -68, [12], 13]], [0, "d7OIa5D5dKIJtUVA2uN13/", 1, 0], [5, 93, 86], [0, 38.823, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "x10", 36, false, false, 1, 1, 1, 1, 14, [20]], [7, "今日剩余%d次", 32, false, false, 1, 1, 1, 1, 15, [21]], [1, "frame_gmtl_00", 6, [[2, -69, [24], 25]], [0, "db2yuUwVRBQ6MEU+5XD4dv", 1, 0], [5, 138, 135], [0, 37.714, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_gmtl_item_02", 6, [[2, -70, [26], 27]], [0, "aeTd7StRdDVbi2oMkpC8Yx", 1, 0], [5, 116, 102], [0, 37.714, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 6, [[2, -71, [28], 29]], [0, "edNukg/3pH16ho30AiMctY", 1, 0], [5, 47, 55], [-50.276, -129.281, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [32, "100", 32, false, false, 1, 1, 1, 17, [30]], [7, "x30", 36, false, false, 1, 1, 1, 1, 18, [33]], [7, "今日剩余%d次", 32, false, false, 1, 1, 1, 1, 19, [34]], [34, 1.1, 3, 4, [[8, "8b8d1cDbpRMdK3Tq1f8qmZ6", "costGet", 1]], [4, 4293322470], [4, 3363338360], 4]], 0, [0, 6, 1, 0, 7, 34, 0, 8, 3, 0, 9, 27, 0, 10, 26, 0, 11, 33, 0, 12, 32, 0, 13, 31, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, 0, 2, 0, -1, 21, 0, -2, 22, 0, -3, 11, 0, -4, 3, 0, -5, 4, 0, 0, 3, 0, -1, 5, 0, -2, 14, 0, -3, 15, 0, -4, 16, 0, -1, 34, 0, -1, 6, 0, -2, 18, 0, -3, 19, 0, -4, 20, 0, 0, 5, 0, -1, 24, 0, -2, 25, 0, -3, 12, 0, -4, 13, 0, 0, 6, 0, -1, 28, 0, -2, 29, 0, -3, 30, 0, -4, 17, 0, 0, 7, 0, 0, 7, 0, -1, 23, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 26, 0, 0, 14, 0, -1, 27, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 31, 0, 0, 17, 0, -1, 32, 0, 0, 18, 0, -1, 33, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 14, 1, 2, 4, 9, 7, 4, 11, 10, 4, 22, 71], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 27, 31, 32, 33, 34, 34, 34], [-1, 1, -1, 1, -1, 2, -1, -1, 1, 5, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, 1, 2, 2, 2, 2, 2, 15, 16, 5], [0, 7, 0, 8, 0, 3, 0, 0, 9, 4, 0, 5, 0, 10, 0, 11, 0, 3, 0, 2, 0, 0, 0, 2, 0, 5, 0, 12, 0, 13, 0, 0, 2, 0, 0, 0, 14, 0, 15, 1, 1, 1, 1, 1, 16, 17, 4]], [[{"name": "frame_gmtl_00", "rect": [0, 0, 138, 135], "offset": [0, 0.5], "originalSize": [138, 136], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [18]], [[{"name": "icon_gmtl_item_01", "rect": [11, 11, 93, 86], "offset": [-0.5, -1], "originalSize": [116, 106], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [19]], [[{"name": "bg_tili_02", "rect": [0, 0, 226, 344], "offset": [0, 0], "originalSize": [226, 344], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [20]]]]