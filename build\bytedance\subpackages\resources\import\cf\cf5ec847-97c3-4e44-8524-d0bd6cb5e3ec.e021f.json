[1, ["43yLK9z9tF7apY/SsocPOG"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "buff3", ".bin", "\nbuff3.png\nsize: 792,523\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbbb\n  rotate: true\n  xy: 521, 43\n  size: 154, 75\n  orig: 154, 77\n  offset: 0, 1\n  index: -1\nbbb_00001\n  rotate: true\n  xy: 407, 2\n  size: 7, 12\n  orig: 154, 77\n  offset: 28, 64\n  index: -1\nbbb_00002\n  rotate: false\n  xy: 741, 77\n  size: 42, 60\n  orig: 154, 77\n  offset: 2, 16\n  index: -1\nbbb_00003\n  rotate: false\n  xy: 670, 32\n  size: 50, 66\n  orig: 154, 77\n  offset: 1, 10\n  index: -1\nbbb_00004\n  rotate: false\n  xy: 598, 13\n  size: 70, 72\n  orig: 154, 77\n  offset: 1, 4\n  index: -1\nbbb_00005\n  rotate: true\n  xy: 668, 100\n  size: 112, 71\n  orig: 154, 77\n  offset: 1, 4\n  index: -1\nbbb_00006\n  rotate: true\n  xy: 598, 87\n  size: 115, 68\n  orig: 154, 77\n  offset: 1, 2\n  index: -1\nbbb_00007\n  rotate: true\n  xy: 588, 204\n  size: 123, 68\n  orig: 154, 77\n  offset: 2, 1\n  index: -1\nbbb_00008\n  rotate: true\n  xy: 518, 199\n  size: 126, 68\n  orig: 154, 77\n  offset: 17, 1\n  index: -1\nbbb_00009\n  rotate: true\n  xy: 658, 214\n  size: 113, 68\n  orig: 154, 77\n  offset: 34, 1\n  index: -1\nbbb_00010\n  rotate: true\n  xy: 339, 6\n  size: 99, 66\n  orig: 154, 77\n  offset: 54, 2\n  index: -1\nbbb_00011\n  rotate: true\n  xy: 741, 139\n  size: 80, 47\n  orig: 154, 77\n  offset: 74, 2\n  index: -1\nbbb_00012\n  rotate: false\n  xy: 728, 286\n  size: 62, 44\n  orig: 154, 77\n  offset: 92, 2\n  index: -1\nbbb_00013\n  rotate: false\n  xy: 521, 6\n  size: 41, 35\n  orig: 154, 77\n  offset: 113, 2\n  index: -1\nbbb_00014\n  rotate: true\n  xy: 108, 105\n  size: 16, 15\n  orig: 154, 77\n  offset: 137, 6\n  index: -1\ngx_01\n  rotate: true\n  xy: 183, 7\n  size: 98, 154\n  orig: 123, 261\n  offset: 15, 16\n  index: -1\ngx_03\n  rotate: false\n  xy: 410, 11\n  size: 109, 180\n  orig: 123, 261\n  offset: 8, 12\n  index: -1\ngx_05\n  rotate: false\n  xy: 563, 329\n  size: 111, 192\n  orig: 123, 261\n  offset: 7, 11\n  index: -1\ngx_07\n  rotate: false\n  xy: 450, 327\n  size: 111, 194\n  orig: 123, 261\n  offset: 7, 11\n  index: -1\ngx_09\n  rotate: true\n  xy: 221, 107\n  size: 111, 187\n  orig: 123, 261\n  offset: 7, 13\n  index: -1\ngx_11\n  rotate: false\n  xy: 676, 332\n  size: 111, 189\n  orig: 123, 261\n  offset: 7, 13\n  index: -1\ngx_13\n  rotate: false\n  xy: 224, 324\n  size: 111, 197\n  orig: 123, 261\n  offset: 7, 14\n  index: -1\ngx_15\n  rotate: false\n  xy: 108, 123\n  size: 111, 189\n  orig: 123, 261\n  offset: 7, 14\n  index: -1\ngx_17\n  rotate: false\n  xy: 337, 324\n  size: 111, 197\n  orig: 123, 261\n  offset: 7, 11\n  index: -1\ngx_19\n  rotate: false\n  xy: 112, 318\n  size: 110, 203\n  orig: 123, 261\n  offset: 7, 11\n  index: -1\ngx_21\n  rotate: false\n  xy: 2, 314\n  size: 108, 207\n  orig: 123, 261\n  offset: 7, 12\n  index: -1\ngx_23\n  rotate: false\n  xy: 2, 105\n  size: 104, 207\n  orig: 123, 261\n  offset: 8, 12\n  index: -1\ngx_25\n  rotate: true\n  xy: 224, 220\n  size: 102, 199\n  orig: 123, 261\n  offset: 10, 13\n  index: -1\ngx_27\n  rotate: true\n  xy: 2, 2\n  size: 101, 179\n  orig: 123, 261\n  offset: 10, 16\n  index: -1\ngx_29\n  rotate: false\n  xy: 425, 193\n  size: 91, 129\n  orig: 123, 261\n  offset: 17, 22\n  index: -1\nxh\n  rotate: false\n  xy: 564, 16\n  size: 25, 25\n  orig: 25, 25\n  offset: 0, 0\n  index: -1\nyanwu\n  rotate: false\n  xy: 728, 221\n  size: 60, 63\n  orig: 63, 63\n  offset: 3, 0\n  index: -1\n", ["buff3.png"], [0]], -1], 0, 0, [0], [-1], [0]]