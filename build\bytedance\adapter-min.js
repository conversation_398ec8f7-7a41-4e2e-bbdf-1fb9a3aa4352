!function r(o,i,a){function c(t,e){if(!i[t]){if(!o[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(u)return u(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}n=i[t]={exports:{}},o[t][0].call(n.exports,function(e){return c(o[t][1][e]||e)},n,n.exports,r,o,i,a)}return i[t].exports}for(var u="function"==typeof require&&require,e=0;e<a.length;e++)c(a[e]);return c}({1:[function(e,t,n){},{}],2:[function(e,t,n){"use strict";function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r=window.fsUtils,o=r.getUserDataPath,i=r.readJsonSync,c=r.makeDirSync,a=r.writeFileSync,f=r.copyFile,d=r.downloadFile,u=r.writeFile,s=r.deleteFile,p=r.rmdirSync,h=r.unzip,m=r.isOutOfStorage,y=!1,b=null,g=!1,v=[],w=[],_=!1,E=0,S=/^https?:\/\/.*/;cc.assetManager.cacheManager=t.exports={cacheDir:"gamecaches",cachedFileName:"cacheList.json",cacheEnabled:!0,autoClear:!0,cacheInterval:500,deleteInterval:500,writeFileInterval:2e3,outOfStorage:!1,tempFiles:null,cachedFiles:null,cacheQueue:{},version:"1.0",getCache:function(e){return this.cachedFiles.has(e)?this.cachedFiles.get(e).url:""},getTemp:function(e){return this.tempFiles.has(e)?this.tempFiles.get(e):""},init:function(){this.cacheDir=o()+"/"+this.cacheDir;var e=this.cacheDir+"/"+this.cachedFileName,t=i(e);t instanceof Error||!t.version?(t instanceof Error||p(this.cacheDir,!0),this.cachedFiles=new cc.AssetManager.Cache,c(this.cacheDir,!0),a(e,JSON.stringify({files:this.cachedFiles._map,version:this.version}),"utf8")):this.cachedFiles=new cc.AssetManager.Cache(t.files),this.tempFiles=new cc.AssetManager.Cache},updateLastTime:function(e){this.cachedFiles.has(e)&&(this.cachedFiles.get(e).lastTime=Date.now())},_write:function(){g=!(b=null),u(this.cacheDir+"/"+this.cachedFileName,JSON.stringify({files:this.cachedFiles._map,version:this.version}),"utf8",function(){g=!1;for(var e=0,t=w.length;e<t;e++)w[e]();w.length=0,w.push.apply(w,v),v.length=0})},writeCacheFile:function(e){!b&&(b=setTimeout(this._write.bind(this),this.writeFileInterval),!0===g)?e&&v.push(e):e&&w.push(e)},_cache:function(){var t,e,n=this,r=this;for(t in this.cacheQueue){s=void 0,e=function(e){if(y=!1,e){if(m(e.message))return r.outOfStorage=!0,void(r.autoClear&&r.clearLRU())}else r.cachedFiles.add(t,{bundle:a,url:u,lastTime:c}),delete r.cacheQueue[t],r.writeCacheFile();cc.js.isEmptyObject(r.cacheQueue)||(y=!0,setTimeout(r._cache.bind(r),r.cacheInterval))},s=n.cacheQueue[t],o=s.srcUrl,i=s.isCopy,a=s.cacheBundleRoot,c=Date.now().toString(),u="",u=(a?"".concat(n.cacheDir,"/").concat(a,"/"):"".concat(n.cacheDir,"/")).concat(c).concat(E++).concat(cc.path.extname(t)),i?f(o,u,e):d(o,u,null,e);var o,i,a,c,u,s={v:void 0};if("object"===l(s))return s.v}y=!1},cacheFile:function(e,t,n,r,o){!(n=void 0!==n?n:this.cacheEnabled)||this.cacheQueue[e]||this.cachedFiles.has(e)||(this.cacheQueue[e]={srcUrl:t,cacheBundleRoot:r,isCopy:o},y)||(y=!0,this.outOfStorage?y=!1:setTimeout(this._cache.bind(this),this.cacheInterval))},clearCache:function(){var t=this,e=(p(this.cacheDir,!0),this.cachedFiles=new cc.AssetManager.Cache,c(this.cacheDir,!0),this.cacheDir+"/"+this.cachedFileName);this.outOfStorage=!1,a(e,JSON.stringify({files:this.cachedFiles._map,version:this.version}),"utf8"),cc.assetManager.bundles.forEach(function(e){S.test(e.base)&&t.makeBundleFolder(e.name)})},clearLRU:function(){if(!_){_=!0;var n=[],r=this;if(this.cachedFiles.forEach(function(t,e){"internal"===t.bundle||r._isZipFile(e)&&cc.assetManager.bundles.find(function(e){return-1!==e.base.indexOf(t.url)})||n.push({originUrl:e,url:t.url,lastTime:t.lastTime})}),n.sort(function(e,t){return e.lastTime-t.lastTime}),n.length=Math.floor(n.length/3),0!==n.length){for(var e=0,t=n.length;e<t;e++)this.cachedFiles.remove(n[e].originUrl);this.writeCacheFile(function(){setTimeout(function e(){var t=n.pop();r._isZipFile(t.originUrl)?(p(t.url,!0),r._deleteFileCB()):s(t.url,r._deleteFileCB.bind(r)),0<n.length?setTimeout(e,r.deleteInterval):_=!1},r.deleteInterval)})}}},removeCache:function(e){var t,n;this.cachedFiles.has(e)&&(n=(t=this).cachedFiles.remove(e).url,this.writeCacheFile(function(){t._isZipFile(e)?(p(n,!0),t._deleteFileCB()):s(n,t._deleteFileCB.bind(t))}))},_deleteFileCB:function(e){e||(this.outOfStorage=!1)},makeBundleFolder:function(e){c(this.cacheDir+"/"+e,!0)},unzipAndCacheBundle:function(t,e,n,r){var o=Date.now().toString(),i="".concat(this.cacheDir,"/").concat(n,"/").concat(o).concat(E++),a=this;c(i,!0),h(e,i,function(e){e?(p(i,!0),m(e.message)&&(a.outOfStorage=!0,a.autoClear)&&a.clearLRU(),r&&r(e)):(a.cachedFiles.add(t,{bundle:n,url:i,lastTime:o}),a.writeCacheFile(),r&&r(null,i))})},_isZipFile:function(e){return".zip"===e.slice(-4)}}},{}],3:[function(e,L,F){"use strict";var r,f,d=e("../cache-manager"),e=window.fsUtils,p=e.fs,h=e.downloadFile,o=e.readText,i=e.readArrayBuffer,a=e.readJson,m=e.loadSubpackage,y=e.getUserDataPath,c=e.exists,b=/^https?:\/\/.*/,g={},e=cc.assetManager.downloader,t=cc.assetManager.parser,n=cc.assetManager.presets,v=__globalAdapter.isSubContext,w=(e.maxConcurrency=8,e.maxRequestsPerFrame=64,n.scene.maxConcurrency=10,n.scene.maxRequestsPerFrame=64,{}),_={};function u(e,t,n){"function"==typeof t&&(n=t,t=null),b.test(e)?n&&n(new Error("Can not load remote scripts")):(__cocos_require__(e),n&&n(null))}function s(e,t,n){"function"==typeof t&&(n=t,t=null);t=cc.sys,t=(t.platform===t.TAOBAO||t.platform===t.TAOBAO_MINIGAME?window.document:document).createElement("audio");t.src=e,n&&n(null,t)}function l(r,t,o,e,i){var n=R(r,o);n.inLocal?t(n.url,o,i):n.inCache?(d.updateLastTime(r),t(n.url,o,function(e,t){e&&d.removeCache(r),i(e,t)})):h(r,null,o.header,e,function(e,n){e?i(e,null):t(n,o,function(e,t){e||(d.tempFiles.add(r,n),d.cacheFile(r,n,o.cacheEnabled,o.__cacheBundleRoot__,!0)),i(e,t)})})}function E(e,t,n){i(e,n)}function S(e,t,n){o(e,n)}function O(e,t,n){a(e,n)}var T=v?function(e,t,n){e=(e=R(e,t).url).slice(r.length+1);t=__cocos_require__(cc.path.changeExtname(e,".js"));n&&n(null,t)}:function(e,t,n){l(e,O,t,t.onFileProgress,n)},n=v?function(e,t,n){n(null,"Arial")}:function(e,t,n){n(null,__globalAdapter.loadFont(e)||"Arial")};function N(t,e,n){c(t,function(e){e?n(null,t):n(new Error("file ".concat(t," does not exist!")))})}function A(e,t,n){l(e,N,t,t.onFileProgress,n)}function x(e,n,r){i(e,function(e,t){if(e)return r(e);C(t,n,r)})}function M(e,n,r){i(e,function(e,t){if(e)return r(e);j(t,n,r)})}function P(e,n,r){i(e,function(e,t){if(e)return r(e);k(t,n,r)})}var C=t.parsePVRTex,j=t.parseASTCTex,k=t.parsePKMTex;var D,I=v?function(e,t,n){n(null,e=R(e,t).url)}:A,R=(e.downloadDomAudio=s,e.downloadScript=u,t.parsePVRTex=x,t.parsePKMTex=P,t.parseASTCTex=M,e.register({".js":u,".mp3":A,".ogg":A,".wav":A,".m4a":A,".png":I,".jpg":I,".bmp":I,".jpeg":I,".gif":I,".ico":I,".tiff":I,".image":I,".webp":I,".pvr":A,".pkm":A,".astc":A,".font":A,".eot":A,".ttf":A,".woff":A,".svg":A,".ttc":A,".txt":A,".xml":A,".vsh":A,".fsh":A,".atlas":A,".tmx":A,".tsx":A,".plist":A,".fnt":A,".json":T,".ExportJson":A,".binary":A,".bin":A,".dbbin":A,".skel":A,".mp4":A,".avi":A,".mov":A,".mpg":A,".mpeg":A,".rm":A,".rmvb":A,bundle:function(e,t,a){var n,o=cc.path.basename(e),r=t.version||cc.assetManager.downloader.bundleVers[o],i=r?"".concat(r,"."):"";if(w[o]){var c=((n=cc.sys).platform===n.TAOBAO_MINIGAME?"":"subpackages/").concat(o,"/config.").concat(i,"json"),u=function(){T(c,t,function(e,t){var n,r;(n=t)&&((r=cc.sys).platform===r.TAOBAO_MINIGAME?n.base="".concat(o,"/"):n.base="subpackages/".concat(o,"/")),a(e,t)})};if(g[o])return u();m(o,t.onFileProgress,function(e){e?a(e,null):(g[o]=!0,u())})}else{b.test(e)||!v&&e.startsWith(y())?(l=e,s="src/scripts/".concat(o,"/index.js"),d.makeBundleFolder(o)):_[o]?(l="".concat(f,"remote/").concat(o),s="src/scripts/".concat(o,"/index.js"),d.makeBundleFolder(o)):(l="assets/".concat(o),s="assets/".concat(o,"/index.js")),__cocos_require__(s),t.__cacheBundleRoot__=o;var s,l,c="".concat(l,"/config.").concat(r?r+".":"","json");T(c,t,function(e,n){var r,o,i;e?a&&a(e):n.isZip?(e=n.zipVersion,e="".concat(l,"/res.").concat(e?e+".":"","zip"),r=e,o=t,i=function(e,t){e?a&&a(e):(n.base=t+"/res/",(e=cc.sys).platform===e.ALIPAY_GAME&&e.os===e.OS_ANDROID&&p.accessSync({path:e=t+"res/"}).success&&(n.base=e),a&&a(null,n))},(e=d.cachedFiles.get(r))?(d.updateLastTime(r),i&&i(null,e.url)):b.test(r)?h(r,null,o.header,o.onFileProgress,function(e,t){e?i&&i(e):d.unzipAndCacheBundle(r,t,o.__cacheBundleRoot__,i)}):d.unzipAndCacheBundle(r,r,o.__cacheBundleRoot__,i)):(n.base=l+"/",a&&a(null,n))})}},default:function(e,t,n){l(e,S,t,t.onFileProgress,n)}}),t.register({".png":e.downloadDomImage,".jpg":e.downloadDomImage,".bmp":e.downloadDomImage,".jpeg":e.downloadDomImage,".gif":e.downloadDomImage,".ico":e.downloadDomImage,".tiff":e.downloadDomImage,".image":e.downloadDomImage,".webp":e.downloadDomImage,".pvr":x,".pkm":P,".astc":M,".font":n,".eot":n,".ttf":n,".woff":n,".svg":n,".ttc":n,".mp3":s,".ogg":s,".wav":s,".m4a":s,".txt":S,".xml":S,".vsh":S,".fsh":S,".atlas":S,".tmx":S,".tsx":S,".fnt":S,".plist":function(e,t,r){o(e,function(e,t){var n=null;e||(n=cc.plistParser.parse(t))||(e=new Error("parse failed")),r&&r(e,n)})},".binary":E,".bin":E,".dbbin":E,".skel":E,".ExportJson":O}),v?function(e,t){return{url:e=b.test(e)?e:r+"/"+e}}:function(e,t){var n=!1,r=!1;return!e.startsWith(y())&&b.test(e)?t.reload||((t=d.cachedFiles.get(e))?(r=!0,e=t.url):(t=d.tempFiles.get(e))&&(n=!0,e=t)):n=!0,{url:e,inLocal:n,inCache:r}});v?(D=cc.assetManager.init,cc.assetManager.init=function(e){D.call(cc.assetManager,e),r=e.subContextRoot||""}):(cc.assetManager.transformPipeline.append(function(e){for(var t=e.output=e.input,n=0,r=t.length;n<r;n++){var o=t[n],i=o.options;o.config?i.__cacheBundleRoot__=o.config.name:"bundle"!==o.ext&&(i.cacheEnabled=void 0!==i.cacheEnabled&&i.cacheEnabled)}}),D=cc.assetManager.init,cc.assetManager.init=function(e){D.call(cc.assetManager,e),e.subpackages&&e.subpackages.forEach(function(e){return w[e]="subpackages/"+e}),e.remoteBundles&&e.remoteBundles.forEach(function(e){return _[e]=!0}),(f=e.server||"")&&!f.endsWith("/")&&(f+="/"),d.init()})},{"../cache-manager":2}],4:[function(e,t,n){"use strict";var r,o=cc._Audio;o&&(r=o.prototype.getDuration,Object.assign(o.prototype,{_createElement:function(){var e=this._src._nativeAsset;this._element||(this._element=__globalAdapter.createInnerAudioContext()),this._element.src=e.src},destroy:function(){this._element&&(this._element.destroy(),this._element=null)},setCurrentTime:function(e){var t=this;this._src&&this._src._ensureLoaded(function(){t._element.seek(e)})},stop:function(){var e=this;this._src&&this._src._ensureLoaded(function(){e._element.seek(0),e._element.stop(),e._unbindEnded(),e.emit("stop"),e._state=o.State.STOPPED})},_bindEnded:function(){var e=this._element;e&&e.onEnded&&!this._onended._binded&&(this._onended._binded=!0,e.onEnded(this._onended))},_unbindEnded:function(){var e=this._element;e&&e.offEnded&&this._onended._binded&&(this._onended._binded=!1,e.offEnded)&&e.offEnded(this._onended)},getDuration:function(){return r.call(this)||(this._element?this._element.duration:0)},_touchToPlay:function(){},_forceUpdatingState:function(){}}))},{}],5:[function(e,t,n){"use strict";cc&&cc.audioEngine&&(cc.audioEngine._maxAudioInstance=10)},{}],6:[function(e,t,n){"use strict";var r=cc.internal.inputManager,o=window.__globalAdapter;Object.assign(r,{setAccelerometerEnabled:function(e){var t=cc.director.getScheduler();t.enableForTarget(this),e?(this._registerAccelerometerEvent(),t.scheduleUpdate(this)):(this._unregisterAccelerometerEvent(),t.unscheduleUpdate(this))},_registerAccelerometerEvent:function(){this._accelCurTime=0;var t=this;this._acceleration=new cc.Acceleration,o.startAccelerometer(function(e){t._acceleration.x=e.x,t._acceleration.y=e.y,t._acceleration.z=e.y})},_unregisterAccelerometerEvent:function(){this._accelCurTime=0,o.stopAccelerometer()}})},{}],7:[function(e,t,n){"use strict";function r(){s.call(this),this._eventListeners={onKeyboardInput:null,onKeyboardConfirm:null,onKeyboardComplete:null}}var o,i,a,c,u,s;cc&&cc.EditBox&&(o=cc.EditBox,i=cc.js,a=o.KeyboardReturnType,u=c=null,s=o._ImplClass,i.extend(r,s),o._ImplClass=r,Object.assign(r.prototype,{init:function(e){e?this._delegate=e:cc.error("EditBox init failed")},beginEditing:function(){var t=this;this._editing||this._ensureKeyboardHide(function(){var e=t._delegate;t._showKeyboard(),t._registerKeyboardEvent(),t._editing=!0,u=t,e.editBoxEditingDidBegan()})},endEditing:function(){this._hideKeyboard();var e=this._eventListeners;e.onKeyboardComplete&&e.onKeyboardComplete()},_registerKeyboardEvent:function(){var n=this,r=this._delegate,e=this._eventListeners;e.onKeyboardInput=function(e){r._string!==e.value&&r.editBoxTextChanged(e.value)},e.onKeyboardConfirm=function(e){r.editBoxEditingReturn();var t=n._eventListeners;t.onKeyboardComplete&&t.onKeyboardComplete(e)},e.onKeyboardComplete=function(e){n._editing=!1,u=null,n._unregisterKeyboardEvent(),e&&e.value&&r._string!==e.value&&r.editBoxTextChanged(e.value),r.editBoxEditingDidEnded()},__globalAdapter.onKeyboardInput(e.onKeyboardInput),__globalAdapter.onKeyboardConfirm(e.onKeyboardConfirm),__globalAdapter.onKeyboardComplete(e.onKeyboardComplete)},_unregisterKeyboardEvent:function(){var e=this._eventListeners;e.onKeyboardInput&&(__globalAdapter.offKeyboardInput(e.onKeyboardInput),e.onKeyboardInput=null),e.onKeyboardConfirm&&(__globalAdapter.offKeyboardConfirm(e.onKeyboardConfirm),e.onKeyboardConfirm=null),e.onKeyboardComplete&&(__globalAdapter.offKeyboardComplete(e.onKeyboardComplete),e.onKeyboardComplete=null)},_otherEditing:function(){return!!u&&u!==this&&u._editing},_ensureKeyboardHide:function(e){var t=this._otherEditing();if(!t&&!c)return e();c&&clearTimeout(c),t&&u.endEditing(),c=setTimeout(function(){c=null,e()},600)},_showKeyboard:function(){var e=this._delegate,t=e.inputMode===o.InputMode.ANY,n=e.maxLength<0?65535:e.maxLength;__globalAdapter.showKeyboard({defaultValue:e._string,maxLength:n,multiple:t,confirmHold:!1,confirmType:function(e){switch(e){case a.DEFAULT:case a.DONE:return"done";case a.SEND:return"send";case a.SEARCH:return"search";case a.GO:return"go";case a.NEXT:return"next"}return"done"}(e.returnType),success:function(e){},fail:function(e){cc.warn(e.errMsg)}})},_hideKeyboard:function(){__globalAdapter.hideKeyboard({success:function(e){},fail:function(e){cc.warn(e.errMsg)}})}}))},{}],8:[function(e,t,n){"use strict";var r=cc.internal.inputManager,o=cc.renderer,i=cc.game,a=cc.dynamicAtlasManager,c=i.run;Object.assign(i,{_banRunningMainLoop:__globalAdapter.isSubContext,_firstSceneLaunched:!1,run:function(){var e=this;cc.director.once(cc.Director.EVENT_AFTER_SCENE_LAUNCH,function(){e._firstSceneLaunched=!0}),c.apply(this,arguments)},setFrameRate:function(e){this.config.frameRate=e,__globalAdapter.setPreferredFramesPerSecond?__globalAdapter.setPreferredFramesPerSecond(e):(this._intervalId&&window.cancelAnimFrame(this._intervalId),this._intervalId=0,this._paused=!0,this._setAnimFrame(),this._runMainLoop())},_runMainLoop:function(){var e,t,n,r,o,i;this._banRunningMainLoop||(n=(e=this).config,r=cc.director,o=!0,i=n.frameRate,cc.debug.setDisplayStats(n.showFPS),t=function(){e._paused||(e._intervalId=window.requestAnimFrame(t),30===i&&!__globalAdapter.setPreferredFramesPerSecond&&(o=!o))||r.mainLoop()},e._intervalId=window.requestAnimFrame(t),e._paused=!1)},_initRenderer:function(){var e,t;this._rendererInitialized||((e=cc.sys).platform===e.TAOBAO||e.platform===e.TAOBAO_MINIGAME?this.frame=this.container=window.document.createElement("DIV"):this.frame=this.container=document.createElement("DIV"),e=__globalAdapter.isSubContext?window.sharedCanvas||__globalAdapter.getSharedCanvas():e.platform===e.TAOBAO||e.platform===e.TAOBAO_MINIGAME?window.canvas:canvas,this.canvas=e,this._determineRenderType(),this.renderType===this.RENDER_TYPE_WEBGL&&(t={stencil:!0,antialias:cc.macro.ENABLE_WEBGL_ANTIALIAS,alpha:cc.macro.ENABLE_TRANSPARENT_CANVAS,preserveDrawingBuffer:!1},o.initWebGL(e,t),this._renderContext=o.device._gl,!cc.macro.CLEANUP_IMAGE_CACHE)&&a&&(a.enabled=!0),this._renderContext||(this.renderType=this.RENDER_TYPE_CANVAS,o.initCanvas(e),this._renderContext=o.device._ctx),this._rendererInitialized=!0)},_initEvents:function(){var e=cc.sys,t=(this.config.registerSystemEvent&&r.registerSystemEvent(this.canvas),!1);e.platform!==e.BYTEDANCE_GAME&&(__globalAdapter.onAudioInterruptionEnd&&__globalAdapter.onAudioInterruptionEnd(function(){cc.audioEngine&&cc.audioEngine._restore()}),__globalAdapter.onAudioInterruptionBegin)&&__globalAdapter.onAudioInterruptionBegin(function(){cc.audioEngine&&cc.audioEngine._break()}),__globalAdapter.onShow&&__globalAdapter.onShow(function(e){t&&(t=!1,i.renderType===i.RENDER_TYPE_WEBGL&&i._renderContext.finish(),i.emit(i.EVENT_SHOW,e))}),__globalAdapter.onHide&&__globalAdapter.onHide(function(){t||(t=!0,i.emit(i.EVENT_HIDE))}),this.on(i.EVENT_HIDE,function(){i.pause()}),this.on(i.EVENT_SHOW,function(){i.resume()})},end:function(){}})},{}],9:[function(e,t,n){"use strict";var r=cc.internal.inputManager,o={left:0,top:0,width:window.innerWidth,height:window.innerHeight};r&&Object.assign(r,{_updateCanvasBoundingRect:function(){},registerSystemEvent:function(e){if(!this._isRegisterEvent){this._glView=cc.view;var t,n=this,r={onTouchStart:this.handleTouchesBegin,onTouchMove:this.handleTouchesMove,onTouchEnd:this.handleTouchesEnd,onTouchCancel:this.handleTouchesCancel};for(t in r)!function(e){var t=r[e];__globalAdapter[e](function(e){e.changedTouches&&t.call(n,n.getTouchesByEvent(e,o))})}(t);this._isRegisterEvent=!0}}})},{}],10:[function(e,t,n){"use strict";Object.assign(cc.screen,{autoFullScreen:function(e,t){}})},{}],11:[function(e,t,n){"use strict";var r=cc.Texture2D;r&&Object.assign(r.prototype,{initWithElement:function(e){e&&(this._image=e,this.handleLoadedTexture())}})},{}],12:[function(e,t,n){"use strict";t.exports=function(e,t){var n=(t=t||__globalAdapter.getSystemInfoSync()).language||"",r=t.system||"iOS",o=t.platform||"iOS",n=(e.isNative=!1,e.isBrowser=!1,e.isMobile=!0,e.language=n.substr(0,2),e.languageCode=n.toLowerCase(),"android"===(o=o.toLowerCase())?e.os=e.OS_ANDROID:"ios"===o&&(e.os=e.OS_IOS),r=r.toLowerCase(),/[\d\.]+/.exec(r="android p"===r?"android p 9.0":r)),o=(e.osVersion=n?n[0]:r,e.osMainVersion=parseInt(e.osVersion),e.browserType=null,e.browserVersion=null,t.windowWidth),n=t.windowHeight,r=t.pixelRatio||1,t=(e.windowPixelResolution={width:r*o,height:r*n},e.localStorage=window.localStorage,!__globalAdapter.isSubContext),o=!1;try{o=document.createElement("canvas").toDataURL("image/webp").startsWith("data:image/webp")}catch(e){}e.capabilities={canvas:!0,opengl:!!t,webp:o},e.__audioSupport={ONLY_ONE:!1,WEB_AUDIO:!1,DELAY_CREATE_CTX:!1,format:[".mp3"]}}},{}],13:[function(e,t,n){"use strict";t.exports=function(e){e._setupContainer=function(e,t,n){var r=e._devicePixelRatio=1;e.isRetinaEnabled()&&(r=e._devicePixelRatio=Math.min(e._maxPixelRatio,window.devicePixelRatio||1)),__globalAdapter.isSubContext||(n*=r,(e=cc.game.canvas).width===(t*=r)&&e.height===n)||(e.width=t,e.height=n)}}},{}],14:[function(e,t,n){"use strict";t.exports=function(e){Object.assign(e,{_adjustViewportMeta:function(){},setRealPixelResolution:function(e,t,n){this.setDesignResolutionSize(e,t,n)},enableAutoFullScreen:function(e){cc.warn("cc.view.enableAutoFullScreen() is not supported on minigame platform.")},isAutoFullScreenEnabled:function(){return!1},setCanvasSize:function(){cc.warn("cc.view.setCanvasSize() is not supported on minigame platform.")},setFrameSize:function(){cc.warn("frame size is readonly on minigame platform.")},_initFrameSize:function(){var e,t=this._frameSize;__globalAdapter.isSubContext?(e=window.sharedCanvas||__globalAdapter.getSharedCanvas(),t.width=e.width,t.height=e.height):(t.width=window.innerWidth,t.height=window.innerHeight)}})}},{}],15:[function(e,t,n){"use strict";var r=window.__globalAdapter;Object.assign(r,{adaptSys:e("./BaseSystemInfo"),adaptView:e("./View"),adaptContainerStrategy:e("./ContainerStrategy")})},{"./BaseSystemInfo":12,"./ContainerStrategy":13,"./View":14}],16:[function(e,t,n){"use strict";e("./Audio"),e("./AudioEngine"),e("./DeviceMotionEvent"),e("./Editbox"),e("./Game"),e("./InputManager"),e("./AssetManager"),e("./Screen"),e("./Texture2D"),e("./misc")},{"./AssetManager":3,"./Audio":4,"./AudioEngine":5,"./DeviceMotionEvent":6,"./Editbox":7,"./Game":8,"./InputManager":9,"./Screen":10,"./Texture2D":11,"./misc":17}],17:[function(e,t,n){"use strict";cc.macro.DOWNLOAD_MAX_CONCURRENT=10},{}],18:[function(e,t,n){"use strict";var r={cloneMethod:function(e,t,n,r){t[n]&&(e[r=r||n]=t[n].bind(t))},encode:function(e){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",n=String(e),r="",o=0,i=void 0;n.charAt(0|o)||(t="=",o%1);){o+=.75;var a=n.charCodeAt(o);if(255<a)throw new Error('"btoa" failed');i=i<<8|a;r+=t.charAt(63&i>>8-o%1*8)}return r},decode:function(e){for(var t,n,r="",o=String(e).replace(/[=]+$/,""),i=0,a=0;n=o.charAt(a);)a+=1,~(n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(n))&&(t=i%4?64*t+n:n,i++%4)&&(r+=String.fromCharCode(255&t>>(-2*i&6)));return r},arrayBufferToBase64:function(e){return r.encode(r.arrayBufferToString(e))},base64ToArrayBuffer:function(e){return r.stringToArrayBuffer(r.decode(e))},arrayBufferToString:function(e){for(var t="",n=new Uint8Array(e),r=n.byteLength,o=0;o<r;o++)t+=String.fromCharCode(n[o]);return t},stringToArrayBuffer:function(e){for(var t=e.length,n=new Uint8Array(t),r=0;r<t;r++)n[r]=e.charCodeAt(r);return n.buffer}};t.exports=r},{}],19:[function(e,t,n){"use strict";function r(e){this.options=e||{locator:{}}}function s(){this.cdata=!1}function l(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function f(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function o(e,t,n){return"string"==typeof e?e.substr(t,n):e.length>=t+n||t?new java.lang.String(e,t,n)+"":e}function d(e,t){(e.currentElement||e.doc).appendChild(t)}r.prototype.parseFromString=function(e,t){var n=this.options,r=new h,o=n.domBuilder||new s,i=n.errorHandler,a=n.locator,c=n.xmlns||{},t=/\/x?html?$/.test(t),u=t?p.entityMap:{lt:"<",gt:">",amp:"&",quot:'"',apos:"'"};return a&&o.setDocumentLocator(a),r.errorHandler=function(r,e,o){if(!r){if(e instanceof s)return e;r=e}var i={},a=r instanceof Function;function t(t){var n=r[t];!n&&a&&(n=2==r.length?function(e){r(t,e)}:r),i[t]=n?function(e){n("[xmldom "+t+"]\t"+e+f(o))}:function(){}}return o=o||{},t("warning"),t("error"),t("fatalError"),i}(i,o,a),r.domBuilder=n.domBuilder||o,t&&(c[""]="http://www.w3.org/1999/xhtml"),c.xml=c.xml||"http://www.w3.org/XML/1998/namespace",e?r.parse(e,c,u):r.errorHandler.error("invalid doc source"),o.doc},s.prototype={startDocument:function(){this.doc=(new i).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,n,r){var o=this.doc,i=o.createElementNS(e,n||t),a=r.length;d(this,i),this.currentElement=i,this.locator&&l(this.locator,i);for(var c=0;c<a;c++){var e=r.getURI(c),u=r.getValue(c),n=r.getQName(c),s=o.createAttributeNS(e,n);this.locator&&l(r.getLocator(c),s),s.value=s.nodeValue=u,i.setAttributeNode(s)}},endElement:function(e,t,n){var r=this.currentElement;r.tagName;this.currentElement=r.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){e=this.doc.createProcessingInstruction(e,t);this.locator&&l(this.locator,e),d(this,e)},ignorableWhitespace:function(e,t,n){},characters:function(e,t,n){var r;(e=o.apply(this,arguments))&&(r=this.cdata?this.doc.createCDATASection(e):this.doc.createTextNode(e),this.currentElement?this.currentElement.appendChild(r):/^\s*$/.test(e)&&this.doc.appendChild(r),this.locator)&&l(this.locator,r)},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,n){e=o.apply(this,arguments);e=this.doc.createComment(e);this.locator&&l(this.locator,e),d(this,e)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,n){var r=this.doc.implementation;r&&r.createDocumentType&&(r=r.createDocumentType(e,t,n),this.locator&&l(this.locator,r),d(this,r))},warning:function(e){console.warn("[xmldom warning]\t"+e,f(this.locator))},error:function(e){console.error("[xmldom error]\t"+e,f(this.locator))},fatalError:function(e){throw console.error("[xmldom fatalError]\t"+e,f(this.locator)),e}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){s.prototype[e]=function(){return null}});var p=e("./entities"),h=e("./sax").XMLReader,i=n.DOMImplementation=e("./dom").DOMImplementation;n.XMLSerializer=e("./dom").XMLSerializer,n.DOMParser=r},{"./dom":20,"./entities":21,"./sax":22}],20:[function(L,F,e){"use strict";function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t(e,t){for(var n in e)t[n]=e[n]}function n(e,t){var n=e.prototype;if(!(n instanceof t)){var r,o=function(){};for(r in o.prototype=t.prototype,o=new o,n)o[r]=n[r];e.prototype=n=o}n.constructor!=e&&("function"!=typeof e&&console.error("unknow Class:"+e),n.constructor=e)}var r,o={},i=(o.ELEMENT_NODE=1,o.ATTRIBUTE_NODE=2,o.TEXT_NODE=3,o.CDATA_SECTION_NODE=4,o.ENTITY_REFERENCE_NODE=5,o.ENTITY_NODE=6,o.PROCESSING_INSTRUCTION_NODE=7,o.COMMENT_NODE=8,o.DOCUMENT_NODE=9,o.DOCUMENT_TYPE_NODE=10,o.DOCUMENT_FRAGMENT_NODE=11,o.NOTATION_NODE=12,{}),a={};i.INDEX_SIZE_ERR=(a[1]="Index size error",1),i.DOMSTRING_SIZE_ERR=(a[2]="DOMString size error",2),i.HIERARCHY_REQUEST_ERR=(a[3]="Hierarchy request error",3),i.WRONG_DOCUMENT_ERR=(a[4]="Wrong document",4),i.INVALID_CHARACTER_ERR=(a[5]="Invalid character",5),i.NO_DATA_ALLOWED_ERR=(a[6]="No data allowed",6),i.NO_MODIFICATION_ALLOWED_ERR=(a[7]="No modification allowed",7),i.NOT_FOUND_ERR=(a[8]="Not found",8),i.NOT_SUPPORTED_ERR=(a[9]="Not supported",9),i.INUSE_ATTRIBUTE_ERR=(a[10]="Attribute in use",10),i.INVALID_STATE_ERR=(a[11]="Invalid state",11),i.SYNTAX_ERR=(a[12]="Syntax error",12),i.INVALID_MODIFICATION_ERR=(a[13]="Invalid modification",13),i.NAMESPACE_ERR=(a[14]="Invalid namespace",14),i.INVALID_ACCESS_ERR=(a[15]="Invalid access",15);function c(e,t){var n;return t instanceof Error?n=t:(n=this,Error.call(this,a[e]),this.message=a[e],Error.captureStackTrace&&Error.captureStackTrace(this,c)),n.code=e,t&&(this.message=this.message+": "+t),n}function p(){}function u(e,t){this._node=e,this._refresh=t,s(this)}function s(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!=t){var n,r=e._refresh(e._node);for(n in V(e,"length",r.length),r)e[n]=r[n];e._inc=t}}function h(){}function l(e,t){for(var n=e.length;n--;)if(e[n]===t)return n}function f(e,t,n,r){r?t[l(t,r)]=n:t[t.length++]=n,e&&(t=(n.ownerElement=e).ownerDocument)&&(r&&_(t,e,r),r=e,e=n,(n=t)&&n._inc++,"http://www.w3.org/2000/xmlns/"==e.namespaceURI)&&(r._nsMap[e.prefix?e.localName:""]=e.value)}function m(e,t,n){var r=l(t,n);if(!(0<=r))throw c(8,new Error(e.tagName+"@"+n));for(var o,i=t.length-1;r<i;)t[r]=t[++r];t.length=i,e&&(o=e.ownerDocument)&&(_(o,e,n),n.ownerElement=null)}function y(e){if(this._features={},e)for(var t in e)this._features=e[t]}function b(){}function g(e){return("<"==e?"&lt;":">"==e&&"&gt;")||("&"==e?"&amp;":'"'==e&&"&quot;")||"&#"+e.charCodeAt()+";"}function v(e,t){if(t(e))return 1;if(e=e.firstChild)do{if(v(e,t))return 1}while(e=e.nextSibling)}function w(){}function _(e,t,n){e&&e._inc++,"http://www.w3.org/2000/xmlns/"==n.namespaceURI&&delete t._nsMap[n.prefix?n.localName:""]}function E(e,t,n){if(e&&e._inc){e._inc++;var r=t.childNodes;if(n)r[r.length++]=n;else{for(var o=t.firstChild,i=0;o;)o=(r[i++]=o).nextSibling;r.length=i}}}function S(e,t){var n=t.previousSibling,r=t.nextSibling;return n?n.nextSibling=r:e.firstChild=r,r?r.previousSibling=n:e.lastChild=n,E(e.ownerDocument,e),t}function O(e,t,n){var r=t.parentNode;if(r&&r.removeChild(t),11===t.nodeType){var o=t.firstChild;if(null==o)return t;var i=t.lastChild}else o=i=t;r=n?n.previousSibling:e.lastChild;for(o.previousSibling=r,i.nextSibling=n,r?r.nextSibling=o:e.firstChild=o,null==n?e.lastChild=i:n.previousSibling=i;o.parentNode=e,o!==i&&(o=o.nextSibling););return E(e.ownerDocument||e,e),11==t.nodeType&&(t.firstChild=t.lastChild=null),t}function T(){this._nsMap={}}function N(){}function A(){}function x(){}function M(){}function P(){}function C(){}function k(){}function B(){}function j(){}function D(){}function I(){}function H(){}function U(e,t){var n,r=[],o=9==this.nodeType&&this.documentElement||this,i=o.prefix,a=o.namespaceURI;return R(this,r,e,t,n=a&&null==i&&null==o.lookupPrefix(a)?[{namespace:a,prefix:null}]:n),r.join("")}function W(e,t,n){var r=e.prefix||"",o=e.namespaceURI;if((r||o)&&("xml"!==r||"http://www.w3.org/XML/1998/namespace"!==o)&&"http://www.w3.org/2000/xmlns/"!=o){for(var i=n.length;i--;){var a=n[i];if(a.prefix==r)return a.namespace!=o}return 1}}function R(e,t,n,r,o){if(r){if(!(e=r(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case 1:o=o||[];var i=e.attributes,a=i.length,c=e.firstChild,u=e.tagName;n="http://www.w3.org/1999/xhtml"===e.namespaceURI||n,t.push("<",u);for(var s=0;s<a;s++)"xmlns"==(l=i.item(s)).prefix?o.push({prefix:l.localName,namespace:l.value}):"xmlns"==l.nodeName&&o.push({prefix:"",namespace:l.value});for(var l,f,d,s=0;s<a;s++)W(l=i.item(s),0,o)&&(f=l.prefix||"",d=l.namespaceURI,t.push(f?" xmlns:"+f:" xmlns",'="',d,'"'),o.push({prefix:f,namespace:d})),R(l,t,n,r,o);if(W(e,0,o)&&(f=e.prefix||"",d=e.namespaceURI,t.push(f?" xmlns:"+f:" xmlns",'="',d,'"'),o.push({prefix:f,namespace:d})),c||n&&!/^(?:meta|link|img|br|hr|input)$/i.test(u)){if(t.push(">"),n&&/^script$/i.test(u))for(;c;)c.data?t.push(c.data):R(c,t,n,r,o),c=c.nextSibling;else for(;c;)R(c,t,n,r,o),c=c.nextSibling;t.push("</",u,">")}else t.push("/>");return;case 9:case 11:for(c=e.firstChild;c;)R(c,t,n,r,o),c=c.nextSibling;return;case 2:return t.push(" ",e.name,'="',e.value.replace(/[<&"]/g,g),'"');case 3:return t.push(e.data.replace(/[<&]/g,g));case 4:return t.push("<![CDATA[",e.data,"]]>");case 8:return t.push("\x3c!--",e.data,"--\x3e");case 10:var u=e.publicId,p=e.systemId;return t.push("<!DOCTYPE ",e.name),void(u?(t.push(' PUBLIC "',u),p&&"."!=p&&t.push('" "',p),t.push('">')):p&&"."!=p?t.push(' SYSTEM "',p,'">'):((u=e.internalSubset)&&t.push(" [",u,"]"),t.push(">")));case 7:return t.push("<?",e.target," ",e.data,"?>");case 5:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function V(e,t,n){e[t]=n}c.prototype=Error.prototype,t(i,c),p.prototype={length:0,item:function(e){return this[e]||null},toString:function(e,t){for(var n=[],r=0;r<this.length;r++)R(this[r],n,e,t);return n.join("")}},u.prototype.item=function(e){return s(this),this[e]},n(u,p),h.prototype={length:0,item:p.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var n=this[t];if(n.nodeName==e)return n}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new c(10);t=this.getNamedItem(e.nodeName);return f(this._ownerElement,this,e,t),t},setNamedItemNS:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new c(10);return t=this.getNamedItemNS(e.namespaceURI,e.localName),f(this._ownerElement,this,e,t),t},removeNamedItem:function(e){e=this.getNamedItem(e);return m(this._ownerElement,this,e),e},removeNamedItemNS:function(e,t){e=this.getNamedItemNS(e,t);return m(this._ownerElement,this,e),e},getNamedItemNS:function(e,t){for(var n=this.length;n--;){var r=this[n];if(r.localName==t&&r.namespaceURI==e)return r}return null}},y.prototype={hasFeature:function(e,t){e=this._features[e.toLowerCase()];return!(!e||t&&!(t in e))},createDocument:function(e,t,n){var r=new w;return r.implementation=this,r.childNodes=new p,(r.doctype=n)&&r.appendChild(n),t&&(n=r.createElementNS(e,t),r.appendChild(n)),r},createDocumentType:function(e,t,n){var r=new C;return r.name=e,r.nodeName=e,r.publicId=t,r.systemId=n,r}},b.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return O(this,e,t)},replaceChild:function(e,t){this.insertBefore(e,t),t&&this.removeChild(t)},removeChild:function(e){return S(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,n,r){var o=new n.constructor;for(var i in n){var a=n[i];"object"!=d(a)&&a!=o[i]&&(o[i]=a)}n.childNodes&&(o.childNodes=new p);o.ownerDocument=t;switch(o.nodeType){case 1:var c=n.attributes,u=o.attributes=new h,s=c.length;u._ownerElement=o;for(var l=0;l<s;l++)o.setAttributeNode(e(t,c.item(l),!0));break;case 2:r=!0}if(r)for(var f=n.firstChild;f;)o.appendChild(e(t,f,r)),f=f.nextSibling;return o}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&3==t.nodeType&&3==e.nodeType?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return 0<this.attributes.length},lookupPrefix:function(e){for(var t=this;t;){var n=t._nsMap;if(n)for(var r in n)if(n[r]==e)return r;t=2==t.nodeType?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var n=t._nsMap;if(n&&e in n)return n[e];t=2==t.nodeType?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},t(o,b),t(o,b.prototype),w.prototype={nodeName:"#document",nodeType:9,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(11==e.nodeType)for(var n=e.firstChild;n;){var r=n.nextSibling;this.insertBefore(n,t),n=r}else null==this.documentElement&&1==e.nodeType&&(this.documentElement=e),O(this,e,t),e.ownerDocument=this;return e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),S(this,e)},importNode:function(e,t){return function e(t,n,r){var o;switch(n.nodeType){case 1:(o=n.cloneNode(!1)).ownerDocument=t;case 11:break;case 2:r=!0}o=o||n.cloneNode(!1);o.ownerDocument=t;o.parentNode=null;if(r)for(var i=n.firstChild;i;)o.appendChild(e(t,i,r)),i=i.nextSibling;return o}(this,e,t)},getElementById:function(t){var n=null;return v(this.documentElement,function(e){if(1==e.nodeType&&e.getAttribute("id")==t)return n=e,!0}),n},createElement:function(e){var t=new T;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.childNodes=new p,(t.attributes=new h)._ownerElement=t},createDocumentFragment:function(){var e=new D;return e.ownerDocument=this,e.childNodes=new p,e},createTextNode:function(e){var t=new x;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new M;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new P;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var n=new I;return n.ownerDocument=this,n.tagName=n.target=e,n.nodeValue=n.data=t,n},createAttribute:function(e){var t=new N;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new j;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var n=new T,r=t.split(":"),o=n.attributes=new h;return n.childNodes=new p,n.ownerDocument=this,n.nodeName=t,n.tagName=t,n.namespaceURI=e,2==r.length?(n.prefix=r[0],n.localName=r[1]):n.localName=t,o._ownerElement=n},createAttributeNS:function(e,t){var n=new N,r=t.split(":");return n.ownerDocument=this,n.nodeName=t,n.name=t,n.namespaceURI=e,n.specified=!0,2==r.length?(n.prefix=r[0],n.localName=r[1]):n.localName=t,n}},n(w,b),w.prototype.getElementsByTagName=(T.prototype={nodeType:1,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){e=this.getAttributeNode(e);return e&&e.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){e=this.ownerDocument.createAttribute(e);e.value=e.nodeValue=""+t,this.setAttributeNode(e)},removeAttribute:function(e){e=this.getAttributeNode(e);e&&this.removeAttributeNode(e)},appendChild:function(e){return 11===e.nodeType?this.insertBefore(e,null):(t=this,(n=(e=e).parentNode)&&(r=t.lastChild,n.removeChild(e),r=t.lastChild),r=t.lastChild,e.parentNode=t,e.previousSibling=r,e.nextSibling=null,r?r.nextSibling=e:t.firstChild=e,t.lastChild=e,E(t.ownerDocument,t,e),e);var t,n,r},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){e=this.getAttributeNodeNS(e,t);e&&this.removeAttributeNode(e)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){e=this.getAttributeNodeNS(e,t);return e&&e.value||""},setAttributeNS:function(e,t,n){e=this.ownerDocument.createAttributeNS(e,t);e.value=e.nodeValue=""+n,this.setAttributeNode(e)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(r){return new u(this,function(t){var n=[];return v(t,function(e){e===t||1!=e.nodeType||"*"!==r&&e.tagName!=r||n.push(e)}),n})},getElementsByTagNameNS:function(r,o){return new u(this,function(t){var n=[];return v(t,function(e){e===t||1!==e.nodeType||"*"!==r&&e.namespaceURI!==r||"*"!==o&&e.localName!=o||n.push(e)}),n})}}).getElementsByTagName,w.prototype.getElementsByTagNameNS=T.prototype.getElementsByTagNameNS,n(T,b),N.prototype.nodeType=2,n(N,b),A.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(a[3])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,n){var r=this.data.substring(0,e),e=this.data.substring(e+t);this.nodeValue=this.data=n=r+n+e,this.length=n.length}},n(A,b),x.prototype={nodeName:"#text",nodeType:3,splitText:function(e){var t=(n=this.data).substring(e),n=n.substring(0,e),e=(this.data=this.nodeValue=n,this.length=n.length,this.ownerDocument.createTextNode(t));return this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling),e}},n(x,A),M.prototype={nodeName:"#comment",nodeType:8},n(M,A),P.prototype={nodeName:"#cdata-section",nodeType:4},n(P,A),C.prototype.nodeType=10,n(C,b),k.prototype.nodeType=12,n(k,b),B.prototype.nodeType=6,n(B,b),j.prototype.nodeType=5,n(j,b),D.prototype.nodeName="#document-fragment",D.prototype.nodeType=11,n(D,b),I.prototype.nodeType=7,n(I,b),H.prototype.serializeToString=function(e,t,n){return U.call(e,t,n)},b.prototype.toString=U;try{Object.defineProperty&&(r=function e(t){switch(t.nodeType){case 1:case 11:var n=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&n.push(e(t)),t=t.nextSibling;return n.join("");default:return t.nodeValue}},Object.defineProperty(u.prototype,"length",{get:function(){return s(this),this.$$length}}),Object.defineProperty(b.prototype,"textContent",{get:function(){return r(this)},set:function(e){switch(this.nodeType){case 1:case 11:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),V=function(e,t,n){e["$$"+t]=n})}catch(e){}e.DOMImplementation=y,e.XMLSerializer=H},{}],21:[function(e,t,n){"use strict";n.entityMap={lt:"<",gt:">",amp:"&",quot:'"',apos:"'",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",times:"×",divide:"÷",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",euro:"€",trade:"™",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"}},{}],22:[function(e,t,n){"use strict";var r=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,o=new RegExp("[\\-\\.0-9"+r.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),i=new RegExp("^"+r.source+o.source+"*(?::"+r.source+o.source+"*)?$"),P=0,C=1,j=2,D=3,I=4,R=5,L=6,F=7;function a(){}function k(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function B(e,t,n){for(var r=e.tagName,o=null,i=e.length;i--;){var a=e[i],c=a.qName,u=a.value,c=0<(l=c.indexOf(":"))?(s=a.prefix=c.slice(0,l),f=c.slice(l+1),"xmlns"===s&&f):(s=null,"xmlns"===(f=c)&&"");a.localName=f,!1!==c&&(null==o&&(o={},p(n,n={})),n[c]=o[c]=u,a.uri="http://www.w3.org/2000/xmlns/",t.startPrefixMapping(c,u))}for(var s,i=e.length;i--;)(s=(a=e[i]).prefix)&&("xml"===s&&(a.uri="http://www.w3.org/XML/1998/namespace"),"xmlns"!==s)&&(a.uri=n[s||""]);var l,f=0<(l=r.indexOf(":"))?(s=e.prefix=r.slice(0,l),e.localName=r.slice(l+1)):(s=null,e.localName=r),d=e.uri=n[s||""];if(t.startElement(d,f,r,e),!e.closed)return e.currentNSMap=n,e.localNSMap=o,1;if(t.endElement(d,f,r),o)for(s in o)t.endPrefixMapping(s)}function p(e,t){for(var n in e)t[n]=e[n]}function H(e){}a.prototype={parse:function(e,t,n){var r=this.domBuilder;r.startDocument(),p(t,t={}),function(n,e,r,o,i){function a(e){var t=e.slice(1,-1);return t in r?r[t]:"#"===t.charAt(0)?65535<(t=parseInt(t.substr(1).replace("x","0x")))?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):String.fromCharCode(t):(i.error("entity not found:"+e),e)}function t(e){var t;h<e&&(t=n.substring(h,e).replace(/&#?\w+;/g,a),f&&c(h),o.characters(t,0,e-h),h=e)}function c(e,t){for(;s<=e&&(t=l.exec(n));)u=t.index,s=u+t[0].length,f.lineNumber++;f.columnNumber=e-u+1}var u=0,s=0,l=/.*(?:\r\n?|\n)|.*$/g,f=o.locator,d=[{currentNSMap:e}],p={},h=0;for(;;){try{var m,y,b=n.indexOf("<",h);if(b<0)return n.substr(h).match(/^\s*$/)||(m=o.doc,y=m.createTextNode(n.substr(h)),m.appendChild(y),o.currentElement=y);switch(h<b&&t(b),n.charAt(b+1)){case"/":var g=n.indexOf(">",b+3),v=n.substring(b+2,g),w=d.pop(),_=(g<0?(v=n.substring(b+2).replace(/[\s<].*/,""),i.error("end tag name: "+v+" is not complete:"+w.tagName),g=b+1+v.length):v.match(/\s</)&&(v=v.replace(/[\s<].*/,""),i.error("end tag name: "+v+" maybe not complete"),g=b+1+v.length),w.localNSMap),E=w.tagName==v;if(E||w.tagName&&w.tagName.toLowerCase()==v.toLowerCase()){if(o.endElement(w.uri,w.localName,v),_)for(var S in _)o.endPrefixMapping(S);E||i.fatalError("end tag name: "+v+" is not match the current start tagName:"+w.tagName)}else d.push(w);g++;break;case"?":f&&c(b),g=function(e,t,n){var r=e.indexOf("?>",t);if(r){e=e.substring(t,r).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(e)return e[0].length,n.processingInstruction(e[1],e[2]),r+2}return-1}(n,b,o);break;case"!":f&&c(b),g=function(e,t,n,r){{if("-"===e.charAt(t+2))return"-"===e.charAt(t+3)?(i=e.indexOf("--\x3e",t+4),t<i?(n.comment(e,t+4,i-t-4),i+3):(r.error("Unclosed comment"),-1)):-1;if("CDATA["==e.substr(t+3,6))return i=e.indexOf("]]>",t+9),n.startCDATA(),n.characters(e,t+9,i-t-9),n.endCDATA(),i+3;var o,r=function(e,t){var n,r=[],o=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;o.lastIndex=t,o.exec(e);for(;n=o.exec(e);)if(r.push(n),n[1])return r}(e,t),i=r.length;if(1<i&&/!doctype/i.test(r[0][0]))return e=r[1][0],t=3<i&&/^public$/i.test(r[2][0])&&r[3][0],o=4<i&&r[4][0],r=r[i-1],n.startDTD(e,t&&t.replace(/^(['"])(.*?)\1$/,"$2"),o&&o.replace(/^(['"])(.*?)\1$/,"$2")),n.endDTD(),r.index+r[0].length}return-1}(n,b,o,i);break;default:f&&c(b);var O=new H,T=d[d.length-1].currentNSMap,g=function(e,t,n,r,o,i){var a,c=++t,u=P;for(;;){var s=e.charAt(c);switch(s){case"=":if(u===C)a=e.slice(t,c);else if(u!==j)throw new Error("attribute equal must after attrName");u=D;break;case"'":case'"':if(u===D||u===C){if(u===C&&(i.warning('attribute value must after "="'),a=e.slice(t,c)),t=c+1,!(0<(c=e.indexOf(s,t))))throw new Error("attribute value no end '"+s+"' match");l=e.slice(t,c).replace(/&#?\w+;/g,o),n.add(a,l,t-1)}else{if(u!=I)throw new Error('attribute value must after "="');l=e.slice(t,c).replace(/&#?\w+;/g,o),n.add(a,l,t),i.warning('attribute "'+a+'" missed start quot('+s+")!!"),t=c+1}u=R;break;case"/":switch(u){case P:n.setTagName(e.slice(t,c));case R:case L:case F:u=F,n.closed=!0;case I:case C:case j:break;default:throw new Error("attribute invalid close char('/')")}break;case"":return i.error("unexpected end of input"),u==P&&n.setTagName(e.slice(t,c)),c;case">":switch(u){case P:n.setTagName(e.slice(t,c));case R:case L:case F:break;case I:case C:"/"===(l=e.slice(t,c)).slice(-1)&&(n.closed=!0,l=l.slice(0,-1));case j:u===j&&(l=a),u==I?(i.warning('attribute "'+l+'" missed quot(")!!'),n.add(a,l.replace(/&#?\w+;/g,o),t)):("http://www.w3.org/1999/xhtml"===r[""]&&l.match(/^(?:disabled|checked|selected)$/i)||i.warning('attribute "'+l+'" missed value!! "'+l+'" instead!!'),n.add(l,l,t));break;case D:throw new Error("attribute value missed!!")}return c;case"":s=" ";default:if(s<=" ")switch(u){case P:n.setTagName(e.slice(t,c)),u=L;break;case C:a=e.slice(t,c),u=j;break;case I:var l=e.slice(t,c).replace(/&#?\w+;/g,o);i.warning('attribute "'+l+'" missed quot(")!!'),n.add(a,l,t);case R:u=L}else switch(u){case j:n.tagName;"http://www.w3.org/1999/xhtml"===r[""]&&a.match(/^(?:disabled|checked|selected)$/i)||i.warning('attribute "'+a+'" missed value!! "'+a+'" instead2!!'),n.add(a,a,t),t=c,u=C;break;case R:i.warning('attribute space is required"'+a+'"!!');case L:u=C,t=c;break;case D:u=I,t=c;break;case F:throw new Error("elements closed character '/' and '>' must be connected to")}}c++}}(n,b,O,T,a,i),N=O.length;if(!O.closed&&function(e,t,n,r){var o=r[n];null==o&&((o=e.lastIndexOf("</"+n+">"))<t&&(o=e.lastIndexOf("</"+n)),r[n]=o);return o<t}(n,g,O.tagName,p)&&(O.closed=!0,r.nbsp||i.warning("unclosed xml attribute")),f&&N){for(var A=k(f,{}),x=0;x<N;x++){var M=O[x];c(M.offset),M.locator=k(f,{})}o.locator=A,B(O,o,T)&&d.push(O),o.locator=f}else B(O,o,T)&&d.push(O);"http://www.w3.org/1999/xhtml"!==O.uri||O.closed?g++:g=function(e,t,n,r,o){if(/^(?:script|textarea)$/i.test(n)){var i=e.indexOf("</"+n+">",t),e=e.substring(t+1,i);if(/[&<]/.test(e))return/^script$/i.test(n)?o.characters(e,0,e.length):(e=e.replace(/&#?\w+;/g,r),o.characters(e,0,e.length)),i}return t+1}(n,g,O.tagName,a,o)}}catch(e){i.error("element parse error: "+e),g=-1}h<g?h=g:t(Math.max(b,h)+1)}}(e,t,n,r,this.errorHandler),r.endDocument()}},H.prototype={setTagName:function(e){if(!i.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},add:function(e,t,n){if(!i.test(e))throw new Error("invalid attribute:"+e);this[this.length++]={qName:e,value:t,offset:n}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},n.XMLReader=a},{}],23:[function(e,t,n){"use strict";var r=GameGlobal,o=r.__globalAdapter={};Object.assign(o,{init:function(){e("./wrapper/builtin"),r.DOMParser=e("../../common/xmldom/dom-parser").DOMParser,e("./wrapper/unify"),e("./wrapper/fs-utils"),e("../../common/engine/globalAdapter"),e("./wrapper/systemInfo")},adaptEngine:function(){e("../../common/engine"),e("./wrapper/engine"),e("./wrapper/sub-context-adapter")}})},{"../../common/engine":16,"../../common/engine/globalAdapter":15,"../../common/xmldom/dom-parser":19,"./wrapper/builtin":46,"./wrapper/engine":53,"./wrapper/fs-utils":54,"./wrapper/sub-context-adapter":1,"./wrapper/systemInfo":55,"./wrapper/unify":56}],24:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=(e=e("./HTMLAudioElement"))&&e.__esModule?e:{default:e};function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function a(){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(r)return(r=Object.getOwnPropertyDescriptor(r,t)).get?r.get.call(arguments.length<3?e:n):r.value}).apply(this,arguments)}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=s(n),t=(e=r?(e=s(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var l=1,f={},e=function(e){var t=o;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e);var n,r=u(o);function o(e){var t;if(!(this instanceof o))throw new TypeError("Cannot call a class as a function");(t=r.call(this))._$sn=l++,t.HAVE_NOTHING=0,t.HAVE_METADATA=1,t.HAVE_CURRENT_DATA=2,t.HAVE_FUTURE_DATA=3,t.HAVE_ENOUGH_DATA=4,t.readyState=0;var n=tt.createInnerAudioContext();return f[t._$sn]=n,t._canplayEvents=["load","loadend","canplay","canplaythrough","loadedmetadata"],n.onCanplay(function(){t._loaded=!0,t.readyState=t.HAVE_CURRENT_DATA,t._canplayEvents.forEach(function(e){t.dispatchEvent({type:e})})}),n.onPlay(function(){t._paused=f[t._$sn].paused,t.dispatchEvent({type:"play"})}),n.onPause(function(){t._paused=f[t._$sn].paused,t.dispatchEvent({type:"pause"})}),n.onEnded(function(){t._paused=f[t._$sn].paused,!1===f[t._$sn].loop&&t.dispatchEvent({type:"ended"}),t.readyState=4}),n.onError(function(){t._paused=f[t._$sn].paused,t.dispatchEvent({type:"error"})}),e?t.src=e:t._src="",t._loop=n.loop,t._autoplay=n.autoplay,t._paused=n.paused,t._volume=n.volume,t._muted=!1,t}return t=o,(e=[{key:"addEventListener",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};a(s(o.prototype),"addEventListener",this).call(this,e,t,n),e=String(e).toLowerCase(),this._loaded&&-1!==this._canplayEvents.indexOf(e)&&this.dispatchEvent({type:e})}},{key:"load",value:function(){}},{key:"play",value:function(){f[this._$sn].play()}},{key:"resume",value:function(){f[this._$sn].resume()}},{key:"pause",value:function(){f[this._$sn].pause()}},{key:"stop",value:function(){f[this._$sn].stop()}},{key:"destroy",value:function(){f[this._$sn].destroy()}},{key:"canPlayType",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return"string"==typeof e&&(-1<e.indexOf("audio/mpeg")||e.indexOf("audio/mp4"))?"probably":""}},{key:"currentTime",get:function(){return f[this._$sn].currentTime},set:function(e){f[this._$sn].seek(e)}},{key:"duration",get:function(){return f[this._$sn].duration}},{key:"src",get:function(){return this._src},set:function(e){this._src=e,this._loaded=!1,this.readyState=this.HAVE_NOTHING,f[this._$sn].src=e}},{key:"loop",get:function(){return this._loop},set:function(e){this._loop=e,f[this._$sn].loop=e}},{key:"autoplay",get:function(){return this.autoplay},set:function(e){this._autoplay=e,f[this._$sn].autoplay=e}},{key:"paused",get:function(){return this._paused}},{key:"volume",get:function(){return this._volume},set:function(e){this._volume=e,this._muted||(f[this._$sn].volume=e)}},{key:"muted",get:function(){return this._muted},set:function(e){this._muted=e,f[this._$sn].volume=e?0:this._volume}},{key:"cloneNode",value:function(){var e=new o;return e.loop=this.loop,e.autoplay=this.autoplay,e.src=this.src,e}}])&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(e.default);n.default=e,t.exports=n.default},{"./HTMLAudioElement":32}],25:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(){var e=tt.createCanvas();e.type="canvas",e.getContext;return e.getBoundingClientRect=function(){return{top:0,left:0,width:window.innerWidth,height:window.innerHeight}},e.style={top:"0px",left:"0px",width:r.innerWidth+"px",height:r.innerHeight+"px"},e.addEventListener=function(e,t){document.addEventListener(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:{})},e.removeEventListener=function(e,t){document.removeEventListener(e,t)},e.dispatchEvent=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};console.log("canvas.dispatchEvent",e.type,e)},Object.defineProperty(e,"clientWidth",{enumerable:!0,get:function(){return r.innerWidth}}),Object.defineProperty(e,"clientHeight",{enumerable:!0,get:function(){return r.innerHeight}}),e};var r=e("./WindowProperties");t.exports=n.default},{"./WindowProperties":43}],26:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=i(n),t=(e=r?(e=i(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e);var n,r,o=u(i);function i(){var e;if(this instanceof i)return(e=o.call(this)).className="",e.children=[],e;throw new TypeError("Cannot call a class as a function")}return t=i,n&&a(t.prototype,n),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}(((e=e("./Node"))&&e.__esModule?e:{default:e}).default);n.default=e,t.exports=n.default},{"./Node":40}],27:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,n.default=i(function e(){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function")}),t.exports=n.default},{}],28:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=e("../util/index.js");function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function a(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}var c=a(function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.touches=[],this.targetTouches=[],this.changedTouches=[],this.preventDefault=r.noop,this.stopPropagation=r.noop,this.type=t,this.target=window.canvas,this.currentTarget=window.canvas});function u(n){return function(e){var t=new c(n);t.touches=e.touches,t.targetTouches=Array.prototype.slice.call(e.touches),t.changedTouches=e.changedTouches,t.timeStamp=e.timeStamp,document.dispatchEvent(t)}}n.default=c,tt.onTouchStart(u("touchstart")),tt.onTouchMove(u("touchmove")),tt.onTouchEnd(u("touchend")),tt.onTouchCancel(u("touchcancel")),t.exports=n.default},{"../util/index.js":50}],29:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"MouseEvent",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(n,"TouchEvent",{enumerable:!0,get:function(){return r.default}});var r=i(e("./TouchEvent")),o=i(e("./MouseEvent"));function i(e){return e&&e.__esModule?e:{default:e}}},{"./MouseEvent":27,"./TouchEvent":28}],30:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=new WeakMap,r=function(){function e(){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");a.set(this,{})}var t,n,r;return t=e,(n=[{key:"addEventListener",value:function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=a.get(this);r||a.set(this,r={}),r[e]||(r[e]=[]),r[e].push(t),n.capture,n.once,n.passive}},{key:"removeEventListener",value:function(e,t){var n=a.get(this);if(n){var r=n[e];if(r&&0<r.length)for(var o=r.length;o--;)if(r[o]===t){r.splice(o,1);break}}}},{key:"dispatchEvent",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=a.get(this)[e.type];if(t)for(var n=0;n<t.length;n++)t[n](e)}}])&&i(t.prototype,n),r&&i(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();n.default=r,t.exports=n.default},{}],31:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=function(){function e(){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function")}var t,n,r;return t=e,(n=[{key:"construct",value:function(){}}])&&i(t.prototype,n),r&&i(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();n.default=r,t.exports=n.default},{}],32:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=i(n),t=(e=r?(e=i(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e);var n,r,o=u(i);function i(){if(this instanceof i)return o.call(this,"audio");throw new TypeError("Cannot call a class as a function")}return t=i,n&&a(t.prototype,n),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}(((e=e("./HTMLMediaElement"))&&e.__esModule?e:{default:e}).default);n.default=e,t.exports=n.default},{"./HTMLMediaElement":36}],33:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=o(e("./Canvas"));o(e("./HTMLElement"));function o(e){return e&&e.__esModule?e:{default:e}}GameGlobal.screencanvas=GameGlobal.screencanvas||new r.default;e=GameGlobal.screencanvas.constructor;n.default=e,t.exports=n.default},{"./Canvas":25,"./HTMLElement":34}],34:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=(r=e("./Element"))&&r.__esModule?r:{default:r},i=e("./util/index.js"),a=e("./WindowProperties");function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function u(e,t){return(u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function s(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=l(n),t=(e=r?(e=l(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function l(e){return(l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}e=function(e){var t=o;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e);var n,r=s(o);function o(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t=this,n=o;if(t instanceof n)return(t=r.call(this)).className="",t.childern=[],t.style={width:"".concat(a.innerWidth,"px"),height:"".concat(a.innerHeight,"px")},t.insertBefore=i.noop,t.innerHTML="",t.tagName=e.toUpperCase(),t;throw new TypeError("Cannot call a class as a function")}return t=o,(e=[{key:"setAttribute",value:function(e,t){this[e]=t}},{key:"getAttribute",value:function(e){return this[e]}},{key:"clientWidth",get:function(){var e=parseInt(this.style.fontSize,10)*this.innerHTML.length;return Number.isNaN(e)?0:e}},{key:"clientHeight",get:function(){var e=parseInt(this.style.fontSize,10);return Number.isNaN(e)?0:e}},{key:"getBoundingClientRect",value:function(){return{top:0,left:0,width:a.innerWidth,height:a.innerHeight}}},{key:"focus",value:function(){}}])&&c(t.prototype,e),n&&c(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(r.default);n.default=e,t.exports=n.default},{"./Element":26,"./WindowProperties":43,"./util/index.js":50}],35:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;(e=e("./HTMLElement"))&&e.__esModule;e=tt.createImage().constructor;n.default=e,t.exports=n.default},{"./HTMLElement":34}],36:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=u(n),t=(e=r?(e=u(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=function(e){var t=o;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&a(t,e);var n,r=c(o);function o(e){if(this instanceof o)return r.call(this,e);throw new TypeError("Cannot call a class as a function")}return t=o,(e=[{key:"addTextTrack",value:function(){}},{key:"captureStream",value:function(){}},{key:"fastSeek",value:function(){}},{key:"load",value:function(){}},{key:"pause",value:function(){}},{key:"play",value:function(){}}])&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(((e=e("./HTMLElement"))&&e.__esModule?e:{default:e}).default);n.default=e,t.exports=n.default},{"./HTMLElement":34}],37:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=i(n),t=(e=r?(e=i(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=function(e){var t=i;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e);var n,r,o=u(i);function i(){if(this instanceof i)return o.call(this,"video");throw new TypeError("Cannot call a class as a function")}return t=i,n&&a(t.prototype,n),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}(((e=e("./HTMLMediaElement"))&&e.__esModule?e:{default:e}).default);n.default=e,t.exports=n.default},{"./HTMLMediaElement":36}],38:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(){return tt.createImage()};(e=e("./HTMLImageElement"))&&e.__esModule;t.exports=n.default},{"./HTMLImageElement":35}],39:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,n.default=i(function e(){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function")}),t.exports=n.default},{}],40:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function c(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=u(n),t=(e=r?(e=u(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=function(e){var t=o;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&a(t,e);var n,r=c(o);function o(){var e;if(this instanceof o)return(e=r.call(this)).childNodes=[],e;throw new TypeError("Cannot call a class as a function")}return t=o,(e=[{key:"appendChild",value:function(e){this.childNodes.push(e)}},{key:"cloneNode",value:function(){var e=Object.create(this);return Object.assign(e,this),e}},{key:"removeChild",value:function(t){var e=this.childNodes.findIndex(function(e){return e===t});return-1<e?this.childNodes.splice(e,1):null}}])&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),o}(((e=e("./EventTarget.js"))&&e.__esModule?e:{default:e}).default);n.default=e,t.exports=n.default},{"./EventTarget.js":30}],41:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,n.default=i(function e(){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function")}),t.exports=n.default},{}],42:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=new WeakMap,i=function(){function i(e){var t=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],r=this,o=i;if(!(r instanceof o))throw new TypeError("Cannot call a class as a function");if(this.binaryType="",this.bufferedAmount=0,this.extensions="",this.onclose=null,this.onerror=null,this.onmessage=null,this.onopen=null,this.protocol="",this.readyState=3,"string"!=typeof e||!/(^ws:\/\/)|(^wss:\/\/)/.test(e))throw new TypeError("Failed to construct 'WebSocket': The URL '".concat(e,"' is invalid"));this.url=e,this.readyState=i.CONNECTING;r=tt.connectSocket({url:e,protocols:Array.isArray(n)?n:[n],tcpNoDelay:!0});return a.set(this,r),r.onClose(function(e){t.readyState=i.CLOSED,"function"==typeof t.onclose&&t.onclose(e)}),r.onMessage(function(e){"function"==typeof t.onmessage&&t.onmessage(e)}),r.onOpen(function(){t.readyState=i.OPEN,"function"==typeof t.onopen&&t.onopen()}),r.onError(function(e){"function"==typeof t.onerror&&t.onerror(new Error(e.errMsg))}),this}var e,t,n;return e=i,(t=[{key:"close",value:function(e,t){this.readyState=i.CLOSING,a.get(this).close({code:e,reason:t})}},{key:"send",value:function(e){if(!("string"==typeof e||e instanceof ArrayBuffer||ArrayBuffer.isView(e)))throw new TypeError("Failed to send message: The data ".concat(e," is invalid"));a.get(this).send({data:e})}}])&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}();(n.default=i).CONNECTING=0,i.OPEN=1,i.CLOSING=2,i.CLOSED=3,t.exports=n.default},{}],43:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.screen=n.performance=n.ontouchstart=n.ontouchmove=n.ontouchend=n.innerWidth=n.innerHeight=n.devicePixelRatio=void 0;var r=tt.getSystemInfoSync(),o=r.screenWidth,i=r.screenHeight,r=r.devicePixelRatio,r=(n.devicePixelRatio=r,o),a=i,o={width:o,height:i,availWidth:n.innerWidth=r,availHeight:n.innerHeight=a,availLeft:0,availTop:0},i=(n.screen=o,{now:Date.now});n.performance=i,n.ontouchstart=null,n.ontouchmove=null;n.ontouchend=null},{}],44:[function(e,t,n){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;e=(e=e("./EventTarget.js"))&&e.__esModule?e:{default:e};function i(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,function(e){e=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(e,"string");return"symbol"===o(e)?e:String(e)}(r.key),r)}}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u(n){var r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=c(n),t=(e=r?(e=c(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),this);if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return s(t)}}function s(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var l=new WeakMap,f=new WeakMap,d=new WeakMap,p=new WeakMap,h=new WeakMap;function m(e){if("function"==typeof this["on".concat(e)]){for(var t=arguments.length,n=new Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this["on".concat(e)].apply(this,n)}}function y(e){this.readyState=e,m.call(this,"readystatechange")}e=function(e){var t=c;if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&a(t,e);var n,r=u(c);function c(){var e;if(this instanceof c)return(e=r.call(this)).onabort=null,e.onerror=null,e.onload=null,e.onloadstart=null,e.onprogress=null,e.ontimeout=null,e.onloadend=null,e.onreadystatechange=null,e.readyState=0,e.response=null,e.responseText=null,e.responseType="",e.responseXML=null,e.status=0,e.statusText="",e.upload={},e.withCredentials=!1,d.set(s(e),{"content-type":"application/x-www-form-urlencoded"}),p.set(s(e),{}),e;throw new TypeError("Cannot call a class as a function")}return t=c,(e=[{key:"abort",value:function(){var e=h.get(this);e&&e.abort()}},{key:"getAllResponseHeaders",value:function(){var t=p.get(this);return Object.keys(t).map(function(e){return"".concat(e,": ").concat(t[e])}).join("\n")}},{key:"getResponseHeader",value:function(e){return p.get(this)[e]}},{key:"open",value:function(e,t){f.set(this,e),l.set(this,t),y.call(this,c.OPENED)}},{key:"overrideMimeType",value:function(){}},{key:"send",value:function(){var a=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";if(this.readyState!==c.OPENED)throw new Error("Failed to execute 'send' on 'XMLHttpRequest': The object's state must be OPENED.");e=tt.request({data:e,url:l.get(this),method:f.get(this),header:d.get(this),dataType:"other",responseType:"arraybuffer"===this.responseType?"arraybuffer":"text",success:function(e){var t=e.data,n=e.statusCode,e=e.header;switch(a.status=n,p.set(a,e),m.call(a,"loadstart"),y.call(a,c.HEADERS_RECEIVED),y.call(a,c.LOADING),a.responseType){case"json":a.responseText=t;try{a.response=JSON.parse(t)}catch(e){a.response=null}break;case"":case"text":a.responseText=a.response=t;break;case"arraybuffer":a.response=t,a.responseText="";for(var r=new Uint8Array(t),o=r.byteLength,i=0;i<o;i++)a.responseText+=String.fromCharCode(r[i]);break;default:a.response=null}y.call(a,c.DONE),m.call(a,"load"),m.call(a,"loadend")},fail:function(e){e=e.errMsg;-1!==e.indexOf("abort")?m.call(a,"abort"):-1!==e.indexOf("timeout")?m.call(a,"timeout"):m.call(a,"error",e),m.call(a,"loadend")}});h.set(this,e)}},{key:"setRequestHeader",value:function(e,t){var n=d.get(this);n[e]=t,d.set(this,n)}},{key:"addEventListener",value:function(e,t){var n;"function"==typeof t&&(n=this,this["on"+e]=function(e){t.call(n,e)})}}])&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),c}(e.default);(n.default=e).UNSEND=0,e.OPENED=1,e.HEADERS_RECEIVED=2,e.LOADING=3,e.DONE=4,t.exports=n.default},{"./EventTarget.js":30}],45:[function(e,t,n){"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};t=f(t);if(t&&t.has(e))return t.get(e);var n,r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(n in e){var i;"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&((i=o?Object.getOwnPropertyDescriptor(e,n):null)&&(i.get||i.set)?Object.defineProperty(r,n,i):r[n]=e[n])}r.default=e,t&&t.set(e,r);return r}(e("./window")),o=l(e("./HTMLElement")),i=l(e("./HTMLVideoElement")),c=l(e("./Image")),u=l(e("./Audio")),s=l(e("./Canvas"));function l(e){return e&&e.__esModule?e:{default:e}}function f(e){var t,n;return"function"!=typeof WeakMap?null:(t=new WeakMap,n=new WeakMap,(f=function(e){return e?n:t})(e))}e("./EventIniter/index.js");var d={},p={readyState:"complete",visibilityState:"visible",documentElement:r,hidden:!1,style:{},location:r.location,ontouchstart:null,ontouchmove:null,ontouchend:null,head:new o.default("head"),body:new o.default("body"),createElement:function(e){return"canvas"===e?new s.default:"audio"===e?new u.default:"img"===e?new c.default:"video"===e?new i.default:new o.default(e)},createElementNS:function(e,t){return this.createElement(t)},getElementById:function(e){return e===r.canvas.id?r.canvas:null},getElementsByTagName:function(e){return"head"===e?[p.head]:"body"===e?[p.body]:"canvas"===e?[r.canvas]:[]},getElementsByName:function(e){return"head"===e?[p.head]:"body"===e?[p.body]:"canvas"===e?[r.canvas]:[]},querySelector:function(e){return"head"===e?p.head:"body"===e?p.body:"canvas"===e||e==="#".concat(r.canvas.id)?r.canvas:null},querySelectorAll:function(e){return"head"===e?[p.head]:"body"===e?[p.body]:"canvas"===e?[r.canvas]:[]},addEventListener:function(e,t){d[e]||(d[e]=[]),d[e].push(t)},removeEventListener:function(e,t){var n=d[e];if(n&&0<n.length)for(var r=n.length;r--;)if(n[r]===t){n.splice(r,1);break}},dispatchEvent:function(e){var t=d[e.type];if(t)for(var n=0;n<t.length;n++)t[n](e)}};n.default=p,t.exports=n.default},{"./Audio":24,"./Canvas":25,"./EventIniter/index.js":29,"./HTMLElement":34,"./HTMLVideoElement":37,"./Image":38,"./window":51}],46:[function(e,t,n){"use strict";function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==a(e)&&"function"!=typeof e)return{default:e};t=c(t);if(t&&t.has(e))return t.get(e);var n,r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(n in e){var i;"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&((i=o?Object.getOwnPropertyDescriptor(e,n):null)&&(i.get||i.set)?Object.defineProperty(r,n,i):r[n]=e[n])}r.default=e,t&&t.set(e,r);return r}(e("./window")),o=i(e("./document"));i(e("./HTMLElement"));function i(e){return e&&e.__esModule?e:{default:e}}function c(e){var t,n;return"function"!=typeof WeakMap?null:(t=new WeakMap,n=new WeakMap,(c=function(e){return e?n:t})(e))}var u=GameGlobal;if(!GameGlobal.__isAdapterInjected){GameGlobal.__isAdapterInjected=!0,r.document=o.default,r.addEventListener=function(e,t){r.document.addEventListener(e,t)},r.removeEventListener=function(e,t){r.document.removeEventListener(e,t)},r.dispatchEvent=r.document.dispatchEvent;e=tt.getSystemInfoSync().platform;if("undefined"==typeof __devtoolssubcontext&&"devtools"===e){for(var s in r){var l=Object.getOwnPropertyDescriptor(u,s);l&&!0!==l.configurable||Object.defineProperty(window,s,{value:r[s]})}for(var f in r.document){var d=Object.getOwnPropertyDescriptor(u.document,f);d&&!0!==d.configurable||Object.defineProperty(u.document,f,{value:r.document[f]})}window.parent=window}else{for(var p in r)u[p]=r[p];u.window=r,(window=u).top=window.parent=window}}},{"./HTMLElement":34,"./document":45,"./window":51}],47:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,n.default={get length(){return tt.getStorageInfoSync().keys.length},key:function(e){return tt.getStorageInfoSync().keys[e]},getItem:function(e){return tt.getStorageSync(e)},setItem:function(e,t){return tt.setStorageSync(e,t)},removeItem:function(e){tt.removeStorageSync(e)},clear:function(){tt.clearStorageSync()}},t.exports=n.default},{}],48:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;n.default={href:"game.js",reload:function(){}},t.exports=n.default},{}],49:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=e("./util/index.js"),r=tt.getSystemInfoSync(),o=(console.log(r),r.system),i=r.platform,a=r.language,r=r.version,o=-1!==o.toLowerCase().indexOf("android")?"Android; CPU ".concat(o):"iPhone; CPU iPhone OS ".concat(o," like Mac OS X"),r="Mozilla/5.0 (".concat(o,") AppleWebKit/603.1.30 (KHTML, like Gecko) Mobile/14E8301 MicroMessenger/").concat(r," MiniGame NetType/WIFI Language/").concat(a),c={platform:i,language:a,appVersion:"5.0 (".concat(o,") AppleWebKit/601.1.46 (KHTML, like Gecko) Version/9.0 Mobile/13B143 Safari/601.1"),userAgent:r,onLine:!0,geolocation:{getCurrentPosition:e.noop,watchPosition:e.noop,clearWatch:e.noop}};tt.onNetworkStatusChange&&tt.onNetworkStatusChange(function(e){c.onLine=e.isConnected}),n.default=c,t.exports=n.default},{"./util/index.js":50}],50:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.noop=function(){}},{}],51:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r={canvas:!0,setTimeout:!0,setInterval:!0,clearTimeout:!0,clearInterval:!0,requestAnimationFrame:!0,cancelAnimationFrame:!0,navigator:!0,XMLHttpRequest:!0,WebSocket:!0,Image:!0,ImageBitmap:!0,Audio:!0,FileReader:!0,HTMLElement:!0,HTMLImageElement:!0,HTMLCanvasElement:!0,HTMLMediaElement:!0,HTMLAudioElement:!0,HTMLVideoElement:!0,WebGLRenderingContext:!0,TouchEvent:!0,MouseEvent:!0,DeviceMotionEvent:!0,localStorage:!0,location:!0},o=(Object.defineProperty(n,"Audio",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(n,"DeviceMotionEvent",{enumerable:!0,get:function(){return v.DeviceMotionEvent}}),Object.defineProperty(n,"FileReader",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(n,"HTMLAudioElement",{enumerable:!0,get:function(){return y.default}}),Object.defineProperty(n,"HTMLCanvasElement",{enumerable:!0,get:function(){return h.default}}),Object.defineProperty(n,"HTMLElement",{enumerable:!0,get:function(){return d.default}}),Object.defineProperty(n,"HTMLImageElement",{enumerable:!0,get:function(){return p.default}}),Object.defineProperty(n,"HTMLMediaElement",{enumerable:!0,get:function(){return m.default}}),Object.defineProperty(n,"HTMLVideoElement",{enumerable:!0,get:function(){return b.default}}),Object.defineProperty(n,"Image",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(n,"ImageBitmap",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(n,"MouseEvent",{enumerable:!0,get:function(){return v.MouseEvent}}),Object.defineProperty(n,"TouchEvent",{enumerable:!0,get:function(){return v.TouchEvent}}),Object.defineProperty(n,"WebGLRenderingContext",{enumerable:!0,get:function(){return g.default}}),Object.defineProperty(n,"WebSocket",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(n,"XMLHttpRequest",{enumerable:!0,get:function(){return a.default}}),n.clearTimeout=n.clearInterval=n.canvas=n.cancelAnimationFrame=void 0,Object.defineProperty(n,"localStorage",{enumerable:!0,get:function(){return w.default}}),Object.defineProperty(n,"location",{enumerable:!0,get:function(){return _.default}}),Object.defineProperty(n,"navigator",{enumerable:!0,get:function(){return i.default}}),n.setTimeout=n.setInterval=n.requestAnimationFrame=void 0,S(e("./Canvas"))),i=S(e("./navigator")),a=S(e("./XMLHttpRequest")),c=S(e("./WebSocket")),u=S(e("./Image")),s=S(e("./ImageBitmap")),l=S(e("./Audio")),f=S(e("./FileReader")),d=S(e("./HTMLElement")),p=S(e("./HTMLImageElement")),h=S(e("./HTMLCanvasElement")),m=S(e("./HTMLMediaElement")),y=S(e("./HTMLAudioElement")),b=S(e("./HTMLVideoElement")),g=S(e("./WebGLRenderingContext")),v=e("./EventIniter/index.js"),w=S(e("./localStorage")),_=S(e("./location")),E=e("./WindowProperties");function S(e){return e&&e.__esModule?e:{default:e}}Object.keys(E).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(r,e)||e in n&&n[e]===E[e]||Object.defineProperty(n,e,{enumerable:!0,get:function(){return E[e]}})}),GameGlobal.screencanvas=GameGlobal.screencanvas||new o.default;var e=GameGlobal.screencanvas,o=(n.canvas=e,GameGlobal),e=o.setTimeout,O=o.setInterval,T=o.clearTimeout,N=o.clearInterval,A=o.requestAnimationFrame,o=o.cancelAnimationFrame;n.cancelAnimationFrame=o,n.requestAnimationFrame=A,n.clearInterval=N,n.clearTimeout=T,n.setInterval=O,n.setTimeout=e},{"./Audio":24,"./Canvas":25,"./EventIniter/index.js":29,"./FileReader":31,"./HTMLAudioElement":32,"./HTMLCanvasElement":33,"./HTMLElement":34,"./HTMLImageElement":35,"./HTMLMediaElement":36,"./HTMLVideoElement":37,"./Image":38,"./ImageBitmap":39,"./WebGLRenderingContext":41,"./WebSocket":42,"./WindowProperties":43,"./XMLHttpRequest":44,"./localStorage":47,"./location":48,"./navigator":49}],52:[function(e,t,n){"use strict";var r,o,i,a,c,u,s;cc&&cc.Label&&(r=cc.gfx,o=cc.Label,(i=__globalAdapter).isSubContext||(a=(u=document.createElement("canvas")).getContext("2d"),c={canvas:u,context:a},cc.game.on(cc.game.EVENT_ENGINE_INITED,function(){Object.assign(o._canvasPool,{get:function(){return c},put:function(){}})})),u=tt.getSystemInfoSync(),Number.parseInt(u.SDKVersion[0])<2)&&(s=o.prototype._updateMaterialWebgl,Object.assign(o.prototype,{_updateMaterialWebgl:function(){var e,t,n;s.call(this),this.srcBlendFactor!==cc.macro.BlendFactor.SRC_ALPHA||i.isDevTool||this.font instanceof cc.BitmapFont||(e=this._materials[0],this._frame&&e&&(t=this.dstBlendFactor,n=cc.macro.BlendFactor.ONE,e.effect.setBlend(!0,r.BLEND_FUNC_ADD,n,t,r.BLEND_FUNC_ADD,n,t)))}}))},{}],53:[function(e,t,n){"use strict";e("./Label")},{"./Label":52}],54:[function(o,e,t){"use strict";var i=tt.getFileSystemManager?tt.getFileSystemManager():null,n=/size.*limit.*exceeded/,a={fs:i,isOutOfStorage:function(e){return n.test(e)},getUserDataPath:function(){return tt.env.USER_DATA_PATH},checkFsValid:function(){return!!i||(console.warn("can not get the file system!"),!1)},deleteFile:function(t,n){i.unlink({filePath:t,success:function(){n&&n(null)},fail:function(e){console.warn("Delete file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg))}})},downloadFile:function(t,e,n,r,o){var i={url:t,success:function(e){200===e.statusCode?o&&o(null,e.tempFilePath||e.filePath):(e.filePath&&a.deleteFile(e.filePath),console.warn("Download file failed: path: ".concat(t," message: ").concat(e.statusCode)),o&&o(new Error(e.statusCode),null))},fail:function(e){console.warn("Download file failed: path: ".concat(t," message: ").concat(e.errMsg)),o&&o(new Error(e.errMsg),null)}},e=(e&&(i.filePath=e),n&&(i.header=n),tt.downloadFile(i));r&&e.onProgressUpdate(r)},saveFile:function(t,e,n){tt.saveFile({tempFilePath:t,filePath:e,success:function(e){n&&n(null)},fail:function(e){console.warn("Save file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg))}})},copyFile:function(t,e,n){i.copyFile({srcPath:t,destPath:e,success:function(){n&&n(null)},fail:function(e){console.warn("Copy file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg))}})},writeFile:function(t,e,n,r){i.writeFile({filePath:t,encoding:n,data:e,success:function(){r&&r(null)},fail:function(e){console.warn("Write file failed: path: ".concat(t," message: ").concat(e.errMsg)),r&&r(new Error(e.errMsg))}})},writeFileSync:function(t,e,n){try{return i.writeFileSync(t,e,n),null}catch(e){return console.warn("Write file failed: path: ".concat(t," message: ").concat(e.message)),new Error(e.message)}},readFile:function(t,e,n){i.readFile({filePath:t,encoding:e,success:function(e){n&&n(null,e.data)},fail:function(e){console.warn("Read file failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg),null)}})},readDir:function(t,n){i.readdir({dirPath:t,success:function(e){n&&n(null,e.files)},fail:function(e){console.warn("Read directory failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error(e.errMsg),null)}})},readText:function(e,t){a.readFile(e,"utf8",t)},readArrayBuffer:function(e,t){a.readFile(e,"",t)},readJson:function(r,o){a.readFile(r,"utf8",function(t,e){var n=null;if(!t)try{n=JSON.parse(e)}catch(e){console.warn("Read json failed: path: ".concat(r," message: ").concat(e.message)),t=new Error(e.message)}o&&o(t,n)})},readJsonSync:function(t){try{var e=i.readFileSync(t,"utf8");return JSON.parse(e)}catch(e){return console.warn("Read json failed: path: ".concat(t," message: ").concat(e.message)),new Error(e.message)}},makeDirSync:function(t,e){try{return i.mkdirSync(t,e),null}catch(e){return console.warn("Make directory failed: path: ".concat(t," message: ").concat(e.message)),new Error(e.message)}},rmdirSync:function(t,e){try{i.rmdirSync(t,e)}catch(e){return console.warn("rm directory failed: path: ".concat(t," message: ").concat(e.message)),new Error(e.message)}},exists:function(e,t){i.access({path:e,success:function(){t&&t(!0)},fail:function(){t&&t(!1)}})},loadSubpackage:function(t,e,n){var r;if(tt.loadSubpackage)return r=tt.loadSubpackage({name:t,success:function(){n&&n()},fail:function(e){console.warn("Load Subpackage failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error("Failed to load subpackage ".concat(t,": ").concat(e.errMsg)))}}),e&&r.onProgressUpdate(e),r;console.warn("tt.loadSubpackage not supported, fallback to loading bundle"),o("./subpackages/".concat(t,"/game.js")),n&&n()},unzip:function(t,e,n){i.unzip({zipFilePath:t,targetPath:e,success:function(){n&&n(null)},fail:function(e){console.warn("unzip failed: path: ".concat(t," message: ").concat(e.errMsg)),n&&n(new Error("unzip failed: "+e.errMsg))}})}};window.fsUtils=e.exports=a},{}],55:[function(e,t,n){"use strict";var o=window.__globalAdapter,r=tt.getSystemInfoSync(),i=o.adaptSys;Object.assign(o,{adaptSys:function(e){var t;i.call(this,e),"windows"===r.platform?(e.isMobile=!1,e.os=e.OS_WINDOWS):o.isDevTool&&(-1<(t=r.system.toLowerCase()).indexOf("android")?e.os=e.OS_ANDROID:-1<t.indexOf("ios")&&(e.os=e.OS_IOS)),tt.getOpenDataContext?e.platform=e.BYTEDANCE_GAME:e.platform=e.BYTEDANCE_GAME_SUB,e.getSafeAreaRect=function(){var e=cc.view,t=o.getSafeArea(),n=e.getFrameSize(),r=new cc.Vec2(t.left,t.bottom),t=new cc.Vec2(t.right,t.top),n={left:0,top:0,width:n.width,height:n.height};return e.convertToLocationInView(r.x,r.y,n,r),e.convertToLocationInView(t.x,t.y,n,t),e._convertPointWithScale(r),e._convertPointWithScale(t),cc.rect(r.x,r.y,t.x-r.x,t.y-r.y)}}})},{}],56:[function(e,t,n){"use strict";var r,c,u,s,l,o,a,e=e("../../../common/utils");window.__globalAdapter&&(r=function(e){s=tt.getSystemInfoSync(),setTimeout(function(){s=tt.getSystemInfoSync(),l=!0},e||5e3)},c=function(){return s.deviceOrientation?"landscape"===s.deviceOrientation:s.screenWidth>s.screenHeight},u=window.__globalAdapter,l=!1,r(),u.isSubContext=void 0===tt.getOpenDataContext,u.isDevTool="devtools"===s.platform,e.cloneMethod(u,tt,"getSystemInfoSync"),e.cloneMethod(u,tt,"onTouchStart"),e.cloneMethod(u,tt,"onTouchMove"),e.cloneMethod(u,tt,"onTouchEnd"),e.cloneMethod(u,tt,"onTouchCancel"),e.cloneMethod(u,tt,"createInnerAudioContext"),e.cloneMethod(u,tt,"onAudioInterruptionEnd"),e.cloneMethod(u,tt,"onAudioInterruptionBegin"),e.cloneMethod(u,tt,"createVideo"),e.cloneMethod(u,tt,"setPreferredFramesPerSecond"),e.cloneMethod(u,tt,"showKeyboard"),e.cloneMethod(u,tt,"hideKeyboard"),e.cloneMethod(u,tt,"updateKeyboard"),e.cloneMethod(u,tt,"onKeyboardInput"),e.cloneMethod(u,tt,"onKeyboardConfirm"),e.cloneMethod(u,tt,"onKeyboardComplete"),e.cloneMethod(u,tt,"offKeyboardInput"),e.cloneMethod(u,tt,"offKeyboardConfirm"),e.cloneMethod(u,tt,"offKeyboardComplete"),e.cloneMethod(u,tt,"getOpenDataContext"),e.cloneMethod(u,tt,"onMessage"),e.cloneMethod(u,tt,"getSharedCanvas"),e.cloneMethod(u,tt,"loadFont"),e.cloneMethod(u,tt,"onShow"),e.cloneMethod(u,tt,"onHide"),e.cloneMethod(u,tt,"onError"),e.cloneMethod(u,tt,"offError"),o=!1,a=1,tt.onDeviceOrientationChange&&tt.onDeviceOrientationChange(function(e){r(),"landscape"===e.value?a=1:"landscapeReverse"===e.value&&(a=-1)}),Object.assign(u,{startAccelerometer:function(i){o?tt.startAccelerometer&&tt.startAccelerometer({fail:function(e){console.error("start accelerometer failed",e)}}):(o=!0,tt.onAccelerometerChange&&tt.onAccelerometerChange(function(e){var t,n={},r=e.x,o=e.y;c()&&(t=r,r=-o,o=t),n.x=r*a,n.y=o*a,n.z=e.z,i&&i(n)}))},stopAccelerometer:function(){tt.stopAccelerometer&&tt.stopAccelerometer({fail:function(e){console.error("stop accelerometer failed",e)}})}}),u.getSafeArea=function(){var e,t=(s=l?s:tt.getSystemInfoSync()).safeArea,n=t.top,r=t.left,o=t.bottom,i=t.right,a=t.width,t=t.height;return"ios"===s.platform&&!u.isDevTool&&c()&&(e=n,n=r,i-=r=e),{top:n,left:r,bottom:o,right:i,width:a,height:t}})},{"../../../common/utils":18}]},{},[23]);