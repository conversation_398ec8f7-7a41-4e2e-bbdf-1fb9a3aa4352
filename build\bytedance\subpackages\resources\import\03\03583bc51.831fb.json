[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "40OtPssnZP9antMAWsDtDT", "29FYIk+N1GYaeWH/q1NxQO", "f3p84uzd5EVLXvLuhlyoRY", "c0VITW/OJML7691KcQocPY", "c6823ZZu5DO5uHXx3BV9h0", "b4w7GuLh5LQK+DJg3cZXk2", "cb5EJlnN1PT6nfkr+BrxYW", "eehUfKZMNPw7mYhUbofgPH", "85frvjwxZKpLrggdX7DmN/", "81Al3UFUlFFK1qwmCMTo+5", "17W0Ut2DJA4bI//cjeDhds", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr", "ff4daW03RH8bB8d8G1s5Be", "d2L7D5oyRForfZIcQqoQ9G", "b3XmQn21ZLir5G3pyAjGPj", "0e5TC+bJBHZoeuduQqxifK", "daAtp3EMZI5pKH1x8mdQpM", "7c5RB1FepOhKk/ITuMUVUV", "aej5KCfVhFI6ZBtIoyh/zN", "94EziLmzlAirmOfZrgDe1Z", "e7okC76N5PdK86LhdukXa3", "cdvDZRLPZKb70zilA0TpQ8", "9bmIQyoihGpr3HFazyeJcu", "4dMZmkcjlJEbhEI0rAtegJ", "a0rvROFd5IVr9UoEF7ZIum", "e12DhtQJRGEqEN0mD7MZuv", "35wnlFNZNBPYbdGy22Co+h", "7f7PpeEnNDl7QCnDxbF648", "aa9tvKJYdPFrfi+umc03ww"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_parent", "_N$disabledSprite", "root", "_N$barSprite", "data", "_N$skeletonData", "asset"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 1, 9, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "_fillType", "_fillStart", "_fillRange", "node", "_materials", "_spriteFrame"], -2, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "_styleFlags", "_lineHeight", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "_N$paddingLeft", "_N$paddingTop", "_N$paddingBottom", "_N$affectedByScale", "node", "_layoutSize"], -5, 1, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_top", "node"], -2, 1], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.Prefab", ["_name"], 2], ["43e4aiRpN1IQqz9TMKv7zk+", ["node", "nodeArr", "labelArr", "imgArr"], 3, 1, 2, 12, 2], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["1cb88LbIMdCF67rE0bwqZML", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6]], [[8, 0, 1, 2, 2], [0, 0, 6, 7, 4, 5, 8, 2], [17, 0, 1, 2, 2], [2, 5, 6, 7, 1], [2, 0, 1, 5, 6, 7, 3], [3, 0, 1, 5, 2, 3, 4, 6, 9, 10, 11, 8], [0, 0, 6, 9, 7, 4, 5, 8, 2], [9, 0, 1, 2, 3, 4], [0, 0, 1, 6, 9, 7, 4, 5, 8, 3], [6, 0, 2, 3, 4, 5, 6, 2], [0, 0, 9, 7, 4, 5, 8, 2], [2, 5, 6, 1], [9, 0, 1, 3, 3], [10, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 2], [0, 0, 6, 9, 7, 4, 5, 2], [0, 0, 6, 9, 4, 8, 2], [0, 0, 6, 7, 4, 5, 11, 8, 2], [0, 0, 1, 6, 7, 4, 5, 8, 3], [7, 0, 2, 3, 4, 5, 7, 6, 2], [14, 0, 1, 2, 2], [5, 0, 1, 2, 5, 4], [3, 0, 1, 5, 2, 3, 4, 6, 9, 10, 8], [3, 0, 1, 8, 5, 2, 7, 3, 4, 6, 9, 10, 10], [11, 0, 2], [0, 0, 2, 9, 7, 4, 5, 3], [0, 0, 6, 9, 4, 5, 8, 2], [0, 0, 1, 6, 9, 4, 5, 8, 3], [0, 0, 3, 6, 7, 4, 10, 5, 3], [0, 0, 1, 2, 6, 7, 4, 5, 8, 4], [0, 0, 6, 7, 4, 5, 2], [0, 0, 6, 4, 8, 2], [6, 0, 1, 2, 3, 4, 5, 6, 3], [7, 0, 1, 2, 3, 4, 5, 6, 3], [12, 0, 1, 2, 3, 1], [8, 1, 2, 1], [13, 0, 1, 2, 3, 3], [4, 0, 1, 2, 8, 9, 4], [4, 0, 1, 3, 2, 8, 9, 5], [4, 0, 1, 4, 5, 6, 3, 2, 7, 8, 9, 9], [2, 1, 5, 6, 7, 2], [2, 0, 2, 3, 4, 5, 6, 7, 5], [2, 0, 1, 5, 6, 3], [15, 0, 1, 2, 2], [5, 3, 0, 4, 5, 4], [5, 0, 5, 2], [16, 0, 1], [3, 0, 1, 2, 3, 4, 9, 10, 11, 6], [3, 0, 1, 5, 2, 7, 3, 4, 6, 9, 10, 11, 9], [3, 0, 1, 5, 2, 7, 3, 4, 6, 9, 10, 9], [18, 0, 1, 2, 3, 4, 5, 6, 6], [19, 0, 1, 2, 2], [20, 0, 1, 2, 3, 4, 5, 6, 5]], [[[{"name": "bg_tc_jse1", "rect": [0, 0, 108, 61], "offset": [-1, 1.5], "originalSize": [110, 64], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [8]], [[{"name": "font_tc_js_title_sb", "rect": [0, 0, 288, 154], "offset": [0, 0], "originalSize": [288, 154], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [9]], [[{"name": "bg_tc_js2", "rect": [0, 0, 78, 43], "offset": [0, 0], "originalSize": [78, 43], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [10]], [[{"name": "bg_tc_jse", "rect": [1, 0, 604, 116], "offset": [0.5, 0], "originalSize": [605, 116], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [11]], [[{"name": "bg_tc_jsd", "rect": [1, 0, 600, 116], "offset": [-1.5, 0], "originalSize": [605, 116], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [12]], [[{"name": "font_tc_js_title_sl", "rect": [2, 0, 283, 170], "offset": [-0.5, 0], "originalSize": [288, 170], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [13]], [[[24, "M33_Pop_GameEnd"], [25, "M32_Pop_GameEnd", 1, [-17, -18], [[34, -16, [-9, -10, -11, -12, -13, -14, -15], [[-4, -5, null, -6, -7, -8], 1, 1, 0, 1, 1, 1], [-2, -3]]], [35, -1, 0], [5, 750, 1334]], [10, "content", [-21, -22, -23, -24, -25, -26, -27, -28], [[37, 1, 2, 40, -19, [5, 620, 634]], [4, 1, 0, -20, [78], 79]], [0, "cfPCIfUtVMApgW4f3jCFGW", 1, 0], [5, 620, 634], [0, -34.591, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Layout", 2, [-30, -31, -32, -33, -34, -35, -36], [[38, 1, 1, 20, 20, -29, [5, 516, 120]]], [0, "af0PhPKIRLu7si1FMxkRrR", 1, 0], [5, 516, 120], [0, -217, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn_adunlock", false, 3, [-39, -40, -41, -42], [[4, 1, 0, -37, [39], 40], [20, "ModeUnlock", -38, [[12, "43e4aiRpN1IQqz9TMKv7zk+", "onVideoUnlock", 1]]]], [0, "2cvD77zsxI5aAz7i1O1NGH", 1, 0], [5, 248, 100], [-402, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [26, "isWin", 2, [-43, -44, -45, -46, -47], [0, "daOklf2K5Pn53tfDcOqukT", 1, 0], [5, 600, 0], [0, 317, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "ProgressBar", 2, [-51, -52, -53], [[4, 1, 0, -48, [29], 30], [43, 298, -50, -49]], [0, "ccCfRmrcdP7pOlGrnFsLrb", 1, 0], [5, 300, 34], [29.714, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bg", 1, [-56, -57, 2], [[21, 45, 750, 1334, -54], [13, -55, [[7, "43e4aiRpN1IQqz9TMKv7zk+", "onBtn", "BackToMain", 1]]]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [10, "rewardList", [-60], [[39, 1, 3, 5, 35, 30, 15, 15, true, -58, [5, 534, 185]], [44, 0, 1, -21.5, -59]], [0, "82ONQDnlFM5LmdsCL2zWC1", 1, 0], [5, 534, 185], [0, 69, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn_next", false, 3, [-63, -64], [[14, 3, -61, [[7, "43e4aiRpN1IQqz9TMKv7zk+", "onBtn", "NextLevel", 1]], [4, 4293322470], [4, 3363338360], 58], [4, 1, 0, -62, [59], 60]], [0, "72ILAahsFECZuKla08Qe/C", 1, 0], [5, 248, 100], [134, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "btn_adReward", 3, [-67, -68], [[4, 1, 0, -65, [45], 46], [20, "GameEndADReward", -66, [[12, "43e4aiRpN1IQqz9TMKv7zk+", "onClickDoubleReward", 1]]]], [0, "19xjLY3mFEvKrzB1VZJwwY", 1, 0], [5, 248, 100], [-134, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "btn_Share", false, 3, [-71, -72], [[4, 1, 0, -69, [51], 52], [46, -70]], [0, "5b7YoakH9Cz5AJpXpgqD+L", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "btn_playagain", 3, [-75, -76], [[14, 3, -73, [[7, "43e4aiRpN1IQqz9TMKv7zk+", "onBtn", "Replay", 1]], [4, 4293322470], [4, 3363338360], 66], [4, 1, 0, -74, [67], 68]], [0, "13YRsAbzpBU7NRucCRvu3D", 1, 0], [5, 248, 100], [134, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "BackToMain", false, 3, [-79, -80], [[14, 3, -77, [[7, "43e4aiRpN1IQqz9TMKv7zk+", "onBtn", "BackToMain", 1]], [4, 4293322470], [4, 3363338360], 72], [4, 1, 0, -78, [73], 74]], [0, "71695rGRNKdJpFf6anJ/Vo", 1, 0], [5, 248, 100], [268, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [27, "isLose", false, 2, [-81, -82], [0, "71Q+fOuOVN3pEheetocyPd", 1, 0], [5, 600, 0], [0, 317, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "line", 2, [-83, -84, -85], [0, "f2LMkrXr9Jrb405qWeHpsn", 1, 0], [0, 277, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "mask", 140, 7, [[40, 0, -86, [0], 1], [21, 45, 100, 100, -87]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "Label_tips", 15, [[47, "获得奖励", 36, false, 1, 1, -88, [20], 21], [2, 3, -89, [4, 4278190080]]], [0, "efxLhK9ihAcKdz4JSDQXXu", 1, 0], [5, 150, 56.4], [0, 4.288, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "num", 6, [[48, "70%", 24, false, false, 1, 1, 1, 1, -90, [25], 26], [2, 2, -91, [4, 4278190080]]], [0, "65IpxRMRJCWq2yxq4DyRMg", 1, 0], [5, 52.74, 54.4], [0, 1.758, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Label", 6, [[5, "关卡进度", 28, false, false, 1, 1, 1, -92, [27], 28], [2, 2, -93, [4, 4278190080]]], [0, "edCW4KNQpE/7eeLTUe8cA9", 1, 0], [5, 116, 54.4], [0, 1, 0.5], [-160.096, 1.758, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New ScrollView", 2, [-95], [[50, false, 0.75, 0.23, null, null, -94, 8]], [0, "0dM5pKLtlKuobBVEgjDSJr", 1, 0], [5, 620, 280], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 20, [8], [[51, 0, -96, [32]]], [0, "02EmAt8cZNbpQ8etZpV5Hj", 1, 0], [5, 620, 280]], [1, "Label", 4, [[5, "提前解锁", 28, false, false, 1, 1, 1, -97, [35], 36], [2, 2, -98, [4, 4278190080]]], [0, "90ZE0oH1NBnrp+oDkUlj8l", 1, 0], [5, 116, 54.4], [34.75, -14.885, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "tips", 4, [[-99, [2, 2, -100, [4, 4278190080]]], 1, 4], [0, "6eH1GRdEdBG7E20+CVvru0", 1, 0], [5, 318.52, 54.4], [-5.791, -78.888, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "adcount", 4, [[-101, [2, 2, -102, [4, 4278190080]]], 1, 4], [0, "efSk83kCZJbZnNOCr4NxzB", 1, 0], [5, 58.41, 54.4], [34.75, 23.965, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 10, [[5, "奖励翻倍", 36, false, false, 1, 1, 1, -103, [43], 44], [2, 2, -104, [4, 4278190080]]], [0, "d2zMNXM4xN4oyi2G+tGsH/", 1, 0], [5, 148, 54.4], [21.988, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 11, [[5, "炫耀一下", 36, false, false, 1, 1, 1, -105, [47], 48], [2, 2, -106, [4, 4278190080]]], [0, "edD+xDHZVG/bpoo9LZyQhj", 1, 0], [5, 148, 54.4], [21.988, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 9, [[5, "下一关", 36, false, false, 1, 1, 1, -107, [53], 54], [2, 2, -108, [4, 4278190080]]], [0, "d8JigGxTFNLqWxREl01EQ5", 1, 0], [5, 112, 54.4], [0, 17.26, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "icon_lightning", 9, [-110], [[3, -109, [56], 57]], [0, "19dBGCp5VMbIiumu0TRzcl", 1, 0], [5, 54, 55], [-20.351, -26.296, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [9, "Label", 28, [[-111, [2, 2, -112, [4, 4278190080]]], 1, 4], [0, "66lOQJVK9NX7slhqUUCXLo", 1, 0], [5, 61.09, 64.47999999999999], [64.405, 9.893, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 12, [[5, "再次挑战", 36, false, false, 1, 1, 1, -113, [61], 62], [2, 2, -114, [4, 4278190080]]], [0, "70mth7lkpD/YaaJt+TupoO", 1, 0], [5, 148, 54.4], [0, 17.808, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "icon_lightning", 12, [-116], [[3, -115, [64], 65]], [0, "c8/PChX4pAjpX1X2O60A4n", 1, 0], [5, 54, 55], [-19.715, -27.48, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [9, "Label", 31, [[-117, [2, 2, -118, [4, 4278190080]]], 1, 4], [0, "c7wmNi7FBGGo3ygDmhRBTu", 1, 0], [5, 47.3, 64.47999999999999], [66.27, 9.398, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "tips", false, 13, [[-119, [2, 3, -120, [4, 3573547008]]], 1, 4], [0, "21IV6GUvZAgbGYrKtH5Td1", 1, 0], [5, 375.56, 56.4], [0, -74.018, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 13, [[5, "返回主页", 36, false, false, 1, 1, 1, -121, [70], 71], [2, 2, -122, [4, 4278190080]]], [0, "67xswwqsFEwobHRMRKAIsS", 1, 0], [5, 148, 54.4], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "icon", false, 1, 3, [[11, -123, [75]], [13, -124, [[12, "43e4aiRpN1IQqz9TMKv7zk+", "onClickDamagePanel", 1]]]], [0, "5an4tLkzFP34f7sbS0fXjJ", 1, 0], [5, 112, 95], [804, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [10, "btn_close", [-126], [[13, -125, [[7, "43e4aiRpN1IQqz9TMKv7zk+", "onBtn", "BackToMain", 1]]]], [0, "2eQHuqJiRNu6lk4pI2dlZ5", 1, 0], [5, 248, 100], [0, -43.134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 36, [[5, "点击空白区域可关闭", 36, false, false, 1, 1, 1, -127, [76], 77], [2, 2, -128, [4, 4278190080]]], [0, "92j1mloLNJB4h494kddB3p", 1, 0], [5, 328, 54.4], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "maskbg", 1, [[45, 45, -129]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [18, "effect_ribbons", false, 7, [[52, "default", "animation", 0, "animation", -130, [2], 3]], [0, "c60DQJ3S5BYKleDWdDZmFO", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 646.266, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pic_tc_js_sl", 5, [[41, 3, 1, 0.5, 1, -131, [4], 5]], [0, "e0kX0urV1GZq6YS5NyfhQ2", 1, 0], [5, 720, 720], [0, -2.374, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title_02", 5, [[3, -132, [6], 7]], [0, "21huFGC8NLLLv/tQufqFsS", 1, 0], [5, 604, 116], [0, 55.794, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_tc_js2", 5, [[3, -133, [8], 9]], [0, "b8/WV8TI1D+LK0Jry3aNwx", 1, 0], [5, 78, 43], [-159.644, 103.094, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_tc_jse1", 5, [[3, -134, [10], 11]], [0, "f7ajLDJXVKqLCiBL6XRkCV", 1, 0], [5, 108, 61], [164.238, 69.787, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "isWin", 5, [-135], [0, "6biTrtGpBP8Y+CLpncmtXq", 1, 0], [5, 283, 170], [0, 0.5, 0], [0, 5.61, 0, 0, 0, 0, 1, 1, 1, 1]], [11, 44, [12]], [1, "title_02", 14, [[3, -136, [13], 14]], [0, "e0aD8GGRVCYZWrroaQmDS/", 1, 0], [5, 600, 116], [0, 56.067, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "isLose", 14, [[3, -137, [15], 16]], [0, "2c37lfUnZJ1pbdsCruetTI", 1, 0], [5, 288, 154], [0, 0.5, 0], [0, 5.883, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "img", false, 2, [-138], [0, "87MVm37BtFSoqozBzSyIi2", 1, 0], [5, 575, 298], [0, 266, 0, 0, 0, 0, 1, 1, 1, 1]], [11, 48, [17]], [1, "line", 15, [[3, -139, [18], 19]], [0, "73Pmp8uitJGJRQEcYQa5vp", 1, 0], [5, 531, 47], [12, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [18, "line", false, 15, [[3, -140, [22], 23]], [0, "c7rG7Lh/FKMoJ/0spLGnmz", 1, 0], [5, 87, 30], [155, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "bar", 6, [-141], [0, "69hl+Cpa9OcYBVG8rUBCMs", 1, 0], [5, 298, 34], [0, 0, 0.5], [-149.213, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [42, 1, 0, 52, [24]], [31, "RewardItem", 8, [36, "37hTL9bKpCDIOEu0ttyZ8W", true, -142, 31], [-204, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 4, [[3, -143, [33], 34]], [0, "23CmJ2i05BPL6t8VIOYwdq", 1, 0], [5, 50, 52], [-71.43, 4.466, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "主线通关第%d关可解锁下一关", 24, false, false, 1, 1, 1, 23, [37]], [49, "0/5", 32, false, false, 1, 1, 1, 1, 24, [38]], [1, "videoicon", 10, [[3, -144, [41], 42]], [0, "04UekMd3VBPpjY/aePy5uB", 1, 0], [5, 50, 52], [-79.553, -1.627, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bt_share", 11, [[3, -145, [49], 50]], [0, "59TJuRTjNHf7BzrMt5YWn9", 1, 0], [5, 48, 46], [-76.112, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "-5", 48, 48, false, false, 1, 1, 1, 1, 29, [55]], [23, "-5", 48, 48, false, false, 1, 1, 1, 1, 32, [63]], [22, "通关主线第2关后可以继续", 32, false, false, 1, 1, 1, 33, [69]], [16, "New Node", 2, [36], [0, "96njot3i9Kmb9A2HJWvY4L", 1, 0], [0, -317, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 6, 1, 0, -1, 45, 0, -2, 49, 0, -1, 60, 0, -2, 61, 0, -4, 62, 0, -5, 56, 0, -6, 57, 0, -1, 8, 0, -2, 4, 0, -3, 9, 0, -4, 6, 0, -5, 3, 0, -6, 5, 0, -7, 14, 0, 0, 1, 0, -1, 38, 0, -2, 7, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 14, 0, -3, 48, 0, -4, 15, 0, -5, 6, 0, -6, 20, 0, -7, 3, 0, -8, 63, 0, 0, 3, 0, -1, 4, 0, -2, 10, 0, -3, 11, 0, -4, 9, 0, -5, 12, 0, -6, 13, 0, -7, 35, 0, 0, 4, 0, 0, 4, 0, -1, 55, 0, -2, 22, 0, -3, 23, 0, -4, 24, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, -4, 43, 0, -5, 44, 0, 0, 6, 0, 7, 53, 0, 0, 6, 0, -1, 52, 0, -2, 18, 0, -3, 19, 0, 0, 7, 0, 0, 7, 0, -1, 16, 0, -2, 39, 0, 0, 8, 0, 0, 8, 0, -1, 54, 0, 0, 9, 0, 0, 9, 0, -1, 27, 0, -2, 28, 0, 0, 10, 0, 0, 10, 0, -1, 58, 0, -2, 25, 0, 0, 11, 0, 0, 11, 0, -1, 26, 0, -2, 59, 0, 0, 12, 0, 0, 12, 0, -1, 30, 0, -2, 31, 0, 0, 13, 0, 0, 13, 0, -1, 33, 0, -2, 34, 0, -1, 46, 0, -2, 47, 0, -1, 50, 0, -2, 17, 0, -3, 51, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -1, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, -1, 56, 0, 0, 23, 0, -1, 57, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -1, 29, 0, -1, 60, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -1, 32, 0, -1, 61, 0, 0, 32, 0, -1, 62, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, -1, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, -1, 45, 0, 0, 46, 0, 0, 47, 0, -1, 49, 0, 0, 50, 0, 0, 51, 0, -1, 53, 0, 6, 54, 0, 0, 55, 0, 0, 58, 0, 0, 59, 0, 8, 1, 2, 4, 7, 8, 4, 21, 36, 4, 63, 145], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 49, 53, 56, 57, 61], [-1, 1, -1, 9, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 2, -1, 1, -1, -1, 2, -1, 2, -1, 1, 10, -1, -1, 1, -1, 2, -1, -1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, -1, 1, 5, -1, 1, -1, 2, -1, -1, 1, 5, -1, 1, -1, -1, 2, 5, -1, 1, -1, -1, 2, -1, 1, 1, 1, 1, 2, 2, 2], [0, 14, 15, 16, 0, 17, 0, 18, 0, 19, 0, 20, 0, 0, 21, 0, 22, 0, 0, 23, 0, 1, 0, 24, 0, 0, 2, 0, 1, 0, 25, 26, 0, 0, 5, 0, 1, 0, 0, 0, 6, 0, 5, 0, 1, 0, 3, 0, 1, 0, 27, 0, 3, 0, 1, 0, 0, 7, 4, 0, 6, 0, 1, 0, 0, 7, 4, 0, 28, 0, 0, 1, 4, 0, 3, 0, 0, 1, 0, 29, 30, 31, 32, 1, 2, 2]], [[{"name": "pic_tc_js_sl", "rect": [0, 0, 720, 720], "offset": [0, 0], "originalSize": [720, 720], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [33]]]]