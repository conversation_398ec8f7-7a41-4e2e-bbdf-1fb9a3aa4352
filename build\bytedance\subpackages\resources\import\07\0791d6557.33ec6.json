[1, ["ecpdLyjvZBwrvm+cedCcQy", "41ozMimvVBbp1Pk1gt48S3", "d8Uh+j4B9Pq7gdV7E6lgEL", "a2MjXRFdtLlYQ5ouAFv/+R", "8bKuW8OblPdqo2tgVNBBXp", "a7d0c1XsVI65tBRRYSHBFe", "fcEfsWwdRKuIu7FqrOs/ub", "2e/6lVLA1CFZfKlgkhRit3"], ["node", "_spriteFrame", "_textureSetter", "root", "icon_fail", "failBg", "lbl_name_bg", "icon", "lbl_lv", "lbl_name", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_color", "_trs"], 0, 9, 4, 5, 1, 2, 5, 7], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 2, 4, 5, 7, 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "node", "_materials"], -4, 1, 3], ["cc.Prefab", ["_name"], 2], ["a5defhqfHlNCq/7Qbl+BNL0", ["node", "lbl_name", "lbl_lv", "icon", "lbl_name_bg", "failBg", "icon_fail"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [1, 3, 4, 1], [9, 0, 1, 2, 3, 4], [10, 0, 1, 2, 2], [7, 0, 2], [0, 0, 7, 3, 4, 5, 2], [0, 0, 1, 2, 6, 3, 4, 8, 5, 4], [0, 0, 6, 3, 4, 5, 2], [0, 0, 1, 6, 3, 4, 5, 9, 3], [3, 0, 1, 6, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 2], [4, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 5, 6, 1], [5, 1, 2, 1], [1, 0, 1, 3, 4, 5, 3], [1, 2, 0, 1, 3, 4, 5, 4], [1, 3, 4, 5, 1], [6, 0, 1, 5, 2, 3, 4, 6, 7, 8, 8], [6, 0, 1, 2, 3, 4, 7, 8, 6]], [[[{"name": "img_fd_cha", "rect": [0, 0, 69, 69], "offset": [0, 0], "originalSize": [69, 69], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [1]], [[[4, "M30_ItemView"], [5, "M30_ItemView", [-9, -10, -11, -12, -13, -14], [[13, -8, -7, -6, -5, -4, -3, -2]], [14, -1, 0], [5, 279, 269]], [6, "fail_bg", false, 156, 1, [[15, 0, false, -15, [6], 7], [2, 45, 100, 100, -16]], [0, "ae7kVI1dJNA4Nu02hejo4L", 1, 0], [4, 4278190080], [5, 279, 269]], [7, "bg", 1, [[16, 1, 0, false, -17, [0], 1], [2, 45, 116, 120, -18]], [0, "a3lph4JQ9Kc7Wv2gjJpFZT", 1, 0], [5, 279, 269]], [9, "lbl_name_bg", 1, [-20], [-19], [0, "64WvKmtO1H56OnF09DvBWA", 1, 0], [5, 248, 45], [0, -51.267, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbl_name", 4, [[-21, [3, 3, -22, [4, 4278190080]]], 1, 4], [0, "520LPfsW1P7pfDMzsML3jc", 1, 0], [5, 240, 56.4]], [12, "lbl_lv", 1, [[-23, [3, 3, -24, [4, 4278190080]]], 1, 4], [0, "c644aGyx5E14D4g0Xlzt3u", 1, 0], [5, 56.85, 56.4], [0, -92.92, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon_fail", false, 1, [[17, -25, [8], 9]], [0, "feyLoH0QxIhYEtG550SuG0", 1, 0], [5, 69, 69], [0, 49.931, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "icon", 1, [-26], [0, "c8TQPIne1PvJf+EhL/y/tz", 1, 0], [5, 104, 105], [0, 50.973, 0, 0, 0, 0, 1, 1, 1, 1]], [1, 8, [2]], [18, "科学家", 26, false, false, 1, 1, 2, 5, [3]], [1, 4, [4]], [19, "Lv.4", 28, false, 1, 1, 6, [5]]], 0, [0, 3, 1, 0, 4, 7, 0, 5, 2, 0, 6, 11, 0, 7, 9, 0, 8, 12, 0, 9, 10, 0, 0, 1, 0, -1, 3, 0, -2, 8, 0, -3, 4, 0, -4, 6, 0, -5, 2, 0, -6, 7, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 11, 0, -1, 5, 0, -1, 10, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 7, 0, -1, 9, 0, 10, 1, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 11], [-1, 1, -1, -1, -1, -1, -1, 1, -1, 1, 1, 1], [0, 2, 0, 0, 0, 0, 0, 3, 0, 4, 5, 6]], [[{"name": "img_fd_qz01", "rect": [0, 0, 248, 45], "offset": [0, 0], "originalSize": [248, 45], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [7]]]]