[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "847uA966ZMM4xY2ASONO3n", "a8lPnHTihBmrRgij+r2Olk", "4dMZmkcjlJEbhEI0rAtegJ", "66pEqh4J5GSqoB0lCmas/G", "7cJxpRDu5AQrSVbfl/Gl3I", "a2MjXRFdtLlYQ5ouAFv/+R", "f9xN5KLk1Fu5fnoIpuvthY", "fcmCL2LrRARLO3LunrRR6/", "35Uia00oxLCIpv12oi22UN", "40OtPssnZP9antMAWsDtDT", "90WnFtfS5BCIc6aeQTWLCf", "e3zCMO7JBO/7yFyWQF0E/z"], ["node", "_spriteFrame", "_N$file", "_parent", "_textureSetter", "_N$target", "root", "itemNeed3", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_children", "_parent", "_trs", "_color", "_anchorPoint", "_eulerAngles"], 0, 4, 5, 9, 2, 1, 7, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_lineHeight", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_enableWrapText", "_N$cacheMode", "node", "_materials", "_N$file"], -6, 1, 3, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 1, 12, 4, 5, 7, 1, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$affectedByScale", "_N$paddingLeft", "node", "_layoutSize"], -2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$target"], 2, 1, 9, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["c79f4q9O5dCxLAuSI7LiBGk", ["node", "nodeArr", "labelArr", "itemNeed3", "sprArr"], 3, 1, 2, 2, 1, 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["6467bRqlENIrawyykX3FMRo", ["node"], 3, 1]], [[5, 0, 1, 2, 2], [14, 0, 1, 2, 2], [0, 0, 7, 5, 3, 4, 8, 2], [0, 0, 6, 5, 3, 4, 8, 2], [4, 0, 6, 2, 3, 4, 5, 2], [1, 1, 0, 2, 3, 4, 3], [2, 0, 6, 1, 2, 3, 4, 5, 9, 10, 11, 8], [2, 0, 6, 1, 2, 3, 4, 5, 9, 10, 8], [1, 2, 3, 4, 1], [13, 0, 1, 2, 3, 4], [0, 0, 7, 6, 5, 3, 4, 8, 2], [0, 0, 7, 6, 3, 8, 2], [10, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 2], [1, 2, 3, 1], [7, 0, 1, 2, 3, 2], [1, 0, 2, 3, 4, 2], [12, 0, 1], [9, 0, 2], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 6, 3, 4, 2], [0, 0, 7, 6, 5, 3, 4, 10, 2], [0, 0, 2, 7, 5, 3, 9, 4, 3], [0, 0, 7, 6, 5, 3, 4, 2], [0, 0, 7, 5, 3, 9, 4, 8, 11, 2], [4, 0, 7, 2, 3, 4, 5, 2], [4, 0, 1, 6, 2, 3, 4, 5, 3], [11, 0, 1, 2, 3, 4, 1], [5, 1, 2, 1], [6, 0, 1, 2, 3, 5, 6, 5], [6, 0, 1, 4, 2, 5, 6, 5], [7, 0, 1, 2, 2], [8, 0, 3, 2], [8, 0, 1, 2, 3, 4], [2, 0, 6, 1, 7, 2, 3, 4, 5, 8, 9, 10, 11, 10], [2, 0, 1, 2, 3, 4, 5, 9, 10, 7], [15, 0, 1]], [[[{"name": "bg_ds_ad", "rect": [0, 0, 101, 30], "offset": [-0.5, 0], "originalSize": [102, 30], "capInsets": [24.5, 12, 23.5, 14]}], [3], 0, [0], [4], [6]], [[[18, "FundBuyView"], [19, "FundBuyView", 1, [-16, -17], [[27, -15, [-14], [-6, -7, -8, -9, -10, -11, -12, -13], -5, [-2, -3, -4]]], [28, -1, 0], [5, 750, 1334]], [3, "img_gmdj_ld", [-20, -21, -22, -23, -24, -25], [[5, 1, 0, -18, [28], 29], [17, -19]], [0, "8dWfj2cbJHX5k5mQb5ipwP", 1, 0], [5, 690, 205], [0, -220.264, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Item_NeedDisplay", [-29, -30], [[[29, 1, 1, 2, true, -27, [5, 88.72999999999999, 40]], -28], 4, 1], [13, "9c+DFHGMhPrL5QGGJQkRWf", -26], [5, 88.72999999999999, 40], [0, 2.088, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [3, "btn_home_yellow", [-34, -35], [[5, 1, 0, -31, [37], 38], [15, 3, -33, [[9, "c79f4q9O5dCxLAuSI7LiBGk", "onBtnClick", "zuan1", 1]], -32]], [0, "81RDUAAyNL/4F3plo2Bs1X", 1, 0], [5, 146, 70], [-167.688, -7.62, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btn_home_yellow", [-39, -40], [[5, 1, 0, -36, [46], 47], [15, 3, -38, [[9, "c79f4q9O5dCxLAuSI7LiBGk", "onBtnClick", "zuan5", 1]], -37]], [0, "eeQWqg1CZM9pycF3ZngE3C", 1, 0], [5, 146, 70], [-167.688, -7.62, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "btn_home_yellow", [-44, -45], [[5, 1, 0, -41, [55], 56], [15, 3, -43, [[9, "c79f4q9O5dCxLAuSI7LiBGk", "onBtnClick", "zuan10", 1]], -42]], [0, "2bsCt/8v5D/ZRB71nlb8OH", 1, 0], [5, 146, 70], [-167.688, -7.62, 0, 0, 0, 0, 1, 1, 1, 0]], [20, "content", [-46, -47, 2, -48], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 680, 1126]], [10, "button03", 2, [3, -51], [[5, 1, 0, -49, [26], 27], [31, 3, -50, [[9, "c79f4q9O5dCxLAuSI7LiBGk", "onBtnClick", "jin10", 1]]]], [0, "69P5UCuQ1NvKWwCZQMTS74", 1, 0], [5, 204, 86], [177.808, 17.632, 0, 0, 0, 0, 1, 1, 1, 0]], [21, "New Node", 7, [-53, -54, -55], [[30, 1, 1, -30, 200, -52, [5, 370, 0]]], [0, "41aV+uGW5AWb7JZAIL7EEd", 1, 0], [5, 370, 0], [0, 0, 0.5]], [10, "icon_djd", 2, [-57, -58], [[16, 0, -56, [14], 15]], [0, "d86QIFkUdG9JuIUcR1hN1R", 1, 0], [5, 110, 120], [-256.294, 25.288, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_djd", [-60, -61], [[8, -59, [33], 34]], [0, "205T+xLRJNWpe9kFfgKZ4H", 1, 0], [5, 70, 76], [-196.361, 74.891, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_djd", [-63, -64], [[8, -62, [42], 43]], [0, "11+LIJUtpAQ4RCV0f+s2At", 1, 0], [5, 70, 76], [-196.361, 74.891, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_djd", [-66, -67], [[8, -65, [51], 52]], [0, "93zKzyhghJ9b8JkrfNwdY9", 1, 0], [5, 70, 76], [-196.361, 74.891, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "maskbg", 155, 1, [[32, 45, -68], [16, 0, -69, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [23, "bg", 1, [7], [[33, 45, 750, 1334, -70]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [2, "img_gmdj_zd", 7, [[5, 1, 0, -71, [2], 3], [17, -72]], [0, "7bwTTKBdRDHLEj2AllesEK", 1, 0], [5, 690, 292], [0, 54.565, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "New Sprite", 2, [-74], [[8, -73, [8], 9]], [0, "d47CEf7aNP1bOQmdpCrTqp", 1, 0], [5, 80, 77], [317.954, 83.234, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "New Label", 17, [[6, "-60%", 24, 31, false, 1, 1, 1, -75, [6], 7], [1, 2, -76, [4, 4279506112]]], [0, "30mVjyPUVHdapkF0t4YT68", 1, 0], [4, 4284019199], [5, 63.77, 43.06], [2.356, 2.813, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, 45]], [2, "New Label", 10, [[6, "+10", 46, 31, false, 1, 1, 1, -77, [10], 11], [1, 2, -78, [4, 4278190080]]], [0, "d61lEq5i1PbbBQ4t7eKtMb", 1, 0], [5, 77.58, 43.06], [-7.983, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 10, [[6, "级", 26, 31, false, 1, 1, 1, -79, [12], 13], [1, 2, -80, [4, 4278190080]]], [0, "381oJybqpF25030t0Z7OZR", 1, 0], [5, 30, 43.06], [48.704, 2.443, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 2, [[6, "以超实惠的价格加快\n你的进度推进！", 26, 31, false, 1, 1, 1, -81, [16], 17], [1, 2, -82, [4, 4278190080]]], [0, "d7IopdWXVNmpF3O7SaPg6y", 1, 0], [5, 238, 74.06], [-54.736, 21.862, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 2, [[-83, [1, 2, -84, [4, 4278190080]]], 1, 4], [0, "3cviPy++VNyIVUPAbWtr8u", 1, 0], [5, 74.54, 43.06], [0, -66.764, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "num", 3, [[34, "100", 30, 36, false, false, 1, 1, 1, 1, -85, [23], 24], [1, 2, -86, [4, 4278190080]]], [13, "c51Kf+QEpFP7uzo7Z29m6A", 3], [5, 53.83, 49.36], [17.450000000000003, 1.904, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "New Label", false, 8, [[-87, [1, 2, -88, [4, 4278190080]]], 1, 4], [0, "efWQlCCYhIkZBgBlSabih6", 1, 0], [5, 105.04, 43.06], [0, 5.84, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Node", 9, [11, 4], [0, "f0tL2qfr1LSbSpmAdHL/Pk", 1, 0], [-30, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 11, [[-89, [1, 2, -90, [4, 4278190080]]], 1, 4], [0, "1dVfKaxWFOm5Slq0oneijJ", 1, 0], [5, 30, 43.06], [0.907, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 11, [[6, "等级", 26, 31, false, 1, 1, 1, -91, [31], 32], [1, 2, -92, [4, 4278190080]]], [0, "ab7WpK9jVNzLS8OVs3v4CS", 1, 0], [5, 56, 43.06], [69.174, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 4, [[-93, [1, 2, -94, [4, 4278190080]]], 1, 4], [0, "77PBdKbt9G7497fce8u6gS", 1, 0], [5, 55.01, 41.8], [26.929, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Node", 9, [12, 5], [0, "b9FzzYp+ZI+Yil6NhQBERi", 1, 0], [170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 12, [[-95, [1, 2, -96, [4, 4278190080]]], 1, 4], [0, "05U5CC0t5DvbkuWkS08lHF", 1, 0], [5, 31.98, 43.06], [0.907, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 12, [[6, "等级", 26, 31, false, 1, 1, 1, -97, [40], 41], [1, 2, -98, [4, 4278190080]]], [0, "5atpuuch5IfKBiXS3faAAt", 1, 0], [5, 56, 43.06], [69.174, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 5, [[-99, [1, 2, -100, [4, 4278190080]]], 1, 4], [0, "5fBAw252RHha5t+usjwHKB", 1, 0], [5, 55.01, 41.8], [26.929, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "New Node", 9, [13, 6], [0, "62Zfnv3qZA+I7eYqFkQ3Pc", 1, 0], [370, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 13, [[-101, [1, 2, -102, [4, 4278190080]]], 1, 4], [0, "9dq01KYHxBXpVAoctRsqp8", 1, 0], [5, 45.59, 43.06], [0.907, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 13, [[6, "等级", 26, 31, false, 1, 1, 1, -103, [49], 50], [1, 2, -104, [4, 4278190080]]], [0, "04ks6GTQlE0K9JsrOyHo07", 1, 0], [5, 56, 43.06], [69.174, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 6, [[-105, [1, 2, -106, [4, 4278190080]]], 1, 4], [0, "3aMDjbYExOBZFWGNXoYs13", 1, 0], [5, 55.01, 41.8], [26.929, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_gmdj_btd", 7, [[8, -107, [4], 5]], [0, "f5Pb1VW6pJfYRfg1Yn3KFO", 1, 0], [5, 466, 299], [0, 271.894, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_storelv", 2, [[5, 1, 0, -108, [18], 19]], [0, "eax+9zbqlOUou1cl1py9o2", 1, 0], [5, 700, 49], [0, -66.136, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "剩余:1", 26, 31, false, 1, 1, 1, 22, [20]], [2, "icon", 3, [[8, -109, [21], 22]], [13, "37+oKdJX9Hr4prAI9kch+w", 3], [5, 47, 55], [-27.914999999999996, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [36, 3], [35, "¥2.99", 31, false, 1, 1, 1, 24, [25]], [7, "+1", 26, 31, false, 1, 1, 1, 26, [30]], [12, "shuijing", 4, [-110], [0, "ffpuVb13dOVK/Tw9fn/wiB", 1, 0], [5, 47, 55], [-33.176, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [14, 44, [35]], [7, "240", 30, 30, false, 1, 1, 1, 28, [36]], [7, "+5", 26, 31, false, 1, 1, 1, 30, [39]], [12, "shuijing", 5, [-111], [0, "3b/J40eT1LGYOQDNYXDRjo", 1, 0], [5, 47, 55], [-33.176, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [14, 48, [44]], [7, "240", 30, 30, false, 1, 1, 1, 32, [45]], [7, "+10", 26, 31, false, 1, 1, 1, 34, [48]], [12, "shuijing", 6, [-112], [0, "adkStxlq9IYLhp4fAJA1jE", 1, 0], [5, 47, 55], [-33.176, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [14, 52, [53]], [7, "240", 30, 30, false, 1, 1, 1, 36, [54]]], 0, [0, 6, 1, 0, -1, 45, 0, -2, 49, 0, -3, 53, 0, 7, 41, 0, -1, 46, 0, -2, 50, 0, -3, 54, 0, -4, 42, 0, -5, 43, 0, -6, 47, 0, -7, 51, 0, -8, 39, 0, -1, 2, 0, 0, 1, 0, -1, 14, 0, -2, 15, 0, 0, 2, 0, 0, 2, 0, -1, 17, 0, -2, 10, 0, -3, 21, 0, -4, 38, 0, -5, 22, 0, -6, 8, 0, 6, 3, 0, 0, 3, 0, -2, 41, 0, -1, 40, 0, -2, 23, 0, 0, 4, 0, 5, 4, 0, 0, 4, 0, -1, 44, 0, -2, 28, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, -1, 48, 0, -2, 32, 0, 0, 6, 0, 5, 6, 0, 0, 6, 0, -1, 52, 0, -2, 36, 0, -1, 16, 0, -2, 37, 0, -4, 9, 0, 0, 8, 0, 0, 8, 0, -2, 24, 0, 0, 9, 0, -1, 25, 0, -2, 29, 0, -3, 33, 0, 0, 10, 0, -1, 19, 0, -2, 20, 0, 0, 11, 0, -1, 26, 0, -2, 27, 0, 0, 12, 0, -1, 30, 0, -2, 31, 0, 0, 13, 0, -1, 34, 0, -2, 35, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, -1, 39, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, -1, 42, 0, 0, 24, 0, -1, 43, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 46, 0, 0, 28, 0, -1, 47, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, -1, 50, 0, 0, 32, 0, -1, 51, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, -1, 54, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 40, 0, -1, 45, 0, -1, 49, 0, -1, 53, 0, 8, 1, 2, 3, 7, 3, 3, 8, 4, 3, 25, 5, 3, 29, 6, 3, 33, 7, 3, 15, 11, 3, 25, 12, 3, 29, 13, 3, 33, 112], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 42, 43, 45, 46, 47, 49, 50, 51, 53, 54], [-1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 1, -1, -1, 1, -1, 2, -1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, -1, -1, 1, -1, -1, 2, -1, 1, -1, -1, -1, 1, -1, -1, 2, -1, 1, -1, -1, -1, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 1, 2], [0, 7, 0, 5, 0, 8, 0, 1, 0, 9, 0, 1, 0, 1, 0, 2, 0, 1, 0, 10, 0, 0, 3, 0, 1, 0, 0, 11, 0, 5, 0, 0, 1, 0, 2, 0, 0, 0, 4, 0, 0, 1, 0, 2, 0, 0, 0, 4, 0, 0, 1, 0, 2, 0, 0, 0, 4, 1, 1, 1, 3, 1, 1, 3, 1, 1, 3, 1]], [[{"name": "gif-2", "rect": [0, 0, 465, 298], "offset": [-0.5, 0.5], "originalSize": [466, 299], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [4], [12]], [[{"name": "img_gmdj_zk", "rect": [0, 0, 80, 77], "offset": [0, 0], "originalSize": [80, 77], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [4], [13]]]]