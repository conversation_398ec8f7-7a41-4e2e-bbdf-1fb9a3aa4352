[1, ["ecpdLyjvZBwrvm+cedCcQy", "16Q7GimVpKMKQVsD3CBhgg", "3aw2Fe8QpHKqTB2OCTx1NL", "34j9M9cF5JNbd2jpTtoV6t", "cdvDZRLPZKb70zilA0TpQ8", "5b0HySO0xNLI16cb6FYAR2", "3dx1OyHXxL7JKFCbx/bLMu", "1axW4/yeBKwJJdSFZ6gdP6", "9cfYuLwOFNY4zbabnUGump", "61A1GvNsNLBL1jGs2Mkf2a", "68wcDjirJCPbVUVithBcoK"], ["node", "_spriteFrame", "_textureSetter", "root", "rarityNum", "rarityLv", "rtag", "equipTypeIcon", "equipTypeBg", "lock", "lvLabel", "data", "asset"], [["cc.Node", ["_name", "_groupIndex", "_active", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs"], 0, 4, 1, 9, 5, 2, 7], ["cc.Sprite", ["_dstBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_lineHeight", "node", "_materials"], -4, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 2, 4, 5, 7, 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["8f6b3WWRtNGn5x0k90e+POI", ["node", "lvLabel", "lock", "equipTypeBg", "equipTypeIcon", "rtag", "rarityLv", "rarityNum"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[6, 0, 1, 2, 2], [10, 0, 1, 2, 2], [1, 3, 4, 1], [4, 0, 1, 6, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [7, 0, 2], [0, 0, 1, 7, 5, 3, 6, 3], [0, 0, 4, 5, 3, 6, 8, 2], [0, 0, 2, 4, 7, 3, 8, 3], [0, 0, 1, 4, 5, 3, 6, 3], [0, 0, 4, 3, 2], [0, 0, 4, 5, 3, 6, 2], [5, 0, 1, 2, 3, 4, 7, 5, 6, 3], [5, 0, 1, 2, 3, 4, 5, 8, 6, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 1], [6, 1, 2, 1], [9, 0, 1, 2, 3, 3], [1, 3, 4, 5, 1], [1, 0, 3, 4, 2], [1, 1, 2, 3, 4, 5, 3], [3, 0, 1, 2, 5, 3, 4, 7, 8, 7], [3, 0, 1, 6, 2, 3, 4, 7, 8, 7], [3, 0, 1, 6, 2, 5, 3, 4, 7, 8, 8]], [[[{"name": "icon_jiezhi", "rect": [25, 26, 72, 71], "offset": [0, -0.5], "originalSize": [122, 122], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [1]], [[{"name": "img_jsdjd2", "rect": [0, 0, 46, 24], "offset": [0, 0], "originalSize": [46, 24], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[{"name": "img_jsjbpz3", "rect": [0, 0, 38, 38], "offset": [0, 0], "originalSize": [38, 38], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [3]], [[[5, "Role_EquipItem"], [6, "Role_EquipItem", 1, [-10, -11, -12, -13, -14, -15, -16], [[14, -9, -8, -7, -6, -5, -4, -3, -2]], [15, -1, 0], [5, 122, 122]], [3, "type", 1, [-18], [-17], [0, "ccCOItz0pNjrojT7L/Dcbz", 1, 0], [5, 38, 38], [-48.656, 45.574, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "rarityLv", 1, [-20], [-19], [0, "9f5ZcZR5FNcbWdcGywvGv8", 1, 0], [5, 46, 24], [28.906, -40.398, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [12, "num", 1, 3, [[-21, [1, 3, -22, [4, 4278190080]]], 1, 4], [0, "94aswZ0zFC2acfvOqTpR8Z", 1, 0], [4, 4282842111], [5, 29.94, 31.2], [0.733, 1.509, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lv", 1, 1, [[-23, [1, 2, -24, [4, 4278190080]]], 1, 4], [0, "f3PW/VkslCs5Mu6cOo91jX", 1, 0], [5, 55.12, 29.2], [0, 1, 0.5], [60.197, 46.166, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lcok", 1, [[17, -25, [7], 8]], [0, "81P8Sq+gVIzLiyfVQiRMQU", 1, 0], [5, 47, 56], [-0.514, -53.462, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [8, "isFitOut", false, 1, [-26, -27], [0, "38wc2V2qJKLbmWwCH0N1di", 1, 0], [0, -37.434, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [9, "lv", 1, 7, [[20, "装备中", 25, false, 1, 1, 1, -28, [11]], [1, 2, -29, [4, 4278190080]]], [0, "3aWd1FRCNOKoKNRgIkrB5e", 1, 0], [5, 79, 54.4]], [10, "RewardItem", 1, [16, "99btH7MoxAMYJ4k+WD5tZW", true, -30, 0]], [4, "icon", 2, [-31], [0, "a3UX57tTlBvYLxX11t6NpF", 1, 0], [5, 72, 71], [0, 0, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [18, 772, 10, [1]], [2, 2, [2]], [4, "rtag", 1, [-32], [0, "ae4+KMRBRAJpjpWlSgWVrA", 1, 0], [5, 34, 45], [-41.562, -38.362, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, 13, [3]], [21, "+2", 21, 20, false, 1, 1, 4, [4]], [2, 3, [5]], [22, "等级2", 20, 20, false, 1, 1, 1, 5, [6]], [11, "img_djsd", 7, [[19, 1, 0, -33, [9], 10]], [0, "17+d1GndtIjao6/zdiAEbr", 1, 0], [5, 117, 40]]], 0, [0, 3, 1, 0, 4, 15, 0, 5, 16, 0, 6, 14, 0, 7, 11, 0, 8, 12, 0, 9, 6, 0, 10, 17, 0, 0, 1, 0, -1, 9, 0, -2, 2, 0, -3, 13, 0, -4, 3, 0, -5, 5, 0, -6, 6, 0, -7, 7, 0, -1, 12, 0, -1, 10, 0, -1, 16, 0, -1, 4, 0, -1, 15, 0, 0, 4, 0, -1, 17, 0, 0, 5, 0, 0, 6, 0, -1, 18, 0, -2, 8, 0, 0, 8, 0, 0, 8, 0, 3, 9, 0, -1, 11, 0, -1, 14, 0, 0, 18, 0, 11, 1, 33], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 12, 14, 16], [12, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 1, 1, 1], [4, 0, 0, 0, 0, 0, 0, 0, 5, 0, 6, 0, 7, 8, 9, 10]]]]