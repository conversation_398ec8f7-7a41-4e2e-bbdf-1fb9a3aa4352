[1, ["ecpdLyjvZBwrvm+cedCcQy", "35RFcv50lBVotRgr46UtiC", "f7293wEF9JhIuMEsrLuqng", "a2MjXRFdtLlYQ5ouAFv/+R", "c0VITW/OJML7691KcQocPY", "73jGpne/9JUI/171Zur5Pr", "f8npR6F8ZIZoCp3cKXjJQz", "c1f4UDWZJNUJmmvN1IN/rK", "40OtPssnZP9antMAWsDtDT", "6eIHunmG5Msrk3dvFEvPBX"], ["node", "_spriteFrame", "_N$file", "_parent", "root", "allEditBox", "layoutList", "btn", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_N$overflow", "_enableWrapText", "_styleFlags", "_lineHeight", "_N$cacheMode", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "node"], -4, 1], ["cc.Layout", ["_N$layoutType", "_resize", "_N$paddingLeft", "_N$spacingX", "_N$spacingY", "_N$paddingRight", "_N$paddingTop", "node", "_layoutSize"], -4, 1, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], 1, 1, 12, 4, 5, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "_enabled", "node", "_color"], 1, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 1, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["d95206PhxJI/ZqaVO45+tCI", ["node", "btn", "layoutList", "allEditBox"], 3, 1, 1, 1, 1]], [[6, 0, 1, 2, 2], [7, 0, 2, 3, 2], [0, 0, 6, 8, 5, 3, 4, 7, 2], [2, 1, 0, 3, 4, 5, 3], [8, 0, 1, 2, 2], [0, 0, 6, 5, 3, 4, 2], [9, 0, 1, 2, 3, 4], [0, 0, 6, 5, 3, 9, 4, 7, 2], [1, 4, 0, 1, 2, 3, 5, 10, 11, 7], [1, 4, 0, 6, 1, 2, 3, 5, 10, 11, 8], [7, 1, 0, 2, 3, 3], [0, 0, 6, 5, 3, 4, 10, 7, 2], [0, 0, 8, 5, 3, 4, 7, 2], [3, 0, 1, 2, 7, 4], [4, 1, 0, 2, 5, 3, 4, 7, 8, 7], [0, 0, 8, 5, 3, 4, 2], [0, 0, 1, 6, 8, 5, 3, 4, 7, 3], [2, 1, 0, 3, 4, 3], [3, 3, 0, 4, 1, 2, 7, 6], [4, 0, 2, 3, 7, 8, 4], [1, 4, 0, 6, 1, 7, 2, 3, 5, 10, 11, 12, 9], [9, 0, 1, 3, 3], [10, 0, 2], [0, 0, 2, 6, 5, 3, 9, 4, 3], [0, 0, 6, 8, 5, 3, 4, 2], [0, 0, 8, 5, 3, 4, 10, 7, 2], [0, 0, 6, 8, 3, 4, 7, 2], [0, 0, 5, 3, 4, 7, 2], [0, 0, 1, 6, 5, 3, 9, 4, 3], [11, 0, 1, 2, 3, 4, 5, 6, 2], [5, 0, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [5, 0, 2, 3, 4, 8, 5, 6, 7, 2], [2, 0, 3, 4, 5, 2], [2, 1, 0, 2, 3, 4, 5, 4], [3, 3, 0, 1, 2, 7, 5], [3, 3, 0, 4, 5, 6, 1, 7, 7], [6, 1, 2, 1], [4, 1, 0, 6, 4, 7, 8, 5], [4, 1, 0, 7, 8, 3], [1, 0, 8, 6, 1, 2, 3, 5, 10, 11, 8], [1, 4, 0, 8, 6, 1, 7, 2, 3, 5, 10, 11, 10], [1, 4, 0, 8, 1, 7, 2, 3, 10, 11, 12, 8], [1, 4, 0, 6, 1, 2, 3, 5, 9, 10, 11, 9], [1, 4, 0, 1, 7, 2, 3, 10, 11, 12, 7], [8, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 3], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 1]], [[22, "TestView"], [15, "TestView", [-6, -7, -8, -9, -10, -11], [[49, -5, -4, -3, -2]], [37, -1, 0], [5, 750, 1334]], [25, "content", [-14, -15, -16, -17, -18], [[36, 0, 41, 10, 10, 9.69000244140625, 220, -12], [38, 1, 2, 100, 15, -13, [5, 680, 411]]], [0, "cdRhBwBSZF8p+4/IhSxn6x", 1, 0], [5, 680, 411], [0, 0.5, 1], [0, 615.3099975585938, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "monster_hp", [-20, -21, -22, -23, -24, -25], [[19, 1, 20, 20, -19, [5, 700, 68]]], [0, "beHznHo75NxISLrgsegdzN", 1, 0], [5, 700, 68], [0, 44, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "monster_hit", [-27, -28, -29, -30, -31, -32], [[19, 1, 20, 20, -26, [5, 700, 68]]], [0, "eblmqbLUFIobwV2/87qGaD", 1, 0], [5, 700, 68], [0, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "item", false, 1, [-36], [[3, 1, 0, -33, [44], 45], [4, 3, -34, [[6, "d95206PhxJI/ZqaVO45+tCI", "onNoBgMoving", "atk_save", 1]]], [39, 1, 1, -35, [5, 200, 68]]], [0, "f6SDM5wl9AJ5AB8yrV8FYg", 1, 0], [5, 200, 68], [-228.605, 627.938, 0, 0, 0, 0, 1, 1, 1, 0]], [29, "allEditBox", 2, [-38, -39, -40], [-37], [0, "3eHordzz1D+r068fLL0Mp3", 1, 0], [5, 400, 54], [0, -269, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "page_lv", [-43], [[48, false, 0.75, 0.23, null, null, -41, 2], [13, 45, 700, 44, -42]], [0, "e7mDug+UJAybIoAItbZS7i", 1, 0], [5, 700, 1250]], [24, "view", 7, [2], [[47, 0, -44, [33]], [13, 45, 240, 250, -45]], [0, "2ff3SxgG9AJayq4qOy/yER", 1, 0], [5, 700, 1250]], [16, "layout_9999", false, 2, [3, 4], [[14, 1, 2, 20, 20, 30, 20, -46, [5, 700, 156]]], [0, "20qh6mZ2JIP4kZ6wSm/BJ1", 1, 0], [5, 700, 156], [0, -396, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "hp_minus", 3, [-49], [[3, 1, 0, -47, [4], 5], [4, 3, -48, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "hp_minus", 1]]]], [0, "f1ldXbMMtMQJNlOa/cP3OS", 1, 0], [5, 55, 68], [-82.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "hp_plus", 3, [-52], [[3, 1, 0, -50, [9], 10], [4, 3, -51, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "hp_plus", 1]]]], [0, "b9BeYziAhNg5QuN1z0UA5q", 1, 0], [5, 55, 68], [142.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "item", 3, [-55], [[3, 1, 0, -53, [12], 13], [4, 3, -54, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "hp_save", 1]]]], [0, "aeqX8PqtpLwLFq+kCDPFT+", 1, 0], [5, 140, 68], [260, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "atk_minus", 4, [-58], [[3, 1, 0, -56, [16], 17], [4, 3, -57, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "atk_minus", 1]]]], [0, "77DxDilNdOUaNoeI58K1QO", 1, 0], [5, 55, 68], [-82.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "atk_plus", 4, [-61], [[3, 1, 0, -59, [21], 22], [4, 3, -60, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "atk_plus", 1]]]], [0, "c4uJRUotRI86c/XSi8m16F", 1, 0], [5, 55, 68], [142.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "item", 4, [-64], [[3, 1, 0, -62, [24], 25], [4, 3, -63, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "atk_save", 1]]]], [0, "62WOjtqZlEF6YB0RJHLKii", 1, 0], [5, 140, 68], [260, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "item", [-67], [[3, 1, 0, -65, [31], 32], [4, 3, -66, [[6, "d95206PhxJI/ZqaVO45+tCI", "onBtn", "cleanData", 1]]]], [0, "9e5J4h18BK8JzYqYwaTz2y", 1, 0], [5, 200, 68], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [27, "New Label", [[42, "修改器", 38, 50, false, 1, 1, 1, -68, [36], 37], [1, 3, -69, [4, 4278190080]], [45, -70, [[21, "d95206PhxJI/ZqaVO45+tCI", "onTitleClick", 1]]]], [0, "19NIVlQ7hLFKyvYQx1e03K", 1, 0], [5, 120, 69], [0, 7.719, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Close", 1, [-73], [[3, 1, 0, -71, [40], 41], [4, 3, -72, [[21, "d95206PhxJI/ZqaVO45+tCI", "close", 1]]]], [0, "3cfXHqKWFKZLg1CmKObfO9", 1, 0], [5, 61.99999999999999, 65], [289.097, 598.232, 0, 0, 0, 0, 1, 1, 1, 0]], [23, "maskbg", 58, 1, [[33, 0, -74, [0], 1], [13, 45, 750, 1334, -75]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4281542699], [5, 750, 1334]], [2, "page", 1, [7], [[34, 1, 0, false, -76, [34], 35]], [0, "d2/Ba3hXBAeK7Ow8vT6gUH", 1, 0], [5, 700, 1250], [0, 24.497, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "New Label", 3, [[8, "本关怪物血量", 30, false, 1, 1, 2, -77, [2]], [10, false, 2, -78, [4, 4278190080]]], [0, "615lm0LnREb4s2rmFuY2AJ", 1, 0], [4, 4278190080], [5, 200, 68], [-230, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Label", 10, [[9, "-", 30, false, false, 1, 1, 2, -79, [3]], [1, 2, -80, [4, 4278190080]]], [0, "2c9ijPENFH07uDMHVrgynE", 1, 0], [5, 200, 68]], [7, "hp_value", 3, [[8, "1", 30, false, 1, 1, 2, -81, [6]], [10, false, 2, -82, [4, 4278190080]]], [0, "b6Yl/9rvhHQZXN90H0HCZH", 1, 0], [4, 4278190080], [5, 60, 68], [-5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "hp_value_unit", 3, [[8, "倍", 30, false, 1, 1, 2, -83, [7]], [10, false, 2, -84, [4, 4278190080]]], [0, "e5nNxg+uRLlb5Exk0adHKh", 1, 0], [4, 4278190080], [5, 50, 68], [70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Label", 11, [[9, "+", 30, false, false, 1, 1, 2, -85, [8]], [1, 2, -86, [4, 4278190080]]], [0, "15J3w7bIhP84zIW76MKfEs", 1, 0], [5, 200, 68]], [5, "New Label", 12, [[9, "保存", 30, false, false, 1, 1, 2, -87, [11]], [1, 2, -88, [4, 4278190080]]], [0, "80S6bFP31JcrAQRafNNSN8", 1, 0], [5, 200, 68]], [7, "New Label", 4, [[8, "本关怪物伤害", 30, false, 1, 1, 2, -89, [14]], [10, false, 2, -90, [4, 4278190080]]], [0, "d3NAS+D91IKaTL5V6Odc/l", 1, 0], [4, 4278190080], [5, 200, 68], [-230, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Label", 13, [[9, "-", 30, false, false, 1, 1, 2, -91, [15]], [1, 2, -92, [4, 4278190080]]], [0, "75HJW0kHVEMY92ukFLxSND", 1, 0], [5, 200, 68]], [7, "atk_value", 4, [[8, "1", 30, false, 1, 1, 2, -93, [18]], [10, false, 2, -94, [4, 4278190080]]], [0, "30HzrHMOpE4KircGk7s9SG", 1, 0], [4, 4278190080], [5, 60, 68], [-5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "atk_value_unit", 4, [[8, "倍", 30, false, 1, 1, 2, -95, [19]], [10, false, 2, -96, [4, 4278190080]]], [0, "dd9Ls5GOVHAKfUDtJ8z1Ev", 1, 0], [4, 4278190080], [5, 50, 68], [70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Label", 14, [[9, "+", 30, false, false, 1, 1, 2, -97, [20]], [1, 2, -98, [4, 4278190080]]], [0, "92hkO0lqpOjK5QiOZkfgzw", 1, 0], [5, 200, 68]], [5, "New Label", 15, [[9, "保存", 30, false, false, 1, 1, 2, -99, [23]], [1, 2, -100, [4, 4278190080]]], [0, "baAkTGnhJOOIRvMu/NB0H2", 1, 0], [5, 200, 68]], [30, "BACKGROUND_SPRITE", 6, [[-101, [35, 0, 45, 160, 40, -102]], 1, 4], [0, "91FNsQuw5AHqsGZfnxiQ86", 1, 0], [5, 400, 54]], [31, "TEXT_LABEL", false, 6, [[-103, [18, 0, 45, 2, 158, 40, -104]], 1, 4], [0, "41eoH1j3ZHZbtsgYAnIakE", 1, 0], [5, 118, 44], [0, 0, 1], [-58, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "PLACEHOLDER_LABEL", 6, [[-105, [18, 0, 45, 2, 158, 40, -106]], 1, 4], [0, "9aeh6DgCxBx6OJr3HSkQSO", 1, 0], [4, 4290493371], [5, 398, 54], [0, 0, 1], [-198, 27, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Label", 16, [[20, "清除数据", 30, false, false, 1, 1, 1, 2, -107, [29], 30], [1, 2, -108, [4, 4278190080]]], [0, "f9dYM0K99INp0gBJSAGa7V", 1, 0], [5, 200, 68]], [2, "title", 1, [17], [[17, 1, 0, -109, [38]]], [0, "0a/KpReAFF8ZMD8P89HNqp", 1, 0], [5, 438, 87], [0, 595.155, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Label", 5, [[20, "背景不动", 30, false, false, 1, 1, 1, 2, -110, [42], 43], [1, 2, -111, [4, 4278190080]]], [0, "bf4QixaadHWIZNhtCklQop", 1, 0], [5, 200, 68]], [11, "serverTime", 1, [[44, "11", 20, false, 1, 1, 1, -112, [46], 47], [1, 2, -113, [4, 4278190080]]], [0, "a62K89FSdO/pHvdHvj+oLn", 1, 0], [5, 26.2, 54.4], [0, 1, 0.5], [326.148, -555.262, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "layout_0", 2, [[14, 1, 3, 20, 20, 30, 10, -114, [5, 700, 44]]], [0, "f1RLM1UkZM25Ht8tzDG0tu", 1, 0], [5, 700, 44], [0, 0.5, 1], [0, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "layout_999", 2, [[14, 1, 3, 20, 20, 30, 10, -115, [5, 700, 68]]], [0, "7aXwfr1qxPGL35mFU31vB/", 1, 0], [5, 700, 68], [0, 0.5, 1], [0, -159, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 1, 0, 33, [26]], [40, 20, 25, false, false, 1, 1, 1, 34, [27]], [41, "ID,数量", 30, 25, false, false, 1, 1, 1, 1, 35, [28]], [46, 100, 6, 6, 43, 44, 42], [26, "layout_99999", 2, [16], [0, "871CvZU9xMtYNVW8MR17ag", 1, 0], [5, 700, 100], [0, -361, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "Label", false, 18, [[43, "x", 20, false, false, 1, 1, 1, 1, -116, [39]]], [0, "fbuBNj8ylDaKO7eyPxKJiF", 1, 0], [4, 4278190080], [5, 100, 40]]], 0, [0, 4, 1, 0, 5, 45, 0, 6, 2, 0, 7, 5, 0, 0, 1, 0, -1, 19, 0, -2, 20, 0, -3, 37, 0, -4, 18, 0, -5, 5, 0, -6, 39, 0, 0, 2, 0, 0, 2, 0, -1, 40, 0, -2, 41, 0, -3, 9, 0, -4, 6, 0, -5, 46, 0, 0, 3, 0, -1, 21, 0, -2, 10, 0, -3, 23, 0, -4, 24, 0, -5, 11, 0, -6, 12, 0, 0, 4, 0, -1, 27, 0, -2, 13, 0, -3, 29, 0, -4, 30, 0, -5, 14, 0, -6, 15, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 38, 0, -1, 45, 0, -1, 33, 0, -2, 34, 0, -3, 35, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 22, 0, 0, 11, 0, 0, 11, 0, -1, 25, 0, 0, 12, 0, 0, 12, 0, -1, 26, 0, 0, 13, 0, 0, 13, 0, -1, 28, 0, 0, 14, 0, 0, 14, 0, -1, 31, 0, 0, 15, 0, 0, 15, 0, -1, 32, 0, 0, 16, 0, 0, 16, 0, -1, 36, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, -1, 47, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, -1, 42, 0, 0, 33, 0, -1, 43, 0, 0, 34, 0, -1, 44, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 47, 0, 8, 1, 2, 3, 8, 3, 3, 9, 4, 3, 9, 7, 3, 20, 16, 3, 46, 17, 3, 37, 116], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 43, 44], [-1, 1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 2, -1, 1, -1, -1, 1, -1, 2, -1, -1, -1, 1, -1, 2, -1, 1, -1, 2, 1, 2, 2], [0, 3, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 2, 0, 4, 0, 0, 5, 0, 6, 0, 0, 0, 7, 0, 2, 0, 8, 0, 2, 9, 2, 2]]