[1, ["ecpdLyjvZBwrvm+cedCcQy", "9fUicDpb9ESYDEBfH7jjiH", "d3gQGt3U5ODrxK5EaBPUsd", "e40u3avyZHWYWu7f8ivsQQ", "06C03H5hpIpIJtdpMrwLUp", "2e6uN21gNFlI+8zvsXRnGA", "adwjhVdAFJ94bfPleWxJwJ", "7aSL72M71OCKrrZfvV9xtf", "24j0q6iMpKCYsRGD0VYmMw", "deYeqBbrtAM7ABF8+EGpZQ"], ["node", "_spriteFrame", "_parent", "root", "_textureSetter", "data", "asset"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "node", "_materials"], -3, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["7c70bs/PiRMlIMqEeVWX+WZ", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 1, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[2, 0, 1, 2, 2], [1, 2, 3, 4, 1], [0, 0, 6, 4, 3, 5, 7, 2], [11, 0, 1, 2, 2], [1, 0, 1, 2, 3, 4, 3], [3, 0, 1, 5, 2, 3, 4, 6, 7, 7], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 6, 8, 4, 3, 5, 7, 2], [0, 0, 6, 4, 3, 5, 10, 7, 2], [0, 0, 6, 4, 3, 9, 5, 7, 2], [0, 0, 1, 6, 8, 4, 3, 5, 7, 3], [5, 0, 2], [0, 0, 8, 4, 3, 5, 2], [0, 0, 6, 4, 3, 9, 5, 2], [0, 0, 6, 4, 3, 5, 2], [0, 0, 2, 6, 4, 3, 5, 7, 3], [0, 0, 6, 3, 7, 2], [6, 0, 1], [2, 1, 2, 1], [7, 0, 1, 2, 3, 3], [1, 0, 1, 2, 3, 3], [8, 0, 1, 2, 3, 3], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3], [3, 0, 1, 2, 3, 4, 6, 7, 6]], [[[{"name": "img_jj_txz", "rect": [0, 0, 614, 274], "offset": [0, 0], "originalSize": [614, 274], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [4]], [[[11, "StartChallangeItem"], [12, "StartChallangeItem", [-3, -4, -5, -6, -7, -8, -9, -10], [[17, -2]], [18, -1, 0], [5, 614, 274]], [6, "img_start0", [-12, -13, -14, -15], [[4, 1, 0, -11, [22], 23]], [0, "dcAljfxXpKj53WueATJ2Gy", 1, 0], [5, 205, 45], [0, 77.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "img_start1", [-17, -18, -19, -20], [[4, 1, 0, -16, [31], 32]], [0, "29rNmc+zdAIYMgSZR6qTU9", 1, 0], [5, 205, 45], [0, 27.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "img_start2", [-22, -23, -24, -25], [[4, 1, 0, -21, [40], 41]], [0, "35BeCts25I6oqmuGUW8RMd", 1, 0], [5, 205, 45], [0, -22.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "cnd", 1, [2, 3, 4], [[21, 2, 5, -26, [5, 205, 200]]], [0, "64l/rE3IZLFJYjPHuRdc+8", 1, 0], [5, 205, 200], [152.027, 28.409, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button02", 1, [-29, -30], [[20, 1, 0, -27, [44]], [22, 3, -28, [[23, "7c70bs/PiRMlIMqEeVWX+WZ", "onClickItem", 1]]]], [0, "4a4F/++HpNf75j7MftgC9K", 1, 0], [5, 274, 108], [169.304, -80.363, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [7, "img_sjd", 1, [-32], [[4, 1, 0, -31, [7], 8]], [0, "a6uDQoBYxJjZXwRaVZme5G", 1, 0], [5, 280, 62], [-168.932, 105.731, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lbNe", 7, [[5, "第一赛季", 32, 31, 1, 1, 1, -33, [6]], [3, 3, -34, [4, 4278190080]]], [0, "fem8wXdGlBl5A89ldwj9U3", 1, 0], [4, 4283563357], [5, 134, 45.06]], [10, "img_buff0", 200, 1, [-36], [[4, 1, 0, -35, [10], 11]], [0, "ec/FYUeoFHE5HKN82xjBFz", 1, 0], [5, 150, 45], [-203.729, -60.121, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbVal", 9, [[5, "14.4k", 28, 22, 1, 1, 1, -37, [9]], [3, 3, -38, [4, 4278190080]]], [0, "1epXROXpFD/J8fwBAUzcg+", 1, 0], [5, 76.07, 33.72], [0, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "img_buff1", 200, 1, [-40], [[4, 1, 0, -39, [13], 14]], [0, "7bAUsMhT1CWps9Ehlq1F5Q", 1, 0], [5, 150, 45], [-203.729, -109.121, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbVal", 11, [[5, "14.4k", 28, 22, 1, 1, 1, -41, [12]], [3, 3, -42, [4, 4278190080]]], [0, "073pM7V6JA/aeaawwEAqKn", 1, 0], [5, 76.07, 33.72], [0, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbVal", 2, [[5, "成功通关", 20, 20, 1, 1, 1, -43, [19]], [3, 3, -44, [4, 4278190080]]], [0, "89EdXt43ZF64BR5f7jn1bY", 1, 0], [5, 86, 31.2], [0, 0, 0.5], [-57.012, 1.715, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbVal", 3, [[5, "成功通关", 20, 20, 1, 1, 1, -45, [28]], [3, 3, -46, [4, 4278190080]]], [0, "28EM0yVJdNhpS5fvy565W9", 1, 0], [5, 86, 31.2], [0, 0, 0.5], [-57.012, 1.715, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbVal", 4, [[5, "成功通关", 20, 20, 1, 1, 1, -47, [37]], [3, 3, -48, [4, 4278190080]]], [0, "8bnMEboqhNTbufnanXU4zK", 1, 0], [5, 86, 31.2], [0, 0, 0.5], [-57.012, 1.715, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbNe", 6, [[24, "开始挑战", 36, 1, 1, 1, -49, [42]], [3, 3, -50, [4, 4279374353]]], [0, "26zl/LYPREQ5E0N+T7eigv", 1, 0], [5, 150, 56.4], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "img_jj_txz", 1, [[1, -51, [0], 1]], [0, "50aw/yollE8beRovLrrAzp", 1, 0], [5, 614, 274]], [2, "icon_zj01", 1, [[1, -52, [2], 3]], [0, "eeiIool0hPvZ4k8jWanFja", 1, 0], [5, 438, 509], [-173.976, 5.743, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [15, "ggtipicon", false, 1, [[1, -53, [4], 5]], [0, "7ahnBSpJJEtKflgYann7gN", 1, 0], [5, 44, 43], [285.052, 132.137, 0, 0, 0, 0, 1, 0.8, 0.8, 0.8]], [9, "icon_gold", 2, [[1, -54, [15], 16]], [0, "b4ecm448BDy7sZfZigbw9F", 1, 0], [4, 4280427070], [5, 50, 52], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_gold", 2, [[1, -55, [17], 18]], [0, "dd4DGoD9ZALIdIQE9QJHuC", 1, 0], [5, 50, 52], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_dagou", 2, [[1, -56, [20], 21]], [0, "806XC4KhlFaqqOarxEFWCt", 1, 0], [5, 62, 49], [82.359, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "icon_gold", 3, [[1, -57, [24], 25]], [0, "8eTbDo5oZEnLugfrJJnmin", 1, 0], [4, 4280427070], [5, 50, 52], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_gold", 3, [[1, -58, [26], 27]], [0, "0atTQhTa9JfIodSR2YgZDs", 1, 0], [5, 50, 52], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_dagou", 3, [[1, -59, [29], 30]], [0, "09MD/faO9L5oEdkVridFCt", 1, 0], [5, 62, 49], [82.359, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "icon_gold", 4, [[1, -60, [33], 34]], [0, "a3jayW/BVPx7AWdSmtF2Yw", 1, 0], [4, 4280427070], [5, 50, 52], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_gold", 4, [[1, -61, [35], 36]], [0, "70IGZfs7ROfaX+Xjm0DHvD", 1, 0], [5, 50, 52], [-86.708, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_dagou", 4, [[1, -62, [38], 39]], [0, "33ZDnEAldGAJ7qCELuryPD", 1, 0], [5, 62, 49], [82.359, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Item_NeedDisplay", 6, [19, "0a3Z87sHJFm7I+d7u5IFoo", true, -63, 43], [0, -21.006, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 17, 0, -2, 18, 0, -3, 19, 0, -4, 7, 0, -5, 9, 0, -6, 11, 0, -7, 5, 0, -8, 6, 0, 0, 2, 0, -1, 20, 0, -2, 21, 0, -3, 13, 0, -4, 22, 0, 0, 3, 0, -1, 23, 0, -2, 24, 0, -3, 14, 0, -4, 25, 0, 0, 4, 0, -1, 26, 0, -2, 27, 0, -3, 15, 0, -4, 28, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -1, 16, 0, -2, 29, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 3, 29, 0, 5, 1, 2, 2, 5, 3, 2, 5, 4, 2, 5, 63], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 6, -1], [0, 5, 0, 6, 0, 7, 0, 0, 8, 0, 0, 2, 0, 0, 2, 0, 1, 0, 1, 0, 0, 3, 0, 2, 0, 1, 0, 1, 0, 0, 3, 0, 2, 0, 1, 0, 1, 0, 0, 3, 0, 2, 0, 9, 0]]]]