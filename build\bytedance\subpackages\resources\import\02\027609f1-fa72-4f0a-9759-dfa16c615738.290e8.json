[1, ["ecpdLyjvZBwrvm+cedCcQy", "cdvDZRLPZKb70zilA0TpQ8", "a2MjXRFdtLlYQ5ouAFv/+R", "164ddVrz5PdKqjAtT69J0j", "d3gQGt3U5ODrxK5EaBPUsd", "deYeqBbrtAM7ABF8+EGpZQ", "22cjIamqZH/Lbdvp80pFLv", "7a/QZLET9IDreTiBfRn2PD", "adwjhVdAFJ94bfPleWxJwJ", "24j0q6iMpKCYsRGD0VYmMw", "2a/b9BeYdFcZh3j62fkCp3", "09xJQ/x3xKmbwe9rWIGtds", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO"], ["node", "_spriteFrame", "root", "asset", "_parent", "_N$disabledSprite", "target", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_groupIndex", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 1, 9, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$overflow", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_bottom", "node"], -2, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "_enabled", "node", "_color"], 1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], -1, 1, 3], ["cc.RichText", ["_enabled", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["27eb2E7FXJKjoGuF05GIspU", ["node"], 3, 1]], [[6, 0, 1, 2, 2], [0, 0, 6, 7, 4, 5, 8, 2], [9, 0, 1, 2, 3, 3], [0, 0, 6, 4, 8, 2], [7, 0, 2, 3, 2], [0, 0, 6, 9, 7, 4, 5, 8, 2], [1, 1, 0, 3, 4, 5, 3], [1, 3, 4, 5, 1], [3, 0, 1, 2, 2], [2, 0, 1, 5, 2, 3, 4, 10, 11, 7], [2, 0, 1, 2, 3, 4, 10, 11, 6], [0, 0, 1, 6, 9, 7, 4, 5, 8, 3], [1, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 3], [5, 0, 3, 4, 5, 4], [3, 1, 2, 1], [4, 0, 1, 3], [4, 0, 1, 3, 3], [4, 0, 1, 2, 4], [8, 0, 2], [0, 0, 3, 9, 7, 4, 5, 8, 3], [0, 0, 2, 1, 6, 7, 4, 10, 5, 4], [0, 0, 6, 9, 7, 4, 10, 5, 2], [0, 0, 1, 6, 7, 4, 10, 5, 3], [0, 0, 6, 9, 4, 8, 2], [0, 0, 6, 7, 4, 5, 2], [0, 0, 6, 7, 4, 10, 5, 2], [0, 0, 2, 6, 7, 4, 10, 5, 8, 3], [0, 0, 6, 9, 4, 5, 11, 8, 2], [0, 0, 7, 4, 10, 5, 8, 2], [0, 0, 9, 7, 4, 5, 8, 2], [0, 0, 6, 4, 2], [1, 2, 0, 3, 4, 5, 3], [1, 1, 3, 4, 5, 2], [5, 0, 1, 2, 5, 4], [6, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3, 4, 5, 5], [2, 0, 6, 7, 3, 4, 8, 9, 10, 11, 8], [2, 0, 1, 5, 2, 10, 11, 5], [7, 1, 0, 2, 3, 3], [11, 0, 1, 2, 3, 4, 5], [12, 0, 1, 2, 3, 4, 4], [13, 0, 1]], [[19, "StartChallangeSigleView"], [20, "StartChallangeSigleView", 1, [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[8, 3, -3, [[17, "27eb2E7FXJKjoGuF05GIspU", "close", -2]]], [43, -4]], [35, -1, 0], [5, 750, 1335], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [30, "rewardNode", [-19, -20, -21, -22, -23], [[42, 1, 1, 30, -18, [5, 700, 128]]], [0, "55wQQHusNA25v0qZs2JMk9", 1, 0], [5, 700, 128], [0, 10.336, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [22, "bg", 1, [-26, -27], [[14, 45, -2.842170943040401e-14, 2.842170943040401e-14, -24], [32, false, 0, -25, [6], 7]], [0, "a9eVDVVwRP65/QJaB+R1fD", 1, 0], [4, 4278190080], [5, 750, 1335]], [5, "img_txd", 1, [-30, -31], [[6, 1, 0, -28, [23], 24], [15, -29, [[16, "29754hYulZN0pxOmQa0A+9d", "openEquip"]]]], [0, "489zOP8RVJsrweBiF/eqTG", 1, 0], [5, 295, 54], [143.019, 248.047, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "Background", 1, [-35], [[33, 1, -32, [26], 27], [36, 3, -34, [[17, "27eb2E7FXJKjoGuF05GIspU", "close", 1]], [4, 4293322470], [4, 3363338360], -33, 28]], [0, "8eamovTcFJjYdcqve+Jw3w", 1, 0], [5, 52, 56], [272.384, 276.237, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "button02", 1, [-38, -39], [[13, 1, 0, -36, [37]], [8, 3, -37, [[18, "bf1760LkBxFVaSNjXip45ah", "onBtn", "getAward"]]]], [0, "b1ffbYS01CvqchWlJCqohg", 1, 0], [5, 274, 108], [-140, -239.878, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [5, "button03", 1, [-42, -43], [[13, 1, 0, -40, [40]], [8, 3, -41, [[18, "bf1760LkBxFVaSNjXip45ah", "onBtn", "getAward"]]]], [0, "2fd7+alPRFro5RoEmybIg5", 1, 0], [5, 274, 108], [140, -239.878, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [23, "maskbg", 5, 3, [[14, 45, 5.329070518200751e-15, -5.329070518200751e-15, -44], [12, 0, -45, [2], 3], [15, -46, [[16, "d57a7d397FISp9LyX81u3JC", "onClickItem"]]]], [0, "femEAY3ZJOJrRwQw1l4dtX", 1, 0], [4, 4278190080], [5, 750, 1335]], [24, "starNd", 1, [-47, -48, -49], [0, "c8HzRdRsJDaok7JBusZgwI", 1, 0], [-156.939, -3.412, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "lbDayGet", [[41, false, "<outline color=black width=4><color=#ffffff><b>立即领取</b></c></outline>", 24, 24, -50], [39, "每日领取", 24, 24, 1, -51, [41]], [40, false, 3, -52, [4, 4292236816]]], [0, "94pd0CNbBNcKZorZ8+9UaO", 1, 0], [4, 4292236816], [5, 96, 30.24], [-200.133, 82.711, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "mask", false, 177.98999999999998, 1, [[12, 0, -53, [0], 1], [34, 45, 750, 1334, -54]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4281542699], [5, 750, 1335]], [5, "img_sjd", 1, [-56], [[6, 1, 0, -55, [18], 19]], [0, "498nQdIjNEv4Za3VN6Fi33", 1, 0], [5, 280, 62], [-143.973, 244.345, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "lbNe", 12, [[9, "第一赛季", 32, 31, 1, 1, 1, -57, [17]], [4, 3, -58, [4, 4278190080]]], [0, "7e5E37WPRK3ppRQ6Aa2WE0", 1, 0], [4, 4283563357], [5, 134, 45.06]], [1, "lbDesc", 4, [[10, "关卡特性", 30, 1, 1, 1, -59, [20]], [4, 3, -60, [4, 4279900698]]], [0, "d0emLoMetLXrk19HmZeI0c", 1, 0], [5, 126, 56.4], [17.986, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "img_buff0", 200, 1, [-62], [[6, 1, 0, -61, [30], 31]], [0, "b930m33VNG6phey+qEoFY7", 1, 0], [5, 150, 45], [132.451, 9.249, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbVal", 15, [[9, "14.4k", 28, 22, 1, 1, 1, -63, [29]], [4, 3, -64, [4, 4278190080]]], [0, "fefg7+rEJD8KK7Ng08vcWW", 1, 0], [5, 76.07, 33.72], [0, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "img_buff1", 200, 1, [-66], [[6, 1, 0, -65, [33], 34]], [0, "6d8VmIwl5IIbHIA13zcBZv", 1, 0], [5, 150, 45], [132.451, -39.751, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbVal", 17, [[9, "14.4k", 28, 22, 1, 1, 1, -67, [32]], [4, 3, -68, [4, 4278190080]]], [0, "19EsjPUy5K1YdNPqp+W3pF", 1, 0], [5, 76.07, 33.72], [0, 1.7149999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNe", 6, [[10, "开始挑战", 36, 1, 1, 1, -69, [35]], [4, 3, -70, [4, 4279374353]]], [0, "97/Fo7h51IloS3VNZ4taDT", 1, 0], [5, 150, 56.4], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNe", 7, [[10, "领取奖励", 36, 1, 1, 1, -71, [38]], [4, 3, -72, [4, 4279374353]]], [0, "2dmQT9n+xO/LDouPmAwUIM", 1, 0], [5, 150, 56.4], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "nodeRe", 1, [10, 2], [0, "44wy3TMclCv7jm1A1K/eg7", 1, 0], [5, 706, 36.24], [0, 0, 0.5], [0, -144.736, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "showNode", 3, [[6, 1, 0, -73, [4], 5]], [0, "86mGg97ltAKp2qmPWQdxmm", 1, 0], [5, 600, 600], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "anmate", 1, [[37, "default", "animation", 0, "animation", -74, [8]]], [0, "ebqk6UextMYqYBssHkfaL1", 1, 0], [5, 156.93797302246094, 153.38987731933594], [157.728, 52.119, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_zj01", 1, [[7, -75, [9], 10]], [0, "2edVSvLVNDwpaiTWSarJlZ", 1, 0], [5, 438, 509], [-148.687, 98.469, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [1, "img_star", 9, [[7, -76, [11], 12]], [0, "02EEE9yGRIb58muu1EbT+o", 1, 0], [5, 33, 32], [-51.176, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "img_star", 9, [[7, -77, [13], 14]], [0, "de52ZOUbxAx4DrPZIUUUN2", 1, 0], [5, 33, 32]], [1, "img_star", 9, [[7, -78, [15], 16]], [0, "f93B2CHflKpp7IJ0fj/Wp6", 1, 0], [5, 33, 32], [52.882, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_wh", 4, [[7, -79, [21], 22]], [0, "59Q7U4WvZMg5RAxEFbwBqt", 1, 0], [5, 32, 32], [-104.728, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [27, "Label", false, 5, [[38, "返回", false, false, 1, 1, 1, 1, -80, [25]]], [0, "87zO+6bbhMu6FGVysVbeHe", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Item_NeedDisplay", 6, [2, "0a3Z87sHJFm7I+d7u5IFoo", true, -81, 36], [0, -21.006, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Item_NeedDisplay", 7, [2, "0a3Z87sHJFm7I+d7u5IFoo", true, -82, 39], [0, -21.006, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "RewardItem", 2, [2, "2bASLwK1NDL6pZvMKugC5l", true, -83, 42], [-292, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "RewardItem", 2, [2, "03f05kNalMO6UJBfs8i9hV", true, -84, 43], [-146, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "RewardItem", 2, [2, "2e+lztNSdNd7X2S+ieyUj2", true, -85, 44]], [3, "RewardItem", 2, [2, "c8Ct8GHvtHfZKZk2YElbs5", true, -86, 45], [146, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "RewardItem", 2, [2, "c1tbfXQbdETK3fkO9k64YB", true, -87, 46], [292, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 6, 1, 0, 0, 1, 0, 0, 1, 0, -1, 11, 0, -2, 3, 0, -3, 23, 0, -4, 24, 0, -5, 9, 0, -6, 12, 0, -7, 4, 0, -8, 5, 0, -9, 15, 0, -10, 17, 0, -11, 6, 0, -12, 7, 0, -13, 21, 0, 0, 2, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, -5, 36, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, -2, 22, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 28, 0, 0, 5, 0, 7, 5, 0, 0, 5, 0, -1, 29, 0, 0, 6, 0, 0, 6, 0, -1, 19, 0, -2, 30, 0, 0, 7, 0, 0, 7, 0, -1, 20, 0, -2, 31, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -1, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 2, 30, 0, 2, 31, 0, 2, 32, 0, 2, 33, 0, 2, 34, 0, 2, 35, 0, 2, 36, 0, 8, 1, 2, 4, 21, 10, 4, 21, 87], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, 5, -1, -1, 1, -1, -1, 1, -1, 3, -1, -1, 3, -1, -1, 3, 3, 3, 3, 3], [0, 2, 0, 2, 0, 6, 0, 2, 7, 0, 8, 0, 3, 0, 3, 0, 3, 0, 0, 9, 0, 0, 10, 0, 11, 0, 0, 12, 13, 0, 0, 4, 0, 0, 4, 0, 5, 0, 0, 5, 0, 0, 1, 1, 1, 1, 1]]