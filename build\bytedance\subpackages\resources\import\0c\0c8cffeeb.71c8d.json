[1, ["ecpdLyjvZBwrvm+cedCcQy", "ecIeT3uBpINpDaO8jooDN4", "a2MjXRFdtLlYQ5ouAFv/+R", "efELAwoodIt5Z3vf0Nxww5", "1esa22CnNNK6PRvNYLE6k/", "c6e7Id/hNJv5R+BvIjjgmA", "c8sIr7ut5B2rX1LIeDkCwu"], ["node", "_spriteFrame", "_textureSetter", "root", "btnConfirm", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_color"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_styleFlags", "_N$horizontalAlign", "_fontSize", "_lineHeight", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Prefab", ["_name"], 2], ["de188QVLTVMFpVecEjmNRrk", ["isSendViewEvent", "node", "btnConfirm"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -4, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[3, 0, 1, 2, 2], [0, 0, 4, 3, 2, 5, 7, 2], [11, 0, 1, 2, 2], [4, 0, 5, 6, 1, 2, 7, 8, 9, 7], [0, 0, 4, 6, 3, 2, 5, 7, 2], [1, 2, 3, 4, 1], [5, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 4, 6, 2, 5, 2], [0, 0, 1, 4, 3, 2, 8, 5, 3], [0, 0, 4, 3, 2, 5, 2], [6, 0, 1, 2, 2], [3, 1, 2, 1], [1, 1, 0, 2, 3, 4, 3], [1, 0, 2, 3, 4, 2], [7, 0, 1, 2, 3, 3], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8], [10, 0, 1], [4, 0, 1, 3, 4, 2, 8, 9, 6]], [[[{"name": "img_tlzlbbt", "rect": [0, 0, 498, 118], "offset": [0, 0], "originalSize": [498, 118], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [1]], [[[6, "RecommendCodeView"], [7, "RecommendCodeView", [-4, -5], [[11, true, -3, -2]], [12, -1, 0]], [8, "bg", 1, [-6, -7, -8, -9, -10], [0, "9aauAs2UtLcrYG0grdcB8F", 1, 0], [5, 750, 1334]], [4, "btn", 2, [-13], [[13, 1, 0, -11, [8], 9], [15, 0.9, 3, -12, [[16, "de188QVLTVMFpVecEjmNRrk", "onClickGet", 1]]]], [0, "fdCRaJ+RpDrp67oHi3w7ZM", 1, 0], [5, 240, 108], [0, -284.638, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "maskbg", 190, 1, [[14, 0, -14, [0], 1], [17, 45, -375, -375, -667, -667, 750, 1334, -15]], [0, "f5bEsQry9Ga5pz4f8uuHIz", 1, 0], [4, 4281542699], [5, 750, 1334]], [10, "New Node", 2, [[5, -16, [2], 3], [18, -17]], [0, "1ff4RY7sFKcaG1uyCiMpt8", 1, 0], [5, 651, 732]], [4, "New Node", 2, [-19], [[5, -18, [5], 6]], [0, "4edBUCATVJNrjdHantPh/M", 1, 0], [5, 498, 118], [0, 356.712, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 6, [[19, "屠龙者礼包", false, 1, 1, 1, -20, [4]], [2, 3, -21, [4, 4278190080]]], [0, "40tS2T/e1A1r67CN4zK06f", 1, 0], [5, 206, 56.4], [0, 8.201, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Node", 3, [[3, "领取", 36, 36, false, 1, 1, -22, [7]], [2, 3, -23, [4, 4279374353]]], [0, "e5BQOgSPZDAJgIOS2Woh19", 1, 0], [5, 78, 51.36], [0, 6.411, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tips", 2, [[3, "可随机获得1   10张免广券", 30, 30, false, 1, 1, -24, [10]], [2, 3, -25, [4, 4278190080]]], [0, "41EVmk1u5MlpSzqEVAf+zg", 1, 0], [5, 351.06, 43.8], [0, -194.47, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "text", 2, [[3, "~", 30, 30, false, 1, 1, -26, [11]], [2, 3, -27, [4, 4278190080]]], [0, "5fuuq2+2dKbq4RkSqKrnpZ", 1, 0], [5, 23.52, 43.8], [5.975, -205.921, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 4, 3, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -1, 5, 0, -2, 6, 0, -3, 3, 0, -4, 9, 0, -5, 10, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 5, 1, 27], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1], [0, 2, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0]], [[{"name": "img_tlzlb", "rect": [0, 0, 651, 732], "offset": [0, 0], "originalSize": [651, 732], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [6]]]]