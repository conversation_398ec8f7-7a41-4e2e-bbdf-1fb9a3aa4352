[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "f6NcoTBPNJ4qSyD40PNpRP", "cdvDZRLPZKb70zilA0TpQ8", "97IAXRHnlLq7dNeJPB6SbL", "22cjIamqZH/Lbdvp80pFLv", "e52TQxfs9FgYg1TfDFagDC"], ["node", "_spriteFrame", "root", "_parent", "asset", "target", "_N$content", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color"], -1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "_materials"], -5, 1, 3], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_anchorPoint"], 2, 12, 4, 5, 7, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "node"], 0, 1], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["d57a7d397FISp9LyX81u3JC", ["node", "nodeArr", "lbArr"], 3, 1, 2, 2]], [[4, 0, 1, 2, 2], [10, 0, 1, 2, 2], [0, 0, 7, 8, 5, 4, 6, 9, 2], [0, 0, 7, 5, 4, 6, 9, 2], [3, 0, 5, 1, 2, 3, 4, 2], [6, 0, 1, 2, 3, 4], [1, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 5, 3], [7, 0, 1, 1], [8, 0, 1, 2, 3], [5, 0, 2], [0, 0, 1, 8, 5, 4, 6, 3], [0, 0, 7, 8, 5, 4, 10, 6, 9, 2], [0, 0, 2, 7, 5, 4, 10, 6, 3], [0, 0, 8, 5, 4, 6, 9, 2], [0, 3, 0, 7, 4, 9, 3], [0, 0, 7, 8, 5, 4, 6, 2], [3, 0, 1, 2, 3, 6, 4, 2], [1, 1, 0, 3, 4, 3], [1, 3, 4, 5, 1], [1, 2, 0, 3, 4, 5, 3], [4, 1, 2, 1], [9, 0, 1, 2, 3, 3], [2, 0, 7, 1, 2, 3, 4, 8, 9, 7], [2, 0, 5, 6, 1, 2, 3, 8, 9, 7], [2, 0, 5, 6, 1, 2, 3, 4, 8, 9, 8], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 1]], [[10, "FundGoodTips"], [11, "FundGoodTips", 1, [-11], [[28, -8, [-5, -6, -7], [-2, -3, -4]], [8, -10, [[9, "d57a7d397FISp9LyX81u3JC", "onClickItem", -9]]]], [21, -1, 0], [5, 388, 356]], [14, "showNode", [-13, -14, -15, -16, -17], [[7, 1, 0, -12, [12], 13]], [0, "e8oxXB+zZDfobHerep4c8A", 1, 0], [5, 388, 356], [0, -206, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg", 1, [-20, 2, -21], [[5, 45, -2.842170943040401e-14, 2.842170943040401e-14, -18], [20, false, 0, -19, [16], 17]], [0, "c1FmtRCFdF2K4sr9j6PL8D", 1, 0], [4, 4278190080], [5, 388, 356], [0, 2.842170943040401e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "maskbg", 5, 3, [[5, 45, 5.329070518200751e-15, -5.329070518200751e-15, -22], [6, 0, -23, [0], 1], [8, -24, [[9, "d57a7d397FISp9LyX81u3JC", "onClickItem", 1]]]], [0, "c0HT4FwnBJcpBNExyYrzmy", 1, 0], [4, 4278190080], [5, 388, 356]], [2, "New ScrollView", 2, [-28], [[18, 1, 0, -25, [11]], [27, false, 0.75, 0.23, null, null, -27, -26]], [0, "e6Pb8MGpJDWJGBMH6mAtq/", 1, 0], [5, 370, 70], [0, -126.197, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "New Label", [[-29, [1, 3, -30, [4, 4279374353]]], 1, 4], [0, "44xnSpf3BCx50KpU2AOdrT", 1, 0], [5, 370, 85.1], [0, 0.5, 1], [0, 41.519, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "title_zhua<PERSON><PERSON>", 2, [-32], [[7, 1, 0, -31, [3], 4]], [0, "ecPRR5FgFHnK8jNh3q0ujD", 1, 0], [5, 388, 82], [0, 141.194, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label_title", 7, [[-33, [1, 4, -34, [4, 4278190080]]], 1, 4], [0, "49d4m8RmJEJYNb9Gdj4Oiy", 1, 0], [5, 285, 56.4], [0, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 512, "RewardItem", 2, [22, "a72Hgff0lGOpdbpeM7CcYY", true, -35, 5], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Label", 2, [[-36, [1, 3, -37, [4, 4279374353]]], 1, 4], [0, "10gRgkQQJBcLqbGjdrbDED", 1, 0], [5, 139.35, 36.239999999999995], [0, -59.732, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "view", 5, [6], [[26, 0, -38, [10]]], [0, "72DklTpGtNuJUTWVtiLxQp", 1, 0], [5, 370, 70]], [3, "img_jt", 3, [[19, -39, [14], 15]], [0, "82CzoUhn1HuJvHc9DwzJY7", 1, 0], [5, 47, 38], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "额外奖励", false, 1, 1, 1, 2, 8, [2]], [24, "拥有数量：3", 24, 24, 1, 1, 1, 10, [6]], [3, "img_fgx", 2, [[6, 0, -40, [7], 8]], [0, "4cGU/Y4nRNt6cmvkTLtdqv", 1, 0], [5, 322, 3], [0, -84.778, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "用于挑战巨龙巢用于挑战巨龙巢", 28, 35, 1, 1, 1, 3, 6, [9]]], 0, [0, 2, 1, 0, -1, 14, 0, -2, 16, 0, -3, 13, 0, -1, 9, 0, -2, 2, 0, -3, 12, 0, 0, 1, 0, 5, 1, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, -1, 7, 0, -2, 9, 0, -3, 10, 0, -4, 15, 0, -5, 5, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, -3, 12, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 6, 6, 0, 0, 5, 0, -1, 11, 0, -1, 16, 0, 0, 6, 0, 0, 7, 0, -1, 8, 0, -1, 13, 0, 0, 8, 0, 2, 9, 0, -1, 14, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 15, 0, 7, 1, 2, 3, 3, 6, 3, 11, 40], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, 4, -1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1], [0, 1, 0, 0, 2, 3, 0, 0, 4, 0, 0, 0, 0, 5, 0, 6, 0, 1]]