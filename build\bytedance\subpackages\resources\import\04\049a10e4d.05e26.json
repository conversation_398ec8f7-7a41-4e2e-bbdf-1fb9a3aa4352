[1, ["ecpdLyjvZBwrvm+cedCcQy", "04EtOiP79FvLWwmAINvdMa", "bbsTpokpJAhJQd40EFxmGS", "7a/QZLET9IDreTiBfRn2PD"], ["node", "_spriteFrame", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_anchorPoint"], 1, 4, 5, 1, 9, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$verticalDirection", "node", "_layoutSize"], 0, 1, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], -1, 1, 3]], [[1, 0, 1, 2, 2], [0, 0, 1, 4, 5, 2, 3, 6, 3], [2, 0, 1, 2, 3, 2], [0, 0, 4, 5, 2, 3, 6, 2], [6, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 1, 2, 2], [4, 0, 2], [0, 0, 7, 2, 3, 2], [0, 0, 4, 7, 5, 2, 3, 8, 6, 2], [0, 0, 4, 5, 2, 3, 2], [1, 1, 2, 1], [5, 0, 1, 2, 3, 4, 4], [2, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "bg_jindu", "rect": [0, 0, 53, 49], "offset": [0, 0], "originalSize": [53, 49], "capInsets": [21, 0, 22, 0]}], [3], 0, [0], [2], [2]], [[[6, "M20Prop"], [7, "M20Prop", [-2, -3, -4, -5, -6, -7], [10, -1, 0], [5, 100, 100]], [8, "gemStones", 1, [-9, -10, -11, -12], [[11, 1, 2, 0, -8, [5, 203.84, 196]]], [0, "8ehv3E4XZHk6dsRaQ7or38", 1, 0], [5, 203.84, 196], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [3, "name", 1, [[4, "Label", 18, false, 1, 1, -13, [12]], [5, 3, -14, [4, 4278190080]]], [0, "fcvhj/DWBIV5fJVMhGQw1T", 1, 0], [5, 50.04, 56.4], [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "num", 1, [[4, "5", 18, false, 1, 1, -15, [13]], [5, 3, -16, [4, 4278190080]]], [0, "60ZatS3m9DvIlg/t0AOTUK", 1, 0], [5, 16.009999999999998, 56.4], [-24.375, 25.909, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon", 1, [[12, -17, [0]]], [0, "67Oqd3xPJCTKDdh43orWRS", 1, 0], [5, 266, 85], [168, 56, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "ske", 1, [[13, "default", "animation", 0, "animation", -18, [1]]], [0, "09PGxB5DNCvZcbk4/KF02g", 1, 0], [5, 85.61434936523438, 84.71674346923828]], [1, "lv", false, 1, [[2, 0, -19, [2], 3]], [0, "98TD6eulVBtYzyqQ5ZFrVx", 1, 0], [5, 50.96, 49], [83.36, 0.282, 0, 0, 0, 0, 1, 0.7, 0.7, 0.8]], [1, "trough", false, 2, [[2, 0, -20, [4], 5]], [0, "11NcWCrSNDkLW/8Lhgh1hB", 1, 0], [5, 50.96, 49], [25.48, 24.5, 0, 0, 0, 0, 1, 1, 1, 0.7]], [1, "trough", false, 2, [[2, 0, -21, [6], 7]], [0, "cdlmTIJHNMk5YjRbfZKot5", 1, 0], [5, 50.96, 49], [76.44, 73.5, 0, 0, 0, 0, 1, 1, 1, 0.7]], [1, "trough", false, 2, [[2, 0, -22, [8], 9]], [0, "63kp2O55hLFb/m/bAZTDzl", 1, 0], [5, 50.96, 49], [127.4, 122.5, 0, 0, 0, 0, 1, 1, 1, 0.7]], [1, "trough", false, 2, [[2, 0, -23, [10], 11]], [0, "a5eVtv4O9G6Jj5jtSjoHbm", 1, 0], [5, 50.96, 49], [178.35999999999999, 171.5, 0, 0, 0, 0, 1, 1, 1, 0.7]]], 0, [0, 3, 1, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 2, 0, -5, 3, 0, -6, 4, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 4, 1, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1], [0, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0]]]]