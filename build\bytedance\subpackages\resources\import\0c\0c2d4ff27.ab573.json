[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "efDt7SUvNIxarizdFZULzA", "3abTDM3VVCaJ1tVX3h52C/", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "cdvDZRLPZKb70zilA0TpQ8", "d0OK77qkxAUbyMQt/5KeHg"], ["node", "_parent", "_spriteFrame", "root", "content", "data", "_N$disabledSprite", "asset", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_active", "_prefab", "_contentSize", "_components", "_children", "_trs", "_parent", "_color", "_anchorPoint"], -1, 4, 5, 9, 2, 7, 1, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials"], -5, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["f2285I7BtdCp7NPvz5uUCu8", ["node", "content"], 3, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -5, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1]], [[2, 0, 1, 2, 2], [3, 1, 0, 2, 3, 4, 3], [0, 0, 9, 7, 6, 4, 5, 2], [0, 0, 9, 7, 6, 4, 5, 8, 2], [1, 0, 1, 2, 8, 4], [6, 0, 2], [0, 0, 2, 7, 6, 4, 5, 3], [0, 0, 7, 6, 4, 5, 8, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [0, 0, 7, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 1, 9, 6, 4, 10, 5, 3], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 1, 9, 6, 4, 5, 8, 3], [0, 0, 3, 9, 6, 4, 10, 5, 8, 3], [0, 0, 9, 4, 8, 2], [7, 0, 1, 1], [2, 1, 2, 1], [8, 0, 1, 2, 3, 3], [3, 0, 2, 3, 4, 2], [1, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [1, 0, 8, 2], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [10, 0, 1, 2, 2], [4, 0, 1, 4, 2, 3, 8, 9, 6], [4, 0, 5, 1, 2, 3, 6, 7, 8, 9, 8], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 2], [13, 0, 1, 2, 3], [14, 0, 1, 2, 3, 4, 5, 6, 6]], [[[[5, "M20_Prepare_Bag"], [6, "M20_Prepare_Bag", 1, [-4, -5], [[16, -3, -2]], [17, -1, 0], [5, 750, 1334]], [7, "Background", [-8], [[1, 1, 0, -6, [8], 9], [20, 0, 45, 9.5, 9.5, 1.8549999999999995, 13.145, 100, 40, -7]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 61, 65], [0, 5.645, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "content", [-10], [[22, 1, 3, 15, 10, 10, 10, 20, 15, -9, [5, 550, 140]]], [0, "88gX/YXhZEzapwHf0lgW9e", 1, 0], [5, 550, 140], [0, 0.5, 1], [0, 159.017, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "content", [-11, -12, -13], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 645, 930]], [2, "title_zhua<PERSON><PERSON>", 4, [-15, -16], [[1, 1, 0, -14, [5], 6]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 650, 930]], [10, "view", [3], [[23, 0, -17, [12]], [4, 45, 240, 250, -18]], [0, "b3uzR3mfJLH5TeL3+v59Dq", 1, 0], [5, 585, 780]], [11, "maskbg", 70, 1, [[21, 45, -19], [19, 0, -20, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [2, "bg", 1, [4], [[4, 45, 750, 1334, -21]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [12, "Label_title", 5, [[24, "背包", false, 1, 1, 1, -22, [4]], [26, 3, -23, [4, 4279374353]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 86, 56.4], [0, 420.516, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnclose", 4, [2], [[27, 3, -24, [[28, "f2285I7BtdCp7NPvz5uUCu8", "close", 1]], [4, 4293322470], [4, 3363338360], 2, 10]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [249.321, 414.898, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New ScrollView", 4, [6], [[29, false, 0.75, 0.23, null, null, -25, 3]], [0, "3duH15Hz5B1oIeNpuwhWuk", 1, 0], [5, 585, 780], [0, -38.43, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "img_fgx", 40, 5, [[1, 1, 0, -26, [2], 3]], [0, "08ZpR4lZFKY7+GPeCJk8EK", 1, 0], [5, 600, 800], [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Label", false, 2, [[25, "返回", false, false, 1, 1, 1, 1, -27, [7]]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RewardItem", 3, [18, "1c39X23AxDS5KHIWleBG8w", true, -28, 11], [-202, -70, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 4, 3, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, 0, 2, 0, 0, 2, 0, -1, 13, 0, 0, 3, 0, -1, 14, 0, -1, 5, 0, -2, 10, 0, -3, 11, 0, 0, 5, 0, -1, 12, 0, -2, 9, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 3, 14, 0, 5, 1, 2, 1, 10, 3, 1, 6, 4, 1, 8, 6, 1, 11, 28], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 2, -1, -1, 2, -1, -1, 2, 6, 7, -1], [0, 1, 0, 2, 0, 0, 3, 0, 0, 4, 5, 6, 0]], [[{"name": "img_tj_zbpzk1", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [26, 27, 34, 29]}], [5], 0, [0], [8], [7]]]]