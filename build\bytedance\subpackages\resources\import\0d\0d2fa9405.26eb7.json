[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "29FYIk+N1GYaeWH/q1NxQO", "f7293wEF9JhIuMEsrLuqng", "aej5KCfVhFI6ZBtIoyh/zN", "29R34cGbFB7YQJIiPE06Fl", "f3p84uzd5EVLXvLuhlyoRY", "deYeqBbrtAM7ABF8+EGpZQ", "67PQP9XEhPDKw4h+AcKPjj", "9cpQ3wcGlFtKTW+u7dowuj", "4dMZmkcjlJEbhEI0rAtegJ", "c69ukEixVGW5ujUhgZUUq4", "003lZC4rBDvL0h4cXKc4zg", "baco1XpPhGLrQJffvgvKPq", "3fMzOUt21Ibrx305LLFfTd", "b6qVs8oLFHobTBuzliZb/d", "94Vf0afFtICJyk7vhXtr31", "eeCkPRRSdCq6E37EA96nso", "965S0bAGhNQpqYYtzfXQ8Z", "69OoKTp+5H0KIChcprfKMo", "f0BIwQ8D5Ml7nTNQbh1YlS", "57R+YvXfJBe7+sIxFsZONQ", "40OtPssnZP9antMAWsDtDT", "9fJcHytXpHGJJKTcFv9khH", "beGFs8qqFAHKxXNslKO58f", "cf5h7rSQ9F65nFZY3gyb3C", "16jEcUhApE44gxGbIkTvIy", "09sJULYrtAT42/JysiBw5n", "fbW93Hw8FGxa8CKtZCKMho", "f5/niJFohEAoX9Zs2qoltQ", "89elJqpRpNeY1ecsBef1ng", "21bTJzWdxFgbLo/YuoYayz", "87btSzQ8lJvLKf56sGGw1t", "79MOUNm15IiITViiMP4AR/", "2aS9x4RXRL75GY8/5QG9a6", "41ouYuMwZFG5G8hG4Mao8m", "af3+y/s6VOQoQff4vKcF8m", "3avRVQxlBOJbXtY2p6HJgT", "e1ewMz11FCvoK+82ZnvbVl", "97EhhUozpD3YNgA0qyjRoB", "f6cayDPDxPhqpE/WEopwl+", "b8fgGBYrBG1bTyY6BYFZz+", "b1ZPJQ7j9EWqLau13s9v3/", "c3vkyuAsNKgYj45Eh+kcvs", "3fCg1XlzFNZoOOZN+aThbV"], ["node", "_spriteFrame", "_textureSetter", "_parent", "_N$file", "root", "asset", "_N$disabledSprite", "_N$normalSprite", "scrollView", "bg", "icon", "desc", "itemName", "_N$barSprite", "_N$target", "data", "_N$font"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_groupIndex", "_prefab", "_components", "_contentSize", "_trs", "_children", "_parent", "_color", "_anchorPoint"], 0, 4, 9, 5, 7, 2, 1, 5, 5], ["cc.Label", ["_N$horizontalAlign", "_N$verticalAlign", "_string", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_lineHeight", "_enableWrapText", "_N$cacheMode", "_N$fontFamily", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_bottom", "_top", "_left", "_verticalCenter", "node"], -4, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_N$paddingLeft", "_N$paddingTop", "_N$paddingBottom", "_N$paddingRight", "_N$affectedByScale", "node", "_layoutSize"], -6, 1, 5], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent", "_anchorPoint"], 2, 12, 4, 5, 7, 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite", "_N$normalSprite", "_N$target"], 1, 1, 9, 5, 5, 6, 6, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$fontSize", "_N$lineHeight", "_N$string", "_N$horizontalAlign", "_N$maxWidth", "node"], -3, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["3e387DdZzVOZZek9TuZnt8L", ["UItype", "node", "nodeArr", "labelArr", "scrollView"], 2, 1, 2, 2, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["b08efGBdHpI8p1jm4bYRVPN", ["node"], 3, 1], ["8b218EMHRlJ/6adWyDa8zJr", ["node", "itemName", "desc", "icon", "bg"], 3, 1, 1, 1, 1, 1], ["4a553NdH6RCaIU0ckY3RVqV", ["node"], 3, 1], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1]], [[10, 0, 1, 2, 2], [1, 0, 8, 4, 3, 5, 6, 2], [21, 0, 1, 2, 2], [3, 3, 4, 5, 1], [1, 0, 8, 7, 4, 3, 5, 6, 2], [3, 1, 0, 3, 4, 5, 3], [1, 0, 8, 4, 3, 9, 5, 6, 2], [1, 0, 8, 3, 6, 2], [14, 0, 1, 2, 3, 3], [1, 0, 7, 4, 3, 5, 6, 2], [1, 0, 1, 8, 7, 4, 3, 5, 6, 3], [11, 0, 1, 3, 3], [2, 2, 3, 4, 0, 1, 10, 11, 12, 6], [6, 0, 6, 1, 2, 3, 4, 2], [4, 0, 7, 2], [5, 0, 1, 2, 9, 10, 4], [5, 0, 1, 4, 2, 3, 9, 10, 6], [3, 1, 0, 3, 4, 3], [2, 2, 3, 4, 5, 0, 1, 10, 11, 7], [22, 0, 1], [1, 0, 8, 7, 4, 3, 5, 2], [7, 0, 2, 3, 4, 5, 7, 6, 2], [2, 3, 4, 0, 1, 10, 11, 5], [1, 0, 7, 4, 3, 5, 2], [9, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 7, 4], [4, 0, 4, 7, 3], [11, 0, 1, 2, 3, 4], [7, 1, 0, 2, 3, 4, 5, 6, 3], [2, 2, 3, 4, 0, 1, 10, 11, 6], [2, 2, 3, 7, 4, 5, 0, 1, 8, 10, 11, 9], [8, 0, 4, 1, 5, 2, 6, 6], [8, 0, 3, 1, 2, 6, 5], [12, 0, 2], [1, 0, 2, 7, 4, 3, 5, 3], [1, 0, 7, 4, 3, 5, 10, 6, 2], [1, 0, 8, 7, 4, 3, 9, 5, 6, 2], [1, 0, 1, 8, 4, 3, 5, 6, 3], [6, 0, 5, 1, 2, 3, 2], [6, 0, 5, 1, 2, 3, 4, 2], [6, 0, 6, 1, 2, 3, 7, 4, 2], [9, 0, 1, 2, 3, 4, 6, 5, 2], [13, 0, 1, 2, 3, 4, 2], [10, 1, 2, 1], [4, 0, 3, 1, 2, 7, 5], [4, 0, 4, 3, 1, 2, 7, 6], [4, 0, 5, 6, 7, 4], [5, 0, 1, 5, 6, 3, 9, 10, 6], [5, 0, 1, 4, 7, 2, 8, 9, 10, 7], [5, 0, 1, 2, 3, 9, 10, 5], [3, 2, 1, 0, 3, 4, 5, 4], [3, 0, 3, 4, 5, 2], [3, 3, 4, 1], [3, 2, 1, 0, 3, 4, 4], [15, 0, 1], [16, 0, 1, 2, 3, 4, 1], [17, 0, 1], [18, 0, 1, 2, 2], [19, 0, 1, 2, 2], [7, 1, 0, 2, 3, 4, 5, 8, 7, 6, 3], [20, 0, 1, 2, 2], [2, 2, 3, 7, 4, 0, 1, 8, 10, 11, 12, 8], [2, 2, 3, 6, 4, 5, 0, 1, 9, 10, 11, 12, 9], [2, 2, 4, 5, 0, 1, 10, 11, 6], [2, 2, 3, 6, 5, 0, 1, 9, 10, 11, 8], [2, 2, 3, 6, 4, 5, 0, 1, 10, 11, 8], [8, 0, 3, 4, 1, 5, 2, 6, 7], [23, 0, 1, 2, 3, 4, 5, 6, 6]], [[[{"name": "icon_bx02", "rect": [3, 4, 184, 157], "offset": [0.5, 0.5], "originalSize": [189, 166], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [11]], [[{"name": "img_sddb05", "rect": [0, 0, 681, 362], "offset": [0, 0], "originalSize": [681, 362], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [12]], [[{"name": "bar_dp_mrbj1", "rect": [2, 1, 85, 17], "offset": [0, 0], "originalSize": [89, 19], "capInsets": [9, 0, 12, 0]}], [0], 0, [0], [2], [13]], [[[33, "Shop_View"], [34, "Shop_View", 1, [-10, -11], [[42, 0, -9, [-7, -8], [-3, -4, -5, -6], -2]], [43, -1, 0], [5, 750, 1334]], [35, "content", [-14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25], [[44, 41, 330, 220, 400, -12], [47, 1, 2, 30, 30, 30, -13, [5, 750, 1605]]], [0, "657vJeI1ZD5bNTdihXAbiW", 1, 0], [5, 750, 1605], [0, 0.5, 1], [0, 365, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "Shop_GoodsDrawBox", [-28, -29, -30, -31, -32], [[5, 1, 0, -26, [101], 102], [54, -27]], [0, "e2eaziD9FFW6iLBGTnU5Wh", 1, 0], [5, 319, 471]], [38, "Shop_GoodsSkillBox", [-39, -40, -41, -42], [[-33, [55, -38, -37, -36, -35, -34]], 1, 4], [0, "22YAjEE7NM357VkaM63+N8", 1, 0], [5, 310, 463]], [23, "Shop_GoodsLegendBox", [-45, -46, -47, -48, -49], [[3, -43, [81], 82], [56, -44]], [0, "d1qL4Se0tAfZUm0/H1iRxo", 1, 0], [5, 681, 362]], [9, "progress", [-53, -54, -55, -56, -57], [[5, 1, 0, -50, [38], 39], [57, 400, -52, -51]], [0, "7ayf2plKpPeabDtPNerkrQ", 1, 0], [5, 400, 19], [15.356, 253.058, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnrefrsh", [-60, -61], [[58, "ShopAdRefresh", -58, [[11, "3e387DdZzVOZZek9TuZnt8L", "onClcikDailyDealRefresh", 1]]], [3, -59, [19], 20]], [0, "7aMeluWTRKA63w0H7m+4KT", 1, 0], [5, 206, 80], [264.992, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnad", [-64, -65, -66], [[21, 3, -62, [[11, "8b218EMHRlJ/6adWyDa8zJr", "onClickVideoBuy", 4]], [4, 4293322470], [4, 3363338360], 51, 52], [5, 1, 0, -63, [53], 54]], [0, "62gZYqqoFAp7c+kIs6VaEG", 1, 0], [5, 106, 64], [90, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "btnad", [-69, -70, -71], [[21, 3, -67, [[11, "4a553NdH6RCaIU0ckY3RVqV", "onClickVideoBuy", 5]], [4, 4293322470], [4, 3363338360], 71, 72], [5, 1, 0, -68, [73], 74]], [0, "022Mcs+adC2Y0WI+WvPL34", 1, 0], [5, 106, 97], [-180, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "btnad", [-74, -75, -76], [[21, 3, -72, [[11, "b08efGBdHpI8p1jm4bYRVPN", "onClickVideoBuy", 3]], [4, 4293322470], [4, 3363338360], 97, 98], [5, 1, 0, -73, [99], 100]], [0, "62gZYqqoFAp7c+kIs6VaEG", 1, 0], [5, 106, 97], [77.19, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [20, "maskbg", 1, [-79, -80], [[14, 45, -77], [5, 1, 0, -78, [4], 5]], [0, "75Dyl3A+hF77zn4xUgio5F", 1, 0], [5, 750, 1334]], [4, "middle", 2, [-82, 7, -83], [[14, 40, -81]], [0, "f7l3+y7RNBJJz9HPg7Iga8", 1, 0], [5, 750, 60], [0, -137, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "box_2", 2, [-85, 6, -86], [[50, false, 1, 0, -84, [56], 57]], [0, "19tPzbE99DUJDKw/1crvMs", 1, 0], [5, 720, 540], [0, -886, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnLayer", 5, [9, -88, -89], [[15, 1, 1, 10, -87, [5, 466, 97]]], [0, "7a+8Qbq/pGVKPdGdXT6oKl", 1, 0], [5, 466, 97], [114.582, -94.222, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [9, "btnzsj", [-93], [[28, 0.9, 3, -90, [[11, "b08efGBdHpI8p1jm4bYRVPN", "onClickBuy", 3]], [4, 4293322470], [4, 3363338360], 91], [17, 1, 0, -91, [92]], [48, 1, 1, 26, 26, 10, true, -92, [5, 144.38, 97]]], [0, "acTGj2pstDbYbjIsxxKyOv", 1, 0], [5, 144.38, 97], [-58, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [39, "New ScrollView", [-96], [[-94, [45, 45, 431, 173, 750, 1334, -95]], 1, 4], [0, "b9hopJtVFKeaqHiYvJKJfl", 1, 0], [5, 750, 730], [0, -129, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "view", 16, [2], [[60, 0, -97, [113]], [25, 45, 240, 250, -98]], [0, "4dRr3rzM9F6KE22UoXKPeP", 1, 0], [5, 750, 730]], [4, "bg_huo<PERSON>an", 12, [-100, -101], [[3, -99, [13], 14]], [0, "67KUq2VxJPkKAZ/vGRkjAD", 1, 0], [5, 193, 52], [-234.052, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "refrs<PERSON><PERSON>", 18, [[-102, [46, 10, 56.705, 2.927, -103], [2, 2, -104, [4, 4278190080]]], 1, 4, 4], [0, "17854B4E5AxoyD99PllJYN", 1, 0], [5, 109.56, 41.8], [14.985, 2.927, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "name", 4, [[-105, [14, 16, -106], [2, 3, -107, [4, 4281217588]]], 1, 4, 4], [0, "d6vm2qMDhIJIRk+8+ctERY", 1, 0], [5, 134, 56.4], [0, 218.862, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnLayer", 4, [-109, 8], [[15, 1, 1, 10, -108, [5, 286, 97]]], [0, "faiL86PlVAS6GKKdaiWLZf", 1, 0], [5, 286, 97], [0, -133, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnzsj", 21, [-112], [[28, 0.9, 3, -110, [[27, "8b218EMHRlJ/6adWyDa8zJr", "onClickBuy", "11", 4]], [4, 4293322470], [4, 3363338360], 43], [5, 1, 0, -111, [44], 45]], [0, "acTGj2pstDbYbjIsxxKyOv", 1, 0], [5, 170, 64], [-58, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn", 14, [-114, -115], [[17, 1, 0, -113, [77]]], [0, "bcL823l2JHCrrwdyT4juKv", 1, 0], [5, 170, 97], [-32, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "btn", 14, [-117, -118], [[17, 1, 0, -116, [80]]], [0, "d4CIJvSYNAFIJlKz57lg3o", 1, 0], [5, 170, 97], [148, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "name", 3, [[18, "普通箱子", 30, false, 1, 1, 1, -119, [89]], [14, 16, -120], [2, 3, -121, [4, 4278190080]]], [0, "d6vm2qMDhIJIRk+8+ctERY", 1, 0], [5, 126, 56.4], [0, 198.186, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnLayer", 3, [15, 10], [[15, 1, 1, 10, -122, [5, 260.38, 97]]], [0, "faiL86PlVAS6GKKdaiWLZf", 1, 0], [5, 260.38, 97], [0, -156.923, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "pic_dp_00", 11, [[3, -123, [0], 1], [26, 1, 78.38599999999997, -124]], [0, "89bAQdYkdMroeG6LeQjuaZ", 1, 0], [5, 776, 363], [0, 407.11400000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "font_dp_title_thsp", 11, [[3, -125, [2], 3], [26, 1, 215.589, -126]], [0, "44PSRZ7ONFQa+IdO5eTDGc", 1, 0], [5, 284, 92], [-161.824, 405.411, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "bg", 1, [16], [[25, 45, 750, 1334, -127]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [4, "title_store", 2, [-129], [[3, -128, [8], 9]], [0, "86nfYSFYFC/ZllE/R/nzTw", 1, 0], [5, 531, 47], [-12, -53.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", 30, [[12, "每日商店", 36, false, 1, 1, -130, [6], 7], [2, 3, -131, [4, 4279637528]]], [0, "f1Ry5VMa9D8qDW9ionQNts", 1, 0], [4, 4293062126], [5, 150, 56.4], [12, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 7, [[61, "刷新", 36, false, false, 1, 1, 1, -132, [15], 16], [2, 2, -133, [4, 4278190080]]], [0, "6dCe+w0RpLgZl8kroM8Lcy", 1, 0], [5, 76, 54.4], [29.256, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 7, [[3, -134, [17], 18], [19, -135]], [0, "5bDjWsSYdBB5xdkq39qAuA", 1, 0], [5, 50, 52], [-45.93, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [40, "refrshcount", 12, [[-136, [2, 2, -137, [4, 4278190080]]], 1, 4], [0, "74cbI0sMhAvLktTV7aMcaY", 1, 0], [5, 148, 41.8], [0, 1, 0.5], [149.991, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "box_1", 2, [-139], [[16, 1, 3, 30, 14, 14, -138, [5, 750, 312]]], [0, "9clMbhs7VHP58nH1FDi41M", 1, 0], [5, 750, 312], [0, -353, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title_store", 2, [-141], [[3, -140, [25], 26]], [0, "8f2EvkJ7xJ65vlchgjVQ6T", 1, 0], [5, 531, 47], [-12, -562.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", 36, [[12, "法宝碎片", 36, false, 1, 1, -142, [23], 24], [2, 3, -143, [4, 4279637528]]], [0, "ec7AoRK/hB4Zp0rMvHLFNf", 1, 0], [4, 4293062126], [5, 150, 56.4], [12, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "percent", 6, [[-144, [2, 2, -145, [4, 4278190080]]], 1, 4], [0, "c2OWM1pWFP4ZMyfrv8+aSx", 1, 0], [5, 57.28, 54.4], [0, 1.676, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lv", 6, [[-146, [2, 2, -147, [4, 4278190080]]], 1, 4], [0, "31i/4K2MJL0qrs/ccJNKEZ", 1, 0], [5, 47.03, 54.4], [-236.703, 1.153, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btninfo", 6, [-150], [[59, 0.9, 3, -149, [[27, "3e387DdZzVOZZek9TuZnt8L", "openView", "ui/shop/Shop_SkillBoxInfo", 1]], [4, 4293322470], [4, 3363338360], -148, 36, 37]], [0, "d8fajb2GBFzZ5qJDFo97oQ", 1, 0], [5, 100, 80], [238.739, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 40, [[3, -151, [34], 35]], [0, "d4oOy6CnNLHIvIc6XD/Au9", 1, 0], [5, 62, 44], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "content", 13, [4], [[15, 1, 1, 10, -152, [5, 310, 200]]], [0, "3fazbaxRFK86UP7x1p6OeZ", 1, 0], [5, 310, 200], [0, -52.257, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 8, [[3, -153, [46], 47], [19, -154]], [0, "aewEdGpLxD4JsNXPh1Lnv1", 1, 0], [5, 50, 52], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [6, "desc", 8, [[62, "免费00:00", 24, 24, false, 1, 1, 1, "Consolas", -155, [48], 49], [2, 2, -156, [4, 4278190080]]], [0, "b0NRPY6WVGWZAo461/PZHp", 1, 0], [4, 4278255518], [5, 111.95, 34.239999999999995], [1.443, -40.989, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc_ads", 8, [[22, 25, false, 1, 1, -157, [50]], [2, 2, -158, [4, 4278190080]]], [0, "4eRuETFA5LnpZ0DiRBe9Vp", 1, 0], [5, 4, 54.4], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "title_4", false, 2, [-160], [[3, -159, [60], 61]], [0, "b6dtiZd/FCrqCzrzmqQNY8", 1, 0], [5, 531, 47], [-12, -1209.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", 46, [[12, "装备宝箱", 36, false, 1, 1, -161, [58], 59], [2, 3, -162, [4, 4279637528]]], [0, "b2EePhrANBP6eQ+ITFW6g7", 1, 0], [4, 4293062126], [5, 150, 56.4], [12, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "box_4", false, 2, [5], [[16, 1, 2, 30, 14, 14, -163, [5, 750, 362]]], [0, "42xjQ0ZZRHq6lWnWd/d+yS", 1, 0], [5, 750, 362], [0, -1444, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "name", 5, [[63, "黄金宝箱", false, 1, 1, 1, -164, [62]], [2, 4, -165, [4, 4278190080]]], [0, "67cY8sQcdOBL1Yr4VriJp7", 1, 0], [4, 4293062126], [5, 168, 58.4], [0, 142.14, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "pop", 5, [-167], [[5, 1, 0, -166, [65], 66]], [0, "ecuaKNv8ZPXowOQN0gKMUO", 1, 0], [5, 360, 129], [131.326, 32.047, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 9, [[3, -168, [67], 68], [19, -169]], [0, "87Cd94ne5MD7C8RHXMQrRg", 1, 0], [5, 50, 52], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "desc", 9, [[29, "00:00", 20, false, 1, 1, -170, [69]], [2, 2, -171, [4, 4278190080]]], [0, "747bZe1CBMVoWfQN6ctlZj", 1, 0], [5, 53.69, 54.4], [0, -52.995, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc_ads", 9, [[22, 25, false, 1, 1, -172, [70]], [2, 2, -173, [4, 4278190080]]], [0, "812adDI6JOq7LVhkSoYtRp", 1, 0], [5, 4, 54.4], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 23, [[30, "买一个", 30, false, false, 1, 1, 1, 1, -174, [75]], [2, 3, -175, [4, 4278190080]]], [0, "afhB2JU3VKtpkd9ILSNFOo", 1, 0], [5, 96, 56.4], [0, 21.604, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 24, [[30, "买十个", 30, false, false, 1, 1, 1, 1, -176, [78]], [2, 3, -177, [4, 4278190080]]], [0, "ca+snb6yRITJqiTmEZhC6J", 1, 0], [5, 96, 56.4], [0, 21.604, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "box_5", false, 2, [3], [[49, 1, 1, 37, 14, -178, [5, 319, 471]]], [0, "76O8ECwTpHW6mfxlKpov3s", 1, 0], [5, 319, 471], [0, -1890.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "ginfo", 3, [-180], [[51, 0, -179, [85], 86]], [0, "41oNjnqt1EvIGm9MhlcgYF", 1, 0], [4, 4278190080], [5, 312, 35], [0, -78.6, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "pop", 3, [-182], [[5, 1, 0, -181, [87], 88]], [0, "79jINnM6lGgKgXPomP5dUG", 1, 0], [5, 279, 96], [0, 114.544, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_ads", 10, [[3, -183, [93], 94], [19, -184]], [0, "aewEdGpLxD4JsNXPh1Lnv1", 1, 0], [5, 50, 52], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "desc", 10, [[29, "00:00", 20, false, 1, 1, -185, [95]], [2, 2, -186, [4, 4278190080]]], [0, "b0NRPY6WVGWZAo461/PZHp", 1, 0], [5, 53.69, 54.4], [0, -52.995, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc_ads", 10, [[22, 25, false, 1, 1, -187, [96]], [2, 2, -188, [4, 4278190080]]], [0, "4eRuETFA5LnpZ0DiRBe9Vp", 1, 0], [5, 4, 54.4], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title_store", 2, [-190], [[3, -189, [105], 106]], [0, "d5jfhxJCFEkZaiabPf3DxL", 1, 0], [5, 531, 47], [-12, -1209.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", 62, [[12, "灵石", 36, false, 1, 1, -191, [103], 104], [2, 3, -192, [4, 4279637528]]], [0, "ccQmB+5llMz7osFeb7lh3/", 1, 0], [4, 4293062126], [5, 78, 56.4], [12, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "box_3", 2, [-194], [[16, 1, 3, 30, 14, 14, -193, [5, 750, 312]]], [0, "e14ClMsN1HVJS/pj/bsLY2", 1, 0], [5, 750, 312], [0, -1419, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "title_6", false, 2, [-196], [[3, -195, [110], 111]], [0, "61KUFeSsVPIYK5snKkWN+S", 1, 0], [5, 531, 47], [-12, -1628.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "title", 65, [[12, "灵币", 36, false, 1, 1, -197, [108], 109], [2, 4, -198, [4, 4279637528]]], [0, "996LgaIqBCWaPpI+XTrY9v", 1, 0], [4, 4293062126], [5, 80, 58.4], [12, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "box_6", false, 2, [-200], [[16, 1, 3, 30, 14, 14, -199, [5, 750, 312]]], [0, "49jJ+cDLlFe4vrRpOHipYV", 1, 0], [5, 750, 312], [0, -1838, 0, 0, 0, 0, 1, 1, 1, 1]], [64, "00:00:00", 24, 30, 1, 1, 1, "Consolas", 19, [10]], [37, "icon_clock", false, 18, [[3, -201, [11], 12]], [0, "aeclLw06NIba6wu3B6fm3p", 1, 0], [5, 62, 44], [-83.653, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "今日剩余次数", 24, 30, false, 1, 2, 1, 34, [21]], [7, "Shop_GoodsItem", 35, [8, "a6/7Ud5e9HmKP40GyUdFzJ", true, -202, 22], [-234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sp_shop_levelbg", 13, [[3, -203, [27], 28]], [0, "a3DvnFgrlLP4CV2ZTRlpAM", 1, 0], [5, 916, 445], [0, -17.827, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "bar", 6, [-204], [0, "67jPwIiw5Du5dcr1bgUoi5", 1, 0], [5, 400, 19], [0, 0, 0.5], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 1, 0, 73, [29]], [18, "3000", 24, false, 1, 1, 1, 38, [30]], [1, "bg_storelv", 6, [[3, -205, [31], 32]], [0, "89VsMm5SVNarglfmzUmmKm", 1, 0], [5, 75, 32], [-237.486, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [18, "LV.1", 24, false, 1, 1, 1, 39, [33]], [24, "icon", 4, [-206], [0, "9c2w+aSZNKy7SgnjuRxoTr", 1, 0], [5, 159, 136], [0, 51.355, 0, 0, 0, 0, 1, 1, 1, 1]], [52, 78, [40]], [18, "普通箱子", 32, false, 1, 1, 1, 20, [41]], [24, "desc", 4, [-207], [0, "74Z34NGpZMR4lCRS7p4hlh", 1, 0], [5, 270, 40.32], [0, -45.232, 0, 0, 0, 0, 1, 1, 1, 1]], [66, false, "<outline color=black width=2>必出<color=#0fffff>高级</color>装备</outline>", 1, 24, 270, 32, 81], [7, "Item_NeedDisplay", 22, [8, "46BaSyJb9ET5R09NYuNF27", true, -208, 42], [-7.105427357601002e-15, 2.117, 0, 0, 0, 0, 1, 1, 1, 1]], [53, false, 1, 0, 4, [55]], [1, "icon_bx03", 5, [[3, -209, [63], 64]], [0, "09Y9QF6sBJLL9S2HuR4+r/", 1, 0], [5, 189, 165], [-202.842, -63.71, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc", 50, [[31, false, 1, 24, 280, 32, -210]], [0, "ccSrJAfoNLT7dcKxB/C0U3", 1, 0], [5, 280, 40.32], [15.326, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "gMsg", 5, [[32, false, "<b><outline color=black width=4><color=#f8a64e>8</c>次内必出<color=#cd47ff>史诗</c>装备</outline>", 22, 50, -211]], [0, "efmACGDg5PBp78yxGOzRFe", 1, 0], [5, 220.07, 63], [-197.114, 64.048, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Item_NeedDisplay", 23, [8, "a2W1simAVAWJV68o/sIgLw", true, -212, 76], [0, -17.134, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Item_NeedDisplay", 24, [8, "22awru1WtM5q1qQ5SIxwoG", true, -213, 79], [0, -17.134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon", 3, [[3, -214, [83], 84]], [0, "639R/tscBM9oEs3ykqBmYM", 1, 0], [5, 184, 157], [0, 0.724, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc", 57, [[32, false, "<b><outline color=black width=4><color=#f8a64e>8</c>次内必出<color=#cd47ff>史诗</c>装备</outline>", 22, 50, -215]], [0, "11POZGxF5OkoVmSH7MNTve", 1, 0], [5, 220.07, 63], [0, 0.36, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "desc", 58, [[31, false, 1, 24, 260, 32, -216]], [0, "daD6yPA85G06ivtb+Z26wh", 1, 0], [5, 260, 40.32], [0, 11.448, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Item_NeedDisplay", 15, [8, "afhNhN3LJAip02i5GDuIJQ", true, -217, 90], [0, 2.117, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Shop_GoodsItem", 64, [8, "a6/7Ud5e9HmKP40GyUdFzJ", true, -218, 107], [-234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Shop_GoodsItem", 67, [8, "a6/7Ud5e9HmKP40GyUdFzJ", true, -219, 112], [-234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [67, false, 0.75, 0.23, null, null, 16, 2]], 0, [0, 5, 1, 0, 9, 96, 0, -1, 68, 0, -2, 70, 0, -3, 77, 0, -4, 75, 0, -1, 2, 0, -2, 7, 0, 0, 1, 0, -1, 11, 0, -2, 29, 0, 0, 2, 0, 0, 2, 0, -1, 30, 0, -2, 12, 0, -3, 35, 0, -4, 36, 0, -5, 13, 0, -6, 46, 0, -7, 48, 0, -8, 56, 0, -9, 62, 0, -10, 64, 0, -11, 65, 0, -12, 67, 0, 0, 3, 0, 0, 3, 0, -1, 90, 0, -2, 57, 0, -3, 58, 0, -4, 25, 0, -5, 26, 0, -1, 84, 0, 10, 84, 0, 11, 79, 0, 12, 82, 0, 13, 80, 0, 0, 4, 0, -1, 78, 0, -2, 20, 0, -3, 81, 0, -4, 21, 0, 0, 5, 0, 0, 5, 0, -1, 49, 0, -2, 85, 0, -3, 50, 0, -4, 87, 0, -5, 14, 0, 0, 6, 0, 14, 74, 0, 0, 6, 0, -1, 73, 0, -2, 38, 0, -3, 76, 0, -4, 39, 0, -5, 40, 0, 0, 7, 0, 0, 7, 0, -1, 32, 0, -2, 33, 0, 0, 8, 0, 0, 8, 0, -1, 43, 0, -2, 44, 0, -3, 45, 0, 0, 9, 0, 0, 9, 0, -1, 51, 0, -2, 52, 0, -3, 53, 0, 0, 10, 0, 0, 10, 0, -1, 59, 0, -2, 60, 0, -3, 61, 0, 0, 11, 0, 0, 11, 0, -1, 27, 0, -2, 28, 0, 0, 12, 0, -1, 18, 0, -3, 34, 0, 0, 13, 0, -1, 72, 0, -3, 42, 0, 0, 14, 0, -2, 23, 0, -3, 24, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -1, 93, 0, -1, 96, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, -2, 69, 0, -1, 68, 0, 0, 19, 0, 0, 19, 0, -1, 80, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, -1, 83, 0, 0, 23, 0, -1, 54, 0, -2, 88, 0, 0, 24, 0, -1, 55, 0, -2, 89, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, -1, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, -1, 70, 0, 0, 34, 0, 0, 35, 0, -1, 71, 0, 0, 36, 0, -1, 37, 0, 0, 37, 0, 0, 37, 0, -1, 75, 0, 0, 38, 0, -1, 77, 0, 0, 39, 0, 15, 41, 0, 0, 40, 0, -1, 41, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, -1, 47, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, -1, 86, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, -1, 91, 0, 0, 58, 0, -1, 92, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, -1, 63, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, -1, 94, 0, 0, 65, 0, -1, 66, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, -1, 95, 0, 0, 69, 0, 5, 71, 0, 0, 72, 0, -1, 74, 0, 0, 76, 0, -1, 79, 0, -1, 82, 0, 5, 83, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 5, 88, 0, 5, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 5, 93, 0, 5, 94, 0, 5, 95, 0, 16, 1, 2, 3, 17, 3, 3, 56, 4, 3, 42, 5, 3, 48, 6, 3, 13, 7, 3, 12, 8, 3, 21, 9, 3, 14, 10, 3, 26, 15, 3, 26, 16, 3, 29, 219], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 70, 74, 75, 77, 80, 82, 84], [-1, 1, -1, 1, -1, 1, -1, 4, -1, 1, -1, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 6, -1, 4, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, 8, 7, -1, 1, -1, -1, 6, 7, -1, 1, -1, 1, -1, 4, -1, 8, 7, -1, 1, -1, -1, 1, -1, 4, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 8, 7, -1, 1, -1, 6, -1, -1, 6, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 6, 7, -1, -1, 1, -1, -1, 8, 7, -1, 1, -1, 1, -1, 4, -1, 1, 6, -1, 4, -1, 1, 6, -1, 4, 1, 4, 4, 4, 17, 1], [0, 14, 0, 15, 0, 16, 0, 1, 0, 4, 0, 0, 9, 0, 17, 0, 1, 0, 6, 0, 10, 0, 8, 0, 1, 0, 4, 0, 18, 0, 0, 0, 19, 0, 0, 9, 20, 2, 0, 21, 0, 0, 7, 2, 0, 22, 0, 6, 0, 3, 0, 5, 2, 0, 10, 0, 0, 23, 0, 1, 0, 4, 0, 0, 24, 0, 25, 0, 6, 0, 0, 5, 2, 0, 5, 0, 7, 0, 0, 7, 0, 0, 26, 0, 27, 0, 28, 0, 29, 0, 7, 2, 0, 0, 6, 0, 0, 5, 2, 0, 5, 0, 30, 0, 1, 0, 4, 8, 0, 1, 0, 4, 8, 0, 3, 31, 3, 3, 3, 3, 32]], [[{"name": "pic_dp_00", "rect": [0, 0, 776, 363], "offset": [0, 0.5], "originalSize": [776, 364], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [33]], [[{"name": "bar_dp_mrbj0", "rect": [0, 0, 89, 19], "offset": [0, 0], "originalSize": [89, 19], "capInsets": [20, 0, 19, 0]}], [0], 0, [0], [2], [34]], [[{"name": "bg_goods_04", "rect": [0, 0, 330, 463], "offset": [0, 0], "originalSize": [330, 463], "capInsets": [0, 340, 0, 108]}], [0], 0, [0], [2], [35]], [[{"name": "img_sddb07", "rect": [0, 0, 319, 471], "offset": [0, 0], "originalSize": [319, 471], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [36]], [[{"name": "sp_shop_topbg", "rect": [0, 0, 720, 900], "offset": [0, 0], "originalSize": [720, 900], "capInsets": [0, 425, 0, 0]}], [0], 0, [0], [2], [37]], [[{"name": "sp_shop_levelbg", "rect": [16, 0, 916, 445], "offset": [8, 0], "originalSize": [932, 445], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[{"name": "bg_treasurebox_01", "rect": [0, 0, 65, 64], "offset": [0, 0], "originalSize": [65, 64], "capInsets": [23, 20, 22, 30]}], [0], 0, [0], [2], [39]], [[{"name": "font_dp_title_thsp", "rect": [6, 1, 284, 92], "offset": [1, 1], "originalSize": [294, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "icon_bx03", "rect": [0, 0, 189, 165], "offset": [0, 0.5], "originalSize": [189, 166], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "img_sdqp01", "rect": [0, 0, 96, 101], "offset": [0, 0], "originalSize": [96, 101], "capInsets": [45, 66, 21, 19]}], [0], 0, [0], [2], [42]], [[{"name": "sp_naozhong", "rect": [0, 0, 193, 52], "offset": [0, 0.5], "originalSize": [193, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "img_sdqp02", "rect": [0, 0, 97, 76], "offset": [0, 0], "originalSize": [97, 76], "capInsets": [64, 16, 17, 37]}], [0], 0, [0], [2], [44]]]]