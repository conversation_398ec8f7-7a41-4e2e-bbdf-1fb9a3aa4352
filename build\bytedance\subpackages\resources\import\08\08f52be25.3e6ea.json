[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "deYeqBbrtAM7ABF8+EGpZQ", "40OtPssnZP9antMAWsDtDT", "28/4GcOCxEmal1I14Kaffm", "9aKDX8IElLDo/qWBJQqmpn", "d2a46cmrJBZaXhLR6SWKQQ", "f8npR6F8ZIZoCp3cKXjJQz", "39cfJeWbpN0rQ4tUp8g4Wz", "b1ko8IaBRDq4S83Z7hRN4u", "4dMZmkcjlJEbhEI0rAtegJ", "03kOui8H9NrKJrjppqX/Jk", "59Olpc2FdPj4eJWrfow3PE"], ["node", "_spriteFrame", "root", "_N$file", "_textureSetter", "asset", "btnLayout", "data", "_parent"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children"], 0, 4, 5, 1, 9, 7, 2], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_N$cacheMode", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["90766qbHMdEoL6kD19TV/Ae", ["node", "btnLayout"], 3, 1, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[2, 0, 1, 2, 2], [0, 0, 5, 8, 6, 3, 4, 7, 2], [0, 0, 5, 6, 3, 4, 7, 2], [4, 2, 3, 4, 1], [10, 0, 1, 2, 2], [0, 0, 5, 3, 7, 2], [8, 0, 1, 2, 3, 3], [4, 0, 1, 2, 3, 4, 3], [5, 0, 1, 7, 2, 3, 4, 5, 8, 9, 10, 11, 9], [0, 0, 5, 6, 3, 4, 2], [6, 0, 2], [0, 0, 1, 8, 6, 3, 4, 3], [0, 0, 8, 3, 4, 7, 2], [0, 0, 5, 8, 3, 2], [0, 0, 2, 5, 6, 3, 4, 7, 3], [7, 0, 1, 1], [2, 1, 2, 1], [9, 0, 1, 2, 3, 4, 4], [3, 0, 1, 4, 3], [3, 0, 2, 3, 4, 4], [5, 0, 1, 6, 2, 3, 4, 5, 9, 10, 11, 8]], [[[{"name": "sp_unlocknewModule", "rect": [5, 0, 197, 210], "offset": [-1.5, 0], "originalSize": [210, 210], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [4]], [[{"name": "bg_qd_03", "rect": [0, 0, 161, 84], "offset": [-0.5, 0], "originalSize": [162, 84], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [5]], [[{"name": "05", "rect": [24, 47, 552, 506], "offset": [0, 0], "originalSize": [600, 600], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [6]], [[[10, "Shop_DrawCards_Skill"], [11, "Shop_DrawCards_Skill", 1, [-4, -5, -6, -7, -8], [[15, -3, -2]], [16, -1, 0], [5, 750, 1334]], [1, "btnLayer", 1, [-11, -12, -13], [[17, 1, 1, 18, -9, [5, 669, 100]], [18, 4, 139.60800000000006, -10]], [0, "8374C8kdJHEKh/ApMchH4T", 1, 0], [5, 669, 100], [0, -477.39199999999994, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "upgrade_btn", 2, [-15, -16], [[7, 1, 0, -14, [9], 10]], [0, "47TdskmQtNpaR4/7b3luMy", 1, 0], [5, 211, 104], [-229, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "upgrade_btn", 2, [-18, -19], [[7, 1, 0, -17, [14], 15]], [0, "61c8gCJQhJqKq2hJjTn9ji", 1, 0], [5, 211, 104], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "upgrade_btn", 2, [-21, -22], [[7, 1, 0, -20, [19], 20]], [0, "96x1jU4D1JlKZeO6VcWp1/", 1, 0], [5, 211, 104], [229, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "wz_cwzh", 1, [-24], [[3, -23, [2], 3]], [0, "48juG/VLFM54MJwJExAjdl", 1, 0], [5, 161, 84], [0, 432.353, 0, 0, 0, 0, 1, 2, 2, 1]], [2, "New Label", 6, [[20, "法宝召唤", 64, 64, false, 1, 1, 1, -25, [0], 1], [4, 3, -26, [4, 4278190080]]], [0, "17ad/YUgFPtpqCpJ8Nr72f", 1, 0], [5, 262, 86.64], [0, -7.252, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "num", 3, [[8, "召唤10次", 30, false, false, 1, 1, 1, 1, -27, [6], 7], [4, 2, -28, [4, 4278190080]]], [0, "catt1b0khOz5qXw6d3JD6/", 1, 0], [5, 127.3, 54.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "num", 4, [[8, "召唤10次", 30, false, false, 1, 1, 1, 1, -29, [11], 12], [4, 2, -30, [4, 4278190080]]], [0, "eawcEuMkRJR6pkeEnl0gFJ", 1, 0], [5, 127.3, 54.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "num", 5, [[8, "召唤10次", 30, false, false, 1, 1, 1, 1, -31, [16], 17], [4, 2, -32, [4, 4278190080]]], [0, "5adu/RUmdLVIjKVavuAY62", 1, 0], [5, 127.3, 54.4], [0, 20.683, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "item", [-33, -34], [0, "191rQZymRLp5eihrKUst35", 1, 0], [5, 160, 160], [-221.378, 70.148, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bg", 1, [[19, 45, 750, 1334, -35]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [2, "05", 1, [[3, -36, [4], 5]], [0, "06EQMNtB1DPbLPPxfdqB5n", 1, 0], [5, 552, 506], [0, -102.381, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Item_NeedDisplay", 3, [6, "45Oef25dhKrYdtNYAUPCr1", true, -37, 8], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Item_NeedDisplay", 4, [6, "e25ywXAAFLKKuohh310s+7", true, -38, 13], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Item_NeedDisplay", 5, [6, "dcQmZS4pVEuobUrSQMsgXp", true, -39, 18], [0, -19.133, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "popList", 1, [11], [0, "48InWlhhpNPb/eIVT8dSKF", 1, 0]], [14, "img_qp", 200, 11, [[3, -40, [21], 22]], [0, "77a17RMmNM65HR75uud660", 1, 0], [5, 197, 210], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [9, "zb_yc_104", 11, [[3, -41, [23], 24]], [0, "a4agLGQl1LJLO4sy0glccU", 1, 0], [5, 103, 105]]], 0, [0, 2, 1, 0, 6, 2, 0, 0, 1, 0, -1, 12, 0, -2, 6, 0, -3, 13, 0, -4, 2, 0, -5, 17, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, 0, 3, 0, -1, 8, 0, -2, 14, 0, 0, 4, 0, -1, 9, 0, -2, 15, 0, 0, 5, 0, -1, 10, 0, -2, 16, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 18, 0, -2, 19, 0, 0, 12, 0, 0, 13, 0, 2, 14, 0, 2, 15, 0, 2, 16, 0, 0, 18, 0, 0, 19, 0, 7, 1, 11, 8, 17, 41], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 3, -1, 1, -1, 1, -1, 3, 5, -1, 1, -1, 3, 5, -1, 1, -1, 3, 5, -1, 1, -1, 1, -1, 1], [0, 7, 0, 8, 0, 9, 0, 1, 2, 0, 3, 0, 1, 2, 0, 10, 0, 1, 2, 0, 3, 0, 11, 0, 12]]]]