{"version": 3, "sources": ["assets\\start\\scripts\\Launcher.js"], "names": ["i", "$callID", "require", "$cfg", "$mVC", "$notifier", "$listenID", "$manager", "$time", "$uIManager", "$alertManager", "$eventController", "$game", "$sdkConfig", "$wonderSdk", "$gameUtil", "$moduleLauncher", "$sdkLauncher", "$uILauncher", "k", "cc", "_decorator", "O", "ccclass", "I", "property", "A", "e", "t", "o", "apply", "arguments", "testMode", "CustomPlatform", "EPlatform", "WEB_DEV", "bmsVersion", "progress", "progressText", "wonderlogo", "gamelogo", "logos", "scenebg", "softRightText", "softICPText", "logomap", "tc", "en", "th", "vn", "cn", "_saveOffset", "__extends", "prototype", "onLoad", "console", "log", "_gameManager", "Manager", "game", "addPersistRootNode", "node", "macro", "ENABLE_MULTI_TOUCH", "sys", "<PERSON><PERSON><PERSON><PERSON>", "view", "enableAutoFullScreen", "scheduleOnce", "fit", "hasFont", "Notifier", "send", "ListenID", "Event_SendEvent", "Type", "Scene", "setPhysics", "getVisibleSize", "width", "height", "Math", "round", "<PERSON><PERSON>", "instance", "fitHeight", "fit<PERSON><PERSON><PERSON>", "debug", "setDisplayStats", "onEnable", "changeListener", "onDisable", "Login_Finish", "onLogin_Finish", "PriorLowest", "Game_Load", "onOpenGame", "Fight_BackToMain", "backToMain", "lateUpdate", "parent", "destroy", "gameStart", "setActive", "responsive", "getDesignResolutionSize", "getFrameSize", "n", "start", "__awaiter", "__generator", "label", "initWonderFrameWork", "wonderSdk", "isNative", "EventController", "BMS_APP_NAME", "BMS_VERSION", "checkPlatformInfo", "loadConfig", "sent", "window", "tpdg", "Cfg", "language", "getAll", "initlang", "initLanguageInfo", "spriteFrame", "isIOS", "UILauncher", "string", "js", "formatStr", "<PERSON><PERSON><PERSON><PERSON>au<PERSON><PERSON>", "SdkLauncher", "active", "SoftRightHodler", "SoftICP", "update", "Time", "UIManager", "WonderSdk", "init", "length", "BMSInfoList", "<PERSON><PERSON><PERSON>", "push", "initByBaseConfig", "Promise", "all", "initLangCode", "CCTool", "Language", "LANGUAGE_ENGLISH", "languageCode", "indexOf", "oldGroupMatrix", "JSON", "stringify", "collisionMatrix", "Object", "defineProperty", "parse", "isLive", "vo", "userVo", "code", "requestLoginCode", "then", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showNormalTips", "Open", "saveUserData", "call", "CallID", "Platform_Query", "mode", "Is_Back_From_Try_Play", "MiniGameLv", "get", "Game", "getMouth", "type", "mouth", "MVC", "openArgs", "set<PERSON>ara<PERSON>", "id", "isTryPaly", "BottomBar_OpenView", "isByteDance", "Platform_CdKey", "ByteDance_Check_Gift", "isBLMicro", "Platform_CheckScene", "Platform_GetScene", "isBreak", "__decorate", "displayName", "ProgressBar", "Label", "Node", "Sprite", "SpriteFrame", "Component", "exports"], "mappings": ";;;;;;AAAA,IAAIA,CAAC;AACL,IAAIC,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;AACjC,IAAIC,IAAI,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC3B,IAAIE,IAAI,GAAGF,OAAO,CAAC,OAAO,CAAC;AAC3B,IAAIG,SAAS,GAAGH,OAAO,CAAC,YAAY,CAAC;AACrC,IAAII,SAAS,GAAGJ,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIK,QAAQ,GAAGL,OAAO,CAAC,WAAW,CAAC;AACnC,IAAIM,KAAK,GAAGN,OAAO,CAAC,QAAQ,CAAC;AAC7B,IAAIO,UAAU,GAAGP,OAAO,CAAC,aAAa,CAAC;AACvC,IAAIQ,aAAa,GAAGR,OAAO,CAAC,gBAAgB,CAAC;AAC7C,IAAIS,gBAAgB,GAAGT,OAAO,CAAC,mBAAmB,CAAC;AACnD,IAAIU,KAAK,GAAGV,OAAO,CAAC,QAAQ,CAAC;AAC7B,IAAIW,UAAU,GAAGX,OAAO,CAAC,aAAa,CAAC;AACvC,IAAIY,UAAU,GAAGZ,OAAO,CAAC,aAAa,CAAC;AACvC,IAAIa,SAAS,GAAGb,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIc,eAAe,GAAGd,OAAO,CAAC,kBAAkB,CAAC;AACjD,IAAIe,YAAY,GAAGf,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAIgB,WAAW,GAAGhB,OAAO,CAAC,cAAc,CAAC;AACzC,IAAIiB,CAAC,GAAGC,EAAE,CAACC,UAAU;AACrB,IAAIC,CAAC,GAAGH,CAAC,CAACI,OAAO;AACjB,IAAIC,CAAC,GAAGL,CAAC,CAACM,QAAQ;AAClB,IAAIC,CAAC,GAAI,UAAUC,CAAC,EAAE;EAClB,SAASC,CAACA,CAAA,EAAG;IACT,IAAIA,CAAC;IACL,IAAIC,CAAC,GAAI,IAAI,KAAKF,CAAC,IAAIA,CAAC,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAK,IAAI;IACxDF,CAAC,CAACG,QAAQ,GAAG,CAAC,CAAC;IACfH,CAAC,CAACI,cAAc,GAAGpB,UAAU,CAACqB,SAAS,CAACC,OAAO;IAC/CN,CAAC,CAACO,UAAU,GAAG,EAAE;IACjBP,CAAC,CAACQ,QAAQ,GAAG,IAAI;IACjBR,CAAC,CAACS,YAAY,GAAG,IAAI;IACrBT,CAAC,CAACU,UAAU,GAAG,IAAI;IACnBV,CAAC,CAACW,QAAQ,GAAG,IAAI;IACjBX,CAAC,CAACY,KAAK,GAAG,EAAE;IACZZ,CAAC,CAACa,OAAO,GAAG,IAAI;IAChBb,CAAC,CAACc,aAAa,GAAG,IAAI;IACtBd,CAAC,CAACe,WAAW,GAAG,IAAI;IACpBf,CAAC,CAACgB,OAAO,IAAK,CAACjB,CAAC,GAAG,CAAC,CAAC,EAAEkB,EAAE,GAAG,CAAC,EAAIlB,CAAC,CAACmB,EAAE,GAAG,CAAC,EAAInB,CAAC,CAACoB,EAAE,GAAG,CAAC,EAAIpB,CAAC,CAACqB,EAAE,GAAG,CAAC,EAAIrB,CAAC,CAACsB,EAAE,GAAG,CAAC,EAAGtB,CAAC,CAAC;IAClFC,CAAC,CAACsB,WAAW,GAAG,CAAC;IACjB,OAAOtB,CAAC;EACZ;EACAuB,SAAS,CAACxB,CAAC,EAAED,CAAC,CAAC;EACfC,CAAC,CAACyB,SAAS,CAACC,MAAM,GAAG,YAAY;IAC7B,IAAI3B,CAAC,GAAG,IAAI;IACZ4B,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjCpC,EAAE,CAACqC,YAAY,GAAGlD,QAAQ,CAACmD,OAAO;IAClCtC,EAAE,CAACuC,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACC,IAAI,CAAC;IACrCzC,EAAE,CAAC0C,KAAK,CAACC,kBAAkB,GAAG,CAAC,CAAC;IAChC,IAAI3C,EAAE,CAAC4C,GAAG,CAACC,SAAS,EAAE;MAClB7C,EAAE,CAAC8C,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC,CAAC;MAChC,IAAI,CAACC,YAAY,CAAC,YAAY;QAC1BzC,CAAC,CAAC0C,GAAG,EAAE;MACX,CAAC,CAAC;IACN;IACAjD,EAAE,CAAC4C,GAAG,CAACM,OAAO,GAAG,CAAC,CAAC;IACnBjE,SAAS,CAACkE,QAAQ,CAACC,IAAI,CAAClE,SAAS,CAACmE,QAAQ,CAACC,eAAe,EAAE,MAAM,EAAE;MAChEC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;IACX,CAAC,CAAC;IACFrE,QAAQ,CAACmD,OAAO,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/BtE,QAAQ,CAACmD,OAAO,CAACmB,UAAU,CAAC,CAAC,CAAC,CAAC;EACnC,CAAC;EACDjD,CAAC,CAACyB,SAAS,CAACgB,GAAG,GAAG,YAAY;IAC1B,IAAI1C,CAAC,GAAGP,EAAE,CAAC8C,IAAI,CAACY,cAAc,EAAE;IAChC,IAAIlD,CAAC,GAAGD,CAAC,CAACoD,KAAK,GAAGpD,CAAC,CAACqD,MAAM;IAC1B,IAAInD,CAAC,GAAGoD,IAAI,CAACC,KAAK,CAAC,GAAG,GAAGtD,CAAC,CAAC;IAC3B,IAAIC,CAAC,GAAG,EAAE,EAAE;MACR,IAAIA,CAAC,IAAI,GAAG,EAAE;QACTT,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACC,SAAS,GAAG,CAAC,CAAC,EAAIjE,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACE,QAAQ,GAAG,CAAC,CAAE;MAC3E,CAAC,MAAM;QACFlE,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACC,SAAS,GAAG,CAAC,CAAC,EAAIjE,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACE,QAAQ,GAAG,CAAC,CAAE;MAC3E;IACJ;IACAlE,EAAE,CAACmE,KAAK,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACD5D,CAAC,CAACyB,SAAS,CAACoC,QAAQ,GAAG,YAAY;IAC/B,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EACD9D,CAAC,CAACyB,SAAS,CAACsC,SAAS,GAAG,YAAY;IAChC,IAAI,CAACD,cAAc,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EACD9D,CAAC,CAACyB,SAAS,CAACqC,cAAc,GAAG,UAAU/D,CAAC,EAAE;IACtCtB,SAAS,CAACkE,QAAQ,CAACmB,cAAc,CAC7B/D,CAAC,EACDrB,SAAS,CAACmE,QAAQ,CAACmB,YAAY,EAC/B,IAAI,CAACC,cAAc,EACnB,IAAI,EACJxF,SAAS,CAACyF,WAAW,CACxB;IACDzF,SAAS,CAACkE,QAAQ,CAACmB,cAAc,CAAC/D,CAAC,EAAErB,SAAS,CAACmE,QAAQ,CAACsB,SAAS,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC;IAC/F3F,SAAS,CAACkE,QAAQ,CAACmB,cAAc,CAAC/D,CAAC,EAAErB,SAAS,CAACmE,QAAQ,CAACwB,gBAAgB,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,EAAE,GAAG,CAAC;EACzG,CAAC;EACDtE,CAAC,CAACyB,SAAS,CAAC8C,UAAU,GAAG,YAAY,CAAC,CAAC;EACvCvE,CAAC,CAACyB,SAAS,CAACwC,cAAc,GAAG,YAAY;IACrC,IAAI,CAACtD,UAAU,CAAC6D,MAAM,CAACC,OAAO,EAAE;IAChC,IAAI,CAACC,SAAS,EAAE;EACpB,CAAC;EACD1E,CAAC,CAACyB,SAAS,CAAC2C,UAAU,GAAG,YAAY;IACjC,IAAI,CAACtD,OAAO,CAAC6D,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;EACD3E,CAAC,CAACyB,SAAS,CAAC6C,UAAU,GAAG,YAAY;IACjC,IAAI,CAACxD,OAAO,CAAC6D,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;EACD3E,CAAC,CAACyB,SAAS,CAACmD,UAAU,GAAG,YAAY;IACjC,IAAI7E,CAAC,GAAGP,EAAE,CAAC8C,IAAI,CAACuC,uBAAuB,EAAE;IACzC,IAAI7E,CAAC,GAAGR,EAAE,CAAC8C,IAAI,CAACwC,YAAY,EAAE;IAC9B,IAAI7E,CAAC,GAAG,SAAJA,CAACA,CAAA,EAAe;MAChBT,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACC,SAAS,GAAG,CAAC,CAAC;MACjCjE,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACE,QAAQ,GAAG,CAAC,CAAC;IACpC,CAAC;IACD,IAAItF,CAAC,GAAG2B,CAAC,CAACoD,KAAK,GAAGpD,CAAC,CAACqD,MAAM;IAC1B,IAAI2B,CAAC,GAAG/E,CAAC,CAACmD,KAAK,GAAGnD,CAAC,CAACoD,MAAM;IAC1B,IAAIhF,CAAC,GAAG,CAAC,EAAE;MACP,IAAI2G,CAAC,GAAG,CAAC,EAAE;QACP,IAAIA,CAAC,GAAG3G,CAAC,EAAE;UACP6B,CAAC,EAAE;QACP,CAAC,MAAM;UACFT,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACC,SAAS,GAAG,CAAC,CAAC,EAAIjE,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACE,QAAQ,GAAG,CAAC,CAAE;QAC3E;MACJ,CAAC,MAAM;QACHzD,CAAC,EAAE;MACP;IACJ,CAAC,MAAM;MACH,IAAI8E,CAAC,GAAG,CAAC,EAAE;QACP,IAAIA,CAAC,GAAG3G,CAAC,EAAE;UACP6B,CAAC,EAAE;QACP,CAAC,MAAM;UACFT,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACC,SAAS,GAAG,CAAC,CAAC,EAAIjE,EAAE,CAAC+D,MAAM,CAACC,QAAQ,CAACE,QAAQ,GAAG,CAAC,CAAE;QAC3E;MACJ,CAAC,MAAM;QACHzD,CAAC,EAAE;MACP;IACJ;EACJ,CAAC;EACDD,CAAC,CAACyB,SAAS,CAACuD,KAAK,GAAG,YAAY;IAC5B,IAAIjF,CAAC;IACL,OAAOkF,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,OAAOC,WAAW,CAAC,IAAI,EAAE,UAAUlF,CAAC,EAAE;QAClC,QAAQA,CAAC,CAACmF,KAAK;UACX,KAAK,CAAC;YACF,IAAI,CAACC,mBAAmB,EAAE;YAC1B,IAAIC,SAAS,CAACC,QAAQ,EAAE;cACpB;YAAA,CACH,MAAM;cACH,IAAIvG,gBAAgB,CAACwG,eAAe,EAAE;YAC1C;YACA5D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyD,SAAS,CAACG,YAAY,CAAC;YAC/D7D,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEyD,SAAS,CAACI,WAAW,CAAC;YAC7D,IAAI,CAACC,iBAAiB,EAAE;YACxB,OAAO,CAAC,CAAC,EAAE,IAAI,CAACC,UAAU,EAAE,CAAC;UACjC,KAAK,CAAC;YACF3F,CAAC,CAAC4F,IAAI,EAAE;YACRC,MAAM,CAACC,IAAI,GAAGvH,IAAI,CAACwH,GAAG,CAACC,QAAQ,CAACC,MAAM,EAAE;YACxCJ,MAAM,CAACK,QAAQ,CAAC,MAAM,CAAC;YACvB,IAAI,CAACC,gBAAgB,EAAE;YACvB,IAAI,CAACvF,QAAQ,CAACwF,WAAW,GACrB,IAAI,CAACvF,KAAK,CAAC,IAAI,MAAMd,CAAC,GAAG,IAAI,CAACkB,OAAO,CAACzB,EAAE,CAAC4C,GAAG,CAAC4D,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC,KAAKjG,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;YACpF,IAAIsF,SAAS,CAACgB,KAAK,EAAE;cACjB,IAAI,CAAC1F,UAAU,CAACgE,SAAS,CAAC,CAAC,CAAC,CAAC;YACjC;YACA,IAAIrF,WAAW,CAACgH,UAAU,EAAE;YAC5B,IAAI,CAAC5F,YAAY,CAAC6F,MAAM,GAAG/G,EAAE,CAACgH,EAAE,CAACC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC;YACpD,IAAI,CAAChG,QAAQ,CAACA,QAAQ,GAAG,CAAC;YAC1B,IAAIrB,eAAe,CAACsH,cAAc,EAAE;YACpC,IAAIrH,YAAY,CAACsH,WAAW,CAAC,IAAI,CAACjG,YAAY,EAAE,IAAI,CAACD,QAAQ,CAAC;YAC9D,OAAO,CAAC,CAAC,CAAC;QAAC;MAEvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACDT,CAAC,CAACyB,SAAS,CAACiE,iBAAiB,GAAG,YAAY;IACxC,IAAIL,SAAS,CAACC,QAAQ,EAAE;MACpB,IAAI,CAAC3E,UAAU,CAACiG,MAAM,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAAC7F,aAAa,CAACwF,MAAM,GAAGtH,UAAU,CAAC4H,eAAe,CAAC,IAAI,CAACxG,cAAc,CAAC;IAC3E,IAAI,CAACW,WAAW,CAACuF,MAAM,GAAGtH,UAAU,CAAC6H,OAAO,CAAC,IAAI,CAACzG,cAAc,CAAC;EACrE,CAAC;EACDL,CAAC,CAACyB,SAAS,CAACsF,MAAM,GAAG,UAAUhH,CAAC,EAAE;IAC9BnB,KAAK,CAACoI,IAAI,CAACD,MAAM,CAAChH,CAAC,CAAC;IACpBlB,UAAU,CAACoI,SAAS,CAACF,MAAM,CAAChH,CAAC,CAAC;EAClC,CAAC;EACDC,CAAC,CAACyB,SAAS,CAAC2D,mBAAmB,GAAG,YAAY;IAC1ClG,UAAU,CAACgI,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC9G,cAAc,EAAE,IAAI,CAACD,QAAQ,CAAC;IAC7D,IAAI,IAAI,CAACI,UAAU,CAAC4G,MAAM,GAAG,CAAC,EAAE;MAC5BnI,UAAU,CAACoI,WAAW,CAAC,IAAI,CAAChH,cAAc,CAAC,CAACoF,WAAW,GAAG,IAAI,CAACjF,UAAU;MACzEmB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC5B;EACJ,CAAC;EACD5B,CAAC,CAACyB,SAAS,CAACkE,UAAU,GAAG,YAAY;IACjC,IAAI5F,CAAC,GAAG,EAAE;IACV,IAAIC,CAAC,GAAGqF,SAAS,CAACC,QAAQ;IAC1B,KAAK,IAAIrF,CAAC,IAAI1B,IAAI,CAACwH,GAAG,CAACuB,OAAO,EAC1B,IAAItH,CAAC,IAAI,UAAU,IAAIC,CAAC,EAAE;MACtBF,CAAC,CAACwH,IAAI,CAAChJ,IAAI,CAACwH,GAAG,CAACyB,gBAAgB,CAACvH,CAAC,EAAE,IAAI,CAACS,YAAY,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAC;MACtE;IACJ;;IACJ,OAAOgH,OAAO,CAACC,GAAG,CAAC3H,CAAC,CAAC;EACzB,CAAC;EACDC,CAAC,CAACyB,SAAS,CAAC0E,gBAAgB,GAAG,YAAY;IACvC,IAAIpG,CAAC,GAAG,IAAI,CAAC4H,YAAY,EAAE;IAC3BxI,SAAS,CAACyI,MAAM,CAACC,QAAQ,CAACV,IAAI,CAACpH,CAAC,EAAE,YAAY;MAC1C4B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE7B,CAAC,CAAC;IACjD,CAAC,CAAC;EACN,CAAC;EACDC,CAAC,CAACyB,SAAS,CAACkG,YAAY,GAAG,YAAY;IACnC,IAAI5H,CAAC,GAAGP,EAAE,CAAC4C,GAAG,CAAC0F,gBAAgB;IAC/B,IAAI;MACA,IAAI9H,CAAC,GAAGR,EAAE,CAAC4C,GAAG,CAAC4D,QAAQ;MACvB,IAAI/F,CAAC,GAAGT,EAAE,CAAC4C,GAAG,CAAC2F,YAAY;MAC3BvI,EAAE,CAACoC,GAAG,CAAC,SAAS,EAAE5B,CAAC,EAAEC,CAAC,CAAC;MACvB,IAAI,IAAI,KAAKD,CAAC,EAAE;QACZ,IACI,CAAC,CAAC,IAAIC,CAAC,CAAC+H,OAAO,CAAC,MAAM,CAAC,IACvB,CAAC,CAAC,IAAI/H,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,IACrB,CAAC,CAAC,IAAI/H,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,IACrB,CAAC,CAAC,IAAI/H,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,EACvB;UACGjI,CAAC,GAAG,IAAI,EAAIP,EAAE,CAAC4C,GAAG,CAACM,OAAO,GAAG,CAAC,CAAE;QACrC,CAAC,MAAM;UACH3C,CAAC,GAAG,IAAI;QACZ;MACJ,CAAC,MAAM;QACH,IAAI,IAAI,IAAIC,CAAC,EAAE;UACVD,CAAC,GAAG,IAAI,EAAIP,EAAE,CAAC4C,GAAG,CAACM,OAAO,GAAG,CAAC,CAAE;QACrC,CAAC,MAAM;UACH,IAAI,IAAI,IAAI1C,CAAC,EAAE;YACVD,CAAC,GAAG,IAAI,EAAIP,EAAE,CAAC4C,GAAG,CAACM,OAAO,GAAG,CAAC,CAAE;UACrC,CAAC,MAAM;YACH,IAAI,CAAC,CAAC,IAAIzC,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI/H,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,EAAE;cAC/CjI,CAAC,GAAG,IAAI,EAAIP,EAAE,CAAC4C,GAAG,CAACM,OAAO,GAAG,CAAC,CAAE;YACrC,CAAC,MAAM;cACH3C,CAAC,GACG,CAAC,CAAC,IAAIE,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAIhI,CAAC,CAACgI,OAAO,CAAC,IAAI,CAAC,GACxC,IAAI,GACJ,CAAC,CAAC,IAAI/H,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI/H,CAAC,CAAC+H,OAAO,CAAC,IAAI,CAAC,GAC9C,KAAK,GACL,IAAI;YAClB;UACJ;QACJ;MACJ;MACArG,OAAO,CAACC,GAAG,CACP,8BAA8B,GAAG7B,CAAC,GAAG,YAAY,GAAGC,CAAC,GAAG,YAAY,GAAGR,EAAE,CAAC4C,GAAG,CAAC2F,YAAY,CAC7F;MACD,OAAOhI,CAAC;IACZ,CAAC,CAAC,OAAOA,CAAC,EAAE,CAAC;EACjB,CAAC;EACDC,CAAC,CAACyB,SAAS,CAACiD,SAAS,GAAG,YAAY;IAChC,IAAI3E,CAAC;IACL4B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACnB,QAAQ,CAACA,QAAQ,CAAC;IAC5D9B,QAAQ,CAACmD,OAAO,CAACmG,cAAc,GAAGC,IAAI,CAACC,SAAS,CAAC3I,EAAE,CAACuC,IAAI,CAACqG,eAAe,CAAC;IACzEC,MAAM,CAACC,cAAc,CAAC9I,EAAE,CAACuC,IAAI,EAAE,iBAAiB,EAAEmG,IAAI,CAACK,KAAK,CAAC5J,QAAQ,CAACmD,OAAO,CAACmG,cAAc,CAAC,CAAC;IAC9F,IAAI,CAAC,IAAI,CAAC7H,QAAQ,IAAIiF,SAAS,CAACmD,MAAM,EAAE;MACpC,IAAI7J,QAAQ,CAACmD,OAAO,CAAC2G,EAAE,CAACC,MAAM,CAACC,IAAI,EAAE;QACjCtD,SAAS,CACJuD,gBAAgB,CAACjK,QAAQ,CAACmD,OAAO,CAAC2G,EAAE,CAACC,MAAM,CAACC,IAAI,CAAC,CACjDE,IAAI,CAAC,YAAY;UACd/J,aAAa,CAACgK,YAAY,CAACC,cAAc,CAAC,MAAM,CAAC;QACrD,CAAC,CAAC,SACI,CAAC,YAAY;UACf;UACA;UACAjK,aAAa,CAACgK,YAAY,CAACC,cAAc,CAAC,OAAO,CAAC;UAClDlK,UAAU,CAACoI,SAAS,CAAC+B,IAAI,CAAC,uBAAuB,CAAC;UAClDrK,QAAQ,CAACmD,OAAO,CAAC2G,EAAE,CAACQ,YAAY,EAAE;QACtC,CAAC,CAAC;MACV,CAAC,MAAM;QACHpK,UAAU,CAACoI,SAAS,CAAC+B,IAAI,CAAC,uBAAuB,CAAC;MACtD;IACJ;IACA,IAAIhJ,CAAC,GAAG,IAAI;IACZ,IAAI,IAAI,MAAMD,CAAC,GAAGtB,SAAS,CAACkE,QAAQ,CAACuG,IAAI,CAAC7K,OAAO,CAAC8K,MAAM,CAACC,cAAc,CAAC,CAAC,IAAI,KAAK,CAAC,KAAKrJ,CAAC,EAAE;MACvFC,CAAC,GAAG,KAAK,CAAC;IACd,CAAC,MAAM;MACHA,CAAC,GAAGD,CAAC,CAACsJ,IAAI;IACd;IACA,IAAIrJ,CAAC,EAAE;MACHvB,SAAS,CAACkE,QAAQ,CAACC,IAAI,CAAClE,SAAS,CAACmE,QAAQ,CAACyG,qBAAqB,CAAC;MACjE,IAAIrJ,CAAC,GAAG1B,IAAI,CAACwH,GAAG,CAACwD,UAAU,CAACC,GAAG,CAACxJ,CAAC,CAAC;MAClC,IAAI5B,CAAC,GAAGY,KAAK,CAACyK,IAAI,CAACC,QAAQ,CAACzJ,CAAC,CAAC0J,IAAI,CAAC;MACnClL,SAAS,CAACkE,QAAQ,CAACC,IAAI,CACnBxE,CAAC,CAACwL,KAAK,EACP3J,CAAC,CAAC0J,IAAI,EACNnL,IAAI,CAACqL,GAAG,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC;QACzBC,EAAE,EAAE/J,CAAC,CAAC+J,EAAE;QACRC,SAAS,EAAE,CAAC;MAChB,CAAC,CAAC,CACL;IACL,CAAC,MAAM;MACHxL,SAAS,CAACkE,QAAQ,CAACC,IAAI,CAAClE,SAAS,CAACmE,QAAQ,CAACqH,kBAAkB,EAAE,CAAC,CAAC;IACrE;IACA,IAAI7E,SAAS,CAAC8E,WAAW,EAAE;MACvB,IAAIpF,CAAC,GAAGtG,SAAS,CAACkE,QAAQ,CAACuG,IAAI,CAAC7K,OAAO,CAAC8K,MAAM,CAACiB,cAAc,CAAC;MAC9D3L,SAAS,CAACkE,QAAQ,CAACC,IAAI,CAAClE,SAAS,CAACmE,QAAQ,CAACwH,oBAAoB,EAAEtF,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC,MAAM;MACH,IAAIM,SAAS,CAACiF,SAAS,EAAE;QACrB7L,SAAS,CAACkE,QAAQ,CAACC,IAAI,CACnBlE,SAAS,CAACmE,QAAQ,CAAC0H,mBAAmB,EACtC9L,SAAS,CAACkE,QAAQ,CAACuG,IAAI,CAAC7K,OAAO,CAAC8K,MAAM,CAACqB,iBAAiB,CAAC,CAC5D;MACL;IACJ;IACA/L,SAAS,CAACkE,QAAQ,CAACC,IAAI,CAAClE,SAAS,CAACmE,QAAQ,CAACC,eAAe,EAAE,MAAM,EAAE;MAChEC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE;IACX,CAAC,CAAC;EACN,CAAC;EACDhD,CAAC,CAACyK,OAAO,GAAG,CAAC,CAAC;EACdC,UAAU,CACN,CACI9K,CAAC,CAAC;IACE+K,WAAW,EAAE;EACjB,CAAC,CAAC,CACL,EACD3K,CAAC,CAACyB,SAAS,EACX,UAAU,EACV,KAAK,CAAC,CACT;EACDiJ,UAAU,CACN,CACI9K,CAAC,CAAC;IACE+J,IAAI,EAAE1K,UAAU,CAACqB,SAAS;IAC1BqK,WAAW,EAAE;EACjB,CAAC,CAAC,CACL,EACD3K,CAAC,CAACyB,SAAS,EACX,gBAAgB,EAChB,KAAK,CAAC,CACT;EACDiJ,UAAU,CACN,CACI9K,CAAC,CAAC;IACE+K,WAAW,EAAE;EACjB,CAAC,CAAC,CACL,EACD3K,CAAC,CAACyB,SAAS,EACX,YAAY,EACZ,KAAK,CAAC,CACT;EACDiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACoL,WAAW,CAAC,CAAC,EAAE5K,CAAC,CAACyB,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;EAChEiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACqL,KAAK,CAAC,CAAC,EAAE7K,CAAC,CAACyB,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;EAC9DiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACsL,IAAI,CAAC,CAAC,EAAE9K,CAAC,CAACyB,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;EAC3DiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACuL,MAAM,CAAC,CAAC,EAAE/K,CAAC,CAACyB,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;EAC3DiJ,UAAU,CAAC,CAAC9K,CAAC,CAAC,CAACJ,EAAE,CAACwL,WAAW,CAAC,CAAC,CAAC,EAAEhL,CAAC,CAACyB,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC/DiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACsL,IAAI,CAAC,CAAC,EAAE9K,CAAC,CAACyB,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EACxDiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACqL,KAAK,CAAC,CAAC,EAAE7K,CAAC,CAACyB,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;EAC/DiJ,UAAU,CAAC,CAAC9K,CAAC,CAACJ,EAAE,CAACqL,KAAK,CAAC,CAAC,EAAE7K,CAAC,CAACyB,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;EAC7D,OAAOiJ,UAAU,CAAC,CAAChL,CAAC,CAAC,EAAEM,CAAC,CAAC;AAC7B,CAAC,CAAER,EAAE,CAACyL,SAAS,CAAC;AAChBC,OAAO,WAAQ,GAAGpL,CAAC", "sourceRoot": "/", "sourcesContent": ["var i;\nvar $callID = require(\"./CallID\");\nvar $cfg = require(\"./Cfg\");\nvar $mVC = require(\"./MVC\");\nvar $notifier = require(\"./Notifier\");\nvar $listenID = require(\"./ListenID\");\nvar $manager = require(\"./Manager\");\nvar $time = require(\"./Time\");\nvar $uIManager = require(\"./UIManager\");\nvar $alertManager = require(\"./AlertManager\");\nvar $eventController = require(\"./EventController\");\nvar $game = require(\"./Game\");\nvar $sdkConfig = require(\"./SdkConfig\");\nvar $wonderSdk = require(\"./WonderSdk\");\nvar $gameUtil = require(\"./GameUtil\");\nvar $moduleLauncher = require(\"./ModuleLauncher\");\nvar $sdkLauncher = require(\"./SdkLauncher\");\nvar $uILauncher = require(\"./UILauncher\");\nvar k = cc._decorator;\nvar O = k.ccclass;\nvar I = k.property;\nvar A = (function (e) {\n    function t() {\n        var t;\n        var o = (null !== e && e.apply(this, arguments)) || this;\n        o.testMode = !1;\n        o.CustomPlatform = $sdkConfig.EPlatform.WEB_DEV;\n        o.bmsVersion = \"\";\n        o.progress = null;\n        o.progressText = null;\n        o.wonderlogo = null;\n        o.gamelogo = null;\n        o.logos = [];\n        o.scenebg = null;\n        o.softRightText = null;\n        o.softICPText = null;\n        o.logomap = (((t = {}).tc = 0), (t.en = 1), (t.th = 2), (t.vn = 3), (t.cn = 4), t);\n        o._saveOffset = 0;\n        return o;\n    }\n    __extends(t, e);\n    t.prototype.onLoad = function () {\n        var e = this;\n        console.log(\"[Launcher][onLoad]\");\n        cc._gameManager = $manager.Manager;\n        cc.game.addPersistRootNode(this.node);\n        cc.macro.ENABLE_MULTI_TOUCH = !1;\n        if (cc.sys.isBrowser) {\n            cc.view.enableAutoFullScreen(!1);\n            this.scheduleOnce(function () {\n                e.fit();\n            });\n        }\n        cc.sys.hasFont = !1;\n        $notifier.Notifier.send($listenID.ListenID.Event_SendEvent, \"View\", {\n            Type: \"show\",\n            Scene: \"loading\"\n        });\n        $manager.Manager.setPhysics(!0);\n        $manager.Manager.setPhysics(!1);\n    };\n    t.prototype.fit = function () {\n        var e = cc.view.getVisibleSize();\n        var t = e.width / e.height;\n        var o = Math.round(100 * t);\n        if (o > 57) {\n            if (o >= 100) {\n                (cc.Canvas.instance.fitHeight = !0), (cc.Canvas.instance.fitWidth = !0);\n            } else {\n                (cc.Canvas.instance.fitHeight = !0), (cc.Canvas.instance.fitWidth = !1);\n            }\n        }\n        cc.debug.setDisplayStats(!1);\n    };\n    t.prototype.onEnable = function () {\n        this.changeListener(!0);\n    };\n    t.prototype.onDisable = function () {\n        this.changeListener(!1);\n    };\n    t.prototype.changeListener = function (e) {\n        $notifier.Notifier.changeListener(\n            e,\n            $listenID.ListenID.Login_Finish,\n            this.onLogin_Finish,\n            this,\n            $notifier.PriorLowest\n        );\n        $notifier.Notifier.changeListener(e, $listenID.ListenID.Game_Load, this.onOpenGame, this, -200);\n        $notifier.Notifier.changeListener(e, $listenID.ListenID.Fight_BackToMain, this.backToMain, this, 200);\n    };\n    t.prototype.lateUpdate = function () {};\n    t.prototype.onLogin_Finish = function () {\n        this.wonderlogo.parent.destroy();\n        this.gameStart();\n    };\n    t.prototype.onOpenGame = function () {\n        this.scenebg.setActive(!1);\n    };\n    t.prototype.backToMain = function () {\n        this.scenebg.setActive(!0);\n    };\n    t.prototype.responsive = function () {\n        var e = cc.view.getDesignResolutionSize();\n        var t = cc.view.getFrameSize();\n        var o = function () {\n            cc.Canvas.instance.fitHeight = !0;\n            cc.Canvas.instance.fitWidth = !0;\n        };\n        var i = e.width / e.height;\n        var n = t.width / t.height;\n        if (i < 1) {\n            if (n < 1) {\n                if (n > i) {\n                    o();\n                } else {\n                    (cc.Canvas.instance.fitHeight = !1), (cc.Canvas.instance.fitWidth = !0);\n                }\n            } else {\n                o();\n            }\n        } else {\n            if (n > 1) {\n                if (n < i) {\n                    o();\n                } else {\n                    (cc.Canvas.instance.fitHeight = !0), (cc.Canvas.instance.fitWidth = !1);\n                }\n            } else {\n                o();\n            }\n        }\n    };\n    t.prototype.start = function () {\n        var e;\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (t) {\n                switch (t.label) {\n                    case 0:\n                        this.initWonderFrameWork();\n                        if (wonderSdk.isNative) {\n                            //\n                        } else {\n                            new $eventController.EventController();\n                        }\n                        console.log(\"[Launcher][BMS_APP_NAME]\", wonderSdk.BMS_APP_NAME);\n                        console.log(\"[Launcher][BMS_VERSION]\", wonderSdk.BMS_VERSION);\n                        this.checkPlatformInfo();\n                        return [4, this.loadConfig()];\n                    case 1:\n                        t.sent();\n                        window.tpdg = $cfg.Cfg.language.getAll();\n                        window.initlang(\"tpdg\");\n                        this.initLanguageInfo();\n                        this.gamelogo.spriteFrame =\n                            this.logos[null !== (e = this.logomap[cc.sys.language]) && void 0 !== e ? e : 4];\n                        if (wonderSdk.isIOS) {\n                            this.wonderlogo.setActive(!1);\n                        }\n                        new $uILauncher.UILauncher();\n                        this.progressText.string = cc.js.formatStr(\"%d%\", 0);\n                        this.progress.progress = 0;\n                        new $moduleLauncher.ModuleLauncher();\n                        new $sdkLauncher.SdkLauncher(this.progressText, this.progress);\n                        return [2];\n                }\n            });\n        });\n    };\n    t.prototype.checkPlatformInfo = function () {\n        if (wonderSdk.isNative) {\n            this.wonderlogo.active = !1;\n        }\n        this.softRightText.string = $sdkConfig.SoftRightHodler[this.CustomPlatform];\n        this.softICPText.string = $sdkConfig.SoftICP[this.CustomPlatform];\n    };\n    t.prototype.update = function (e) {\n        $time.Time.update(e);\n        $uIManager.UIManager.update(e);\n    };\n    t.prototype.initWonderFrameWork = function () {\n        $wonderSdk.WonderSdk.init(this.CustomPlatform, this.testMode);\n        if (this.bmsVersion.length > 0) {\n            $sdkConfig.BMSInfoList[this.CustomPlatform].BMS_VERSION = this.bmsVersion;\n            console.log(\"已修改Bms版本号\");\n        }\n    };\n    t.prototype.loadConfig = function () {\n        var e = [];\n        var t = wonderSdk.isNative;\n        for (var o in $cfg.Cfg.keyJson)\n            if (t || \"language\" != o) {\n                e.push($cfg.Cfg.initByBaseConfig(o, this.progressText, this.progress));\n                // e.push($cfg.Cfg.initLocalJson(o, this.progressText, this.progress));\n            }\n        return Promise.all(e);\n    };\n    t.prototype.initLanguageInfo = function () {\n        var e = this.initLangCode();\n        $gameUtil.CCTool.Language.init(e, function () {\n            console.log(\"[languageFun][init]语言包初始化完成\", e);\n        });\n    };\n    t.prototype.initLangCode = function () {\n        var e = cc.sys.LANGUAGE_ENGLISH;\n        try {\n            var t = cc.sys.language;\n            var o = cc.sys.languageCode;\n            cc.log(\"[lType]\", t, o);\n            if (\"zh\" === t) {\n                if (\n                    -1 != o.indexOf(\"hant\") ||\n                    -1 != o.indexOf(\"tw\") ||\n                    -1 != o.indexOf(\"hk\") ||\n                    -1 != o.indexOf(\"mo\")\n                ) {\n                    (e = \"tc\"), (cc.sys.hasFont = !1);\n                } else {\n                    e = \"zh\";\n                }\n            } else {\n                if (\"ja\" == t) {\n                    (e = \"jp\"), (cc.sys.hasFont = !1);\n                } else {\n                    if (\"ko\" == t) {\n                        (e = \"kr\"), (cc.sys.hasFont = !1);\n                    } else {\n                        if (-1 != o.indexOf(\"vi\") || -1 != o.indexOf(\"vn\")) {\n                            (e = \"vn\"), (cc.sys.hasFont = !1);\n                        } else {\n                            e =\n                                -1 != o.indexOf(\"th\") || -1 != t.indexOf(\"th\")\n                                    ? \"en\"\n                                    : -1 != o.indexOf(\"id\") || -1 != o.indexOf(\"in\")\n                                    ? \"ina\"\n                                    : \"en\";\n                        }\n                    }\n                }\n            }\n            console.log(\n                \"[Language] --> 初始化语言:  lan: \" + e + \" systype: \" + t + \" syscode: \" + cc.sys.languageCode\n            );\n            return e;\n        } catch (e) {}\n    };\n    t.prototype.gameStart = function () {\n        var e;\n        console.log(\"[进入游戏 gameStart] 1.00\", this.progress.progress);\n        $manager.Manager.oldGroupMatrix = JSON.stringify(cc.game.collisionMatrix);\n        Object.defineProperty(cc.game, \"collisionMatrix\", JSON.parse($manager.Manager.oldGroupMatrix));\n        if (!this.testMode && wonderSdk.isLive) {\n            if ($manager.Manager.vo.userVo.code) {\n                wonderSdk\n                    .requestLoginCode($manager.Manager.vo.userVo.code)\n                    .then(function () {\n                        $alertManager.AlertManager.showNormalTips(\"验证成功\");\n                    })\n                    .catch(function () {\n                        // $manager.Manager.vo.userVo.code = null;\n                        // $manager.Manager.vo.userVo.ca_code = null;\n                        $alertManager.AlertManager.showNormalTips(\"验证码过期\");\n                        $uIManager.UIManager.Open(\"ui/setting/H5CodeView\");\n                        $manager.Manager.vo.saveUserData();\n                    });\n            } else {\n                $uIManager.UIManager.Open(\"ui/setting/H5CodeView\");\n            }\n        }\n        var t = null;\n        if (null === (e = $notifier.Notifier.call($callID.CallID.Platform_Query)) || void 0 === e) {\n            t = void 0;\n        } else {\n            t = e.mode;\n        }\n        if (t) {\n            $notifier.Notifier.send($listenID.ListenID.Is_Back_From_Try_Play);\n            var o = $cfg.Cfg.MiniGameLv.get(t);\n            var i = $game.Game.getMouth(o.type);\n            $notifier.Notifier.send(\n                i.mouth,\n                o.type,\n                $mVC.MVC.openArgs().setParam({\n                    id: o.id,\n                    isTryPaly: !0\n                })\n            );\n        } else {\n            $notifier.Notifier.send($listenID.ListenID.BottomBar_OpenView, 1);\n        }\n        if (wonderSdk.isByteDance) {\n            var n = $notifier.Notifier.call($callID.CallID.Platform_CdKey);\n            $notifier.Notifier.send($listenID.ListenID.ByteDance_Check_Gift, n, !0);\n        } else {\n            if (wonderSdk.isBLMicro) {\n                $notifier.Notifier.send(\n                    $listenID.ListenID.Platform_CheckScene,\n                    $notifier.Notifier.call($callID.CallID.Platform_GetScene)\n                );\n            }\n        }\n        $notifier.Notifier.send($listenID.ListenID.Event_SendEvent, \"View\", {\n            Type: \"hide\",\n            Scene: \"loading\"\n        });\n    };\n    t.isBreak = !1;\n    __decorate(\n        [\n            I({\n                displayName: \"测试模式\"\n            })\n        ],\n        t.prototype,\n        \"testMode\",\n        void 0\n    );\n    __decorate(\n        [\n            I({\n                type: $sdkConfig.EPlatform,\n                displayName: \"自定义平台\"\n            })\n        ],\n        t.prototype,\n        \"CustomPlatform\",\n        void 0\n    );\n    __decorate(\n        [\n            I({\n                displayName: \"BMS版本号\"\n            })\n        ],\n        t.prototype,\n        \"bmsVersion\",\n        void 0\n    );\n    __decorate([I(cc.ProgressBar)], t.prototype, \"progress\", void 0);\n    __decorate([I(cc.Label)], t.prototype, \"progressText\", void 0);\n    __decorate([I(cc.Node)], t.prototype, \"wonderlogo\", void 0);\n    __decorate([I(cc.Sprite)], t.prototype, \"gamelogo\", void 0);\n    __decorate([I([cc.SpriteFrame])], t.prototype, \"logos\", void 0);\n    __decorate([I(cc.Node)], t.prototype, \"scenebg\", void 0);\n    __decorate([I(cc.Label)], t.prototype, \"softRightText\", void 0);\n    __decorate([I(cc.Label)], t.prototype, \"softICPText\", void 0);\n    return __decorate([O], t);\n})(cc.Component);\nexports.default = A;\n"]}