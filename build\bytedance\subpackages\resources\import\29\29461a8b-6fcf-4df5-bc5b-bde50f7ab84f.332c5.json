[1, ["ecpdLyjvZBwrvm+cedCcQy", "bd4SPLOTxFVIpHuKsAOYEs", "cdxCCfRH5E1rySxNNVFJeD"], ["node", "_parent", "_spriteFrame", "root", "gameUiNode", "gameNode", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_prefab", "_components", "_contentSize", "_parent", "_children", "_anchorPoint", "_trs"], 0, 4, 9, 5, 1, 2, 5, 7], ["cc.Sprite", ["_enabled", "_type", "_sizeMode", "_fillType", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "alignMode", "_top", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_children", "_prefab"], 1, 1, 12, 4], ["13f645/b3FHe54Ow6QJV/H5", ["node", "gameNode", "gameUiNode"], 3, 1, 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 1, 6, 3, 3], [0, 0, 1, 6, 4, 3, 5, 3], [4, 0, 2], [0, 0, 1, 7, 4, 3, 5, 3], [0, 0, 1, 6, 7, 3, 3], [0, 0, 2, 7, 4, 3, 5, 8, 3], [0, 0, 6, 4, 3, 5, 9, 2], [5, 0, 1, 2, 3, 4, 3], [2, 0, 3, 2], [2, 1, 0, 2, 3, 4], [3, 1, 2, 1], [1, 0, 4, 5, 2], [1, 4, 5, 6, 1], [1, 1, 2, 3, 4, 5, 6, 4], [6, 0, 1, 2, 1]], [[3, "M38_FightScene"], [4, "M38_FightScene", 2, [-5, -6], [[15, -4, -3, -2]], [11, -1, 0], [5, 750, 1334]], [8, "gameNode", 2, 1, [[-7, -8, [1, "entityNode", 2, -9, [0, "b1KowK4rtNPbyN8VtU8Jnq", 1, 0]], [1, "bulletNode", 2, -10, [0, "7fEPR+GJ9AcbrNf7ESTs84", 1, 0]], [1, "topEffectNode", 2, -11, [0, "a7hg81Wx1A46HZhtgBAqDb", 1, 0]]], 1, 1, 4, 4, 4], [0, "f5owRzPXBMDYgAlftZpjYQ", 1, 0]], [2, "GameUI", 1, 1, [[9, 45, -12]], [0, "10vUquDsBOiIb12dhmKmZG", 1, 0], [5, 750, 1334]], [6, "line", false, [-14], [[14, 1, 0, 1, -13, [3], 4]], [0, "f28tcrWCRHSoBW/GY8pcFx", 1, 0], [5, 14, 451], [0, 0.5, 0]], [7, "img_foresight", 4, [[13, -15, [1], 2], [10, 2, 1, -50, -16]], [0, "f5wL38cJBNH6qIqmHp6m2n", 1, 0], [5, 104, 105], [0, 448.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "mapNode", 2, 2, [[12, false, -17, [0]]], [0, "40MfQatGdGMLor9LAdrWrc", 1, 0], [5, 1125, 2535]], [5, "botEffectNode", 2, 2, [4], [0, "11zhKkpoJCErwJQSwNxU4d", 1, 0]]], 0, [0, 3, 1, 0, 4, 3, 0, 5, 2, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -1, 6, 0, -2, 7, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 0, 3, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 6, 1, 4, 1, 7, 17], [0, 0, 0, 0, 0], [-1, -1, 2, -1, 2], [0, 0, 1, 0, 2]]