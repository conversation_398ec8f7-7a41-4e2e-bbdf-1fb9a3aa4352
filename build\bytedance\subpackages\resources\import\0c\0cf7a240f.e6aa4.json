[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "89YqH0KIZJ26WKtSNfrXcv", "5b0HySO0xNLI16cb6FYAR2", "d7pE9KwHNGM53EhlOj4PkX", "70F/PynC1O5KjZzbBQaXb6", "8ahCanR2NJrrCZ22RXarV8"], ["node", "_spriteFrame", "connectedBody", "root", "<PERSON><PERSON><PERSON><PERSON>", "data", "_textureSetter"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 0, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.RigidBody", ["_type", "node"], 2, 1], ["cc.RopeJoint", ["_maxLength", "node", "connectedBody"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "node", "_materials"], -2, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["bd345jqwLlNhbDxAyfuqffD", ["node", "<PERSON><PERSON><PERSON><PERSON>"], 3, 1, 1], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node"], 1, 1], ["cc.PhysicsBoxCollider", ["_friction", "node", "_size"], 2, 1, 5], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_offset", "_size"], 3, 1, 5, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[3, 0, 1, 2, 2], [1, 0, 3, 4, 5, 2], [10, 0, 1, 2, 3], [5, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 4], [4, 0, 1, 2], [4, 1, 1], [1, 1, 0, 3, 4, 5, 3], [0, 0, 1, 6, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 5, 7, 2], [11, 0, 1, 2, 2], [13, 0, 1, 2, 2], [8, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 2, 1, 6, 3, 4, 5, 7, 4], [0, 0, 6, 8, 3, 4, 5, 7, 2], [0, 0, 1, 6, 8, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 9, 5, 10, 7, 2], [2, 0, 1, 3, 4, 5, 6, 7, 3], [2, 0, 3, 4, 5, 6, 7, 2], [9, 0, 1, 1], [3, 1, 2, 1], [1, 3, 4, 5, 1], [1, 2, 0, 3, 4, 5, 3], [5, 0, 1, 2], [12, 0, 1, 2, 1], [6, 0, 4, 1, 2, 3, 5, 6, 6], [6, 0, 1, 2, 3, 5, 6, 5]], [[[[12, "M31_LockWall"], [13, "M31_LockWall", 8, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14], [[20, -3, -2]], [21, -1, 0], [5, 300, 250]], [14, "cord", 0, 8, 1, [[1, 0, -15, [5], 6], [5, 0, -16], [3, 40, -18, -17], [2, 0.5, 25, -19]], [0, "2fo8E/rOdFBalY8HylrnQb", 1, 0], [5, 40, 40], [-87.99, 53.885, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "cord", 0, 8, 1, [[[1, 0, -20, [7], 8], -21, [3, 40, -23, -22], [2, 0.5, 25, -24]], 4, 1, 4, 4], [0, "41rMrwSVVHXJE7PpcmV20F", 1, 0], [5, 40, 40], [-57.367, 40.474, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "cord", 0, 8, 1, [[[1, 0, -25, [9], 10], -26, [3, 40, -28, -27], [2, 0.5, 25, -29]], 4, 1, 4, 4], [0, "95DLVNoSBNL4dus61mBtFX", 1, 0], [5, 40, 40], [-25.283, 35.74, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "cord", 0, 8, 1, [[[1, 0, -30, [11], 12], -31, [3, 40, -33, -32], [2, 0.5, 25, -34]], 4, 1, 4, 4], [0, "f8zJf0GZBKA4DUfiFw1aWB", 1, 0], [5, 40, 40], [15.77, 38.107, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "cord", 0, 8, 1, [[[1, 0, -35, [13], 14], -36, [3, 40, -38, -37], [2, 0.5, 25, -39]], 4, 1, 4, 4], [0, "d4wPpAd9JBhpysRl1ifIoK", 1, 0], [5, 40, 40], [52.317, 46.785, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "cord", 0, 1, [[[1, 0, -40, [15], 16], -41, [24, 40, -42], [2, 0.5, 25, -43]], 4, 1, 4, 4], [0, "804aeC1N1AJq0Tk/TALR73", 1, 0], [5, 40, 40], [93.894, 61.512, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "cord", 1, [-45, -46], [[7, 1, 0, -44, [3], 4]], [0, "c7XuZeVARPCJlvxv5uHVEQ", 1, 0], [5, 225, 76], [10.698, 23.803, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 8, 1, [[7, 1, 0, -47, [17], 18], [5, 0, -48], [10, 0, -49, [5, 50, 250]]], [0, "ffunKf4uVCvYQjHtGf35Ie", 1, 0], [5, 50, 250], [126.189, 0.106, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 8, 1, [[7, 1, 0, -50, [19], 20], [5, 0, -51], [10, 0, -52, [5, 50, 250]]], [0, "c8dXgEBJdEgadnMezmsEGU", 1, 0], [5, 50, 250], [-126.469, 0.106, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "2", 5, 1, [-55], [[1, 0, -53, [24], 25], [25, -54, [0, 0, 20], [5, 200, 10]]], [0, "cf1myLNW5JP5YlHGAgnZoJ", 1, 0], [5, 200, 50], [0, -85.018, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lock_num", 8, [[-56, [11, 3, -57, [4, 4278190080]]], 1, 4], [0, "1fvA8l9xVGApRkyOCbqIFk", 1, 0], [5, 28.25, 56.4], [-10.11, -7.508, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "label_x2", 11, [[26, "x2", 50, false, 1, 1, -58, [23]], [11, 3, -59, [4, 4278190080]]], [0, "c2YXkvZs9B9poc/GCrxqq8", 1, 0], [5, 58.81, 56.4], [0, 5.091, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "icon_lock_small", 8, [[22, -60, [0], 1]], [0, "cdrlPqoN5DRaL4+itOIzLB", 1, 0], [5, 47, 56], [-53.416, -6.584, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [27, "0", false, 1, 1, 12, [2]], [6, 3], [6, 4], [6, 5], [6, 6], [5, 0, 7], [17, "checkNumBox", 1, [[23, false, 0, -61, [21], 22]], [0, "3e9jel4wtLNLHlnrmiJZo2", 1, 0], [4, 4278701567], [5, 200, 200], [0, 0, 0], [-101.824, -13.972, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 4, 15, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 3, 0, -4, 4, 0, -5, 5, 0, -6, 6, 0, -7, 7, 0, -8, 9, 0, -9, 10, 0, -10, 21, 0, -11, 11, 0, 0, 2, 0, 0, 2, 0, 2, 16, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, -2, 16, 0, 2, 17, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -2, 17, 0, 2, 18, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 18, 0, 2, 19, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -2, 19, 0, 2, 20, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -2, 20, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 14, 0, -2, 12, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 13, 0, -1, 15, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 21, 0, 5, 1, 61], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1], [0, 3, 0, 0, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 1, 0, 0, 5]], [[{"name": "obstacle01", "rect": [0, 0, 175, 76], "offset": [0, 0], "originalSize": [175, 76], "capInsets": [82, 0, 84, 0]}], [7], 0, [0], [6], [6]]]]