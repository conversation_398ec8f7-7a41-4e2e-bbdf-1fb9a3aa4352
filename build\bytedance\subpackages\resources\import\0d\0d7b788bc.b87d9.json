[1, ["ecpdLyjvZBwrvm+cedCcQy", "43FRasT/BD55xr9umqF0KT", "a2MjXRFdtLlYQ5ouAFv/+R", "81jsMPk5pFd5aU8/jPdXJo", "c5Qkv3/MpHdqtdmxjMGj2p", "8cBsfLm1ROW72+8zHAH7+3", "d13V49au5Bu4CErdyqDBV5", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "22cjIamqZH/Lbdvp80pFLv", "87mQtqAblBkKA6xla7Hxkd", "0dLNTjV5BKUa6GZmky8KXF", "10TtK20cFHWJx0n6nzNo9G", "d45TjwuKpAd7TStaJsOV7X", "c6bUPxAiREL4F7K5aT5uC2"], ["node", "_spriteFrame", "_textureSetter", "root", "target", "btnLb", "desc", "title", "_N$target", "data", "_parent", "_N$disabledSprite"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color"], -1, 9, 4, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_isSystemFontUsed", "_styleFlags", "_N$overflow", "_fontSize", "_lineHeight", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -7, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["f9e5695RjNPCLTnW0c5qbW4", ["node", "title", "desc", "btnLb"], 3, 1, 1, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[6, 0, 1, 2, 2], [0, 0, 7, 4, 5, 6, 9, 2], [1, 3, 4, 5, 1], [1, 1, 0, 3, 4, 5, 3], [0, 0, 7, 8, 4, 5, 6, 9, 2], [9, 0, 1, 2, 3, 4, 5, 2], [4, 1, 2, 1], [5, 0, 1, 2, 3], [11, 0, 1, 2, 2], [0, 0, 2, 7, 4, 5, 10, 6, 3], [7, 0, 1, 2, 5, 4], [1, 0, 3, 4, 5, 2], [8, 0, 2], [0, 0, 1, 8, 4, 5, 6, 3], [0, 0, 7, 8, 4, 5, 10, 6, 2], [0, 0, 8, 4, 5, 6, 9, 2], [0, 0, 7, 8, 4, 5, 6, 2], [0, 0, 7, 4, 5, 6, 2], [0, 0, 3, 7, 4, 5, 10, 6, 9, 3], [4, 0, 1, 2, 3, 4, 5, 6, 2], [5, 0, 1, 3], [10, 0, 1, 2, 3, 1], [6, 1, 2, 1], [7, 0, 3, 4, 5, 4], [1, 2, 0, 3, 4, 5, 3], [1, 1, 3, 4, 5, 2], [1, 1, 0, 3, 4, 3], [3, 0, 3, 4, 1, 2, 5, 10, 11, 7], [3, 0, 8, 3, 1, 2, 5, 9, 10, 11, 8], [3, 0, 6, 7, 3, 4, 1, 2, 10, 11, 8], [3, 0, 6, 7, 4, 1, 2, 10, 11, 7]], [[[[12, "ShareView"], [13, "ShareView", 1, [-8, -9], [[6, -3, [[7, "31060Wp8DNAh5ScH0KVBLqA", "close", -2]]], [21, -7, -6, -5, -4]], [22, -1, 0], [5, 750, 1335]], [14, "bg", 1, [-12, -13, -14, -15], [[10, 45, -2.842170943040401e-14, 2.842170943040401e-14, -10], [24, false, 0, -11, [32], 33]], [0, "a9eVDVVwRP65/QJaB+R1fD", 1, 0], [4, 4278190080], [5, 750, 1335]], [15, "img_hpxxd", [-17, -18, -19, -20, -21], [[3, 1, 0, -16, [21], 22]], [0, "3ccj7n9qBARJVwj0xZIcKs", 1, 0], [5, 420, 120], [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "showNode", 2, [-23, -24, 3, -25], [[3, 1, 0, -22, [27], 28]], [0, "a9hnsxLFFALI2B3biKtkPY", 1, 0], [5, 500, 600]], [4, "Background", 4, [-29], [[25, 1, -26, [24], 25], [19, 3, -28, [[7, "f9e5695RjNPCLTnW0c5qbW4", "close", 1]], [4, 4293322470], [4, 3363338360], -27, 26]], [0, "3fMAxYxIRKwbgz1XxMloLB", 1, 0], [5, 52, 56], [210, 260, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [9, "maskbg", 5, 2, [[10, 45, 5.329070518200751e-15, -5.329070518200751e-15, -30], [11, 0, -31, [2], 3], [6, -32, [[20, "d57a7d397FISp9LyX81u3JC", "onClickItem"]]]], [0, "femEAY3ZJOJrRwQw1l4dtX", 1, 0], [4, 4278190080], [5, 750, 1335]], [4, "title_zhua<PERSON><PERSON>", 4, [-34, -35], [[3, 1, 0, -33, [9], 10]], [0, "7bVg4oZuVEp4FeHdyIWcCd", 1, 0], [5, 500, 600], [0, 1.4210854715202004e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "button03", 2, [-38], [[26, 1, 0, -36, [31]], [6, -37, [[7, "f9e5695RjNPCLTnW0c5qbW4", "onJump", 1]]]], [0, "639V0d06RL3Zhma7Vn7FX9", 1, 0], [5, 260, 86], [0, -200, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "mask", 200, 1, [[11, 0, -39, [0], 1], [23, 45, 750, 1334, -40]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4278190080], [5, 750, 1335]], [5, "Label_title", 7, [[-41, [8, 4, -42, [4, 4278190080]]], 1, 4], [0, "feYhcjccRIZ4akjhA8kzbZ", 1, 0], [5, 285, 56.4], [0, 280, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbDesc", 2, [[-43, [8, 4, -44, [4, 4278190080]]], 1, 4], [0, "e1tu+svNNOqacTCX0qF0sj", 1, 0], [5, 476.69, 143.6], [0, -25.054, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnLb", 8, [[-45, [8, 3, -46, [4, 4279374353]]], 1, 4], [0, "168PCgPiBPfZ0QrMKH5tY1", 1, 0], [5, 107.66, 43.8], [0, 3.025, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_xb", 4, [[2, -47, [4], 5]], [0, "7b5Yw8spVDwovn8Vi/m+xV", 1, 0], [5, 341, 244], [0, 420, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_hpbt", 7, [[3, 1, 0, -48, [6], 7]], [0, "29ha7l16hC5YO6bFNQh4bq", 1, 0], [5, 350, 100], [0, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "Five star praise", false, 1, 1, 1, 2, 10, [8]], [1, "img_xx", 3, [[2, -49, [11], 12]], [0, "adhDsTHBZHxpXjlrSHlHSN", 1, 0], [5, 60, 71], [-150, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_xx", 3, [[2, -50, [13], 14]], [0, "0fL8le5MhHkYf6Ky6AtzCP", 1, 0], [5, 60, 71], [-75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "img_xx", 3, [[2, -51, [15], 16]], [0, "008mTZzbhMT6Mp3RbxIoQn", 1, 0], [5, 60, 71]], [1, "img_xx", 3, [[2, -52, [17], 18]], [0, "a0EmaPRxhBOq+t7+d2m9Kk", 1, 0], [5, 60, 71], [75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_xx", 3, [[2, -53, [19], 20]], [0, "54UWODxUlFX589TfA434qV", 1, 0], [5, 60, 71], [150, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "Label", false, 5, [[28, "返回", false, false, 1, 1, 1, 1, -54, [23]]], [0, "8d0F4swmxFToxVZ9XX5M4A", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "Are you enjoying the game?\nRate us?", 35, 60, false, 1, 1, 1, 11, [29]], [30, "Submit", 30, 30, 1, 1, 1, 12, [30]]], 0, [0, 3, 1, 0, 4, 1, 0, 0, 1, 0, 5, 23, 0, 6, 22, 0, 7, 15, 0, 0, 1, 0, -1, 9, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 4, 0, -3, 11, 0, -4, 8, 0, 0, 3, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, 0, 4, 0, -1, 13, 0, -2, 7, 0, -4, 5, 0, 0, 5, 0, 8, 5, 0, 0, 5, 0, -1, 21, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 14, 0, -2, 10, 0, 0, 8, 0, 0, 8, 0, -1, 12, 0, 0, 9, 0, 0, 9, 0, -1, 15, 0, 0, 10, 0, -1, 22, 0, 0, 11, 0, -1, 23, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 9, 1, 3, 10, 4, 54], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 11, -1, 1, -1, -1, -1, -1, 1], [0, 2, 0, 2, 0, 3, 0, 4, 0, 0, 5, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 6, 0, 0, 7, 8, 0, 9, 0, 0, 0, 0, 2]], [[{"name": "img_xx", "rect": [0, 0, 60, 71], "offset": [0, 0], "originalSize": [60, 71], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [10]], [[{"name": "img_xb", "rect": [0, 0, 341, 244], "offset": [0, 0], "originalSize": [341, 244], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [11]], [[{"name": "img_hptc", "rect": [0, 0, 552, 551], "offset": [0, 0], "originalSize": [552, 551], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [12]], [[{"name": "img_hpbt", "rect": [0, 0, 388, 91], "offset": [0, 0], "originalSize": [388, 91], "capInsets": [105, 20, 121, 30]}], [2], 0, [0], [2], [13]], [[{"name": "img_hpxxd", "rect": [0, 0, 60, 60], "offset": [0, 0], "originalSize": [60, 60], "capInsets": [13, 19, 14, 23]}], [2], 0, [0], [2], [14]]]]