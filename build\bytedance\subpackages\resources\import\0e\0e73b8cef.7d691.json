[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "15rp+R/fZB6YMs7NKdiHHV", "b4d0IpVyBGTppGF4E/ckRC", "c1f83oCtJCFJFn7xcFpHQh", "b845d7GlpAupkVRbsPIOkb"], ["node", "_spriteFrame", "root", "data", "_file", "_texture", "_textureSetter"], [["cc.Node", ["_name", "_prefab", "_parent", "_components", "_children", "_contentSize", "_trs"], 2, 4, 1, 9, 2, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.MotionStreak", ["_fadeTime", "_stroke", "_fastMode", "node", "_materials", "_texture"], 0, 1, 3, 6], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "startRadius", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -14, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6]], [[1, 0, 1, 2, 2], [3, 0, 2], [0, 0, 4, 1, 2], [0, 0, 2, 4, 3, 1, 5, 2], [0, 0, 2, 3, 1, 6, 2], [0, 0, 2, 3, 1, 5, 6, 2], [1, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 4], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 18], [6, 0, 1, 2, 1]], [[[[1, "fx_arch02"], [2, "fx_arch02", [-2], [6, -1, 0]], [3, "New Sprite", 1, [-4, -5], [[7, 0.8, 70, true, -3, [5], 6]], [0, "735/Yps3BHJ5GpwrH1k2pC", 1, 0], [5, 40, 36]], [4, "New Particle", 2, [[8, 1, true, 50, 999.999985098839, 0.8, 1, 360, 360, 20, 10, 5, 5, 30, 30, 0, true, 100, -6, [0], [4, 2751463423], [4, 0], [4, 3607101439], [4, 0], [0, 20, 20], [0, 0, -100], 1, 2]], [0, "592PfOB0FKiYqkHWbQ2YJt", 1, 0], [2.33, -2.697, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "head01", 2, [[9, -7, [3], 4]], [0, "d0W+JlsH5Pz62ZIFvSurtJ", 1, 0], [5, 141, 157], [0, 33.443, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, -1, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, 0, 4, 0, 3, 1, 7], [0, 0, 0, 0, 0, 0, 0], [-1, 4, 1, -1, 1, -1, 5], [0, 1, 2, 0, 3, 0, 4]], [[{"name": "head01", "rect": [0, 0, 141, 157], "offset": [0, 0], "originalSize": [141, 157], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [6], [5]]]]