[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "a2MjXRFdtLlYQ5ouAFv/+R", "b76uHOgvlBK44IpR7/IIXg", "2fyKStw/BIZJo+ld7ER3CQ", "aej5KCfVhFI6ZBtIoyh/zN", "94EziLmzlAirmOfZrgDe1Z", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr"], ["node", "_spriteFrame", "_parent", "_N$file", "_N$skeletonData", "root", "rewardNode", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "_top", "node"], -2, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "_N$spacingY", "_N$affectedByScale", "node", "_layoutSize"], -5, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["54990cDu2NHCb7PDc7tpcdh", ["node", "rewardNode"], 3, 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 9, 2], [2, 1, 2, 3, 1], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [1, 0, 1, 3, 5, 4], [6, 0, 1, 2, 2], [5, 0, 2], [0, 0, 1, 8, 6, 4, 5, 3], [0, 0, 2, 7, 6, 4, 10, 5, 3], [0, 0, 8, 4, 5, 2], [0, 0, 7, 8, 4, 9, 2], [0, 0, 3, 7, 6, 4, 5, 9, 3], [0, 0, 8, 6, 4, 5, 2], [0, 0, 6, 4, 5, 11, 9, 2], [1, 0, 5, 2], [1, 2, 0, 1, 5, 4], [1, 0, 4, 5, 3], [2, 0, 1, 2, 3, 2], [3, 1, 2, 1], [4, 0, 1, 5, 2, 3, 4, 6, 7, 8, 9, 8], [4, 0, 1, 2, 3, 4, 7, 8, 9, 6], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 3, 4, 5, 6, 5], [11, 0, 1, 1]], [[7, "<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 1, [-4, -5], [[26, -3, -2]], [19, -1, 0], [5, 750, 1334]], [10, "content", [-6, -7, -8, -9, -10, -11], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [14, "rewardList", [[22, 1, 3, 40, 30, 30, 15, 15, true, -12, [5, 597, 180]], [16, 0, 41, 520, -13]], [0, "64hvudvlhES7vFSlHjZwac", 1, 0], [5, 597, 180], [0, 0.5, 1], [0, 242, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "line", 2, [-14, -15, -16], [0, "195jSQZK9CCZYyU6pqkIgo", 1, 0], [0, 301.428, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "view", [3], [[23, 0, -17, [14]], [5, 45, 240, 250, -18]], [0, "de+uD88qFFDqjNcUZ6aJIp", 1, 0], [5, 597, 484]], [9, "maskbg", 140, 1, [[15, 45, -19], [18, 0, -20, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [3, "bg", 1, [2], [[5, 45, 750, 1334, -21]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [1, "Label", 2, [[20, "点击空白继续", 36, false, false, 1, 1, 1, -22, [2], 3], [6, 2, -23, [4, 4278190080]]], [0, "b9drdWxu1MT6D97Nyh78mH", 1, 0], [5, 220, 54.4], [0, -319.217, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label_tips", 4, [[21, "你获得了", 36, false, 1, 1, -24, [10], 11], [6, 3, -25, [4, 4278190080]]], [0, "5cWjNjK1lDxpqtR9KtG3i2", 1, 0], [5, 150, 56.4], [0, 5.503, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New ScrollView", 2, [5], [[24, false, 0.75, 0.23, null, null, -26, 3]], [0, "d9kWm8IINKUb9o7UJoDW2/", 1, 0], [5, 597, 484]], [1, "effect_ribbons", 2, [[25, "default", "animation", 0, "animation", -27, [15], 16], [17, 1, 3.9951904144287482, -28]], [0, "85Abx3vRlHYpcjHeEf+/ds", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 542.266, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "pic_jl_00", 2, [[2, -29, [4], 5]], [0, "e8rODEeO1A3rgCisWNmIzp", 1, 0], [5, 608, 587]], [4, "sp_light", 2, [[2, -30, [6], 7]], [0, "ebBioO5DVER4oHWr1DUFId", 1, 0], [5, 511, 505]], [1, "line", 4, [[2, -31, [8], 9]], [0, "6eofV5yPxArqA7R33lR7vX", 1, 0], [5, 531, 47], [12, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [12, "line", false, 4, [[2, -32, [12], 13]], [0, "40NkES53tEwovo3mutJd9J", 1, 0], [5, 87, 30], [175, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 6, 3, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -1, 8, 0, -2, 12, 0, -3, 13, 0, -4, 4, 0, -5, 10, 0, -6, 11, 0, 0, 3, 0, 0, 3, 0, -1, 14, 0, -2, 9, 0, -3, 15, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 7, 1, 2, 2, 7, 3, 2, 5, 5, 2, 10, 32], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, -1, 4], [0, 2, 0, 1, 0, 3, 0, 4, 0, 5, 0, 1, 0, 6, 0, 7, 8]]