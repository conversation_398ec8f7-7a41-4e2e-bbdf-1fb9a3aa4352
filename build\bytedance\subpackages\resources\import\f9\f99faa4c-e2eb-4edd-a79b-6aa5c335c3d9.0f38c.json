[1, ["dcmekv8S1DXbBmxfJN61/W"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "long_red_f", "\nlong_red_f.png\nsize: 582,993\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\njj\n  rotate: false\n  xy: 2, 585\n  size: 271, 171\n  orig: 271, 171\n  offset: 0, 0\n  index: -1\nlll\n  rotate: true\n  xy: 2, 185\n  size: 195, 173\n  orig: 195, 173\n  offset: 0, 0\n  index: -1\nlong_3\n  rotate: false\n  xy: 2, 758\n  size: 425, 233\n  orig: 425, 233\n  offset: 0, 0\n  index: -1\nlong_4\n  rotate: true\n  xy: 304, 21\n  size: 98, 124\n  orig: 98, 124\n  offset: 0, 0\n  index: -1\ntw_00\n  rotate: true\n  xy: 113, 2\n  size: 181, 89\n  orig: 183, 109\n  offset: 1, 9\n  index: -1\ntw_01\n  rotate: true\n  xy: 382, 404\n  size: 172, 104\n  orig: 183, 109\n  offset: 10, 0\n  index: -1\ntw_02\n  rotate: true\n  xy: 481, 227\n  size: 175, 99\n  orig: 183, 109\n  offset: 2, 0\n  index: -1\ntw_03\n  rotate: true\n  xy: 273, 406\n  size: 172, 107\n  orig: 183, 109\n  offset: 1, 0\n  index: -1\ntw_04\n  rotate: true\n  xy: 275, 580\n  size: 176, 109\n  orig: 183, 109\n  offset: 0, 0\n  index: -1\ntw_05\n  rotate: true\n  xy: 2, 3\n  size: 180, 109\n  orig: 183, 109\n  offset: 0, 0\n  index: -1\ntw_06\n  rotate: true\n  xy: 429, 810\n  size: 181, 109\n  orig: 183, 109\n  offset: 0, 0\n  index: -1\ntw_07\n  rotate: true\n  xy: 178, 404\n  size: 179, 93\n  orig: 183, 109\n  offset: 4, 16\n  index: -1\ntw_08\n  rotate: true\n  xy: 386, 578\n  size: 178, 93\n  orig: 183, 109\n  offset: 5, 16\n  index: -1\ntw_09\n  rotate: true\n  xy: 488, 445\n  size: 180, 91\n  orig: 183, 109\n  offset: 0, 18\n  index: -1\ntw_10\n  rotate: true\n  xy: 283, 234\n  size: 170, 90\n  orig: 183, 109\n  offset: 4, 19\n  index: -1\ntw_11\n  rotate: true\n  xy: 204, 23\n  size: 176, 98\n  orig: 183, 109\n  offset: 0, 11\n  index: -1\ntw_12\n  rotate: true\n  xy: 177, 201\n  size: 179, 104\n  orig: 183, 109\n  offset: 4, 5\n  index: -1\ntw_13\n  rotate: false\n  xy: 304, 121\n  size: 167, 104\n  orig: 183, 109\n  offset: 16, 5\n  index: -1\ntw_14\n  rotate: true\n  xy: 375, 227\n  size: 175, 104\n  orig: 183, 109\n  offset: 8, 4\n  index: -1\ntw_15\n  rotate: true\n  xy: 481, 627\n  size: 181, 90\n  orig: 183, 109\n  offset: 2, 7\n  index: -1\nxx\n  rotate: false\n  xy: 430, 11\n  size: 106, 106\n  orig: 106, 106\n  offset: 0, 0\n  index: -1\nxx2\n  rotate: true\n  xy: 473, 119\n  size: 106, 107\n  orig: 106, 107\n  offset: 0, 0\n  index: -1\nzheng_5\n  rotate: false\n  xy: 2, 382\n  size: 174, 201\n  orig: 176, 201\n  offset: 0, 0\n  index: -1\n", ["long_red_f.png"], {"skeleton": {"hash": "g/vOfHfUrQv3NbpvhDDP2wyBJF4", "spine": "3.8.99", "x": -230.52, "y": -239.13, "width": 440.33, "height": 410.83, "images": "./images/", "audio": "J:/工作/我要上巅峰/青龙/正"}, "bones": [{"name": "root"}, {"name": "aa", "parent": "root", "x": 0.71}, {"name": "all", "parent": "aa", "x": -0.71, "y": -94.12, "color": "000000ff"}, {"name": "zheng_0", "parent": "all", "length": 82.81, "rotation": 90.76, "x": 0.49, "y": -52.63, "color": "ff0000ff"}, {"name": "zheng_3", "parent": "zheng_0", "length": 54.64, "rotation": 1.1, "x": 77.16, "y": 0.21, "color": "ca00ffff"}, {"name": "zheng_4", "parent": "zheng_3", "length": 66.16, "rotation": -1.09, "x": 54.64, "color": "ca00ffff"}, {"name": "zheng_5", "parent": "zheng_4", "length": 55.12, "rotation": 2, "x": 66.16, "color": "ca00ffff"}, {"name": "zheng_6", "parent": "all", "length": 45.53, "rotation": -89.48, "x": -1.31, "y": -48.56, "color": "ff0000ff"}, {"name": "zheng_7", "parent": "zheng_6", "length": 11.13, "rotation": -5.71, "x": 45.61, "y": -0.29, "color": "ff0000ff"}, {"name": "zheng_8", "parent": "zheng_7", "length": 9.52, "rotation": 4.2, "x": 11.13, "color": "ff0000ff"}, {"name": "zheng_9", "parent": "zheng_8", "length": 9.31, "rotation": -18.14, "x": 9.52, "color": "ff0000ff"}, {"name": "zheng_10", "parent": "zheng_9", "length": 8.71, "rotation": 9.79, "x": 9.31, "color": "ff0000ff"}, {"name": "zheng_11", "parent": "zheng_10", "length": 15.2, "rotation": 63.13, "x": 8.71, "color": "ff0000ff"}, {"name": "zheng_2", "parent": "zheng_0", "length": 29.13, "rotation": 39.74, "x": 26.54, "y": 47.34, "color": "fffc00ff"}, {"name": "zheng_12", "parent": "zheng_2", "length": 25.13, "rotation": -4.45, "x": 29.13, "color": "fffc00ff"}, {"name": "zheng_13", "parent": "zheng_12", "length": 20.91, "rotation": 17.69, "x": 25.13, "color": "fffc00ff"}, {"name": "zheng_14", "parent": "zheng_13", "length": 33.54, "rotation": 3.69, "x": 20.91, "color": "fffc00ff"}, {"name": "zheng_15", "parent": "zheng_14", "length": 47.3, "rotation": 50.03, "x": 33.54, "scaleX": 0.6, "color": "fffc00ff"}, {"name": "zheng_1", "parent": "zheng_0", "length": 27.87, "rotation": -34.67, "x": 23.82, "y": -48.08, "color": "fffc00ff"}, {"name": "zheng_16", "parent": "zheng_1", "length": 24.62, "rotation": -16.84, "x": 27.87, "color": "fffc00ff"}, {"name": "zheng_17", "parent": "zheng_16", "length": 25.5, "rotation": 6.04, "x": 24.62, "color": "fffc00ff"}, {"name": "zheng_18", "parent": "zheng_17", "length": 32.13, "rotation": -2.12, "x": 25.5, "color": "fffc00ff"}, {"name": "zheng_19", "parent": "zheng_18", "length": 56.28, "rotation": -71.48, "x": 32.13, "scaleX": 0.5705, "color": "fffc00ff"}, {"name": "zheng_21", "parent": "zheng_0", "length": 138.77, "rotation": 37.27, "x": 117.82, "y": 31.37, "color": "ff0000ff"}, {"name": "zheng_22", "parent": "zheng_0", "length": 138.14, "rotation": -36.83, "x": 116.95, "y": -34.51, "color": "ff0000ff"}, {"name": "zheng_23", "parent": "zheng_0", "length": 26.61, "rotation": 55.55, "x": 20.51, "y": 41.82, "color": "3fff00ff"}, {"name": "zheng_24", "parent": "zheng_23", "length": 22.95, "rotation": 38.3, "x": 26.61, "color": "3fff00ff"}, {"name": "zheng_25", "parent": "zheng_24", "length": 26.57, "rotation": 20.89, "x": 22.95, "color": "3fff00ff"}, {"name": "zheng_26", "parent": "zheng_25", "length": 25.5, "rotation": 5.89, "x": 26.57, "color": "3fff00ff"}, {"name": "zheng_27", "parent": "zheng_26", "length": 29.56, "rotation": -7.06, "x": 25.5, "color": "3fff00ff"}, {"name": "zheng_28", "parent": "zheng_27", "length": 22.17, "rotation": -21.46, "x": 29.56, "color": "3fff00ff"}, {"name": "zheng_29", "parent": "zheng_28", "length": 18.15, "rotation": -29.43, "x": 22.17, "color": "3fff00ff"}, {"name": "zheng_30", "parent": "zheng_29", "length": 20.35, "rotation": -41.05, "x": 18.15, "color": "3fff00ff"}, {"name": "zheng_31", "parent": "zheng_30", "length": 27.83, "rotation": -28.47, "x": 20.35, "color": "3fff00ff"}, {"name": "zheng_32", "parent": "zheng_31", "length": 30.72, "rotation": -9.95, "x": 27.83, "color": "3fff00ff"}, {"name": "zheng_33", "parent": "zheng_32", "length": 29.27, "rotation": -6.97, "x": 30.72, "color": "3fff00ff"}, {"name": "zheng_34", "parent": "zheng_33", "length": 26.92, "rotation": -5.66, "x": 29.27, "color": "3fff00ff"}, {"name": "zheng_35", "parent": "zheng_34", "length": 26.4, "rotation": 2.11, "x": 26.92, "color": "3fff00ff"}, {"name": "zheng_36", "parent": "zheng_35", "length": 18.62, "rotation": 12.8, "x": 26.4, "color": "3fff00ff"}, {"name": "zheng_37", "parent": "zheng_36", "length": 13.71, "rotation": 37.57, "x": 18.62, "color": "3fff00ff"}, {"name": "zheng_38", "parent": "zheng_37", "length": 15.94, "rotation": 71.51, "x": 13.71, "color": "3fff00ff"}, {"name": "zheng_20", "parent": "zheng_0", "x": 23.16, "y": -0.06, "color": "0014ffff"}, {"name": "zheng_39", "parent": "zheng_5", "length": 33.5, "rotation": 91.01, "x": -23.8, "y": 133.47, "color": "ca00ffff"}, {"name": "zheng_40", "parent": "zheng_39", "length": 20.75, "rotation": -18.45, "x": 33.5, "color": "ca00ffff"}, {"name": "zheng_41", "parent": "zheng_40", "length": 23.13, "rotation": -25.78, "x": 20.75, "color": "ca00ffff"}, {"name": "zheng_42", "parent": "zheng_41", "length": 27.82, "rotation": -71.43, "x": 23.13, "color": "ca00ffff"}, {"name": "zheng_43", "parent": "zheng_42", "length": 24.09, "rotation": -26.39, "x": 27.82, "color": "ca00ffff"}, {"name": "zheng_44", "parent": "zheng_43", "length": 27.94, "rotation": 22.24, "x": 24.09, "color": "ca00ffff"}, {"name": "zheng_45", "parent": "zheng_5", "length": 23.62, "rotation": 44.43, "x": -20.09, "y": 103.67, "color": "ca00ffff"}, {"name": "zheng_46", "parent": "zheng_45", "length": 22.61, "rotation": -24.2, "x": 23.62, "color": "ca00ffff"}, {"name": "zheng_47", "parent": "zheng_46", "length": 18.19, "rotation": -4.78, "x": 22.61, "color": "ca00ffff"}, {"name": "zheng_48", "parent": "zheng_47", "length": 23.05, "rotation": -0.98, "x": 18.19, "color": "ca00ffff"}, {"name": "zheng_49", "parent": "zheng_5", "length": 23.62, "rotation": -22.39, "x": 27.23, "y": 72.28, "color": "ca00ffff"}, {"name": "zheng_50", "parent": "zheng_49", "length": 24.83, "rotation": 0.42, "x": 23.62, "color": "ca00ffff"}, {"name": "zheng_51", "parent": "zheng_50", "length": 22.79, "rotation": 29.26, "x": 24.83, "color": "ca00ffff"}, {"name": "zheng_52", "parent": "zheng_51", "length": 19.04, "rotation": 31.86, "x": 22.79, "color": "ca00ffff"}, {"name": "zheng_53", "parent": "zheng_52", "length": 19.2, "rotation": 14.45, "x": 19.04, "color": "ca00ffff"}, {"name": "zheng_54", "parent": "zheng_5", "length": 19.37, "rotation": -15.01, "x": 48.16, "y": 20.35, "color": "ca00ffff"}, {"name": "zheng_55", "parent": "zheng_54", "length": 17.93, "rotation": 11.68, "x": 19.37, "color": "ca00ffff"}, {"name": "zheng_56", "parent": "zheng_55", "length": 23.03, "rotation": 12.76, "x": 17.93, "color": "ca00ffff"}, {"name": "zheng_57", "parent": "zheng_5", "length": 22.31, "rotation": 25.97, "x": 26.46, "y": -35.04, "color": "ca00ffff"}, {"name": "zheng_58", "parent": "zheng_57", "length": 26.36, "rotation": -21.52, "x": 22.31, "color": "ca00ffff"}, {"name": "zheng_59", "parent": "zheng_58", "length": 21.91, "rotation": -37.68, "x": 26.36, "color": "ca00ffff"}, {"name": "zheng_60", "parent": "zheng_59", "length": 24.53, "rotation": -27.56, "x": 21.91, "color": "ca00ffff"}, {"name": "zheng_61", "parent": "zheng_5", "length": 21.45, "rotation": -26.81, "x": 29.35, "y": -69.35, "color": "ca00ffff"}, {"name": "zheng_62", "parent": "zheng_61", "length": 15.24, "rotation": -54.9, "x": 21.45, "color": "ca00ffff"}, {"name": "zheng_63", "parent": "zheng_5", "length": 32.05, "rotation": -38.24, "x": -14.16, "y": -106.48, "color": "ca00ffff"}, {"name": "zheng_64", "parent": "zheng_63", "length": 28.12, "rotation": -5.24, "x": 32.05, "color": "ca00ffff"}, {"name": "zheng_65", "parent": "zheng_64", "length": 24.32, "rotation": -3.24, "x": 28.12, "color": "ca00ffff"}, {"name": "zheng_66", "parent": "zheng_65", "length": 27.28, "rotation": 16.44, "x": 24.32, "color": "ca00ffff"}, {"name": "zheng_67", "parent": "zheng_5", "length": 25.03, "rotation": -73.82, "x": -61.53, "y": -91.7, "color": "ca00ffff"}, {"name": "zheng_68", "parent": "zheng_67", "length": 27.98, "rotation": -5.76, "x": 25.03, "color": "ca00ffff"}, {"name": "zheng_69", "parent": "zheng_68", "length": 25.82, "rotation": 25.05, "x": 27.98, "color": "ca00ffff"}, {"name": "zheng_70", "parent": "zheng_69", "length": 23.3, "rotation": 12.89, "x": 25.82, "color": "ca00ffff"}, {"name": "zheng_71", "parent": "zheng_70", "length": 18.34, "rotation": 14.5, "x": 23.3, "color": "ca00ffff"}, {"name": "zheng_72", "parent": "zheng_0", "length": 26.61, "rotation": 124.66, "x": 19.51, "y": -40.29, "scaleX": -1, "color": "3fff00ff"}, {"name": "zheng_73", "parent": "zheng_72", "length": 22.95, "rotation": 38.3, "x": 26.61, "color": "3fff00ff"}, {"name": "zheng_74", "parent": "zheng_73", "length": 26.57, "rotation": 20.89, "x": 22.95, "color": "3fff00ff"}, {"name": "zheng_75", "parent": "zheng_74", "length": 25.5, "rotation": 5.89, "x": 26.57, "color": "3fff00ff"}, {"name": "zheng_76", "parent": "zheng_75", "length": 29.56, "rotation": -7.06, "x": 25.5, "color": "3fff00ff"}, {"name": "zheng_77", "parent": "zheng_76", "length": 22.17, "rotation": -21.46, "x": 29.56, "color": "3fff00ff"}, {"name": "zheng_78", "parent": "zheng_77", "length": 18.15, "rotation": -29.43, "x": 22.17, "color": "3fff00ff"}, {"name": "zheng_79", "parent": "zheng_78", "length": 20.35, "rotation": -41.05, "x": 18.15, "color": "3fff00ff"}, {"name": "zheng_80", "parent": "zheng_79", "length": 27.83, "rotation": -28.47, "x": 20.35, "color": "3fff00ff"}, {"name": "zheng_81", "parent": "zheng_80", "length": 30.72, "rotation": -9.95, "x": 27.83, "color": "3fff00ff"}, {"name": "zheng_82", "parent": "zheng_81", "length": 29.27, "rotation": -6.97, "x": 30.72, "color": "3fff00ff"}, {"name": "zheng_83", "parent": "zheng_82", "length": 26.92, "rotation": -5.66, "x": 29.27, "color": "3fff00ff"}, {"name": "zheng_84", "parent": "zheng_83", "length": 26.4, "rotation": 2.11, "x": 26.92, "color": "3fff00ff"}, {"name": "zheng_85", "parent": "zheng_84", "length": 18.62, "rotation": 12.8, "x": 26.4, "color": "3fff00ff"}, {"name": "zheng_86", "parent": "zheng_85", "length": 13.71, "rotation": 37.57, "x": 18.62, "color": "3fff00ff"}, {"name": "zheng_87", "parent": "zheng_86", "length": 15.94, "rotation": 71.51, "x": 13.71, "color": "3fff00ff"}, {"name": "huo", "parent": "zheng_0", "rotation": -90.76, "x": 52.63, "y": -0.21, "color": "000000ff"}, {"name": "huo2", "parent": "zheng_0", "rotation": -90.76, "x": 52.63, "y": -0.21, "color": "000000ff"}, {"name": "zheng_88", "parent": "zheng_44", "length": 26.71, "rotation": 66.18, "x": 28.24, "y": 0.83, "color": "ca00ffff"}, {"name": "zheng_89", "parent": "zheng_62", "length": 19.77, "rotation": 21.77, "x": 17.21, "y": 0.04, "color": "ca00ffff"}, {"name": "zheng_90", "parent": "zheng_66", "length": 19.4, "rotation": 21.54, "x": 29.84, "y": -0.21, "color": "ca00ffff"}, {"name": "zheng_91", "parent": "zheng_71", "length": 30.87, "rotation": 32.95, "x": 20.41, "y": -0.47, "color": "ca00ffff"}], "slots": [{"name": "zheng_8", "bone": "zheng_6", "attachment": "long_4"}, {"name": "zheng_5", "bone": "zheng_23", "attachment": "zheng_5"}, {"name": "zheng_7", "bone": "zheng_72", "attachment": "zheng_5"}, {"name": "long_3", "bone": "zheng_3", "attachment": "long_3"}, {"name": "zheng_22", "bone": "zheng_22", "attachment": "jj"}, {"name": "xx", "bone": "zheng_2", "attachment": "xx"}, {"name": "xx2", "bone": "zheng_1", "attachment": "xx2"}, {"name": "zheng_2", "bone": "zheng_0", "attachment": "lll"}, {"name": "huo", "bone": "huo", "blend": "additive"}, {"name": "huo2", "bone": "huo2", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"huo": {"tw_00": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_01": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_02": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_03": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_04": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_05": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_06": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_07": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_08": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_09": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_10": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_11": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_12": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_13": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_14": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_15": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}}, "huo2": {"tw_00": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_01": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_02": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_03": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_04": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_05": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_06": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_07": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_08": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_09": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_10": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_11": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_12": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_13": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_14": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_15": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}}, "long_3": {"long_3": {"type": "mesh", "hull": 97, "width": 425, "height": 233, "uvs": [0.22519, 0.62128, 0.15046, 0.61265, 0.10664, 0.59942, 0.10128, 0.56031, 0.13344, 0.50108, 0.17316, 0.42114, 0.1864, 0.35673, 0.17255, 0.27923, 0.13124, 0.20389, 0.05399, 0.16765, 0.11579, 0.22862, 0.12494, 0.25967, 0.11146, 0.3177, 0.06815, 0.3891, 0.01817, 0.47418, 0, 0.53344, 0, 0.59649, 0.05732, 0.63599, 0.10521, 0.66562, 0.18143, 0.76513, 0.17809, 0.67777, 0.23099, 0.69676, 0.28278, 0.7583, 0.32526, 0.8958, 0.39023, 0.91631, 0.54432, 1, 0.72049, 0.91631, 0.78129, 0.84111, 0.85376, 0.7621, 0.93622, 0.6755, 1, 0.53269, 1, 0.41266, 0.96579, 0.29062, 0.95621, 0.41444, 0.94663, 0.51244, 0.9129, 0.61499, 0.8446, 0.69172, 0.76797, 0.6902, 0.8346, 0.6431, 0.87917, 0.51016, 0.92872, 0.36734, 0.95455, 0.23668, 0.95663, 0.04274, 0.93789, 0.04578, 0.91873, 0.20683, 0.8725, 0.28355, 0.80628, 0.34357, 0.75964, 0.4484, 0.74173, 0.35952, 0.74756, 0.31394, 0.78879, 0.27596, 0.81336, 0.17948, 0.79462, 0.17644, 0.77588, 0.23569, 0.74381, 0.25772, 0.69134, 0.25772, 0.66718, 0.29571, 0.65594, 0.34357, 0.61959, 0.27596, 0.60585, 0.20531, 0.61668, 0.1377, 0.65708, 0.06173, 0.59544, 0.10807, 0.56171, 0.1772, 0.54421, 0.25089, 0.54838, 0.30254, 0.52547, 0.27444, 0.51839, 0.2167, 0.5288, 0.12478, 0.50423, 0.17312, 0.49632, 0.22478, 0.49215, 0.14958, 0.47799, 0.07361, 0.44926, 0, 0.45717, 0.07893, 0.46425, 0.14122, 0.44012, 0.23811, 0.40055, 0.32699, 0.41638, 0.2176, 0.4043, 0.11657, 0.36682, 0.03452, 0.31601, 0, 0.2981, 0, 0.3081, 0.03225, 0.3485, 0.07782, 0.37557, 0.14239, 0.37851, 0.22311, 0.34769, 0.35377, 0.32395, 0.4624, 0.30563, 0.49431, 0.2644, 0.47304, 0.23566, 0.41227, 0.22275, 0.33858, 0.18569, 0.28161, 0.20401, 0.35377, 0.20318, 0.43734, 0.20401, 0.54521], "triangles": [25, 37, 26, 57, 25, 65, 37, 25, 57, 37, 57, 56, 26, 37, 27, 48, 47, 56, 48, 56, 55, 37, 56, 47, 65, 25, 24, 65, 58, 57, 65, 24, 77, 77, 24, 23, 77, 88, 87, 77, 66, 65, 23, 22, 88, 22, 89, 88, 77, 23, 88, 28, 37, 36, 28, 27, 37, 19, 18, 20, 18, 1, 20, 29, 36, 35, 29, 28, 36, 77, 76, 66, 76, 70, 66, 70, 67, 66, 89, 22, 0, 70, 75, 71, 20, 0, 21, 22, 21, 0, 37, 47, 38, 0, 90, 89, 20, 1, 0, 29, 34, 30, 29, 35, 34, 18, 2, 1, 18, 17, 2, 38, 47, 39, 17, 3, 2, 14, 3, 17, 0, 96, 90, 17, 16, 15, 17, 15, 14, 14, 13, 3, 3, 13, 4, 90, 95, 91, 90, 96, 95, 30, 34, 31, 34, 33, 31, 47, 46, 39, 46, 45, 39, 39, 45, 40, 13, 12, 4, 4, 12, 5, 6, 5, 11, 11, 5, 12, 70, 76, 75, 95, 94, 91, 6, 11, 7, 31, 33, 32, 94, 92, 91, 45, 44, 40, 40, 44, 41, 49, 55, 54, 49, 48, 55, 77, 87, 86, 94, 93, 92, 77, 86, 78, 50, 54, 53, 50, 49, 54, 58, 64, 59, 59, 64, 63, 64, 58, 65, 11, 8, 7, 51, 53, 52, 51, 50, 53, 11, 10, 8, 41, 44, 42, 42, 44, 43, 10, 9, 8, 70, 69, 67, 86, 85, 78, 85, 79, 78, 67, 69, 68, 63, 62, 59, 59, 62, 60, 85, 84, 79, 71, 75, 72, 75, 74, 72, 60, 62, 61, 84, 80, 79, 74, 73, 72, 83, 81, 84, 84, 81, 80, 83, 82, 81], "vertices": [7, 42, -1.15, -5.93, 0.50381, 43, -31, -16.59, 0.1437, 48, 21.74, 18.95, 0.02603, 49, -9.49, 16.51, 0.31203, 50, -33.36, 13.78, 0.0144, 51, -51.78, 12.9, 3e-05, 96, 14.01, 322.14, 0, 9, 42, 30.4, -10.03, 0.45562, 43, 0.24, -10.5, 0.29218, 44, -13.91, -18.37, 0.16953, 45, 5.62, -40.96, 0.0126, 46, -1.68, -46.56, 0.00272, 48, 46.4, 39.06, 0.02603, 49, 4.76, 44.96, 0.04131, 51, -40.45, 42.63, 0, 96, 20.74, 353.25, 0, 6, 42, 48.79, -14.33, 0.18963, 43, 19.03, -8.76, 0.30091, 44, 2.27, -8.63, 0.33744, 45, 1.54, -22.53, 0.14176, 46, -13.53, -31.86, 0.03026, 96, 26.56, 371.2, 0, 5, 43, 23.55, -17, 0.15721, 44, 9.91, -14.09, 0.36049, 45, 9.15, -17.02, 0.22467, 46, -9.16, -23.54, 0.24327, 47, -39.69, -9.2, 0.01435, 5, 43, 13.82, -33.81, 0.00874, 44, 8.46, -33.46, 0.19145, 45, 27.05, -24.56, 0.21313, 46, 10.22, -22.34, 0.33152, 47, -21.29, -15.43, 0.25517, 5, 44, 7.7, -58.59, 0.02354, 45, 50.63, -33.28, 0.08397, 46, 35.22, -19.68, 0.31488, 47, 2.85, -22.43, 0.57041, 93, -31.53, 13.83, 0.00721, 5, 44, 13.15, -73.66, 0.00049, 45, 66.65, -32.91, 0.00105, 46, 49.41, -12.23, 0.10186, 47, 18.81, -20.9, 0.7597, 93, -23.69, -0.14, 0.1369, 3, 46, 57.04, 5.17, 0.01089, 47, 32.45, -7.69, 0.51888, 93, -6.09, -7.29, 0.47023, 3, 46, 55.62, 29.95, 9e-05, 47, 40.52, 15.79, 0.20561, 93, 18.64, -5.19, 0.7943, 4, 46, 36.74, 58.11, 0.0001, 47, 33.71, 48.99, 0.00196, 93, 46.27, 14.46, 0.99794, 96, 129.38, 378.31, 0, 4, 46, 46.89, 30.02, 0.00145, 47, 32.46, 19.16, 0.02731, 93, 18.47, 3.54, 0.97124, 96, 111.41, 354.46, 0, 4, 46, 44.97, 22.04, 0.04075, 47, 27.67, 12.49, 0.30618, 93, 10.44, 5.24, 0.65306, 96, 103.68, 351.7, 0, 5, 45, 63.22, 0.03, 0.05266, 46, 31.7, 15.76, 0.32066, 47, 13.01, 11.71, 0.30695, 93, 3.8, 18.33, 0.31973, 96, 91.17, 359.38, 0, 6, 44, 46.51, -35.32, 0.00673, 45, 40.92, 10.92, 0.37409, 46, 6.88, 15.6, 0.32448, 47, -10.02, 20.95, 0.2816, 93, 2.95, 43.13, 0.0131, 96, 77.46, 380.06, 0, 5, 44, 49.81, -6.45, 0.05688, 45, 14.61, 23.24, 0.65727, 46, -22.16, 14.95, 0.28509, 47, -37.15, 31.34, 0.00077, 96, 61.03, 404.02, 0, 4, 44, 46.73, 9.07, 0.1589, 45, -1.08, 25.26, 0.83592, 46, -37.11, 9.78, 0.00518, 96, 48.53, 413.72, 0, 3, 44, 37.2, 20.25, 0.48551, 45, -14.71, 19.78, 0.51449, 96, 34, 415.91, 0, 6, 4, 99.74, 202.28, 0, 43, 37.15, 4.79, 0.32575, 44, 12.69, 11.45, 0.44294, 45, -14.18, -6.25, 0.23131, 70, -271.65, 124.35, 0, 96, 21.27, 393.2, 0, 6, 4, 92.18, 182.16, 2e-05, 42, 50.4, 1.02, 0.33331, 43, 15.71, 6.31, 0.32575, 44, -7.28, 3.5, 0.34091, 70, -254.64, 111.21, 0, 96, 11.4, 374.1, 0, 7, 4, 67.96, 150.54, 3e-05, 5, 10.44, 150.76, 0, 42, 19.61, 26.29, 0.66664, 43, -21.49, 20.54, 0.32575, 44, -46.97, 0.13, 0.00758, 70, -231.54, 78.76, 0, 96, -16.36, 345.54, 0, 7, 4, 88.35, 151.29, 0.00172, 5, 30.81, 151.91, 0.00017, 42, 19.68, 5.88, 0.94543, 43, -14.97, 1.21, 0.00027, 48, 27.48, 42.21, 0.05241, 70, -226.27, 98.47, 0, 96, 3.98, 343.9, 0, 8, 4, 83.19, 128.97, 0.02555, 5, 26.09, 129.49, 0.01509, 42, -2.46, 11.78, 0.67164, 43, -37.84, -0.2, 0.00034, 48, 7.98, 30.19, 0.28735, 52, -80.33, 30.21, 1e-05, 70, -206.45, 86.99, 2e-05, 96, -3.75, 322.33, 0, 7, 4, 68.15, 107.43, 0.10664, 5, 11.45, 107.67, 0.05448, 42, -23.48, 27.54, 0.34866, 43, -62.76, 8.09, 0.00037, 48, -17.92, 25.75, 0.48979, 52, -86.44, 4.66, 1e-05, 70, -190.28, 66.27, 6e-05, 7, 4, 35.54, 90.43, 0.24163, 5, -20.82, 90.05, 0.1044, 42, -39.38, 60.69, 0.07256, 43, -88.34, 34.52, 0.00011, 48, -52.93, 36.99, 0.57994, 52, -110.55, -23.11, 0.00124, 70, -183.62, 30.11, 0.00012, 8, 4, 29.87, 62.99, 0.551, 5, -25.97, 62.5, 0.08948, 42, -66.62, 67.28, 0.01303, 43, -116.26, 32.15, 4e-05, 48, -76.44, 21.74, 0.345, 52, -105.78, -50.72, 0.00123, 70, -159.05, 16.62, 0.00022, 96, -64.43, 263.05, 0, 9, 4, 8.25, -1.84, 0.53923, 5, -46.35, -2.72, 0.05903, 42, -130.68, 91.05, 0.00269, 43, -184.56, 34.43, 2e-05, 48, -137.74, -8.44, 0.1426, 52, -102.15, -118.95, 0.00123, 60, -109.09, 93.46, 2e-05, 70, -103.45, -23.09, 0.25517, 96, -93.49, 201.2, 0, 9, 4, 25.3, -77.3, 0.40936, 5, -27.86, -77.85, 0.00895, 43, -252.05, -3.4, 0, 48, -179.43, -73.62, 5e-05, 52, -58.64, -182.93, 0, 60, -128, 18.44, 2e-05, 70, -26.3, -28.97, 0.57582, 71, -48.17, -33.98, 0.00581, 96, -85.38, 124.26, 0, 9, 4, 41.98, -103.7, 0.07614, 5, -10.68, -103.92, 0.00894, 43, -272.61, -26.89, 0, 48, -186.49, -104.03, 5e-05, 60, -125.06, -12.65, 2e-05, 70, 3.83, -20.79, 0.57638, 71, -19.01, -22.81, 0.33842, 73, -76.25, 16.66, 5e-05, 96, -71.91, 96.09, 0, 8, 4, 59.37, -135.08, 0.00681, 43, -297.74, -52.5, 0, 48, -196.58, -138.47, 0, 70, 38.94, -13.38, 0.32138, 71, 15.18, -11.92, 0.33919, 72, -16.65, -5.38, 0.26161, 73, -42.59, 4.23, 0.07101, 96, -58.31, 62.89, 0, 7, 64, -21.57, -122.33, 0, 70, 78.64, -5.68, 0.00067, 71, 53.9, -0.27, 0.33338, 72, 23.37, -11.22, 0.26166, 73, -4.89, -10.39, 0.07101, 74, -29.89, -3.01, 0.33328, 96, -43.59, 25.22, 0, 6, 64, 19.86, -133.53, 0, 71, 87.89, 25.94, 0.00077, 72, 65.25, -1.87, 0.26166, 73, 38.03, -10.62, 0.07096, 74, 11.61, -13.97, 0.33328, 96, -14.73, -6.54, 0.33333, 5, 64, 45.4, -122.14, 0, 71, 94.27, 53.17, 0, 72, 82.57, 20.1, 5e-05, 74, 37.08, -2.43, 0.33328, 96, 12.92, -10.72, 0.66667, 2, 71, 86.6, 84.17, 0, 96, 43.21, -0.59, 1, 3, 73, 47.8, 21.16, 0.01161, 74, 29.02, 14.35, 0.32173, 96, 15.29, 7.74, 0.66667, 4, 72, 50.36, 15.88, 0.17404, 73, 27.47, 10, 0.17091, 74, 6.55, 8.64, 0.32173, 96, -6.68, 15.18, 0.33333, 4, 71, 47.47, 15.72, 0.33333, 72, 24.31, 5.99, 0.17404, 73, -0.13, 6.17, 0.17091, 74, -21.14, 11.84, 0.32173, 8, 5, 23.76, -131.29, 0.00619, 60, -107.48, -52.97, 0.00249, 64, -40.88, -88.3, 0.0033, 66, -11.37, -38.56, 0.09081, 70, 40.58, 3.39, 0.23056, 71, 15.13, 4.94, 0.33333, 72, -9.55, 9.91, 0.17404, 73, -32.26, 17.55, 0.1593, 6, 5, 24.55, -98.73, 0.00619, 60, -91.51, -24.59, 0.00249, 64, -53.82, -58.42, 0.0033, 66, -29.98, -11.83, 0.40269, 70, 9.89, 14.31, 0.25201, 71, -16.5, 12.71, 0.33333, 8, 5, 35.15, -127.19, 0.00619, 60, -95.5, -54.7, 0.00249, 64, -32.27, -79.81, 0.0033, 66, -4.61, -28.52, 0.56483, 67, -33.9, -31.75, 0.16436, 68, -60.12, -35.21, 0.00678, 95, -115.9, 35.37, 5e-05, 70, 40.24, 15.49, 0.25201, 6, 66, 31.61, -25.97, 0.47402, 67, 1.93, -25.91, 0.19589, 68, -24.67, -27.35, 0.27429, 69, -54.73, -12.36, 0.03228, 95, -83.13, 19.75, 0.00207, 70, 68.22, 38.63, 0.02145, 5, 66, 70.93, -23.82, 0.16214, 67, 40.9, -20.17, 0.19589, 68, 13.9, -19.41, 0.27429, 69, -15.49, -15.67, 0.36562, 95, -47.84, 2.26, 0.00207, 5, 67, 71.13, -8.63, 0.03153, 68, 43.43, -6.18, 0.26751, 69, 16.58, -11.34, 0.36562, 95, -16.42, -5.49, 0.33535, 96, 56.35, 2.26, 0, 3, 69, 57.07, 8.75, 0.33333, 95, 28.61, -1.67, 0.66667, 96, 100.9, -5.36, 0, 3, 69, 52.76, 15.48, 0.33333, 95, 27.08, 6.18, 0.66667, 96, 101.39, 2.62, 0, 5, 67, 66.48, 7.44, 0.02793, 68, 37.88, 9.6, 0.30141, 69, 15.72, 5.37, 0.33733, 95, -11.09, 10.37, 0.33333, 96, 65.5, 16.28, 0, 7, 64, 50.8, -60.4, 0.00531, 65, 66.29, -10.71, 0.00079, 66, 72.96, 6.97, 0.04854, 67, 40.11, 10.68, 0.29822, 68, 11.37, 11.34, 0.30981, 69, -9.21, 14.54, 0.33733, 96, 50.76, 38.37, 0, 10, 5, 105.09, -116.1, 0.00215, 60, -28.52, -77.7, 0.00222, 64, 26.57, -40.39, 0.11515, 65, 35.99, -19.04, 0.01818, 66, 45.25, 21.78, 0.24002, 67, 11.16, 22.89, 0.30728, 68, -18.23, 21.9, 0.30981, 69, -34.61, 33.04, 0.00399, 70, 51.53, 85.4, 0.0012, 96, 41.13, 68.29, 0, 9, 5, 80.93, -95.95, 0.00215, 60, -40.41, -48.58, 0.00222, 64, -3.81, -32.24, 0.30512, 65, 11.85, -39.2, 0.13363, 66, 13.85, 23.75, 0.26795, 67, -20.29, 21.99, 0.27934, 68, -49.57, 19.21, 0.00841, 70, 24.85, 68.74, 0.0012, 96, 19.94, 91.53, 0, 8, 5, 101.74, -88.61, 0.00215, 60, -18.59, -51.86, 0.00222, 64, 12, -16.85, 0.3197, 65, 8.35, -17.42, 0.44085, 66, 26.3, 41.96, 0.22483, 67, -9.55, 41.26, 0.00905, 70, 24.37, 90.8, 0.0012, 96, 41.55, 95.97, 0, 6, 43, -227.64, -142.09, 0, 64, 22.71, -14.79, 0.20986, 65, 12.82, -7.47, 0.42346, 94, -6.86, -5.35, 0.33333, 66, 36.39, 46.11, 0.03335, 96, 51.68, 91.93, 0, 6, 43, -242.35, -155.09, 0, 64, 37.93, -27.19, 0.0199, 65, 31.72, -2.15, 0.31254, 94, 12.66, -7.41, 0.66214, 66, 53.76, 36.97, 0.00543, 96, 57.82, 73.29, 0, 5, 43, -246.76, -179.48, 0, 64, 62.71, -27.57, 1e-05, 65, 46.28, 17.91, 0.0078, 94, 33.63, 5.82, 0.99219, 66, 78.13, 41.51, 0, 5, 64, 60.12, -20, 0.0001, 65, 38.6, 20.13, 0.0078, 94, 27.32, 10.73, 0.99209, 66, 74.08, 48.41, 0, 96, 80.38, 67.37, 0, 6, 43, -234.66, -162.78, 0, 64, 44.26, -18.35, 0.0001, 65, 28.13, 8.11, 0.33017, 94, 13.14, 3.45, 0.66973, 66, 58.22, 46.89, 0, 96, 67.91, 77.31, 0, 7, 43, -222.78, -154.36, 0, 60, 1.78, -64.04, 0.00404, 61, 4.4, -67.11, 0.00068, 64, 34.02, -8, 0.26242, 65, 13.77, 5.69, 0.39317, 94, -1.1, 6.52, 0.33968, 96, 64.87, 91.55, 0, 7, 43, -201.2, -148.71, 0, 60, 12.51, -44.49, 0.03735, 61, 7.2, -44.98, 0.0058, 64, 24.94, 12.37, 0.55723, 65, -8.12, 9.97, 0.39317, 94, -19.84, 18.61, 0.00644, 96, 68.2, 113.61, 0, 5, 60, 9.68, -31.23, 0.16436, 61, -0.29, -33.68, 0.02583, 64, 12.67, 18.14, 0.74352, 65, -19.89, 3.25, 0.06628, 96, 60.98, 125.08, 0, 5, 60, 2.2, -21.68, 0.26774, 61, -10.75, -27.54, 0.22908, 62, -12.53, -44.48, 0.00273, 64, 0.54, 17.97, 0.50046, 96, 50.67, 131.47, 0, 5, 60, 23.44, -15.71, 0.23443, 61, 6.82, -14.2, 0.3693, 62, -6.78, -23.18, 0.1904, 64, 8.64, 38.49, 0.20587, 96, 68.55, 144.39, 0, 7, 43, -162.96, -151.32, 0, 60, 40.69, -18.51, 0.10741, 61, 23.88, -10.47, 0.34927, 62, 4.44, -9.8, 0.35316, 63, -10.95, -16.77, 0.17057, 64, 21.29, 50.53, 0.01958, 96, 85.7, 147.71, 0, 8, 43, -163.42, -167.73, 0, 57, 52.6, -47.57, 0, 58, 22.92, -53.31, 0, 61, 38.93, -17.02, 0.14535, 62, 20.35, -5.78, 0.35044, 63, 1.29, -5.85, 0.5039, 64, 37.56, 52.74, 0.00031, 96, 100.59, 140.81, 0, 8, 6, 103.51, -57.22, 0, 43, -175.55, -189.2, 0, 57, 73.54, -60.6, 0, 58, 40.79, -70.31, 0, 61, 54.34, -36.27, 0.00019, 62, 44.31, -11.6, 0.36815, 63, 25.23, 0.08, 0.63166, 96, 115.53, 121.19, 0, 8, 6, 93.99, -30.53, 0.00222, 43, -152.94, -172.12, 0, 57, 57.44, -37.29, 8e-05, 58, 29.74, -44.22, 6e-05, 61, 46.92, -8.93, 0.13365, 62, 21.73, 5.5, 0.4029, 63, -2.71, 4.79, 0.46109, 96, 108.76, 148.7, 0, 6, 6, 78.59, -15.43, 0.07143, 57, 38.66, -26.69, 0.00067, 58, 13.49, -30.04, 0.00048, 61, 32.74, 7.32, 0.39483, 62, 0.57, 9.69, 0.40483, 63, -23.4, -1.28, 0.12776, 7, 6, 61.8, -7.18, 0.33547, 43, -140.31, -134.42, 0, 57, 20.3, -23.06, 0.00067, 58, -3.75, -22.77, 0.00048, 60, 43.97, 9.57, 0.01545, 61, 16.64, 16.85, 0.44848, 62, -17.99, 7.39, 0.19945, 8, 6, 49.7, -8.37, 0.65608, 43, -145.07, -123.22, 0, 57, 8.92, -27.34, 0.00668, 58, -15.77, -24.66, 0.00484, 60, 32.57, 13.81, 0.01545, 61, 4.48, 16.61, 0.31502, 62, -27.47, -0.23, 0.00193, 96, 66.94, 175.24, 0, 8, 6, 56.71, 1.04, 0.6282, 43, -133.99, -127.09, 0, 57, 13.25, -16.44, 0.04495, 58, -9.32, -14.86, 0.2423, 60, 42.99, 19.19, 0.01885, 61, 12.2, 25.44, 0.0657, 62, -26.76, 11.48, 0, 96, 74.87, 183.89, 0, 7, 6, 70.29, 3.4, 0.36698, 57, 25.76, -10.65, 0.04495, 58, 4.11, -11.72, 0.54121, 60, 56.23, 15.36, 0.01045, 61, 25.92, 26.74, 0.03641, 62, -16.69, 20.89, 1e-05, 96, 88.62, 184.86, 0, 7, 6, 91.47, -2.06, 0.06187, 57, 47.63, -10.43, 0.04048, 58, 25.57, -15.94, 0.82592, 60, 72.88, 1.18, 0.01599, 61, 46.61, 19.66, 0.05574, 62, 4.01, 27.94, 1e-05, 96, 109.14, 177.29, 0, 7, 6, 80.72, 8.92, 0.03715, 57, 34.41, -2.62, 0.06773, 58, 14.2, -5.61, 0.83415, 60, 68.03, 15.76, 0.01359, 61, 36.75, 31.43, 0.04738, 62, -10.99, 31.23, 1e-05, 96, 99.56, 189.29, 0, 8, 6, 68.86, 12.86, 0.03432, 57, 21.93, -1.88, 0.06773, 58, 2.13, -2.36, 0.55987, 59, -15.93, 1.19, 0.3087, 60, 59.09, 24.49, 0.00655, 61, 25.23, 36.28, 0.02282, 62, -23.07, 28.02, 0, 96, 88.16, 194.41, 0, 8, 6, 86.45, 13.78, 0.01661, 57, 38.68, 3.56, 0.06669, 58, 19.64, -0.42, 0.53167, 59, 1.57, -0.79, 0.38053, 60, 75.31, 17.62, 0.001, 61, 42.84, 35.83, 0.0035, 62, -8.87, 38.43, 0, 96, 105.75, 193.55, 0, 6, 52, 91.69, -19.91, 2e-05, 53, 67.92, -20.41, 4e-05, 57, 54.71, 13.2, 0.00225, 58, 37.28, 5.77, 0.61716, 59, 20.14, 1.36, 0.38053, 96, 124.15, 196.86, 0, 6, 52, 103.74, -2.65, 2e-05, 53, 80.1, -3.23, 4e-05, 57, 68.88, 28.77, 0.00311, 58, 54.31, 18.15, 0.86859, 59, 39.49, 9.67, 0.12823, 96, 142.93, 206.37, 0, 6, 52, 87.55, -11.99, 0.0001, 53, 63.84, -12.46, 0.00036, 57, 51.62, 21.58, 0.00254, 58, 35.95, 14.61, 0.75388, 59, 20.8, 10.27, 0.24311, 96, 124.25, 205.79, 0, 6, 52, 74.89, -19.7, 0.01422, 53, 51.12, -20.07, 0.01635, 57, 38.07, 15.57, 0.22086, 58, 21.47, 11.46, 0.50546, 59, 5.98, 10.39, 0.24311, 96, 109.45, 204.98, 0, 8, 5, 131.75, 39.18, 0.00038, 6, 66.92, 36.87, 0.00104, 52, 50.18, -17.62, 0.15748, 53, 26.43, -17.81, 0.141, 57, 13.84, 20.8, 0.28398, 58, -1.21, 21.49, 0.2294, 59, -13.92, 25.19, 0.18671, 96, 88.65, 218.5, 0, 9, 5, 111.27, 56.27, 0.00038, 6, 47.04, 54.66, 0.00105, 52, 25.03, -8.74, 0.15823, 53, 1.34, -8.75, 0.30818, 54, -24.77, 3.85, 0.16494, 57, -9.96, 32.85, 0.28404, 58, -22.08, 38.11, 0.08318, 70, -110.3, 145.06, 0, 96, 70.69, 238.21, 0, 9, 5, 136.66, 49.2, 0.00038, 6, 72.18, 46.71, 0.00105, 52, 51.3, -6.51, 0.14409, 53, 27.62, -6.72, 0.29278, 54, -0.85, -7.22, 0.38935, 55, -23.89, 6.34, 0.10829, 57, 16.37, 31.68, 0.06405, 70, -95.66, 166.98, 0, 96, 94.88, 227.76, 0, 10, 5, 160.27, 54.02, 0, 6, 95.94, 50.7, 1e-05, 52, 71.75, 6.23, 0.00083, 53, 48.17, 5.88, 0.16812, 54, 23.23, -6.28, 0.38935, 55, -2.94, -5.57, 0.21052, 56, -22.68, 0.09, 0.2311, 57, 38.29, 41.68, 6e-05, 70, -92.87, 190.92, 0, 96, 118.93, 229.32, 0, 10, 5, 179.6, 69.69, 0, 6, 115.8, 65.69, 0, 52, 84.4, 27.65, 0, 53, 60.98, 27.21, 0.00063, 54, 44.83, 6.07, 0.22441, 55, 21.92, -6.48, 0.21058, 56, 1.17, -7, 0.56438, 57, 53.59, 61.3, 0, 70, -101.73, 214.17, 0, 96, 140.21, 242.22, 0, 9, 5, 187.93, 91.18, 0, 6, 124.88, 86.87, 0, 52, 84.73, 50.69, 0, 53, 61.47, 50.25, 0, 55, 42.34, 4.21, 0.10246, 56, 23.61, -1.73, 0.89754, 57, 56.88, 84.11, 0, 70, -119.54, 228.79, 0, 96, 151.38, 262.37, 0, 7, 6, 125.25, 94.47, 0, 52, 82.17, 57.86, 0, 53, 58.97, 57.43, 0, 55, 47.42, 9.88, 0.00053, 56, 29.94, 2.48, 0.99947, 57, 55.27, 91.55, 0, 96, 152.52, 269.9, 0, 6, 6, 117.54, 90.59, 0, 52, 76.52, 51.34, 0, 53, 53.27, 50.95, 0, 55, 38.99, 11.73, 0.12335, 56, 22.25, 6.39, 0.87665, 57, 48.82, 85.8, 0, 7, 6, 106.1, 73.96, 0, 52, 72.28, 31.6, 1e-05, 53, 48.89, 31.24, 0, 54, 36.26, 15.5, 0.20767, 55, 19.62, 6.05, 0.24883, 56, 2.07, 5.72, 0.54349, 57, 42.08, 66.78, 0, 5, 52, 61.98, 15.71, 1e-05, 53, 38.47, 15.43, 0.20878, 54, 19.44, 6.8, 0.33222, 55, 0.74, 7.54, 0.24853, 56, -15.84, 11.87, 0.21046, 5, 52, 44.68, 8.22, 0.32245, 53, 21.12, 8.06, 0.21968, 54, 0.7, 8.85, 0.33222, 55, -14.09, 19.17, 0.12565, 70, -111.3, 171, 0, 9, 5, 105.33, 78.82, 0.00545, 6, 41.9, 77.4, 0.00077, 48, 25.88, -62.15, 0.09849, 49, 27.54, -55.77, 0.03045, 52, 11.61, 10.33, 0.52059, 53, -11.94, 10.42, 0.2197, 54, -26.99, 27.06, 0.12455, 70, -133.58, 146.46, 0, 96, 67.87, 261.36, 0, 8, 5, 80.16, 89.25, 0.00832, 6, 17.1, 88.7, 0.00101, 48, 16.08, -36.73, 0.25912, 49, 8.18, -36.59, 0.10172, 52, -15.62, 11.33, 0.61891, 53, -39.16, 11.62, 0.01092, 70, -151.34, 125.8, 0, 96, 44.35, 275.11, 0, 9, 5, 72.83, 97.13, 0.00832, 6, 10.05, 96.84, 0.00101, 48, 16.75, -25.98, 0.31954, 49, 4.38, -26.52, 0.33523, 50, -15.96, -27.94, 0.02527, 52, -25.24, 16.17, 0.3106, 53, -48.75, 16.53, 3e-05, 70, -161.12, 121.3, 0, 96, 38.16, 283.92, 0, 8, 5, 78.02, 114.59, 0.00287, 6, 15.85, 114.11, 0.00024, 48, 32.97, -17.71, 0.22177, 49, 15.79, -12.32, 0.30478, 50, -5.77, -12.85, 0.35773, 52, -26.46, 34.34, 0.1126, 53, -49.83, 34.71, 1e-05, 96, 45.67, 300.51, 0, 7, 48, 51.55, -19.8, 0.06114, 49, 33.59, -6.61, 0.23351, 50, 11.5, -5.67, 0.35773, 51, -6.59, -5.79, 0.33333, 52, -17.22, 50.6, 0.01429, 53, -40.47, 50.9, 0, 96, 61.5, 310.47, 0, 8, 42, -4.46, -71.73, 0, 48, 67.25, -28.67, 0.00071, 49, 51.54, -8.27, 0, 50, 29.52, -5.83, 0.3342, 51, 11.44, -5.64, 0.66493, 52, -2.89, 61.53, 0.00017, 53, -26.07, 61.73, 0, 96, 79.29, 313.34, 0, 5, 42, 10.38, -86.01, 0, 49, 69.92, 1.04, 1e-05, 50, 47.06, 4.98, 0.00174, 51, 28.78, 5.47, 0.99825, 96, 94.77, 326.93, 0, 5, 42, 3.72, -68.72, 0.00013, 49, 51.4, 0.45, 0.01813, 50, 28.65, 2.84, 0.31682, 51, 10.42, 3.02, 0.66492, 96, 76.98, 321.74, 0, 5, 42, 5.36, -49.32, 0.04832, 49, 33.61, 8.38, 0.28884, 50, 10.27, 9.27, 0.32948, 51, -8.07, 9.13, 0.33335, 96, 57.78, 325, 0, 5, 42, 6.66, -24.21, 0.31431, 48, 40.38, 12.06, 0.02603, 49, 10.34, 17.87, 0.33015, 50, -13.71, 16.79, 0.32948, 51, -32.18, 16.24, 3e-05], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 0, 192]}}, "xx": {"xx": {"type": "mesh", "hull": 27, "width": 106, "height": 106, "uvs": [1, 1, 1, 0.87979, 0.95734, 0.72195, 0.83458, 0.62469, 0.8011, 0.53063, 0.79472, 0.34552, 0.68631, 0.21319, 0.58268, 0.2387, 0.49977, 0.13826, 0.42165, 0, 0.36585, 0, 0.41049, 0.11275, 0.39454, 0.17493, 0.28613, 0.09521, 0.13307, 0.13028, 0, 0.27856, 0, 0.40451, 0.11075, 0.29769, 0.1825, 0.30885, 0.2383, 0.41089, 0.2654, 0.61177, 0.33715, 0.72178, 0.51093, 0.72178, 0.64485, 0.67076, 0.6895, 0.8573, 0.80748, 0.90992, 0.91111, 0.90992, 0.61466, 0.51762, 0.4476, 0.37481], "triangles": [11, 10, 9, 11, 9, 8, 12, 11, 8, 17, 15, 14, 18, 14, 13, 18, 13, 12, 17, 14, 18, 7, 28, 12, 7, 12, 8, 19, 18, 12, 16, 15, 17, 28, 19, 12, 5, 27, 7, 5, 7, 6, 28, 7, 27, 27, 5, 4, 20, 19, 28, 20, 28, 27, 23, 27, 4, 23, 4, 3, 22, 21, 20, 20, 27, 22, 23, 22, 27, 24, 23, 3, 25, 24, 3, 2, 25, 3, 2, 26, 25, 1, 26, 2, 26, 1, 0], "vertices": [1, 13, -7.18, 5.59, 1, 2, 13, 2.51, -2.69, 0.99774, 14, -26.34, -4.74, 0.00226, 2, 13, 18.17, -10.11, 0.68831, 14, -10.15, -10.93, 0.31169, 3, 13, 34.46, -6.91, 0.35498, 14, 5.85, -6.48, 0.63014, 15, -20.34, -0.31, 0.01488, 4, 13, 44.35, -10.69, 0.02391, 14, 16, -9.48, 0.68093, 15, -11.58, -6.26, 0.29016, 16, -32.82, -4.15, 0.00499, 3, 14, 32.26, -20.48, 0.37231, 15, 0.57, -21.68, 0.55549, 16, -21.69, -20.32, 0.0722, 3, 14, 50.36, -19.44, 0.05386, 15, 18.14, -26.19, 0.68224, 16, -4.45, -25.96, 0.2639, 3, 14, 54.64, -8.97, 0.0008, 15, 25.39, -17.51, 0.41402, 16, 3.35, -17.76, 0.58518, 2, 15, 38.78, -20.9, 0.1487, 16, 16.49, -22.01, 0.8513, 2, 15, 54.12, -27.82, 0.00706, 16, 31.36, -29.9, 0.99294, 1, 16, 36.34, -26.71, 1, 2, 15, 48.01, -17.48, 4e-05, 16, 25.92, -19.19, 0.99996, 3, 15, 45.47, -11.17, 4e-05, 16, 23.8, -12.73, 0.87512, 17, -26.69, -0.71, 0.12484, 3, 15, 59.74, -11.18, 4e-05, 16, 38.03, -13.66, 0.54435, 17, -12.64, -12.22, 0.45561, 2, 16, 49.7, -1.79, 0.21106, 17, 15.01, -13.54, 0.78894, 2, 16, 53.13, 19.04, 0.00256, 17, 45.3, -2.78, 0.99744, 2, 16, 45.94, 30.3, 0.00033, 17, 51.98, 9.96, 0.99967, 3, 15, 62.03, 17.12, 8e-05, 16, 42.14, 14.43, 0.06678, 17, 27.65, 2.68, 0.93314, 4, 14, 73.59, 29.7, 7e-05, 15, 55.2, 13.57, 0.01569, 16, 35.1, 11.34, 0.33587, 17, 16.15, 6.09, 0.64837, 4, 14, 61.36, 31.29, 0.01237, 15, 44.03, 18.8, 0.1344, 16, 24.29, 17.27, 0.53747, 17, 12.16, 18.18, 0.31576, 4, 14, 42.46, 41.49, 0.04089, 15, 29.12, 34.27, 0.29166, 16, 10.4, 33.67, 0.61848, 17, 18.24, 39.35, 0.04896, 4, 14, 28.55, 42.21, 0.12776, 15, 16.09, 39.17, 0.45384, 16, -2.28, 39.4, 0.41801, 17, 11.98, 52.76, 0.0004, 4, 13, 48.91, 25.86, 0.00653, 14, 17.71, 27.31, 0.37308, 15, 1.24, 28.28, 0.39697, 16, -17.81, 29.48, 0.22342, 4, 13, 43.81, 11.55, 0.23376, 14, 13.73, 12.65, 0.45066, 15, -7.01, 15.52, 0.23963, 16, -26.86, 17.28, 0.07595, 4, 13, 25.7, 20.79, 0.55229, 14, -5.04, 20.46, 0.37853, 15, -22.52, 28.66, 0.06184, 16, -41.49, 31.4, 0.00734, 2, 13, 13.34, 14.91, 0.87909, 14, -16.91, 13.64, 0.12091, 2, 13, 6.2, 6.55, 0.9852, 14, -23.37, 4.75, 0.0148, 3, 14, 28.74, 5.69, 0.11599, 15, 5.17, 4.32, 0.87582, 16, -15.42, 5.33, 0.00819, 2, 15, 28.4, 2.59, 0.00184, 16, 7.65, 2.1, 0.99816], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52]}}, "xx2": {"xx2": {"type": "mesh", "hull": 28, "width": 106, "height": 107, "uvs": [0, 0.99757, 0, 0.85783, 0.03678, 0.70479, 0.16273, 0.61329, 0.19631, 0.50682, 0.21143, 0.30054, 0.30546, 0.20739, 0.41293, 0.24066, 0.48881, 0.14214, 0.56773, 0, 0.64498, 0, 0.60971, 0.16044, 0.72054, 0.09723, 0.86832, 0.12883, 1, 0.28687, 1, 0.395, 0.91701, 0.31681, 0.83137, 0.30351, 0.75749, 0.40332, 0.73734, 0.58086, 0.69368, 0.68733, 0.59124, 0.72226, 0.45354, 0.69232, 0.37798, 0.65739, 0.31417, 0.69897, 0.3192, 0.84537, 0.24028, 0.89195, 0.09587, 0.90026, 0.40328, 0.4703], "triangles": [11, 9, 10, 8, 9, 11, 17, 12, 13, 16, 17, 13, 11, 12, 17, 14, 16, 13, 16, 14, 15, 18, 11, 17, 7, 5, 6, 28, 5, 7, 4, 5, 28, 18, 7, 11, 23, 4, 28, 24, 3, 4, 7, 8, 11, 18, 28, 7, 19, 28, 18, 28, 21, 22, 22, 23, 28, 21, 28, 19, 23, 24, 4, 20, 21, 19, 26, 3, 24, 26, 24, 25, 2, 3, 26, 27, 1, 2, 26, 27, 2, 0, 1, 27], "vertices": [2, 18, -8.77, -2.27, 0.9989, 19, -34.41, -12.79, 0.0011, 2, 18, 3.64, 6.07, 0.99767, 19, -24.95, -1.21, 0.00233, 3, 18, 19.41, 11.97, 0.68368, 19, -11.57, 9, 0.31518, 20, -35.05, 12.76, 0.00114, 3, 18, 34.98, 6.35, 0.35034, 19, 4.96, 8.14, 0.56408, 20, -18.7, 10.16, 0.08558, 4, 18, 46.42, 9.75, 0.01934, 19, 14.93, 14.71, 0.59797, 20, -8.1, 15.65, 0.36935, 21, -34.15, 14.39, 0.01335, 3, 19, 30.13, 30.78, 0.29376, 20, 8.72, 30.03, 0.64415, 21, -17.88, 29.39, 0.0621, 3, 19, 44.16, 32.2, 0.04578, 20, 22.82, 29.96, 0.73126, 21, -3.79, 29.84, 0.22296, 3, 19, 50.73, 22.23, 0.00956, 20, 28.3, 19.36, 0.46339, 21, 2.08, 19.45, 0.52705, 3, 19, 63.62, 25.31, 0.00092, 20, 41.45, 21.06, 0.18745, 21, 15.16, 21.64, 0.81164, 2, 20, 58.14, 25.81, 0.01589, 21, 31.67, 27, 0.98411, 2, 20, 63.9, 20, 0.00036, 21, 37.64, 21.4, 0.99964, 3, 20, 49.07, 10.58, 0.00036, 21, 23.17, 11.44, 0.87072, 22, -24, -4.87, 0.12892, 3, 20, 62.14, 6.98, 0.00036, 21, 36.36, 8.33, 0.53739, 22, -11.49, 6.66, 0.46225, 2, 21, 45.47, -4.85, 0.20442, 22, 15.49, 11.11, 0.79558, 1, 22, 51.08, 2.84, 1, 2, 21, 36.17, -35.17, 0.00031, 22, 60.7, -7.35, 0.99969, 3, 20, 60.1, -24.35, 0.00066, 21, 35.48, -23.05, 0.06095, 22, 40.17, -4.15, 0.93839, 3, 20, 54.72, -16.89, 0.04443, 21, 29.83, -15.8, 0.30072, 22, 24.98, -7.2, 0.65485, 3, 20, 41.62, -18.84, 0.25898, 21, 16.81, -18.23, 0.41827, 22, 21.77, -20.32, 0.32275, 4, 19, 54.32, -27.71, 0.00259, 20, 26.62, -30.69, 0.52272, 21, 2.25, -30.62, 0.42398, 22, 34.26, -38.06, 0.05071, 4, 19, 43.53, -33.61, 0.01711, 20, 15.27, -35.41, 0.75976, 21, -8.92, -35.76, 0.22221, 22, 36.59, -50.28, 0.00092, 3, 19, 32.76, -29.63, 0.10723, 20, 4.97, -30.32, 0.78131, 21, -19.39, -31.06, 0.11146, 4, 18, 45.16, -23.95, 0.00017, 19, 23.48, -17.92, 0.35288, 20, -3.02, -17.7, 0.60153, 21, -27.84, -18.74, 0.04541, 4, 18, 43.79, -15.22, 0.03052, 19, 19.64, -9.95, 0.64077, 20, -6, -9.37, 0.32131, 21, -31.13, -10.53, 0.00741, 4, 18, 36.32, -12.09, 0.23893, 19, 11.59, -9.12, 0.67557, 20, -13.92, -7.7, 0.08521, 21, -39.1, -9.15, 0.00029, 3, 18, 23.62, -21.27, 0.50761, 19, 2.09, -21.59, 0.4918, 20, -24.68, -19.1, 0.00058, 2, 18, 14.82, -17.11, 0.80951, 19, -7.54, -20.15, 0.19049, 2, 18, 5.54, -4.9, 0.93443, 19, -19.96, -11.16, 0.06557, 2, 19, 34.39, 3.85, 0.00222, 20, 10.12, 2.8, 0.99778], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54]}}, "zheng_2": {"lll": {"type": "mesh", "hull": 36, "width": 195, "height": 173, "uvs": [0, 0.03781, 0.05979, 0.05786, 0.0828, 0.13096, 0.0284, 0.25478, 0.04096, 0.36562, 0.13929, 0.4293, 0.14557, 0.6333, 0.25019, 0.75711, 0.26692, 0.87975, 0.38409, 1, 0.42803, 1, 0.43068, 0.93219, 0.50307, 0.92144, 0.57, 0.93718, 0.58705, 1, 0.63622, 1, 0.74292, 0.86678, 0.75862, 0.74414, 0.86846, 0.62151, 0.85591, 0.46939, 0.9668, 0.3444, 0.96889, 0.23945, 0.91868, 0.1062, 1, 0.03427, 1, 0, 0.93646, 0, 0.79209, 0.08064, 0.75339, 0.16082, 0.63308, 0.21035, 0.66028, 0.11483, 0.46046, 0.24454, 0.36003, 0.11012, 0.22299, 0.11601, 0.13616, 0.02403, 0.05142, 0, 0, 0, 0.33669, 0.57034, 0.32063, 0.51371, 0.29163, 0.46, 0.23983, 0.40687, 0.22637, 0.4565, 0.2331, 0.5248, 0.26107, 0.56217, 0.38641, 0.5429, 0.43147, 0.49619, 0.44546, 0.4428, 0.44183, 0.37799, 0.42629, 0.33071, 0.35875, 0.61357, 0.386, 0.56331, 0.3893, 0.61822, 0.38765, 0.66289, 0.36123, 0.71687, 0.37031, 0.77922, 0.41903, 0.82907, 0.457, 0.87281, 0.54039, 0.87654, 0.58746, 0.82442, 0.63452, 0.77975, 0.6403, 0.71181, 0.61305, 0.66528, 0.61883, 0.62433, 0.65237, 0.60952, 0.61687, 0.55834, 0.4014, 0.76244, 0.43713, 0.78567, 0.5551, 0.80326, 0.59015, 0.76608, 0.50287, 0.80633, 0.50219, 0.70486, 0.50081, 0.5972, 0.49875, 0.53617, 0.45544, 0.55991, 0.55099, 0.55546, 0.45623, 0.60174, 0.54468, 0.59818, 0.50045, 0.4789, 0.50124, 0.3881, 0.44122, 0.51629, 0.54941, 0.51095, 0.26473, 0.63228, 0.31652, 0.74187, 0.36447, 0.85969, 0.38725, 0.92689, 0.45251, 0.88861, 0.50227, 0.88327, 0.54388, 0.88862, 0.6129, 0.91729, 0.63365, 0.85515, 0.69245, 0.76258, 0.72978, 0.63318, 0.66595, 0.56502, 0.68843, 0.50246, 0.72145, 0.4494, 0.7622, 0.40901, 0.77415, 0.46999, 0.76923, 0.53017, 0.72216, 0.56502, 0.61575, 0.52774, 0.56554, 0.49275, 0.56007, 0.39707, 0.57741, 0.33842, 0.63476, 0.46971, 0.70189, 0.3869, 0.79943, 0.32408, 0.84503, 0.23271, 0.34723, 0.4483, 0.29023, 0.37976, 0.19776, 0.32551, 0.16229, 0.26983], "triangles": [25, 24, 23, 0, 35, 34, 1, 34, 33, 0, 34, 1, 22, 26, 25, 22, 25, 23, 2, 1, 33, 2, 33, 32, 105, 26, 22, 27, 26, 105, 105, 22, 21, 28, 30, 29, 109, 2, 32, 3, 2, 109, 104, 27, 105, 28, 27, 104, 108, 109, 32, 47, 31, 30, 101, 30, 28, 103, 101, 28, 77, 47, 30, 20, 105, 21, 104, 105, 20, 4, 3, 109, 77, 46, 47, 31, 108, 32, 107, 31, 47, 107, 108, 31, 104, 103, 28, 30, 101, 77, 100, 77, 101, 102, 100, 101, 39, 108, 107, 94, 103, 104, 5, 4, 109, 5, 109, 108, 5, 108, 39, 45, 46, 77, 106, 107, 47, 106, 47, 46, 106, 46, 45, 93, 103, 94, 40, 5, 39, 38, 107, 106, 39, 107, 38, 40, 39, 38, 19, 104, 20, 94, 104, 19, 103, 102, 101, 102, 103, 93, 95, 94, 19, 93, 94, 95, 76, 45, 77, 76, 77, 100, 99, 76, 100, 102, 99, 100, 44, 106, 45, 44, 45, 76, 92, 102, 93, 79, 76, 99, 37, 38, 106, 43, 37, 106, 78, 44, 76, 41, 40, 38, 41, 38, 37, 98, 99, 102, 98, 102, 92, 96, 93, 95, 92, 93, 96, 71, 78, 76, 71, 76, 79, 44, 43, 106, 43, 44, 78, 98, 73, 79, 98, 79, 99, 71, 79, 73, 91, 63, 98, 73, 98, 63, 72, 78, 71, 43, 78, 72, 42, 41, 37, 43, 36, 37, 49, 43, 72, 92, 91, 98, 97, 92, 96, 91, 92, 97, 49, 36, 43, 42, 37, 36, 70, 71, 73, 72, 71, 70, 75, 70, 73, 74, 72, 70, 49, 72, 74, 62, 63, 91, 48, 36, 49, 50, 49, 74, 48, 49, 50, 61, 63, 62, 80, 42, 36, 80, 36, 48, 90, 97, 96, 96, 95, 19, 18, 96, 19, 41, 6, 5, 41, 5, 40, 6, 41, 42, 6, 42, 80, 51, 48, 50, 75, 63, 61, 63, 75, 73, 60, 75, 61, 69, 70, 75, 69, 75, 60, 74, 70, 69, 62, 60, 61, 59, 60, 62, 62, 91, 97, 90, 62, 97, 59, 62, 90, 52, 48, 51, 80, 48, 52, 81, 80, 52, 18, 17, 90, 18, 90, 96, 89, 59, 90, 7, 6, 80, 7, 80, 81, 51, 74, 69, 74, 51, 50, 64, 51, 69, 52, 51, 64, 17, 89, 90, 67, 69, 60, 67, 60, 59, 53, 52, 64, 81, 52, 53, 58, 67, 59, 58, 59, 89, 65, 64, 69, 66, 69, 67, 68, 65, 69, 66, 68, 69, 57, 66, 67, 57, 67, 58, 54, 64, 65, 53, 64, 54, 88, 57, 58, 82, 81, 53, 82, 53, 54, 16, 89, 17, 55, 65, 68, 54, 65, 55, 56, 68, 66, 56, 66, 57, 85, 55, 68, 8, 7, 81, 8, 81, 82, 56, 85, 68, 84, 54, 55, 86, 56, 57, 87, 57, 88, 86, 57, 87, 86, 12, 85, 86, 85, 56, 84, 83, 82, 84, 82, 54, 11, 83, 84, 84, 55, 85, 84, 85, 12, 11, 84, 12, 13, 86, 87, 12, 86, 13, 88, 58, 89, 83, 8, 82, 9, 83, 11, 9, 8, 83, 10, 9, 11, 14, 13, 87, 16, 88, 89, 16, 87, 88, 15, 87, 16, 14, 87, 15], "vertices": [1, 3, 151.99, 96.27, 1, 1, 3, 148.36, 84.66, 1, 1, 3, 135.66, 80.34, 1, 1, 3, 114.38, 91.23, 1, 1, 3, 95.17, 89.04, 1, 1, 3, 83.9, 70.01, 1, 1, 3, 48.6, 69.26, 1, 2, 3, 26.91, 49.15, 0.99978, 41, 3.74, 49.21, 0.00022, 2, 3, 5.65, 46.16, 0.99912, 41, -17.52, 46.23, 0.00088, 1, 3, -15.46, 23.6, 1, 1, 3, -15.57, 15.03, 1, 1, 3, -3.85, 14.36, 1, 1, 3, -2.18, 0.22, 1, 1, 3, -5.07, -12.8, 1, 1, 3, -15.99, -15.98, 1, 1, 3, -16.11, -25.56, 1, 1, 3, 6.65, -46.68, 1, 1, 3, 27.83, -50.02, 1, 2, 3, 48.76, -71.72, 0.99528, 41, 25.59, -71.66, 0.00472, 2, 3, 75.1, -69.62, 0.99952, 41, 51.94, -69.56, 0.00048, 1, 3, 96.44, -91.53, 1, 1, 3, 114.59, -92.18, 1, 1, 3, 137.77, -82.7, 1, 1, 3, 150, -98.72, 1, 1, 3, 155.93, -98.8, 1, 1, 3, 156.09, -86.41, 1, 2, 3, 142.52, -58.08, 0.95, 41, 119.35, -58.01, 0.05, 2, 3, 128.75, -50.35, 0.95, 41, 105.58, -50.28, 0.05, 2, 3, 120.49, -26.77, 0.95, 41, 97.33, -26.71, 0.05, 2, 3, 136.94, -32.3, 0.95, 41, 113.78, -32.23, 0.05, 2, 3, 115.03, 6.96, 0.95, 41, 91.86, 7.03, 0.05, 2, 3, 138.54, 26.23, 0.95, 41, 115.38, 26.3, 0.05, 2, 3, 137.88, 52.97, 0.95, 41, 114.71, 53.03, 0.05, 1, 3, 154.01, 69.69, 1, 1, 3, 158.39, 86.16, 1, 1, 3, 158.53, 96.18, 1, 2, 3, 58.99, 31.85, 0.95, 41, 35.83, 31.91, 0.05, 2, 3, 68.83, 34.85, 0.95, 41, 45.66, 34.91, 0.05, 2, 3, 78.19, 40.38, 0.95, 41, 55.03, 40.44, 0.05, 2, 3, 87.52, 50.36, 0.95, 41, 64.36, 50.42, 0.05, 2, 3, 78.97, 53.1, 0.95, 41, 55.81, 53.16, 0.05, 2, 3, 67.14, 51.94, 0.95, 41, 43.97, 52.01, 0.05, 2, 3, 60.6, 46.57, 0.95, 41, 37.44, 46.64, 0.05, 2, 3, 63.61, 22.09, 0.95, 41, 40.44, 22.15, 0.05, 2, 3, 71.57, 13.2, 0.95, 41, 48.41, 13.26, 0.05, 2, 3, 80.77, 10.35, 0.95, 41, 57.61, 10.41, 0.05, 2, 3, 91.99, 10.9, 0.95, 41, 68.83, 10.97, 0.05, 2, 3, 100.21, 13.82, 0.95, 41, 77.05, 13.89, 0.05, 2, 3, 51.46, 27.65, 0.85, 41, 28.29, 27.71, 0.15, 2, 3, 60.08, 22.22, 0.95, 41, 36.91, 22.28, 0.05, 2, 3, 50.57, 21.7, 0.85, 41, 27.41, 21.76, 0.15, 2, 3, 42.85, 22.12, 0.85, 41, 19.68, 22.19, 0.15, 2, 3, 33.58, 27.4, 0.85, 41, 10.42, 27.47, 0.15, 2, 3, 22.77, 25.77, 0.8, 41, -0.39, 25.84, 0.2, 2, 3, 14.02, 16.39, 0.8, 41, -9.15, 16.46, 0.2, 2, 3, 6.35, 9.09, 0.8, 41, -16.81, 9.15, 0.2, 2, 3, 5.49, -7.16, 0.8, 41, -17.67, -7.1, 0.2, 2, 3, 14.39, -16.46, 0.8, 41, -8.78, -16.4, 0.2, 2, 3, 21.99, -25.74, 0.8, 41, -1.17, -25.68, 0.2, 2, 3, 33.73, -27.02, 0.85, 41, 10.56, -26.96, 0.15, 2, 3, 41.85, -21.82, 0.85, 41, 18.68, -21.75, 0.15, 2, 3, 48.92, -23.04, 0.85, 41, 25.75, -22.98, 0.15, 2, 3, 51.39, -29.61, 0.85, 41, 28.23, -29.55, 0.15, 2, 3, 60.34, -22.81, 0.95, 41, 37.17, -22.74, 0.05, 2, 3, 25.59, 19.67, 0.8, 41, 2.43, 19.74, 0.2, 2, 3, 21.48, 12.76, 0.8, 41, -1.69, 12.82, 0.2, 2, 3, 18.13, -10.2, 0.8, 41, -5.03, -10.14, 0.2, 2, 3, 24.47, -17.12, 0.8, 41, 1.31, -17.06, 0.2, 2, 3, 17.74, -0.01, 0.85, 41, -5.43, 0.05, 0.15, 1, 3, 35.29, -0.11, 1, 2, 3, 53.92, -0.09, 0.9, 41, 30.75, -0.03, 0.1, 2, 3, 64.48, 0.17, 0.9, 41, 41.32, 0.23, 0.1, 2, 3, 60.49, 8.67, 0.9, 41, 37.32, 8.73, 0.1, 2, 3, 61.01, -9.97, 0.9, 41, 37.84, -9.91, 0.1, 2, 3, 53.25, 8.61, 0.9, 41, 30.08, 8.68, 0.1, 2, 3, 53.63, -8.64, 0.9, 41, 30.47, -8.58, 0.1, 2, 3, 74.38, -0.29, 0.95, 41, 51.22, -0.23, 0.05, 2, 3, 90.09, -0.66, 0.95, 41, 66.92, -0.59, 0.05, 2, 3, 68.07, 11.34, 0.95, 41, 44.9, 11.41, 0.05, 2, 3, 68.71, -9.77, 0.95, 41, 45.55, -9.7, 0.05, 2, 3, 48.46, 46.02, 0.95, 41, 25.3, 46.09, 0.05, 2, 3, 29.37, 36.18, 0.94886, 41, 6.21, 36.24, 0.05114, 2, 3, 8.87, 27.1, 0.95, 41, -14.3, 27.16, 0.05, 1, 3, -2.82, 22.81, 1, 1, 3, 3.63, 10, 1, 1, 3, 4.43, 0.28, 1, 1, 3, 3.39, -7.82, 1, 1, 3, -1.74, -21.21, 1, 2, 3, 8.95, -25.4, 0.95, 41, -14.21, -25.33, 0.05, 2, 3, 24.81, -37.08, 0.95, 41, 1.65, -37.01, 0.05, 2, 3, 47.1, -44.65, 0.95, 41, 23.93, -44.59, 0.05, 2, 3, 59.06, -32.36, 0.95, 41, 35.89, -32.3, 0.05, 2, 3, 69.82, -36.89, 0.94953, 41, 46.65, -36.83, 0.05047, 2, 3, 78.91, -43.45, 0.94987, 41, 55.75, -43.39, 0.05013, 2, 3, 85.79, -51.49, 0.94996, 41, 62.63, -51.43, 0.05004, 2, 3, 75.21, -53.68, 0.94952, 41, 52.05, -53.62, 0.05048, 2, 3, 64.81, -52.58, 0.95, 41, 41.65, -52.52, 0.05, 2, 3, 58.91, -43.32, 0.94714, 41, 35.74, -43.26, 0.05286, 2, 3, 65.63, -22.66, 0.95, 41, 42.47, -22.6, 0.05, 2, 3, 71.82, -12.95, 0.95, 41, 48.65, -12.89, 0.05, 2, 3, 88.38, -12.11, 0.95, 41, 65.22, -12.04, 0.05, 2, 3, 98.48, -15.62, 0.95, 41, 75.32, -15.56, 0.05, 2, 3, 75.62, -26.5, 0.95, 41, 52.46, -26.44, 0.05, 2, 3, 89.77, -39.78, 0.95, 41, 66.61, -39.72, 0.05, 2, 3, 100.39, -58.95, 0.95, 41, 77.22, -58.88, 0.05, 2, 3, 116.07, -68.05, 0.95, 41, 92.91, -67.98, 0.05, 2, 3, 80.07, 29.51, 0.95, 41, 56.91, 29.58, 0.05, 2, 3, 92.08, 40.47, 0.95, 41, 68.91, 40.53, 0.05, 2, 3, 101.7, 58.37, 0.95, 41, 78.54, 58.44, 0.05, 2, 3, 111.43, 65.16, 0.95, 41, 88.26, 65.22, 0.05], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 0, 70, 12, 14]}}, "zheng_5": {"zheng_5": {"type": "mesh", "hull": 49, "width": 176, "height": 201, "uvs": [0.19415, 0.03937, 0.21884, 0.06054, 0.26315, 0.04008, 0.29593, 0.05115, 0.29865, 0.08661, 0.26761, 0.15343, 0.21335, 0.24589, 0.1476, 0.36787, 0.07948, 0.49733, 0.0336, 0.62481, 0.00789, 0.74147, 0.00164, 0.83775, 0.02653, 0.90876, 0.08798, 0.97206, 0.18191, 0.99999, 0.2808, 0.99222, 0.38664, 0.95944, 0.4932, 0.91074, 0.59088, 0.86047, 0.69784, 0.82314, 0.77849, 0.81054, 0.8445, 0.8189, 0.91127, 0.85464, 0.9632, 0.90123, 0.9864, 0.85215, 0.98527, 0.79816, 0.94508, 0.75717, 0.87449, 0.73269, 0.77115, 0.73169, 0.66984, 0.75904, 0.55709, 0.80773, 0.45461, 0.85997, 0.35711, 0.89991, 0.26486, 0.92343, 0.18488, 0.92532, 0.13331, 0.91155, 0.09249, 0.87396, 0.07422, 0.82258, 0.07909, 0.7499, 0.10486, 0.63995, 0.14829, 0.52149, 0.21258, 0.39567, 0.28746, 0.27865, 0.33476, 0.18233, 0.35019, 0.09182, 0.3436, 0.04707, 0.31599, 0.01594, 0.26428, 0.00143, 0.21921, 0.00985], "triangles": [2, 48, 47, 2, 47, 46, 1, 0, 48, 3, 2, 46, 2, 1, 48, 3, 46, 45, 4, 3, 45, 4, 45, 44, 44, 5, 4, 43, 5, 44, 43, 6, 5, 42, 6, 43, 42, 41, 6, 41, 7, 6, 41, 40, 7, 40, 8, 7, 9, 8, 40, 39, 9, 40, 38, 10, 9, 39, 38, 9, 37, 10, 38, 11, 10, 37, 36, 12, 11, 36, 11, 37, 35, 13, 12, 35, 12, 36, 34, 13, 35, 16, 15, 33, 14, 13, 34, 14, 34, 33, 14, 33, 15, 16, 32, 31, 16, 33, 32, 17, 31, 30, 18, 17, 30, 16, 31, 17, 19, 29, 20, 18, 30, 29, 19, 18, 29, 29, 28, 20, 21, 20, 28, 27, 21, 28, 21, 27, 26, 22, 21, 26, 22, 26, 25, 22, 25, 24, 23, 22, 24], "vertices": [2, 39, 18.04, 17.92, 0.00687, 40, 18.37, 1.58, 0.99313, 2, 39, 12.39, 15.67, 0.11592, 40, 14.44, 6.22, 0.88408, 3, 38, 24.73, 13.37, 0.05802, 39, 13, 6.87, 0.27806, 40, 6.29, 2.85, 0.66392, 4, 37, 48.15, 12.37, 0.03467, 38, 23.95, 7.24, 0.19248, 39, 8.64, 2.49, 0.42646, 40, 0.75, 5.6, 0.34639, 5, 36, 68.55, 10.29, 0.00455, 37, 41.99, 8.75, 0.1759, 38, 17.14, 5.08, 0.32694, 39, 1.93, 4.93, 0.3705, 40, 0.94, 12.74, 0.12211, 5, 36, 54.15, 8.64, 0.12021, 37, 27.53, 7.63, 0.31713, 38, 2.79, 7.19, 0.34536, 39, -8.16, 15.35, 0.20836, 40, 7.62, 25.61, 0.00893, 5, 35, 63.16, 4.78, 0.11111, 36, 33.26, 8.1, 0.23587, 37, 6.64, 7.86, 0.38902, 38, -17.53, 12.05, 0.2109, 39, -21.31, 31.59, 0.05309, 5, 34, 67.23, 1.43, 0.11111, 35, 36.07, 5.85, 0.22222, 36, 6.2, 6.49, 0.34243, 37, -20.47, 7.25, 0.24779, 38, -44.1, 17.46, 0.07644, 5, 33, 67.15, -1.05, 0.11111, 34, 38.91, 5.76, 0.22222, 35, 7.44, 6.71, 0.33333, 36, -22.38, 4.52, 0.22677, 37, -49.1, 6.33, 0.10656, 5, 32, 58.26, -15.71, 0.07201, 33, 40.82, 4.26, 0.26132, 34, 12.05, 6.44, 0.33333, 35, -19.31, 4.13, 0.22222, 36, -48.74, -0.69, 0.11111, 5, 31, 45.33, -27.12, 0.0003, 32, 38.31, -2.6, 0.25484, 33, 17.02, 6.27, 0.41153, 34, -11.73, 4.31, 0.22222, 35, -42.66, -0.87, 0.11111, 4, 31, 37.66, -9.32, 0.11171, 32, 20.83, 5.79, 0.43767, 33, -2.34, 5.31, 0.33951, 34, -30.64, 0.02, 0.11111, 4, 30, 46.73, -12.14, 0.0986, 31, 27.36, 1.49, 0.23563, 32, 5.96, 7.17, 0.47646, 33, -16.07, -0.56, 0.1893, 5, 29, 63.99, -12.35, 0.0146, 30, 36.56, 1.1, 0.29372, 31, 11.99, 8.03, 0.35895, 32, -9.92, 2.01, 0.29364, 33, -27.57, -12.66, 0.0391, 4, 29, 51.24, -0.42, 0.14031, 30, 20.33, 7.54, 0.48883, 31, -5.3, 5.66, 0.26005, 32, -21.41, -11.14, 0.11081, 4, 28, 60.63, 1.01, 0.10829, 29, 34.74, 5.32, 0.26883, 30, 2.87, 6.85, 0.48674, 31, -20.17, -3.52, 0.13613, 5, 27, 67.13, 9.3, 0.06554, 28, 41.3, 5.09, 0.26215, 29, 15.05, 6.99, 0.36816, 30, -16.06, 1.2, 0.29163, 31, -33.89, -17.74, 0.01251, 5, 26, 62.88, 24.37, 0.0169, 27, 45.99, 8.54, 0.2253, 28, 20.19, 6.5, 0.41602, 29, -6.07, 5.8, 0.24527, 30, -35.28, -7.64, 0.09651, 5, 25, 52.15, 40.15, 0.00336, 26, 44.93, 15.68, 0.1405, 27, 26.12, 6.82, 0.38611, 28, 0.25, 6.83, 0.35329, 29, -25.9, 3.67, 0.11674, 5, 25, 40.65, 23.47, 0.09493, 26, 25.56, 9.72, 0.287, 27, 5.9, 8.15, 0.41583, 28, -19.73, 10.23, 0.19943, 29, -46.14, 4.59, 0.00282, 4, 25, 30.24, 13.49, 0.2976, 26, 11.21, 8.33, 0.39969, 27, -8, 11.97, 0.25714, 28, -33.16, 15.46, 0.04557, 3, 25, 19.64, 8.44, 0.60467, 26, -0.24, 10.94, 0.299, 27, -17.76, 18.49, 0.09633, 3, 25, 5.88, 7.9, 0.84644, 26, -11.37, 19.05, 0.1525, 27, -25.28, 30.03, 0.00106, 2, 25, -6.92, 10.62, 0.97709, 26, -19.73, 29.12, 0.02291, 1, 25, -4.85, 0.15, 1, 2, 25, 1.34, -8.77, 0.989, 26, -25.27, 8.78, 0.011, 2, 25, 11.79, -11.7, 0.86689, 26, -18.88, 0, 0.13311, 3, 25, 24.86, -8.91, 0.63367, 26, -6.89, -5.91, 0.25522, 27, -29.99, 5.12, 0.11111, 4, 25, 40.11, 1.02, 0.31133, 26, 11.22, -7.57, 0.35533, 27, -13.66, -2.89, 0.2775, 28, -40.31, 1.26, 0.05583, 5, 25, 51.89, 15.48, 0.10011, 26, 29.44, -3.52, 0.23322, 27, 4.8, -5.6, 0.44389, 28, -22.23, -3.34, 0.21773, 29, -46.96, -9.18, 0.00505, 4, 26, 50, 4.64, 0.11111, 27, 26.93, -5.31, 0.38805, 28, -0.19, -5.32, 0.37963, 29, -24.85, -8.44, 0.12121, 4, 27, 47.73, -3.6, 0.22166, 28, 20.67, -5.75, 0.42986, 29, -4.09, -6.3, 0.25701, 30, -29.01, -18.18, 0.09147, 5, 27, 66.67, -3.74, 0.05528, 28, 39.5, -7.83, 0.26796, 29, 14.86, -6.05, 0.38271, 30, -11.47, -11.01, 0.25124, 31, -23.89, -26.12, 0.04281, 5, 28, 55.83, -12.26, 0.10606, 29, 31.6, -8.43, 0.2862, 30, 4.98, -7.1, 0.41156, 31, -11.48, -14.63, 0.19206, 32, -12.74, -30.49, 0.00412, 4, 29, 44.58, -13.89, 0.15039, 30, 19.06, -7.43, 0.38894, 31, 0.94, -8, 0.35265, 32, -7.73, -17.33, 0.10802, 5, 29, 51.71, -20.15, 0.01964, 30, 27.99, -10.64, 0.22973, 31, 10.3, -6.41, 0.4276, 32, -1.71, -9.99, 0.26309, 33, -14.63, -19.3, 0.05994, 4, 30, 34.78, -18.55, 0.06941, 31, 20.1, -9.96, 0.28969, 32, 8.01, -6.23, 0.40992, 33, -7.88, -11.36, 0.23098, 5, 30, 37.48, -29.03, 0.00056, 31, 27.6, -17.76, 0.12911, 32, 18.78, -7.19, 0.3572, 33, 2.05, -7.07, 0.40203, 34, -24.17, -11.41, 0.11111, 5, 31, 33.36, -31.21, 0.01134, 32, 31.97, -13.54, 0.20213, 33, 16.66, -6.37, 0.4532, 34, -9.9, -8.2, 0.22247, 35, -39.32, -13.07, 0.11086, 5, 32, 50.67, -26.15, 0.05118, 33, 39.12, -8.53, 0.28216, 34, 12.59, -6.45, 0.33383, 35, -17.21, -8.61, 0.2236, 36, -45.39, -13.15, 0.10924, 5, 33, 63.61, -13.61, 0.11111, 34, 37.59, -7.22, 0.22296, 35, 7.7, -6.33, 0.33634, 36, -20.84, -8.43, 0.21954, 37, -48.03, -6.67, 0.11005, 5, 34, 65.02, -11.11, 0.1116, 35, 35.4, -6.86, 0.22734, 36, 6.78, -6.22, 0.32983, 37, -20.35, -5.48, 0.26684, 38, -46.8, 5.02, 0.06438, 6, 34, 91.27, -17.28, 0.00025, 35, 62.2, -9.8, 0.11461, 36, 33.74, -6.5, 0.22165, 37, 6.58, -6.75, 0.42363, 38, -20.82, -2.19, 0.216, 39, -32.6, 22.31, 0.02386, 5, 35, 83.27, -9.9, 0.00187, 36, 54.72, -4.52, 0.11136, 37, 27.62, -5.54, 0.36031, 38, -0.04, -5.67, 0.36762, 39, -18.24, 6.88, 0.15884, 5, 36, 71.99, 1.83, 0.00106, 37, 45.11, 0.17, 0.20352, 38, 18.28, -3.98, 0.39049, 39, -2.69, -2.95, 0.3847, 40, -8, 14.63, 0.02022, 4, 37, 52.63, 5.23, 0.04673, 38, 26.74, -0.71, 0.23887, 39, 6, -5.52, 0.56284, 40, -7.68, 5.56, 0.15155, 3, 38, 31.66, 5.5, 0.08725, 39, 13.69, -3.6, 0.51876, 40, -3.42, -1.12, 0.394, 2, 39, 20.03, 3.55, 0.29289, 40, 5.37, -4.87, 0.70711, 2, 39, 21.68, 11.49, 0.09089, 40, 13.43, -3.91, 0.90911], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 0, 96, 40, 42, 42, 44]}}, "zheng_7": {"zheng_5": {"type": "mesh", "hull": 49, "width": 176, "height": 201, "uvs": [0.19415, 0.03937, 0.21884, 0.06054, 0.26315, 0.04008, 0.29593, 0.05115, 0.29865, 0.08661, 0.26761, 0.15343, 0.21335, 0.24589, 0.1476, 0.36787, 0.07948, 0.49733, 0.0336, 0.62481, 0.00789, 0.74147, 0.00164, 0.83775, 0.02653, 0.90876, 0.08798, 0.97206, 0.18191, 0.99999, 0.2808, 0.99222, 0.38664, 0.95944, 0.4932, 0.91074, 0.59088, 0.86047, 0.69784, 0.82314, 0.77849, 0.81054, 0.8445, 0.8189, 0.91127, 0.85464, 0.9632, 0.90123, 0.9864, 0.85215, 0.98527, 0.79816, 0.94508, 0.75717, 0.87449, 0.73269, 0.77115, 0.73169, 0.66984, 0.75904, 0.55709, 0.80773, 0.45461, 0.85997, 0.35711, 0.89991, 0.26486, 0.92343, 0.18488, 0.92532, 0.13331, 0.91155, 0.09249, 0.87396, 0.07422, 0.82258, 0.07909, 0.7499, 0.10486, 0.63995, 0.14829, 0.52149, 0.21258, 0.39567, 0.28746, 0.27865, 0.33476, 0.18233, 0.35019, 0.09182, 0.3436, 0.04707, 0.31599, 0.01594, 0.26428, 0.00143, 0.21921, 0.00985], "triangles": [2, 48, 47, 2, 47, 46, 1, 0, 48, 3, 2, 46, 2, 1, 48, 3, 46, 45, 4, 3, 45, 4, 45, 44, 44, 5, 4, 43, 5, 44, 43, 6, 5, 42, 6, 43, 42, 41, 6, 41, 7, 6, 41, 40, 7, 40, 8, 7, 9, 8, 40, 39, 9, 40, 38, 10, 9, 39, 38, 9, 37, 10, 38, 11, 10, 37, 36, 12, 11, 36, 11, 37, 35, 13, 12, 35, 12, 36, 34, 13, 35, 16, 15, 33, 14, 13, 34, 14, 34, 33, 14, 33, 15, 16, 32, 31, 16, 33, 32, 17, 31, 30, 18, 17, 30, 16, 31, 17, 19, 29, 20, 18, 30, 29, 19, 18, 29, 29, 28, 20, 21, 20, 28, 27, 21, 28, 21, 27, 26, 22, 21, 26, 22, 26, 25, 22, 25, 24, 23, 22, 24], "vertices": [2, 89, 18.04, 17.92, 0.00687, 90, 18.37, 1.58, 0.99313, 2, 89, 12.39, 15.67, 0.11592, 90, 14.44, 6.22, 0.88408, 3, 88, 24.73, 13.37, 0.05802, 89, 13, 6.87, 0.27806, 90, 6.29, 2.85, 0.66392, 4, 87, 48.15, 12.37, 0.03467, 88, 23.95, 7.24, 0.19248, 89, 8.64, 2.49, 0.42646, 90, 0.75, 5.6, 0.34639, 5, 86, 68.55, 10.29, 0.00455, 87, 41.99, 8.75, 0.1759, 88, 17.14, 5.08, 0.32694, 89, 1.93, 4.93, 0.3705, 90, 0.94, 12.74, 0.12211, 5, 86, 54.15, 8.64, 0.12021, 87, 27.53, 7.63, 0.31713, 88, 2.79, 7.19, 0.34536, 89, -8.16, 15.35, 0.20836, 90, 7.62, 25.61, 0.00893, 5, 85, 63.16, 4.78, 0.11111, 86, 33.26, 8.1, 0.23587, 87, 6.64, 7.86, 0.38902, 88, -17.53, 12.05, 0.2109, 89, -21.31, 31.59, 0.05309, 5, 84, 67.23, 1.43, 0.11111, 85, 36.07, 5.85, 0.22222, 86, 6.2, 6.49, 0.34243, 87, -20.47, 7.25, 0.24779, 88, -44.1, 17.46, 0.07644, 5, 83, 67.15, -1.05, 0.11111, 84, 38.91, 5.76, 0.22222, 85, 7.44, 6.71, 0.33333, 86, -22.38, 4.52, 0.22677, 87, -49.1, 6.33, 0.10656, 5, 82, 58.26, -15.71, 0.07201, 83, 40.82, 4.26, 0.26132, 84, 12.05, 6.44, 0.33333, 85, -19.31, 4.13, 0.22222, 86, -48.74, -0.69, 0.11111, 5, 81, 45.33, -27.12, 0.0003, 82, 38.31, -2.6, 0.25484, 83, 17.02, 6.27, 0.41153, 84, -11.73, 4.31, 0.22222, 85, -42.66, -0.87, 0.11111, 4, 81, 37.66, -9.32, 0.11171, 82, 20.83, 5.79, 0.43767, 83, -2.34, 5.31, 0.33951, 84, -30.64, 0.02, 0.11111, 4, 80, 46.73, -12.14, 0.0986, 81, 27.36, 1.49, 0.23563, 82, 5.96, 7.17, 0.47646, 83, -16.07, -0.56, 0.1893, 5, 79, 63.99, -12.35, 0.0146, 80, 36.56, 1.1, 0.29372, 81, 11.99, 8.03, 0.35895, 82, -9.92, 2.01, 0.29364, 83, -27.57, -12.66, 0.0391, 4, 79, 51.24, -0.42, 0.14031, 80, 20.33, 7.54, 0.48883, 81, -5.3, 5.66, 0.26005, 82, -21.41, -11.14, 0.11081, 4, 78, 60.63, 1.01, 0.10829, 79, 34.74, 5.32, 0.26883, 80, 2.87, 6.85, 0.48674, 81, -20.17, -3.52, 0.13613, 5, 77, 67.13, 9.3, 0.06554, 78, 41.3, 5.09, 0.26215, 79, 15.05, 6.99, 0.36816, 80, -16.06, 1.2, 0.29163, 81, -33.89, -17.74, 0.01251, 5, 76, 62.88, 24.37, 0.0169, 77, 45.99, 8.54, 0.2253, 78, 20.19, 6.5, 0.41602, 79, -6.07, 5.8, 0.24527, 80, -35.28, -7.64, 0.09651, 5, 75, 52.15, 40.15, 0.00336, 76, 44.93, 15.68, 0.1405, 77, 26.12, 6.82, 0.38611, 78, 0.25, 6.83, 0.35329, 79, -25.9, 3.67, 0.11674, 5, 75, 40.65, 23.47, 0.09493, 76, 25.56, 9.72, 0.287, 77, 5.9, 8.15, 0.41583, 78, -19.73, 10.23, 0.19943, 79, -46.14, 4.59, 0.00282, 4, 75, 30.24, 13.49, 0.2976, 76, 11.21, 8.33, 0.39969, 77, -8, 11.97, 0.25714, 78, -33.16, 15.46, 0.04557, 3, 75, 19.64, 8.44, 0.60467, 76, -0.24, 10.94, 0.299, 77, -17.76, 18.49, 0.09633, 3, 75, 5.88, 7.9, 0.84644, 76, -11.37, 19.05, 0.1525, 77, -25.28, 30.03, 0.00106, 2, 75, -6.92, 10.62, 0.97709, 76, -19.73, 29.12, 0.02291, 1, 75, -4.85, 0.15, 1, 2, 75, 1.34, -8.77, 0.989, 76, -25.27, 8.78, 0.011, 2, 75, 11.79, -11.7, 0.86689, 76, -18.88, 0, 0.13311, 3, 75, 24.86, -8.91, 0.63367, 76, -6.89, -5.91, 0.25522, 77, -29.99, 5.12, 0.11111, 4, 75, 40.11, 1.02, 0.31133, 76, 11.22, -7.57, 0.35533, 77, -13.66, -2.89, 0.2775, 78, -40.31, 1.26, 0.05583, 5, 75, 51.89, 15.48, 0.10011, 76, 29.44, -3.52, 0.23322, 77, 4.8, -5.6, 0.44389, 78, -22.23, -3.34, 0.21773, 79, -46.96, -9.18, 0.00505, 4, 76, 50, 4.64, 0.11111, 77, 26.93, -5.31, 0.38805, 78, -0.19, -5.32, 0.37963, 79, -24.85, -8.44, 0.12121, 4, 77, 47.73, -3.6, 0.22166, 78, 20.67, -5.75, 0.42986, 79, -4.09, -6.3, 0.25701, 80, -29.01, -18.18, 0.09147, 5, 77, 66.67, -3.74, 0.05528, 78, 39.5, -7.83, 0.26796, 79, 14.86, -6.05, 0.38271, 80, -11.47, -11.01, 0.25124, 81, -23.89, -26.12, 0.04281, 5, 78, 55.83, -12.26, 0.10606, 79, 31.6, -8.43, 0.2862, 80, 4.98, -7.1, 0.41156, 81, -11.48, -14.63, 0.19206, 82, -12.74, -30.49, 0.00412, 4, 79, 44.58, -13.89, 0.15039, 80, 19.06, -7.43, 0.38894, 81, 0.94, -8, 0.35265, 82, -7.73, -17.33, 0.10802, 5, 79, 51.71, -20.15, 0.01964, 80, 27.99, -10.64, 0.22973, 81, 10.3, -6.41, 0.4276, 82, -1.71, -9.99, 0.26309, 83, -14.63, -19.3, 0.05994, 4, 80, 34.78, -18.55, 0.06941, 81, 20.1, -9.96, 0.28969, 82, 8.01, -6.23, 0.40992, 83, -7.88, -11.36, 0.23098, 5, 80, 37.48, -29.03, 0.00056, 81, 27.6, -17.76, 0.12911, 82, 18.78, -7.19, 0.3572, 83, 2.05, -7.07, 0.40203, 84, -24.17, -11.41, 0.11111, 5, 81, 33.36, -31.21, 0.01134, 82, 31.97, -13.54, 0.20213, 83, 16.66, -6.37, 0.4532, 84, -9.9, -8.2, 0.22247, 85, -39.32, -13.07, 0.11086, 5, 82, 50.67, -26.15, 0.05118, 83, 39.12, -8.53, 0.28216, 84, 12.59, -6.45, 0.33383, 85, -17.21, -8.61, 0.2236, 86, -45.39, -13.15, 0.10924, 5, 83, 63.61, -13.61, 0.11111, 84, 37.59, -7.22, 0.22296, 85, 7.7, -6.33, 0.33634, 86, -20.84, -8.43, 0.21954, 87, -48.03, -6.67, 0.11005, 5, 84, 65.02, -11.11, 0.1116, 85, 35.4, -6.86, 0.22734, 86, 6.78, -6.22, 0.32983, 87, -20.35, -5.48, 0.26684, 88, -46.8, 5.02, 0.06438, 6, 84, 91.27, -17.28, 0.00025, 85, 62.2, -9.8, 0.11461, 86, 33.74, -6.5, 0.22165, 87, 6.58, -6.75, 0.42363, 88, -20.82, -2.19, 0.216, 89, -32.6, 22.31, 0.02386, 5, 85, 83.27, -9.9, 0.00187, 86, 54.72, -4.52, 0.11136, 87, 27.62, -5.54, 0.36031, 88, -0.04, -5.67, 0.36762, 89, -18.24, 6.88, 0.15884, 5, 86, 71.99, 1.83, 0.00106, 87, 45.11, 0.17, 0.20352, 88, 18.28, -3.98, 0.39049, 89, -2.69, -2.95, 0.3847, 90, -8, 14.63, 0.02022, 4, 87, 52.63, 5.23, 0.04673, 88, 26.74, -0.71, 0.23887, 89, 6, -5.52, 0.56284, 90, -7.68, 5.56, 0.15155, 3, 88, 31.66, 5.5, 0.08725, 89, 13.69, -3.6, 0.51876, 90, -3.42, -1.12, 0.394, 2, 89, 20.03, 3.55, 0.29289, 90, 5.37, -4.87, 0.70711, 2, 89, 21.68, 11.49, 0.09089, 90, 13.43, -3.91, 0.90911], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 0, 96, 40, 42, 42, 44]}}, "zheng_8": {"long_4": {"type": "mesh", "hull": 25, "width": 98, "height": 124, "uvs": [0, 0, 1, 0, 1, 0.13032, 0.92335, 0.25881, 0.85779, 0.40597, 0.84468, 0.55934, 0.76338, 0.60908, 0.79185, 0.70352, 0.74568, 0.7765, 0.62009, 0.813, 0.56468, 0.86116, 0.56468, 0.91517, 0.65333, 0.99254, 0.57576, 1, 0.48526, 0.97356, 0.37629, 0.93561, 0.31534, 0.88014, 0.32458, 0.80278, 0.3449, 0.76337, 0.25994, 0.73271, 0.19899, 0.64367, 0.22854, 0.60572, 0.16205, 0.55901, 0.14727, 0.39845, 0.05493, 0.21138, 0.39834, 0.5774, 0.58201, 0.57014], "triangles": [12, 13, 11, 13, 14, 11, 14, 15, 11, 11, 15, 10, 10, 15, 17, 15, 16, 17, 17, 18, 10, 10, 18, 9, 20, 21, 19, 26, 18, 25, 18, 19, 25, 19, 21, 25, 18, 26, 9, 9, 26, 8, 7, 26, 6, 7, 8, 26, 6, 26, 5, 26, 25, 23, 5, 26, 4, 23, 4, 26, 4, 23, 3, 23, 24, 3, 2, 3, 1, 1, 3, 24, 24, 0, 1, 23, 25, 22, 21, 22, 25], "vertices": [1, 7, -27.99, -49.22, 1, 1, 7, -27.1, 48.78, 1, 1, 7, -10.95, 48.63, 1, 1, 7, 4.92, 40.97, 1, 1, 7, 23.11, 34.38, 1, 1, 7, 42.11, 32.93, 1, 1, 7, 48.21, 24.9, 1, 3, 7, 59.94, 27.59, 0.50021, 9, 2.5, 29.06, 0.40934, 10, -15.72, 25.43, 0.09044, 4, 7, 68.95, 22.98, 0.37707, 9, 11.63, 24.7, 0.45435, 10, -5.69, 24.13, 0.16812, 12, 14.72, 29.19, 0.00047, 5, 7, 73.37, 10.63, 0.14699, 9, 16.37, 12.47, 0.3181, 10, 2.62, 13.98, 0.46647, 11, -4.21, 14.91, 0.02221, 12, 7.46, 18.27, 0.04622, 5, 7, 79.29, 5.15, 0.02462, 9, 22.43, 7.14, 0.04302, 10, 10.05, 10.81, 0.41647, 11, 2.57, 10.52, 0.17184, 12, 6.61, 10.24, 0.34405, 5, 7, 85.99, 5.09, 0.00149, 9, 29.13, 7.26, 0.00033, 10, 16.37, 13, 0.06099, 11, 9.17, 11.61, 0.05434, 12, 10.57, 4.84, 0.88284, 2, 10, 22.59, 24.35, 0, 12, 23.24, 2.23, 1, 1, 12, 17.66, -3.01, 1, 1, 12, 8.56, -5.6, 1, 4, 9, 31.98, -11.16, 0.00132, 10, 24.82, -3.61, 0.00713, 11, 14.67, -6.19, 0.41288, 12, -2.83, -8.11, 0.57866, 5, 8, 37.53, -15.36, 0.00215, 9, 25.21, -17.25, 0.03059, 10, 20.28, -11.51, 0.13041, 11, 8.85, -13.2, 0.6856, 12, -11.72, -6.09, 0.15126, 5, 8, 27.89, -15.32, 0.06522, 9, 15.6, -16.51, 0.15454, 10, 10.92, -13.8, 0.35511, 11, -0.76, -13.87, 0.41871, 12, -16.65, 2.18, 0.00642, 4, 8, 22.85, -13.78, 0.21179, 9, 10.68, -14.6, 0.30644, 10, 5.65, -13.52, 0.34197, 11, -5.9, -12.7, 0.13981, 4, 8, 19.81, -22.42, 0.53431, 9, 7.02, -22.99, 0.3042, 10, 4.78, -22.63, 0.1498, 11, -8.3, -21.53, 0.01169, 4, 7, 52, -30.44, 0.01678, 8, 9.36, -29.36, 0.72893, 9, -3.91, -29.16, 0.19019, 10, -3.69, -31.89, 0.0641, 1, 7, 47.32, -27.5, 1, 1, 7, 41.47, -33.97, 1, 1, 7, 21.55, -35.24, 1, 1, 7, -1.73, -44.08, 1, 1, 7, 43.96, -10.83, 1, 1, 7, 43.22, 7.17, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48]}}, "zheng_22": {"jj": {"type": "mesh", "hull": 15, "width": 271, "height": 171, "uvs": [0.45192, 0.94068, 0.42216, 0.67795, 0.18836, 0, 0.09201, 0, 0, 0.23556, 0, 0.57914, 0.17703, 0.83739, 0.4505, 0.9856, 0.56669, 0.99458, 0.82175, 0.81268, 1, 0.55444, 1, 0, 0.78632, 0, 0.6007, 0.60384, 0.54686, 0.94068], "triangles": [12, 11, 10, 13, 12, 10, 9, 13, 10, 2, 5, 4, 3, 2, 4, 1, 5, 2, 1, 6, 5, 7, 0, 14, 7, 6, 0, 8, 7, 14, 8, 14, 9, 6, 1, 0, 14, 13, 9], "vertices": [1, 3, 107.52, 13.15, 1, 1, 23, 21.13, -29.59, 1, 1, 23, 151.48, -51.11, 1, 1, 23, 167.56, -30.54, 1, 1, 23, 151.2, 13.92, 1, 1, 23, 104.92, 50.12, 1, 1, 3, 126.18, 87.4, 1, 1, 3, 99.85, 13.63, 1, 1, 3, 97.89, -17.83, 1, 1, 3, 128.07, -87.36, 1, 1, 24, 104.72, -48.69, 1, 1, 24, 181.36, 7.13, 1, 1, 24, 147.27, 53.94, 1, 1, 24, 34.19, 33.81, 1, 1, 3, 107.18, -12.58, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28]}}}}], "animations": {"anger": {"slots": {"huo": {"attachment": [{"name": "tw_00"}, {"time": 0.0333, "name": "tw_01"}, {"time": 0.0667, "name": "tw_02"}, {"time": 0.1, "name": "tw_03"}, {"time": 0.1333, "name": "tw_04"}, {"time": 0.1667, "name": "tw_05"}, {"time": 0.2, "name": "tw_06"}, {"time": 0.2333, "name": "tw_07"}, {"time": 0.2667, "name": "tw_08"}, {"time": 0.3, "name": "tw_09"}, {"time": 0.3333, "name": "tw_10"}, {"time": 0.3667, "name": "tw_11"}, {"time": 0.4, "name": "tw_12"}, {"time": 0.4333, "name": "tw_13"}, {"time": 0.4667, "name": "tw_14"}, {"time": 0.5, "name": "tw_00"}, {"time": 0.5333, "name": "tw_01"}, {"time": 0.5667, "name": "tw_02"}, {"time": 0.6, "name": "tw_03"}, {"time": 0.6333, "name": "tw_04"}, {"time": 0.6667, "name": "tw_05"}, {"time": 0.7, "name": "tw_06"}, {"time": 0.7333, "name": "tw_07"}, {"time": 0.7667, "name": "tw_08"}, {"time": 0.8, "name": "tw_09"}, {"time": 0.8333, "name": "tw_10"}, {"time": 0.8667, "name": "tw_11"}, {"time": 0.9, "name": "tw_12"}, {"time": 0.9333, "name": "tw_13"}, {"time": 0.9667, "name": "tw_14"}, {"time": 1, "name": "tw_15"}]}, "huo2": {"attachment": [{"name": "tw_00"}, {"time": 0.0333, "name": "tw_01"}, {"time": 0.0667, "name": "tw_02"}, {"time": 0.1, "name": "tw_03"}, {"time": 0.1333, "name": "tw_04"}, {"time": 0.1667, "name": "tw_05"}, {"time": 0.2, "name": "tw_06"}, {"time": 0.2333, "name": "tw_07"}, {"time": 0.2667, "name": "tw_08"}, {"time": 0.3, "name": "tw_09"}, {"time": 0.3333, "name": "tw_10"}, {"time": 0.3667, "name": "tw_11"}, {"time": 0.4, "name": "tw_12"}, {"time": 0.4333, "name": "tw_13"}, {"time": 0.4667, "name": "tw_14"}, {"time": 0.5, "name": "tw_00"}, {"time": 0.5333, "name": "tw_01"}, {"time": 0.5667, "name": "tw_02"}, {"time": 0.6, "name": "tw_03"}, {"time": 0.6333, "name": "tw_04"}, {"time": 0.6667, "name": "tw_05"}, {"time": 0.7, "name": "tw_06"}, {"time": 0.7333, "name": "tw_07"}, {"time": 0.7667, "name": "tw_08"}, {"time": 0.8, "name": "tw_09"}, {"time": 0.8333, "name": "tw_10"}, {"time": 0.8667, "name": "tw_11"}, {"time": 0.9, "name": "tw_12"}, {"time": 0.9333, "name": "tw_13"}, {"time": 0.9667, "name": "tw_14"}, {"time": 1, "name": "tw_15"}]}}, "bones": {"all": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "y": 9.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 15.86, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 12.08, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_0": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 10.21, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_3": {"rotate": [{"angle": 0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.51, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_4": {"rotate": [{"angle": 3.97, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 3.97}], "translate": [{"x": -0.47, "y": 0.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -3.58, "y": 0.12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "x": -0.47, "y": 0.02}]}, "zheng_5": {"rotate": [{"angle": 0.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.82}], "translate": [{"x": -2.45, "y": 0.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -6.66, "y": 0.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -2.45, "y": 0.03}]}, "zheng_6": {"translate": [{"y": -2.92, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.2667, "y": 1.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.5, "y": 9.53, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.7667, "y": 15.59, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 1, "y": -2.92}]}, "zheng_7": {"rotate": [{"angle": 0.46, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.68, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.46}], "translate": [{"x": -0.07, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -0.94, "y": 0.01, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -0.07}]}, "zheng_8": {"rotate": [{"angle": -14.12, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -20.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.52, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -14.12}], "translate": [{"x": -0.32, "y": 0.02, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -1.57, "y": 0.1, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "x": -0.32, "y": 0.02}]}, "zheng_9": {"rotate": [{"angle": -7.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -22.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 18.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.58}], "translate": [{"x": -1.02, "y": 0.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -2.77, "y": 0.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -1.02, "y": 0.22}]}, "zheng_10": {"rotate": [{"angle": -5.85, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": -29.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 13.6, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -5.85}], "translate": [{"x": -1.53, "y": 0.21, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -2.81, "y": 0.39, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "x": -1.53, "y": 0.21}]}, "zheng_11": {"rotate": [{"angle": 9.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": -29.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 25.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 9.72}], "translate": [{"x": -2.47, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -3.44, "y": 0.4, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "x": -2.47, "y": 0.29}]}, "zheng_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": 2.16, "y": -2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.1, "y": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.16, "y": -2.75}]}, "zheng_12": {"rotate": [{"angle": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.65, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.39}]}, "zheng_13": {"rotate": [{"angle": 0.6, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -1.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.19, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 0.6}]}, "zheng_14": {"rotate": [{"angle": -2.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 7.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -2.08}]}, "zheng_15": {"rotate": [{"angle": 2.22, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.91, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 2.22}]}, "zheng_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.21, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": 2.2, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.98, "y": 4.29, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.2, "y": -0.03}]}, "zheng_16": {"rotate": [{"angle": -0.37, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.31, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.37}]}, "zheng_17": {"rotate": [{"angle": -0.19, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 2.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.04, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -0.19}]}, "zheng_18": {"rotate": [{"angle": 6.11, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 6.11}]}, "zheng_19": {"rotate": [{"angle": -1.79, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 9.16, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -10.98, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -1.79}]}, "zheng_21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.86, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": -22.97, "y": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -9.48, "y": 0.13, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -22.97, "y": 0.31}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.947, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": -22.97, "y": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -9.48, "y": 0.13, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -22.97, "y": 0.31}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_20": {"translate": [{"x": -37.32, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 17.41, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -37.32, "y": 0.5}]}, "zheng_39": {"rotate": [{"angle": 2.56, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 2.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 2.56}]}, "zheng_40": {"rotate": [{"angle": 2.87, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 4.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.63, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 2.87}]}, "zheng_41": {"rotate": [{"angle": 2.98, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.08, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 2.98}]}, "zheng_42": {"rotate": [{"angle": 1.67, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 8.72, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -4.24, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 1.67}]}, "zheng_43": {"rotate": [{"angle": 1.49, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 12.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 1.49}]}, "zheng_44": {"rotate": [{"angle": -0.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 13.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.32, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -0.21}]}, "zheng_51": {"rotate": [{"angle": 4.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 12.13, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -7.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 4.8}]}, "zheng_52": {"rotate": [{"angle": -2.25, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 6.37, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.48, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -2.25}]}, "zheng_53": {"rotate": [{"angle": -5.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 7.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -11.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -5.94}]}, "zheng_55": {"rotate": [{"angle": 6.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.56, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 6.29}], "translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -2.22, "y": -3.71, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1}]}, "zheng_56": {"rotate": [{"angle": 3.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 3.57}], "translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -8.41, "y": -3.89, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1}]}, "zheng_58": {"rotate": [{"angle": 4.81, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.09, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 4.81}], "translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -4.63, "y": -2.05, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1}]}, "zheng_59": {"rotate": [{"angle": 7.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 15.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.36, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 7.84}], "translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -3.8, "y": -5.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1}]}, "zheng_60": {"rotate": [{"angle": 5.39, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.9, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 5.39}], "translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -5.37, "y": -7.52, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1}]}, "zheng_62": {"rotate": [{"angle": 10.91, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 14.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.8, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 10.91}]}, "zheng_64": {"rotate": [{"angle": 8.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.88, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 8.7}]}, "zheng_65": {"rotate": [{"angle": 9.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 16.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 9.02}]}, "zheng_66": {"rotate": [{"angle": 5.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 23.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.78, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 5.18}]}, "zheng_70": {"rotate": [{"angle": -0.61, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 6.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.33, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -0.61}]}, "zheng_71": {"rotate": [{"angle": 1.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 1.25}]}, "zheng_47": {"rotate": [{"angle": 4.34, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 9.74, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 4.34}]}, "zheng_48": {"rotate": [{"angle": 6.6, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 20.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -4.79, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 6.6}]}, "zheng_46": {"rotate": [{"angle": 5.69, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.6, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 5.69}]}, "zheng_45": {"rotate": [{"angle": 3.31, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.07, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 3.31}]}, "zheng_49": {"rotate": [{"angle": 3.71, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 4.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.46, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 3.71}]}, "zheng_50": {"rotate": [{"angle": 5.61, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.46, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 5.61}]}, "zheng_54": {"rotate": [{"angle": 2.76, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 3.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.44, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 2.76}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.56, "y": -0.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1}]}, "zheng_57": {"rotate": [{"angle": 4.53, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.6, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 4.53}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -2.88, "y": -0.95, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1}]}, "zheng_61": {"rotate": [{"angle": 6.09, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.5, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 6.09}]}, "zheng_63": {"rotate": [{"angle": 8.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -12.11, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 8.48}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -4.25, "y": -4.61, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1}]}, "zheng_69": {"rotate": [{"angle": 5.88, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 10.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 5.88}]}, "zheng_68": {"rotate": [{"angle": 6.49, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 8.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.5, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 6.49}]}, "zheng_67": {"rotate": [{"angle": 6.5, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 7.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.86, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 6.5}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.17, "y": -6.41, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1}]}, "zheng_23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.08, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_25": {"rotate": [{"angle": -0.98, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.16, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.98}]}, "zheng_26": {"rotate": [{"angle": -1.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -1.37}]}, "zheng_27": {"rotate": [{"angle": -1.77, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.74, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.77}]}, "zheng_28": {"rotate": [{"angle": -2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.43}]}, "zheng_29": {"rotate": [{"angle": 3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 3}]}, "zheng_30": {"rotate": [{"angle": 9.11, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "angle": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 16.06, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 9.11}]}, "zheng_31": {"rotate": [{"angle": 8.62, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 15.84, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 8.62}], "scale": [{"x": 0.986, "y": 0.981, "curve": 0.353, "c2": 0.65, "c3": 0.688}, {"time": 0.2667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.7667, "x": 0.956, "y": 0.943, "curve": 0.347, "c2": 0.42, "c3": 0.681, "c4": 0.76}, {"time": 1, "x": 0.986, "y": 0.981}]}, "zheng_32": {"rotate": [{"angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": 0.42}], "scale": [{"x": 0.96, "y": 0.972, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.3, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8, "x": 0.901, "y": 0.931, "curve": 0.345, "c2": 0.41, "c3": 0.679, "c4": 0.74}, {"time": 1, "x": 0.96, "y": 0.972}]}, "zheng_33": {"rotate": [{"angle": -2.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -2.45}], "scale": [{"x": 0.965, "y": 0.938, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 0.3333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8333, "x": 0.929, "y": 0.872, "curve": 0.343, "c2": 0.39, "c3": 0.676, "c4": 0.73}, {"time": 1, "x": 0.965, "y": 0.938}]}, "zheng_34": {"rotate": [{"angle": 9.57, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.3667, "angle": 29.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.48, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1, "angle": 9.57}], "scale": [{"x": 0.975, "y": 0.941, "curve": 0.359, "c2": 0.64, "c3": 0.695}, {"time": 0.3667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8667, "x": 0.957, "y": 0.898, "curve": 0.341, "c2": 0.38, "c3": 0.674, "c4": 0.71}, {"time": 1, "x": 0.975, "y": 0.941}]}, "zheng_35": {"rotate": [{"angle": -7.79, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 21.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.79}], "scale": [{"x": 0.951, "y": 0.909, "curve": 0.361, "c2": 0.64, "c3": 0.697}, {"time": 0.4, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.9, "x": 0.928, "y": 0.865, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.7}, {"time": 1, "x": 0.951, "y": 0.909}]}, "zheng_36": {"rotate": [{"angle": -10.96, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4333, "angle": 16.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -13.01, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1, "angle": -10.96}]}, "zheng_37": {"rotate": [{"angle": -17.54, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "angle": 28.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -18.62, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1, "angle": -17.54}]}, "zheng_38": {"rotate": [{"angle": -30.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 13.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -30.58}]}, "zheng_24": {"rotate": [{"angle": -0.23, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.18, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -0.23}]}, "zheng_87": {"rotate": [{"angle": -30.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 13.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -30.58}]}, "zheng_86": {"rotate": [{"angle": -17.54, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "angle": 28.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -18.62, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1, "angle": -17.54}]}, "zheng_85": {"rotate": [{"angle": -10.96, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4333, "angle": 16.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -13.01, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1, "angle": -10.96}]}, "zheng_84": {"rotate": [{"angle": -7.79, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 21.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.79}], "scale": [{"x": 0.951, "y": 0.909, "curve": 0.361, "c2": 0.64, "c3": 0.697}, {"time": 0.4, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.9, "x": 0.928, "y": 0.865, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.7}, {"time": 1, "x": 0.951, "y": 0.909}]}, "zheng_83": {"rotate": [{"angle": 9.57, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.3667, "angle": 29.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.48, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1, "angle": 9.57}], "scale": [{"x": 0.975, "y": 0.941, "curve": 0.359, "c2": 0.64, "c3": 0.695}, {"time": 0.3667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8667, "x": 0.957, "y": 0.898, "curve": 0.341, "c2": 0.38, "c3": 0.674, "c4": 0.71}, {"time": 1, "x": 0.975, "y": 0.941}]}, "zheng_82": {"rotate": [{"angle": -2.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -2.45}], "scale": [{"x": 0.965, "y": 0.938, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 0.3333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8333, "x": 0.929, "y": 0.872, "curve": 0.343, "c2": 0.39, "c3": 0.676, "c4": 0.73}, {"time": 1, "x": 0.965, "y": 0.938}]}, "zheng_81": {"rotate": [{"angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": 0.42}], "scale": [{"x": 0.96, "y": 0.972, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.3, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8, "x": 0.901, "y": 0.931, "curve": 0.345, "c2": 0.41, "c3": 0.679, "c4": 0.74}, {"time": 1, "x": 0.96, "y": 0.972}]}, "zheng_80": {"rotate": [{"angle": 8.62, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 15.84, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 8.62}], "scale": [{"x": 0.986, "y": 0.981, "curve": 0.353, "c2": 0.65, "c3": 0.688}, {"time": 0.2667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.7667, "x": 0.956, "y": 0.943, "curve": 0.347, "c2": 0.42, "c3": 0.681, "c4": 0.76}, {"time": 1, "x": 0.986, "y": 0.981}]}, "zheng_79": {"rotate": [{"angle": 9.11, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "angle": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 16.06, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 9.11}]}, "zheng_78": {"rotate": [{"angle": 3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 3}]}, "zheng_77": {"rotate": [{"angle": -2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.43}]}, "zheng_76": {"rotate": [{"angle": -1.77, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.74, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.77}]}, "zheng_75": {"rotate": [{"angle": -1.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -1.37}]}, "zheng_74": {"rotate": [{"angle": -0.98, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.16, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.98}]}, "zheng_73": {"rotate": [{"angle": -0.23, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.18, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -0.23}]}, "zheng_72": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.08, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "huo": {"rotate": [{"angle": -56.31}], "translate": [{"x": 65.2, "y": 76.62}], "scale": [{"x": 0.752, "y": 0.487}]}, "huo2": {"rotate": [{"angle": 61.06}], "translate": [{"x": 64.24, "y": -73.76}], "scale": [{"x": -0.752, "y": 0.487}]}}, "deform": {"default": {"zheng_2": {"lll": [{"offset": 166, "vertices": [2.21602, 0.28648, 2.21605, 0.28648, 6.17825, 1.18173, 6.17838, 1.18173, 6.79121, -0.24856, 6.79138, -0.24857, 1.42609, 0.29701, 1.4261, 0.29701]}, {"time": 0.5, "vertices": [-7.3071, 10.01831, -3.35266, 8.97349, -2.39052, 6.72846, 0.00992, 0.74394, 0, 0, 0, 0, -0.65299, -1.26341, 0.83086, -1.28319, 0.83089, -1.28319, 2.35145, 1.45279, 2.35152, 1.45279, 2.3373, 0.39286, 2.11406, -0.45223, 1.48948, 0.40418, 2.33168, -0.03109, 2.32316, -0.66703, 3.81273, -0.26285, 2.75565, -0.03675, 2.74149, -1.09665, 0.87332, 1.89652, 0.22044, 0.63311, 0.22045, 0.63311, 0, 0, 0, 0, 0, 0, 1.34113, 0.9782, -4.68745, -2.92578, -4.68745, -2.92576, -4.683, -2.59379, -2.04944, -4.28909, 1.87671, -8.65778, 1.87671, -8.65778, 3.64723, -0.38065, 3.64723, -0.38065, 3.97923, -0.3851, 3.97923, -0.3851, 2.65576, -0.03541, 2.65576, -0.03541, 6.31187, 0.24786, 6.31187, 0.24786, 3.00102, 0.95608, 3.00102, 0.95608, 0.0463, 3.47169, 0.0463, 3.47169, -0.43315, 4.71818, -4.12636, 6.75162, -5.83243, 9.00655, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.00543, 0.77134, 2.00544, 0.77134, -1.69034, 0.44658, -1.69031, 0.44658, 0, 0, 0, 0, -2.74734, 0.67267, -2.74726, 0.67267, -1.89658, 0.87336, -1.89652, 0.87336, 1.69286, -0.23459, 1.69289, -0.23459, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45848, 1.92761, -1.45843, 1.92761, -0.81698, 2.3431, -0.81692, 2.3431, 0.44371, 1.47822, 0.44373, 1.47822, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.50949, 0.04523, 12.50961, 0.04523, 12.08266, -0.16111, 1.74954, -0.02333, 1.7497, -0.02333, 0.47484, -0.21835, 0.475, -0.21835, -0.58217, 0.00776, -0.58205, 0.00776, -0.58217, 0.00776, -0.58205, 0.00776, -0.58217, 0.00776, -0.58205, 0.00776, -0.58217, 0.00776, -0.58205, 0.00776, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.17676, -0.25438, 3.17683, -0.25438, 4.25081, 0.7914, 4.25087, 0.7914, 2.96764, -0.03957, 2.96768, -0.03957, 2.54359, -0.03392, 2.54359, -0.03392, 2.54359, -0.03392, 2.54359, -0.03392, 2.54359, -0.03392, 3.79572, -1.53475, 3.79578, -1.53474, 5.09299, 0.35614, 5.09309, 0.35614, 2.56344, 1.44995, 2.56348, 1.44995, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.97898, -0.85111, 3.97899, -0.85111, 0, 0, 0, 0, 0, 0, 0, 0, 2.31937, -0.36295, 2.31937, -0.36295, 1.65985, -0.02213, 1.65985, -0.02213, 0, 0, 0, 0, 0, 0, 0, 0, -0.71088, 2.4897, -0.71088, 2.4897, 0.52568, 2.22519, 0.52568, 2.22519]}, {"time": 1, "offset": 166, "vertices": [2.21602, 0.28648, 2.21605, 0.28648, 6.17825, 1.18173, 6.17838, 1.18173, 6.79121, -0.24856, 6.79138, -0.24857, 1.42609, 0.29701, 1.4261, 0.29701]}]}}}}, "idle": {"bones": {"zheng_0": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 10.21, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_3": {"rotate": [{"angle": 0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.51, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_4": {"rotate": [{"angle": 3.97, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 5.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.93, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 3.97}], "translate": [{"x": -0.47, "y": 0.02, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -3.58, "y": 0.12, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "x": -0.47, "y": 0.02}]}, "zheng_5": {"rotate": [{"angle": 0.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.82}], "translate": [{"x": -2.45, "y": 0.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -6.66, "y": 0.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -2.45, "y": 0.03}]}, "zheng_6": {"translate": [{"y": -2.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 16.19, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -2.92}]}, "zheng_7": {"rotate": [{"angle": 0.46, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.68, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.46}], "translate": [{"x": -0.07, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -0.94, "y": 0.01, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -0.07}]}, "zheng_8": {"rotate": [{"angle": -14.12, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -20.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.52, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -14.12}], "translate": [{"x": -0.32, "y": 0.02, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -1.57, "y": 0.1, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "x": -0.32, "y": 0.02}]}, "zheng_9": {"rotate": [{"angle": -7.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -22.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 18.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.58}], "translate": [{"x": -1.02, "y": 0.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -2.77, "y": 0.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -1.02, "y": 0.22}]}, "zheng_10": {"rotate": [{"angle": -5.85, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": -29.07, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 13.6, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -5.85}], "translate": [{"x": -1.53, "y": 0.21, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -2.81, "y": 0.39, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "x": -1.53, "y": 0.21}]}, "zheng_11": {"rotate": [{"angle": 9.72, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": -29.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 25.44, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 9.72}], "translate": [{"x": -2.47, "y": 0.29, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -3.44, "y": 0.4, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "x": -2.47, "y": 0.29}]}, "zheng_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": 2.15, "y": -3.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.1, "y": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.15, "y": -3.83}]}, "zheng_12": {"rotate": [{"angle": 0.39, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.65, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.39}]}, "zheng_13": {"rotate": [{"angle": 0.6, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -1.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.19, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 0.6}]}, "zheng_14": {"rotate": [{"angle": -2.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -7.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 7.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -2.08}]}, "zheng_15": {"rotate": [{"angle": 2.22, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.91, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 2.22}]}, "zheng_1": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.21, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": 2.2, "y": -0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.96, "y": 5.46, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.2, "y": -0.03}]}, "zheng_16": {"rotate": [{"angle": -0.37, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.31, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.37}]}, "zheng_17": {"rotate": [{"angle": -0.19, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 2.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.04, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -0.19}]}, "zheng_18": {"rotate": [{"angle": 6.11, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 14.37, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 6.11}]}, "zheng_19": {"rotate": [{"angle": -1.79, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 9.16, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -10.98, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -1.79}]}, "zheng_21": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.86, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": -22.97, "y": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -7.44, "y": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -22.97, "y": 0.31}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.947, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -3.69, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": -22.97, "y": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -7.44, "y": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -22.97, "y": 0.31}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_20": {"translate": [{"x": -37.32, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 17.41, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -37.32, "y": 0.5}]}, "zheng_39": {"rotate": [{"angle": 2.56, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 2.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.53, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 2.56}]}, "zheng_40": {"rotate": [{"angle": 2.87, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 4.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.63, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 2.87}]}, "zheng_41": {"rotate": [{"angle": 2.98, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.08, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 2.98}]}, "zheng_42": {"rotate": [{"angle": 1.67, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 8.72, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -4.24, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 1.67}]}, "zheng_43": {"rotate": [{"angle": 1.49, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 12.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.81, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 1.49}]}, "zheng_44": {"rotate": [{"angle": -0.21, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 13.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.32, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -0.21}]}, "zheng_51": {"rotate": [{"angle": 4.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 12.13, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -7.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 4.8}]}, "zheng_52": {"rotate": [{"angle": -2.25, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 6.37, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.48, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -2.25}]}, "zheng_53": {"rotate": [{"angle": -5.94, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 7.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -11.41, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -5.94}]}, "zheng_55": {"rotate": [{"angle": 6.29, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 8.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.56, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 6.29}], "translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -2.22, "y": -3.71, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1}]}, "zheng_56": {"rotate": [{"angle": 3.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -8.34, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 3.57}], "translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -8.41, "y": -3.89, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1}]}, "zheng_58": {"rotate": [{"angle": 4.81, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.09, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 4.81}], "translate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -4.63, "y": -2.05, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1}]}, "zheng_59": {"rotate": [{"angle": 7.84, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 15.51, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.36, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 7.84}], "translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -3.8, "y": -5.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1}]}, "zheng_60": {"rotate": [{"angle": 5.39, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 22.45, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -8.9, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 5.39}], "translate": [{"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -5.37, "y": -7.52, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1}]}, "zheng_62": {"rotate": [{"angle": 10.91, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 14.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.8, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 10.91}]}, "zheng_64": {"rotate": [{"angle": 8.7, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 11.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.88, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 8.7}]}, "zheng_65": {"rotate": [{"angle": 9.02, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 16.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 9.02}]}, "zheng_66": {"rotate": [{"angle": 5.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 23.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.78, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 5.18}]}, "zheng_70": {"rotate": [{"angle": -0.61, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 6.23, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.33, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -0.61}]}, "zheng_71": {"rotate": [{"angle": 1.25, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.66, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": 1.25}]}, "zheng_47": {"rotate": [{"angle": 4.34, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 9.74, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 4.34}]}, "zheng_48": {"rotate": [{"angle": 6.6, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 20.18, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -4.79, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 6.6}]}, "zheng_46": {"rotate": [{"angle": 5.69, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.6, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 5.69}]}, "zheng_45": {"rotate": [{"angle": 3.31, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.07, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 3.31}]}, "zheng_49": {"rotate": [{"angle": 3.71, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 4.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.46, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 3.71}]}, "zheng_50": {"rotate": [{"angle": 5.61, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.46, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 5.61}]}, "zheng_54": {"rotate": [{"angle": 2.76, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 3.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.44, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 2.76}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.56, "y": -0.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1}]}, "zheng_57": {"rotate": [{"angle": 4.53, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 4.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.6, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 4.53}], "translate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -2.88, "y": -0.95, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1}]}, "zheng_61": {"rotate": [{"angle": 6.09, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 6.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.5, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 6.09}]}, "zheng_63": {"rotate": [{"angle": 8.48, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 9.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.59, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 8.48}]}, "zheng_69": {"rotate": [{"angle": 5.88, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 10.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.25, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 5.88}]}, "zheng_68": {"rotate": [{"angle": 6.49, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 8.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.5, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 6.49}]}, "zheng_67": {"rotate": [{"angle": 6.5, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 7.05, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -7.39, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 6.5}]}, "zheng_23": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.08, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "zheng_25": {"rotate": [{"angle": -0.98, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.16, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.98}]}, "zheng_26": {"rotate": [{"angle": -1.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -1.37}]}, "zheng_27": {"rotate": [{"angle": -1.77, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.74, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.77}]}, "zheng_28": {"rotate": [{"angle": -2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.43}]}, "zheng_29": {"rotate": [{"angle": 3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 3}]}, "zheng_30": {"rotate": [{"angle": 9.11, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "angle": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 16.06, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 9.11}]}, "zheng_31": {"rotate": [{"angle": 8.62, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 15.84, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 8.62}], "scale": [{"x": 0.986, "y": 0.981, "curve": 0.353, "c2": 0.65, "c3": 0.688}, {"time": 0.2667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.7667, "x": 0.956, "y": 0.943, "curve": 0.347, "c2": 0.42, "c3": 0.681, "c4": 0.76}, {"time": 1, "x": 0.986, "y": 0.981}]}, "zheng_32": {"rotate": [{"angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": 0.42}], "scale": [{"x": 0.96, "y": 0.972, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.3, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8, "x": 0.901, "y": 0.931, "curve": 0.345, "c2": 0.41, "c3": 0.679, "c4": 0.74}, {"time": 1, "x": 0.96, "y": 0.972}]}, "zheng_33": {"rotate": [{"angle": -2.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -2.45}], "scale": [{"x": 0.965, "y": 0.938, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 0.3333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8333, "x": 0.929, "y": 0.872, "curve": 0.343, "c2": 0.39, "c3": 0.676, "c4": 0.73}, {"time": 1, "x": 0.965, "y": 0.938}]}, "zheng_34": {"rotate": [{"angle": 9.57, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.3667, "angle": 29.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.48, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1, "angle": 9.57}], "scale": [{"x": 0.975, "y": 0.941, "curve": 0.359, "c2": 0.64, "c3": 0.695}, {"time": 0.3667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8667, "x": 0.957, "y": 0.898, "curve": 0.341, "c2": 0.38, "c3": 0.674, "c4": 0.71}, {"time": 1, "x": 0.975, "y": 0.941}]}, "zheng_35": {"rotate": [{"angle": -7.79, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 21.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.79}], "scale": [{"x": 0.951, "y": 0.909, "curve": 0.361, "c2": 0.64, "c3": 0.697}, {"time": 0.4, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.9, "x": 0.928, "y": 0.865, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.7}, {"time": 1, "x": 0.951, "y": 0.909}]}, "zheng_36": {"rotate": [{"angle": -10.96, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4333, "angle": 16.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -13.01, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1, "angle": -10.96}]}, "zheng_37": {"rotate": [{"angle": -17.54, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "angle": 28.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -18.62, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1, "angle": -17.54}]}, "zheng_38": {"rotate": [{"angle": -30.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 13.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -30.58}]}, "zheng_24": {"rotate": [{"angle": -0.23, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.18, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -0.23}]}, "zheng_87": {"rotate": [{"angle": -30.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 13.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -30.58}]}, "zheng_86": {"rotate": [{"angle": -17.54, "curve": 0.277, "c2": 0.12, "c3": 0.754}, {"time": 0.4667, "angle": 28.62, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -18.62, "curve": 0.313, "c3": 0.648, "c4": 0.35}, {"time": 1, "angle": -17.54}]}, "zheng_85": {"rotate": [{"angle": -10.96, "curve": 0.3, "c2": 0.21, "c3": 0.756}, {"time": 0.4333, "angle": 16.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -13.01, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 1, "angle": -10.96}]}, "zheng_84": {"rotate": [{"angle": -7.79, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 21.18, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -7.79}], "scale": [{"x": 0.951, "y": 0.909, "curve": 0.361, "c2": 0.64, "c3": 0.697}, {"time": 0.4, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.9, "x": 0.928, "y": 0.865, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.7}, {"time": 1, "x": 0.951, "y": 0.909}]}, "zheng_83": {"rotate": [{"angle": 9.57, "curve": 0.337, "c2": 0.35, "c3": 0.758}, {"time": 0.3667, "angle": 29.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.48, "curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 1, "angle": 9.57}], "scale": [{"x": 0.975, "y": 0.941, "curve": 0.359, "c2": 0.64, "c3": 0.695}, {"time": 0.3667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8667, "x": 0.957, "y": 0.898, "curve": 0.341, "c2": 0.38, "c3": 0.674, "c4": 0.71}, {"time": 1, "x": 0.975, "y": 0.941}]}, "zheng_82": {"rotate": [{"angle": -2.45, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 11.57, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -8, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -2.45}], "scale": [{"x": 0.965, "y": 0.938, "curve": 0.357, "c2": 0.65, "c3": 0.692}, {"time": 0.3333, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8333, "x": 0.929, "y": 0.872, "curve": 0.343, "c2": 0.39, "c3": 0.676, "c4": 0.73}, {"time": 1, "x": 0.965, "y": 0.938}]}, "zheng_81": {"rotate": [{"angle": 0.42, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -3.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": 0.42}], "scale": [{"x": 0.96, "y": 0.972, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.3, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.8, "x": 0.901, "y": 0.931, "curve": 0.345, "c2": 0.41, "c3": 0.679, "c4": 0.74}, {"time": 1, "x": 0.96, "y": 0.972}]}, "zheng_80": {"rotate": [{"angle": 8.62, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 15.84, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 8.62}], "scale": [{"x": 0.986, "y": 0.981, "curve": 0.353, "c2": 0.65, "c3": 0.688}, {"time": 0.2667, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 0.7667, "x": 0.956, "y": 0.943, "curve": 0.347, "c2": 0.42, "c3": 0.681, "c4": 0.76}, {"time": 1, "x": 0.986, "y": 0.981}]}, "zheng_79": {"rotate": [{"angle": 9.11, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.2333, "angle": 3.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 16.06, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 9.11}]}, "zheng_78": {"rotate": [{"angle": 3, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 8.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 3}]}, "zheng_77": {"rotate": [{"angle": -2.43, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.55, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": -2.43}]}, "zheng_76": {"rotate": [{"angle": -1.77, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.74, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.77}]}, "zheng_75": {"rotate": [{"angle": -1.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -10.52, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -1.37}]}, "zheng_74": {"rotate": [{"angle": -0.98, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.16, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.98}]}, "zheng_73": {"rotate": [{"angle": -0.23, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.18, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -0.23}]}, "zheng_72": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 4.08, "y": -0.05, "curve": 0.25, "c3": 0.75}, {"time": 1}]}}, "deform": {"default": {"zheng_2": {"lll": [{"offset": 166, "vertices": [2.21602, 0.28648, 2.21605, 0.28648, 6.17825, 1.18173, 6.17838, 1.18173, 6.79121, -0.24856, 6.79138, -0.24857, 1.42609, 0.29701, 1.4261, 0.29701]}, {"time": 0.5, "vertices": [-7.3071, 10.01831, -3.35266, 8.97349, -2.39052, 6.72846, 0.00992, 0.74394, 0, 0, 0, 0, -0.65299, -1.26341, 0.83086, -1.28319, 0.83089, -1.28319, 2.35145, 1.45279, 2.35152, 1.45279, 2.3373, 0.39286, 2.11406, -0.45223, 1.48948, 0.40418, 2.33168, -0.03109, 2.32316, -0.66703, 3.81273, -0.26285, 2.75565, -0.03675, 2.74149, -1.09665, 0.87332, 1.89652, 0.22044, 0.63311, 0.22045, 0.63311, 0, 0, 0, 0, 0, 0, 1.34113, 0.9782, -4.68745, -2.92578, -4.68745, -2.92576, -4.683, -2.59379, -2.04944, -4.28909, 1.87671, -8.65778, 1.87671, -8.65778, 3.64723, -0.38065, 3.64723, -0.38065, 3.97923, -0.3851, 3.97923, -0.3851, 2.65576, -0.03541, 2.65576, -0.03541, 6.31187, 0.24786, 6.31187, 0.24786, 3.00102, 0.95608, 3.00102, 0.95608, 0.0463, 3.47169, 0.0463, 3.47169, -0.43315, 4.71818, -4.12636, 6.75162, -5.83243, 9.00655, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.00543, 0.77134, 2.00544, 0.77134, -1.69034, 0.44658, -1.69031, 0.44658, 0, 0, 0, 0, -2.74734, 0.67267, -2.74726, 0.67267, -1.89658, 0.87336, -1.89652, 0.87336, 1.69286, -0.23459, 1.69289, -0.23459, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.45848, 1.92761, -1.45843, 1.92761, -0.81698, 2.3431, -0.81692, 2.3431, 0.44371, 1.47822, 0.44373, 1.47822, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.50949, 0.04523, 12.50961, 0.04523, 12.08266, -0.16111, 1.74954, -0.02333, 1.7497, -0.02333, 0.47484, -0.21835, 0.475, -0.21835, -0.58217, 0.00776, -0.58205, 0.00776, -0.58217, 0.00776, -0.58205, 0.00776, -0.58217, 0.00776, -0.58205, 0.00776, -0.58217, 0.00776, -0.58205, 0.00776, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.17676, -0.25438, 3.17683, -0.25438, 4.25081, 0.7914, 4.25087, 0.7914, 2.96764, -0.03957, 2.96768, -0.03957, 2.54359, -0.03392, 2.54359, -0.03392, 2.54359, -0.03392, 2.54359, -0.03392, 2.54359, -0.03392, 3.79572, -1.53475, 3.79578, -1.53474, 5.09299, 0.35614, 5.09309, 0.35614, 2.56344, 1.44995, 2.56348, 1.44995, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.97898, -0.85111, 3.97899, -0.85111, 0, 0, 0, 0, 0, 0, 0, 0, 2.31937, -0.36295, 2.31937, -0.36295, 1.65985, -0.02213, 1.65985, -0.02213, 0, 0, 0, 0, 0, 0, 0, 0, -0.71088, 2.4897, -0.71088, 2.4897, 0.52568, 2.22519, 0.52568, 2.22519]}, {"time": 1, "offset": 166, "vertices": [2.21602, 0.28648, 2.21605, 0.28648, 6.17825, 1.18173, 6.17838, 1.18173, 6.79121, -0.24856, 6.79138, -0.24857, 1.42609, 0.29701, 1.4261, 0.29701]}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]