[1, ["ecpdLyjvZBwrvm+cedCcQy", "aej5KCfVhFI6ZBtIoyh/zN", "f8npR6F8ZIZoCp3cKXjJQz", "81ALTeQlxGSaAwG34nRY43", "58Ux1ovWNBB6A9SQqn+okX", "dbNkclouNG/oj/cNi+uAJ8", "b7M8iPo6VAm4zfIrk8ddTu", "a8Pp4LpoZHEKMX0Wg0fTlB", "a1liQBVvdFh7FCAR5vNwAQ", "453vQnXglLXqY3Ltyxue3q", "f7293wEF9JhIuMEsrLuqng", "544ldEwv9ATrlFla7urT3B", "68RZTnZ/ZMP75CkJLkh/2I", "d6RdAU/89MIbLGS03gTg0Y"], ["node", "_spriteFrame", "_parent", "_N$file", "_textureSetter", "root", "notUnlockBox", "unlockBox", "equipBox", "data", "equipGridItem", "equiplockitem"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_anchorPoint", "_color"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_originalWidth", "_top", "_bottom", "node"], -2, 1], ["cc.Layout", ["_N$layoutType", "_resize", "_N$paddingTop", "_N$spacingX", "_N$paddingLeft", "_N$spacingY", "_enabled", "_N$paddingBottom", "node", "_layoutSize"], -5, 1, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 12, 9, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 5, 7], ["4069eX9B6xKXZWIlQ2qGhOl", ["UItype", "node", "labelArr", "equipBox", "unlockBox", "notUnlockBox", "equipGridItem", "equiplockitem"], 2, 1, 2, 1, 1, 1, 6, 6], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 8, 2], [13, 0, 1, 2, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [6, 0, 1, 2, 3, 4, 3], [6, 2, 3, 4, 1], [4, 0, 1, 2, 3, 4, 6, 7, 8, 6], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 2], [1, 0, 3, 5, 3], [1, 0, 2, 1, 5, 4], [2, 6, 1, 0, 3, 8, 9, 5], [2, 1, 0, 4, 2, 7, 3, 5, 8, 9, 8], [2, 0, 4, 2, 3, 5, 8, 9, 6], [7, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 9, 6, 4, 5, 10, 8, 2], [0, 0, 3, 7, 4, 11, 5, 3], [0, 0, 7, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 10, 8, 2], [0, 0, 7, 6, 4, 11, 5, 8, 2], [0, 0, 1, 7, 6, 4, 5, 3], [0, 0, 1, 7, 6, 4, 5, 10, 8, 3], [0, 0, 1, 7, 6, 4, 5, 8, 3], [8, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 2], [5, 1, 2, 1], [1, 0, 4, 2, 1, 5, 5], [1, 0, 3, 1, 5, 4], [1, 0, 5, 2], [1, 0, 3, 4, 2, 1, 5, 6], [2, 1, 0, 2, 8, 9, 4], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 6, 7, 6], [4, 0, 1, 2, 5, 3, 4, 6, 7, 7]], [[[[14, "M20_PrePare_Equip"], [15, "M20_PrePare_Equip", 1, [-7, -8], [[26, 0, -6, [-5], -4, -3, -2, 25, 26]], [27, -1, 0], [5, 750, 1334]], [16, "content", [-11, -12, -13, -14], [[28, 45, 15.680499999999938, 220, 400, -9], [32, 1, 2, 20, -10, [5, 750, 539]]], [0, "81M0x5MnhKL4jMjZVztdU0", 1, 0], [5, 750, 539], [0, 0.5, 1], [0, 277.34024999999997, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "usingnode", [-16, -17, -18, -19], [[9, 1, 135.50000000000006, -15]], [0, "18nfMVmTNC7aFgkvTxof4K", 1, 0], [5, 750, 603], [0, 230, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "title_zhua<PERSON><PERSON>", 3, [-21, -22, -23, -24], [[9, 1, 40.87399999999997, -20]], [0, "9dPy/AcMdGEK5uvL4aBAml", 1, 0], [5, 531, 47], [0, 237.12600000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bottom", [-27, -28], [[33, false, 0.75, 0.23, null, null, -25, 2], [29, 5, 779.3195000000001, 634, -26]], [0, "4e1G+WfPtPlKsNj5reeNkK", 1, 0], [5, 750, 554.6804999999999], [0, -389.65975000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 1, [5, 3], [[10, 45, 750, 1334, -29]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [8, "view", 5, [2], [[34, 0, -30, [12]], [10, 45, 240, 250, -31]], [0, "596dSxJWNFCLcDp5RKK5Zw", 1, 0], [5, 750, 554.6804999999999]], [3, "title", 2, [-33, -34], [[11, false, 1, 1, 20, -32, [5, 751, 54]]], [0, "95gpLOdQxF5rbH2gX07Z40", 1, 0], [5, 751, 54], [0, -47, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "title", 2, [-36, -37], [[11, false, 1, 1, 20, -35, [5, 751, 55]]], [0, "d9jtM+YQ9EXbDxtt+5gMbd", 1, 0], [5, 751, 55], [0, -101.5, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "bg_goods_01", [[[17, "New Node", 147, -39, [0, "73JoxLZ3hFDbHA0diFaOZN", 1, 0], [4, 4278190080], [5, 165, 221]], -40], 4, 1], [[4, 1, 0, -38, [23], 24]], [0, "94qRFSs6lJT75mnSXlpz1e", 1, 0], [5, 165, 221], [-262.5, 139.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "maskbg", 1, [[30, 45, -41], [4, 1, 0, -42, [0], 1]], [0, "b3IKvCvfZOQLiy5g0K3YYZ", 1, 0], [5, 750, 1334]], [1, "bg_gfb", 5, [[4, 1, 0, -43, [2], 3], [31, 45, -22.65975000000006, -24.659750000000003, 138, 111, -44]], [0, "ad8MXRrwJAM4YvBG+ZGoUQ", 1, 0], [5, 750, 602], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 8, [[6, "已解锁", 36, false, 1, 1, -45, [6], 7], [2, 3, -46, [4, 4278190080]]], [0, "a9wQw+yzNDDrBt49++MjJb", 1, 0], [5, 114, 56.4], [0, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content", 2, [[12, 1, 3, 30, 20, 20, 10, 18, -47, [5, 750, 0]]], [0, "03bQ/ncB5BFbnkj8SZL2SH", 1, 0], [5, 750, 0], [0, -74, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 9, [[6, "待解锁", 36, false, 1, 1, -48, [10], 11], [2, 3, -49, [4, 4278190080]]], [0, "a7ajnBWt5IWbW3/Gk6Jbzq", 1, 0], [5, 114, 56.4], [0, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "content", 2, [[12, 1, 3, 30, 20, 200, 10, 18, -50, [5, 750, 410]]], [0, "bdOWRMCGRCjbYcNZUWeyO+", 1, 0], [5, 750, 410], [0, 0.5, 1], [0, -129, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "New Label", 4, [[6, "上阵法宝", 36, false, 1, 1, -51, [19], 20], [2, 3, -52, [4, 4280098849]]], [0, "39nD9SW8VKXbtJCEBaVVoN", 1, 0], [4, 4293062126], [5, 150, 56.4], [0, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "equipcount", 4, [[-53, [2, 2, -54, [4, 4278190080]]], 1, 4], [0, "23rwFfhQVEuK+zq8jwDpKd", 1, 0], [4, 4293062126], [5, 52.06, 54.4], [302.699, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bgBox", 3, [10], [[13, 3, 30, 50, 10, 18, -55, [5, 750, 600]]], [0, "14Zx5Aql9PILv5CBN40sLz", 1, 0], [5, 750, 600], [0, -75.5, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Label_plus", false, 10, [[35, "+", 60, false, 1, 1, -56, [22]], [2, 2, -57, [4, 4287598479]]], [0, "77sMsBzetCGJSBjJStAPCu", 1, 0], [5, 41.56, 54.4]], [1, "equipBox", 3, [[13, 3, 30, 50, 10, 18, -58, [5, 750, 600]]], [0, "e3Ca7oS81KdL0IoNSF5FjL", 1, 0], [5, 750, 600], [0, -75.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 8, [[5, -59, [4], 5]], [0, "00dbPjXrVF6qh5hoh69kcq", 1, 0], [5, 531, 47], [12, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "line", 9, [[5, -60, [8], 9]], [0, "f1j7moGptOH6TFGPCOIyLF", 1, 0], [5, 531, 47], [12, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [22, "uibg01", false, 3, [[4, 1, 0, -61, [13], 14]], [0, "7cbMoKScJNwZnNHi2j3YwG", 1, 0], [5, 849, 599], [0, 0.5, 1], [0, 250.412, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "icon_zhuangbei_01", false, 4, [[5, -62, [15], 16]], [0, "f0d+lxTY1NrJf7ErYv26du", 1, 0], [5, 65, 51], [-334.381, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_tc_jsb", 4, [[5, -63, [17], 18]], [0, "a621lCjlVGwqHNFpVvgxBZ", 1, 0], [5, 531, 47], [-12, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "8/8", 32, false, 1, 1, 1, 18, [21]]], 0, [0, 5, 1, 0, 6, 16, 0, 7, 14, 0, 8, 21, 0, -1, 27, 0, 0, 1, 0, -1, 11, 0, -2, 6, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 14, 0, -3, 9, 0, -4, 16, 0, 0, 3, 0, -1, 24, 0, -2, 4, 0, -3, 19, 0, -4, 21, 0, 0, 4, 0, -1, 25, 0, -2, 26, 0, -3, 17, 0, -4, 18, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, -2, 7, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 22, 0, -2, 13, 0, 0, 9, 0, -1, 23, 0, -2, 15, 0, 0, 10, 0, 2, 10, 0, -2, 20, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 27, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 9, 1, 2, 2, 7, 3, 2, 6, 5, 2, 6, 10, 2, 19, 63], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27], [-1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, -1, 1, -1, 1, -1, 1, -1, 3, -1, -1, -1, 1, 10, 11, 3], [0, 3, 0, 4, 0, 1, 0, 2, 0, 1, 0, 2, 0, 0, 5, 0, 6, 0, 1, 0, 2, 0, 0, 0, 7, 8, 9, 10]], [[{"name": "sp_bg0", "rect": [0, 0, 720, 400], "offset": [0, 0], "originalSize": [720, 400], "capInsets": [0, 80, 0, 0]}], [3], 0, [0], [4], [11]], [[{"name": "type_0", "rect": [0, 0, 170, 217], "offset": [0, 0], "originalSize": [170, 217], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [4], [12]], [[{"name": "icon_zhuangbei_01", "rect": [0, 0, 65, 51], "offset": [0, 0], "originalSize": [65, 51], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [4], [13]]]]