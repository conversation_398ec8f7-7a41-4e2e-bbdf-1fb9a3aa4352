[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "65OUoZ25pGHqftEDT5VTS4", "29FYIk+N1GYaeWH/q1NxQO", "97IAXRHnlLq7dNeJPB6SbL", "05U5Pxb/BBTZHwRiiAEowD", "f6NcoTBPNJ4qSyD40PNpRP", "23Tw89umhO5pVi85QDxfi5", "c9YjN9JeZO5a//ZfyagWSP", "6fAm8iBJdM86X5VfFktFgv"], ["node", "_spriteFrame", "_parent", "_N$disabledSprite", "templete", "root", "my<PERSON>rid<PERSON>iew", "_N$target", "_N$content", "data"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_styleFlags", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content"], 3, 1, 5, 1], ["74a96OsYzRAS5IsXSM0LPWL", ["node", "sprArr", "my<PERSON>rid<PERSON>iew"], 3, 1, 2, 1]], [[3, 0, 1, 2, 2], [1, 1, 0, 2, 3, 4, 3], [0, 0, 7, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [2, 0, 1, 3, 4, 4], [5, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 9, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 3, 7, 6, 4, 10, 5, 8, 3], [0, 0, 1, 7, 6, 4, 10, 5, 8, 3], [0, 0, 7, 9, 6, 4, 5, 11, 8, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [6, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 4, 2], [2, 2, 0, 1, 4, 4], [1, 0, 2, 3, 4, 2], [1, 1, 2, 3, 4, 2], [1, 2, 3, 1], [3, 1, 2, 1], [4, 0, 1, 5, 2, 3, 4, 8, 9, 7], [4, 0, 6, 1, 2, 3, 4, 7, 8, 9, 8], [8, 0, 1, 2, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 1], [14, 0, 1, 2, 1]], [[5, "PetSkillListView"], [6, "PetSkillListView", 1, [-5, -6], [[32, -4, [-3], -2]], [23, -1, 0], [5, 750, 1334]], [11, "title_zhua<PERSON><PERSON>", [-8, -9, -10, -11], [[1, 1, 0, -7, [14], 15]], [0, "45NuY+WQ9L5J6TOpjXxAQa", 1, 0], [5, 680, 82], [0, 423.977, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "content", [-12, 2, -13, -14], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 680, 914]], [3, "Background", 2, [-18], [[21, 1, -15, [6], 7], [27, 3, -17, [[28, "74a96OsYzRAS5IsXSM0LPWL", "close", 1]], [4, 4293322470], [4, 3363338360], -16, 8]], [0, "90OlI7eu5N6YuKmHM/sieV", 1, 0], [5, 52, 56], [269.386, -0.668, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "New ScrollView", 3, [-22], [[[30, false, 0.75, 0.23, null, null, -20, -19], -21], 4, 1], [0, "c16wooUcRFu7atWQTNsgll", 1, 0], [5, 620, 700], [0, -99.663, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "view", 5, [-25], [[29, 0, -23, [18]], [4, 45, 240, 250, -24]], [0, "f6rJOdci5LpqR2itdXzuYz", 1, 0], [5, 620, 700], [0, 0.5, 1], [0, 350, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "content", 6, [[19, 0, 41, 614, -26]], [0, "ecKprdHUBMRoHqCj7C6P4V", 1, 0], [5, 620, 274], [0, 0, 1], [-310, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "maskbg", 200, 1, [[18, 45, -27], [20, 0, -28, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [8, "bg", 1, [3], [[4, 45, 750, 1334, -29]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [2, "Label_title", 2, [[24, "技能详情", false, 1, 1, 1, 2, -30, [4]], [26, 4, -31, [4, 4278190080]]], [0, "6aRvpyzjtLSqbRuVrwAnNW", 1, 0], [5, 285, 56.4], [0, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_djd", 2, [-33], [[1, 1, 0, -32, [12], 13]], [0, "1bL2rLZKBOLJ4JxDtJDE8Y", 1, 0], [5, 68, 68], [0, -94.104, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg", 3, [[1, 1, 0, -34, [2], 3]], [0, "32XWbHc3VCJoxAH6kEEwjT", 1, 0], [5, 680, 914]], [12, "Label", false, 4, [[25, "返回", false, false, 1, 1, 1, 1, -35, [5]]], [0, "32xj1jPPxLkrFw1riXxUhd", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_fgx", 2, [[1, 1, 0, -36, [9], 10]], [0, "f3SWzwY29A06pnUAmYwz6l", 1, 0], [5, 600, 6], [0, -149.363, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "imgSkill", 11, [-37], [0, "81bejRawtPh4oj6wXfGvgM", 1, 0], [5, 73, 90], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [22, 15, [11]], [13, "fkbg_02", 83, 3, [[1, 1, 0, -38, [16], 17]], [0, "ba3dgY0OJGsr6W/o7C1onV", 1, 0], [4, 4294962402], [5, 640, 810], [0, -34.559, 0, 0, 0, 0, 1, 1, 1, 1]], [31, 5, [0, 0, 20], 7]], 0, [0, 5, 1, 0, 6, 18, 0, -1, 16, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, 0, 2, 0, -1, 10, 0, -2, 4, 0, -3, 14, 0, -4, 11, 0, -1, 12, 0, -3, 17, 0, -4, 5, 0, 0, 4, 0, 7, 4, 0, 0, 4, 0, -1, 13, 0, 8, 7, 0, 0, 5, 0, -2, 18, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -1, 15, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, -1, 16, 0, 0, 17, 0, 9, 1, 2, 2, 3, 3, 2, 9, 38], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 18], [-1, 1, -1, 1, -1, -1, -1, 1, 3, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, 4], [0, 1, 0, 2, 0, 0, 0, 3, 4, 0, 5, 0, 0, 6, 0, 7, 0, 8, 0, 9, 10]]