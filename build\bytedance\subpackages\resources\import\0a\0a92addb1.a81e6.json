[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "2cNmF5A5xPAoYF3UslMZlI", "d3hPg+YpNPrqQd3FIsPNC/", "1dId2QPmBD5bHGxaYmRvM5", "87o0XdNbdCQKPow54f97QC", "e6pgxsVU5OhKQO6svC3ZlO", "32wZeVza9Bn6YCalWckO7d"], ["node", "_spriteFrame", "_textureSetter", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_anchorPoint", "_children"], 0, 9, 4, 5, 1, 7, 5, 2], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "loop", "premultipliedAlpha", "defaultAnimation", "_animationName", "node", "_materials"], -3, 1, 3], ["cc.Prefab", ["_name"], 2], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_offset", "_size"], 3, 1, 5, 5], ["4b202H7DOVAy5gdIzRdnyRl", ["node"], 3, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6]], [[2, 0, 1, 2, 2], [9, 0, 1, 2, 1], [0, 0, 1, 6, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 5, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 1, 2, 2], [4, 0, 2], [0, 0, 1, 9, 3, 4, 5, 8, 3], [0, 0, 2, 1, 6, 3, 4, 5, 7, 4], [0, 0, 2, 1, 6, 3, 4, 5, 8, 7, 4], [0, 0, 2, 6, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 5, 2], [5, 0, 1, 2, 1], [6, 0, 1], [2, 1, 2, 1], [3, 0, 1, 2, 3, 6, 7, 5], [3, 0, 4, 1, 5, 6, 7, 5]], [[[[6, "ModeGuardingPail"], [7, "ModeGuardingPail", 3, [-4, -5, -6, -7, -8, -9, -10, -11], [[12, -2, [0, 0, 30], [5, 100, 50]], [13, -3]], [14, -1, 0], [5, 0, 50.4], [0, 0.5, 0]], [2, "lb_desc", 3, 1, [[4, "攻击力 +10", 20, false, 1, 1, -12, [10]], [5, 3, -13, [4, 4278190080]]], [0, "07CJrlAyBN4467osY6B3V2", 1, 0], [5, 105.48, 56.4], [0, 63.12, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lb_num", 3, 1, [[4, "100", 30, false, 1, 1, -14, [11]], [5, 3, -15, [4, 4278190080]]], [0, "c5OMbZByRB97963SjUEkFj", 1, 0], [5, 56.05, 56.4], [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "role_shadow", false, 3, 1, [[1, -16, [0], 1]], [0, "2exn31a3tGcZcci6+n6NXt", 1, 0], [5, 43, 19], [-0.187, 1.431, 0, 0, 0, 0, 1, 0.7, 0.6, 1]], [9, "role", false, 3, 1, [[15, "default", 0, false, false, -17, [2]]], [0, "81opSTUghCmIaDIq+F1+xC", 1, 0], [5, 82, 89.93084716796875], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [10, "img_bx", false, 1, [[1, -18, [3], 4]], [0, "95B05HHwVIP4IKZn6cdJtG", 1, 0], [5, 134, 112], [0, 35.083, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [11, "flower", 1, [[16, "default", "idle", 0, "idle", -19, [5]]], [0, "f5oh6labNFtoFoFFHS6ou/", 1, 0], [5, 134.00001525878906, 110]], [3, "img_aperture", 1, [[1, -20, [6], 7]], [0, "d9fnuMqKBOM6OPe/x8jTUJ", 1, 0], [5, 112, 112], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "img_icon", 1, [[1, -21, [8], 9]], [0, "4f1y/bsyJMxaqTPML5ikI5", 1, 0], [5, 77, 61], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 2, 0, -8, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 4, 1, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1], [0, 2, 1, 0, 3, 1, 0, 4, 0, 5, 0, 0]], [[{"name": "img_aperture", "rect": [0, 0, 112, 112], "offset": [0, 0], "originalSize": [112, 112], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[{"name": "skill01", "rect": [9, 17, 77, 61], "offset": [0.5, -0.5], "originalSize": [94, 94], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]]]]