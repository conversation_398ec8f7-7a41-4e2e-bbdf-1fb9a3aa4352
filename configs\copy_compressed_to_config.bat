@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

set "SRC=%~dp0config\*.txt"
set "DEST=..\assets\resources\config"

echo Moving compressed config files...

REM Check if source directory exists
if not exist "%~dp0config" (
    echo Error: Source directory config does not exist!
    echo Please run compress-configs.js first to generate compressed files
    pause
    exit /b 1
)

REM Create target directory if it doesn't exist
mkdir "%DEST%" 2>nul

REM Clear all contents in target directory
echo Clearing target directory: %DEST%
if exist "%DEST%\*" (
    del /q "%DEST%\*" 2>nul
    echo Target directory cleared
) else (
    echo Target directory is empty, no need to clear
)

REM Check if there are files to move
set hasFiles=0
for %%f in ("%SRC%") do (
    set hasFiles=1
    goto :break
)
:break

if !hasFiles!==0 (
    echo Warning: No .txt files found in config directory
    echo Please run compress-configs.js first to generate compressed files
    pause
    exit /b 1
)

REM Move all .txt files
echo Moving compressed config files...
set count=0
for %%f in ("%SRC%") do (
    move "%%f" "%DEST%\" >nul 2>&1
    if !errorlevel!==0 (
        set /a count+=1
        echo Moved: %%~nxf
    ) else (
        echo Failed to move: %%~nxf
    )
)

echo.
echo Operation completed!
echo Moved !count! compressed config files to %DEST%
echo.
echo Note: Files have been moved from config directory to target directory
echo To regenerate, please run compress-configs.js
pause
