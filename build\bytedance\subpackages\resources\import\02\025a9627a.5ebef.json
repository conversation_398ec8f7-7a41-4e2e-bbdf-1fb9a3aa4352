[1, ["ecpdLyjvZBwrvm+cedCcQy", "deZPZ1JRVM0qlD7gsc9RLG", "f8npR6F8ZIZoCp3cKXjJQz", "a2MjXRFdtLlYQ5ouAFv/+R", "aej5KCfVhFI6ZBtIoyh/zN", "b76uHOgvlBK44IpR7/IIXg", "2fyKStw/BIZJo+ld7ER3CQ", "60IonSRjVFobQoyrV87Agx", "350a026blFRYMnKQAxLxgU", "f7293wEF9JhIuMEsrLuqng", "17uhJZ2qZL36GVPROTFWr1", "a0W+23ZZtFoYIVTrXP6kHy", "a0n9O63l9No4o7eAkLHEnG"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "root", "rText", "data", "_parent", "_N$font"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_eulerAngles"], -1, 4, 5, 9, 1, 2, 7, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "toValue", "node"], 0, 1], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["a9c34adABlMv6K5e2OGXbwX", ["UItype", "node", "nodeArr", "labelArr", "rText"], 2, 1, 2, 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node"], -1, 1]], [[5, 0, 1, 2, 2], [2, 2, 3, 4, 1], [0, 0, 7, 6, 4, 5, 9, 2], [0, 0, 7, 6, 4, 5, 2], [3, 0, 3, 2], [11, 0, 1, 2, 2], [7, 0, 2], [0, 0, 2, 8, 6, 4, 5, 3], [0, 0, 8, 4, 5, 2], [0, 0, 1, 7, 8, 6, 4, 5, 9, 3], [0, 0, 7, 8, 6, 4, 5, 9, 2], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 9, 11, 2], [0, 0, 1, 7, 6, 4, 5, 3], [8, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 5, 2], [10, 0, 1, 2, 3, 4, 2], [5, 1, 2, 1], [2, 0, 1, 2, 3, 3], [2, 0, 2, 3, 4, 2], [3, 0, 1, 3, 3], [3, 0, 1, 2, 3, 4], [4, 0, 1, 4, 3], [4, 0, 4, 2], [4, 0, 2, 3, 4, 4], [6, 0, 1, 5, 2, 3, 4, 6, 8, 9, 10, 8], [6, 0, 1, 2, 3, 4, 7, 8, 9, 7], [12, 0, 1, 2, 3, 4, 5]], [[[[6, "It<PERSON>_NewUnlock"], [7, "It<PERSON>_NewUnlock", 1, [-6, -7], [[17, 0, -5, [-4], [-3], -2]], [18, -1, 0], [5, 750, 1334]], [8, "content", [-8, -9, -10, -11, -12, -13, -14, -15], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [9, "faguang_big", false, 2, [-18, -19], [[1, -16, [13], 14], [21, 4, 0.6, -17]], [0, "a4k8qouzBChInHyVOxPaXP", 1, 0], [5, 194, 193], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [10, "title_zhua<PERSON><PERSON>", 2, [-22], [[1, -20, [3], 4], [23, 1, 256.03999999999996, -21]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 531, 47], [-12, 283.46000000000004, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 2, [[19, 2, false, -23, [17]], [22, 3, 1, [0, 20], -24]], [0, "b71ozsRZVHg4X4Nuas/cNa", 1, 0], [5, 200, 200], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [11, "maskbg", 200, 1, [[24, 45, -25], [20, 0, -26, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [12, "bg", 1, [2], [[25, 45, 750, 1334, -27]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [15, "Label_title", 4, [[-28, [5, 3, -29, [4, 4278190080]]], 1, 4], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 332, 56.4], [12, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_xx", 3, [[1, -30, [9], 10], [4, 4, -31]], [0, "ef8Kh93PZGoLd7h+5xQK1D", 1, 0], [5, 55, 55], [46.2575, 35.9105, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]], [13, "img_xx", 3, [[1, -32, [11], 12], [4, 4, -33]], [0, "ea7PwktghEKpqKwnGMyNED", 1, 0], [5, 55, 55], [-64.517, -5.478, 0, 0, 0, -0.24192189559966773, 0.9702957262759965, 0.8, 0.8, 0.5], [1, 0, 0, -28]], [2, "Label", 2, [[26, "点击空白继续", 36, false, false, 1, 1, 1, -34, [18], 19], [5, 2, -35, [4, 4278190080]]], [0, "fdJYYGS89B96h2JTQGYsOS", 1, 0], [5, 220, 54.4], [0, -370, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "解锁新技能", 36, false, 1, 1, 3, 8, [2]], [3, "pic_jl_00", 2, [[1, -36, [5], 6]], [0, "bekkM/bCBPt5NtUmKFw5vC", 1, 0], [5, 608, 587]], [3, "sp_light", 2, [[1, -37, [7], 8]], [0, "51ZdTJMVlChYnNPCFrWNex", 1, 0], [5, 511, 505]], [14, "img_tbd", false, 2, [[1, -38, [15], 16]], [0, "3cgMeSSAlKlZJTcQhwlbS7", 1, 0], [5, 114, 114]], [16, "New RichText", 2, [-39], [0, "43ZeyhBaxKFL6v6JTrXiKp", 1, 0], [5, 0, 63], [0, -224.574, 0, 0, 0, 0, 1, 1, 1, 1]], [28, false, "", 36, 50, 16]], 0, [0, 4, 1, 0, 5, 17, 0, -1, 12, 0, -1, 5, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -1, 4, 0, -2, 13, 0, -3, 14, 0, -4, 3, 0, -5, 15, 0, -6, 5, 0, -7, 11, 0, -8, 16, 0, 0, 3, 0, 0, 3, 0, -1, 9, 0, -2, 10, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 12, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, -1, 17, 0, 6, 1, 2, 7, 7, 39], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 17], [-1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 3, 3, 8], [0, 3, 0, 0, 4, 0, 5, 0, 6, 0, 1, 0, 1, 0, 7, 0, 8, 0, 0, 2, 2, 9]], [[{"name": "img_tbd", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [10]], [[{"name": "faguang_big", "rect": [0, 0, 194, 193], "offset": [0, 0], "originalSize": [194, 193], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [11]], [[{"name": "img_xx", "rect": [0, 0, 55, 55], "offset": [0, 0], "originalSize": [55, 55], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [12]]]]