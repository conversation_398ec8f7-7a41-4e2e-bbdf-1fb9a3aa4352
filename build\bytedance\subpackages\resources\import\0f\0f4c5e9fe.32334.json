[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "e4fignOndKb6jUTfeOaOrT", "a2MjXRFdtLlYQ5ouAFv/+R", "aej5KCfVhFI6ZBtIoyh/zN", "39EVKhiWpJZoO45vY8RmLD", "b76uHOgvlBK44IpR7/IIXg", "2fyKStw/BIZJo+ld7ER3CQ", "29FYIk+N1GYaeWH/q1NxQO", "f7293wEF9JhIuMEsrLuqng"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "root", "gname", "img", "_N$target", "data", "_parent", "_N$disabledSprite"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "_right", "alignMode", "node"], -3, 1], ["cc.Sprite", ["_sizeMode", "_enabled", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_enableWrapText", "_lineHeight", "_N$cacheMode", "_styleFlags", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 12, 4, 5, 5, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["7b786YJg6ZDOpzVRlQTM+3h", ["node", "img", "gname"], 3, 1, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "node"], 1, 1]], [[4, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 9, 2], [12, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 2], [2, 3, 4, 5, 1], [1, 0, 2, 3, 6, 4], [6, 0, 2], [0, 0, 1, 8, 6, 4, 5, 3], [0, 0, 8, 4, 5, 2], [0, 0, 7, 8, 6, 4, 5, 9, 2], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 2, 7, 8, 6, 4, 5, 9, 3], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 7, 6, 4, 5, 11, 9, 2], [7, 0, 1, 2, 3, 4, 5, 6, 7, 2], [8, 0, 1, 2, 3, 4, 5, 2], [9, 0, 1, 2, 1], [4, 1, 2, 1], [2, 1, 2, 0, 3, 4, 5, 4], [2, 0, 3, 4, 5, 2], [2, 3, 4, 1], [1, 0, 1, 6, 3], [1, 0, 4, 1, 6, 4], [1, 5, 0, 2, 3, 6, 5], [1, 0, 6, 2], [10, 0, 1, 2, 3, 4, 5, 6, 2], [11, 0, 1, 2, 3], [3, 0, 1, 5, 2, 4, 3, 9, 10, 11, 7], [3, 0, 1, 6, 5, 2, 4, 3, 7, 9, 10, 11, 9], [3, 0, 1, 2, 3, 9, 10, 11, 5], [3, 0, 1, 2, 8, 4, 3, 9, 10, 7], [13, 0, 1, 2, 3]], [[[{"name": "title_huode", "rect": [0, 0, 233, 87], "offset": [0, 0], "originalSize": [233, 87], "capInsets": [107, 0, 109, 0]}], [5], 0, [0], [3], [2]], [[[6, "M20_Pop_NewEquipUnlock"], [7, "M20_Pop_NewEquipUnlock", 1, [-5, -6], [[16, -4, -3, -2]], [17, -1, 0], [5, 750, 1334]], [8, "content", [-7, -8, -9, -10, -11, -12, -13, -14], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [9, "title_zhua<PERSON><PERSON>", 2, [-17, -18], [[18, false, 1, 0, -15, [6], 7], [21, 1, 236.03999999999996, -16]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 500, 87], [0, 283.46000000000004, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg", 1, [-20, 2], [[5, 45, 750, 1334, -19]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [11, "btn_norget", false, 2, [-24], [[25, 3, -22, [[26, "676c7mT+BhM5JxSmXY2oShw", "close", 1]], [4, 4293322470], [4, 3363338360], -21, 15], [22, 33, 215.5, 829.0409999999999, -23]], [0, "4cCOGh02pHoo91MQjCe8ib", 1, 0], [5, 248, 100], [0, -316.04099999999994, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "mask", 200, 4, [[19, 0, -25, [0], 1], [5, 45, 100, 100, -26]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "Label_title", 3, [[27, "解锁新技能", 36, false, false, 1, 1, -27, [4], 5], [2, 3, -28, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 186, 56.4], [0, 5.143, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "faguang_big", 2, [[4, -29, [10], 11], [31, 6, 2, -30]], [0, "a4k8qouzBChInHyVOxPaXP", 1, 0], [5, 511, 505]], [1, "Label", 2, [[28, "点击空白继续", 32, 32, false, false, 1, 1, 1, -31, [13], 14], [2, 2, -32, [4, 4278190080]]], [0, "fdJYYGS89B96h2JTQGYsOS", 1, 0], [5, 196, 44.32], [0, -370, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 5, [[23, 0, 45, 100, 40, -33]], [0, "cejm4E0KlMa5vsqUbgnSWu", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "Label_unlock", 2, [[29, "已解锁", 32, false, 1, -34, [16], 17], [2, 2, -35, [4, 4278190080]]], [0, "35H8+RApJDoZNlsis06wyc", 1, 0], [5, 100, 54.4], [0, 0, 0.5], [4.1, -218.341, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Label_equipname", 2, [[-36, [2, 2, -37, [4, 4278190080]]], 1, 4], [0, "20K1edhStO8L/DHu/cUnPg", 1, 0], [4, 4278243839], [5, 68, 54.4], [0, 1, 0.5], [-7.948, -218.341, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "maskbg", 1, [[24, 45, -38]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [1, "bg_tc_jsb", 3, [[4, -39, [2], 3]], [0, "3cwsojgO5Ls44+y8pvSitJ", 1, 0], [5, 531, 47], [-12, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pic_jl_00", 2, [[4, -40, [8], 9]], [0, "47oPbAxiNHhbIXxAOfWNmq", 1, 0], [5, 608, 587]], [15, "equip_node", 2, [-41], [0, "b71ozsRZVHg4X4Nuas/cNa", 1, 0], [5, 147, 252], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [20, 16, [12]], [30, "飞斧", 32, false, 1, 2, 1, 12, [18]]], 0, [0, 4, 1, 0, 5, 18, 0, 6, 17, 0, 0, 1, 0, -1, 13, 0, -2, 4, 0, -1, 3, 0, -2, 15, 0, -3, 8, 0, -4, 16, 0, -5, 9, 0, -6, 5, 0, -7, 11, 0, -8, 12, 0, 0, 3, 0, 0, 3, 0, -1, 14, 0, -2, 7, 0, 0, 4, 0, -1, 6, 0, 7, 10, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 18, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, -1, 17, 0, 8, 1, 2, 9, 4, 41], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18], [-1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, -1, 2, 10, -1, 2, -1, 2], [0, 3, 0, 4, 0, 1, 0, 5, 0, 6, 0, 7, 0, 0, 1, 8, 0, 1, 0, 9]]]]