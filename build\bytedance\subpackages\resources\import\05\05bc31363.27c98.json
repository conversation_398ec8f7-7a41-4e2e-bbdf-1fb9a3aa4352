[1, ["ecpdLyjvZBwrvm+cedCcQy", "877Vdruw9FpYh9zQ9ZBMEd", "d7o1LFt2hIdKoMlo4Zuq56", "79mU29EK5LiaD6bf5FgR6l", "0btlhzWrRIYr34sA95LRp1", "5elRwD0DdODol54diH9QGg", "4fSCCNhqlEpooZJ4Sq74nQ", "b27CHPk5lCna5xZLwaDYdZ", "847uA966ZMM4xY2ASONO3n", "a2MjXRFdtLlYQ5ouAFv/+R", "42YUPbnM9HG4/lgz1lZ8GZ"], ["node", "_spriteFrame", "root", "asset", "_parent", "_textureSetter", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_color"], -1, 4, 1, 9, 5, 7, 12, 5], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs", "_parent"], 2, 2, 9, 4, 5, 7, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["fb225lOSQ5G161kdxcZ9E9x", ["node", "nodeArr", "lbArr"], 3, 1, 12, 2], ["cc.BlockInputEvents", ["_enabled", "node"], 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Label", ["_string", "_lineHeight", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3]], [[2, 0, 1, 2, 2], [4, 0, 1, 2, 3, 2], [1, 0, 6, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3, 3], [0, 0, 5, 4, 2], [0, 0, 5, 6, 4, 7, 2], [0, 0, 5, 4, 8, 2], [3, 0, 1, 2, 3, 4, 4], [6, 0, 2], [0, 0, 1, 9, 6, 4, 7, 8, 3], [0, 0, 2, 5, 6, 4, 10, 7, 3], [0, 0, 5, 6, 4, 7, 8, 2], [0, 0, 3, 5, 6, 4, 7, 8, 3], [1, 0, 1, 2, 3, 4, 5, 2], [1, 0, 6, 1, 2, 3, 4, 2], [7, 0, 1, 2, 3, 4, 5, 2], [2, 1, 2, 1], [9, 0, 1, 2, 1], [3, 0, 1, 3, 4, 3], [4, 1, 2, 3, 1], [10, 0, 1, 2], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "img_txzld3", "rect": [0, 0, 304, 117], "offset": [0, 0], "originalSize": [304, 117], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [5], [2]], [[[8, "FundIdleTaskItem"], [9, "FundIdleTaskItem", 1, [[-8, -9, -10, -11, -12, -13, -14, [4, "New Node", -16, [0, "e8GglMu3tCFYNh+rVcsfKi", -15, 0]]], 1, 1, 1, 1, 1, 1, 1, 4], [[17, -7, [[null, null, null, -3, null, -4, -5, null, -6, null], 0, 0, 0, 1, 0, 1, 1, 0, 1, 0], [-2]]], [16, -1, 0], [5, 608, 117], [0, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "rwNode", [-18, -19], [[7, 1, 1, 20, -17, [5, 252, 117]]], [0, "5cd0+FiDxPT6XFVs68m0xl", 1, 0], [5, 252, 117], [7.22, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [14, "allNode", 1, [-21, -22], [[18, 1, 1, -20, [5, 608, 117]]], [0, "7drwqb5YJGF63FDWT96tAk", 1, 0], [5, 608, 117]], [2, "icon_djd", 1, [-24], [[19, -23, [14], 15]], [0, "a9SGUi1dJAnK/5HaGJ8qqh", 1, 0], [5, 70, 76], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [10, "New Sprite(Splash)", 156, 1, [[1, 0, -25, [16], 17], [20, false, -26]], [0, "39khVMDw1Oe413Lsg+xUUA", 1, 0], [4, 4278190080], [5, 608, 120]], [2, "img_tzxhd_l", 3, [-28], [[1, 0, -27, [1], 2]], [0, "adlUW+1yhCaLuGqheikEPe", 1, 0], [5, 304, 117], [-152, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "rwNode", 6, [-30], [[7, 1, 1, 20, -29, [5, 116, 117]]], [0, "664cYVIn1MPaBv5ngRpb78", 1, 0], [5, 116, 117], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "img_tzxhd_r", 3, [2], [[1, 0, -31, [5], 6]], [0, "669fqqO4VIb4V1na9ghNQd", 1, 0], [5, 304, 117], [152, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_jdt01", 1, [[1, 0, -32, [11], 12]], [0, "dadgfsSqxA2ZY/OAnSO6YC", 1, 0], [5, 26, 117]], [15, "New Label", 4, [[-33, [21, 3, -34, [4, 4279571992]]], 1, 4], [0, "0dO/nqTLRCAZJ0oc6JlNn6", 1, 0], [5, 108.25, 58.92], [0.907, 0.241, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "FundGoodItem", 7, [3, "6doKNhRsdJRLI7TKtn+djh", true, -35, 0]], [6, "FundGoodItem", 2, [3, "a9rE1byopNCYZAAEwyw62i", true, -36, 3], [-68, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "FundGoodItem", 2, [3, "8eCkQXGFhEK5nZhxniBG57", true, -37, 4], [68, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "img_fgx2", 1, [[1, 0, -38, [7], 8]], [0, "6c24g6SH5NrbgHvBUDpA7R", 1, 0], [5, 608, 7], [0, -58.939, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "img_jdt01", 1, [[1, 0, -39, [9], 10]], [0, "1fBIYoo+hKS6PlfgTYF+b3", 1, 0], [5, 26, 117]], [22, "第5章", 42, false, 1, 1, 1, 10, [13]], [12, "img_jdfgx", false, 1, [[1, 0, -40, [18], 19]], [0, "betkG31VFKlah+bIP8Aq9M", 1, 0], [5, 608, 19], [0, 51.655, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, -1, 16, 0, -4, 2, 0, -6, 9, 0, -7, 4, 0, -9, 5, 0, 0, 1, 0, -1, 3, 0, -2, 14, 0, -3, 15, 0, -4, 9, 0, -5, 4, 0, -6, 5, 0, -7, 17, 0, 2, 1, 0, 4, 1, 0, 0, 2, 0, -1, 12, 0, -2, 13, 0, 0, 3, 0, -1, 6, 0, -2, 8, 0, 0, 4, 0, -1, 10, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, -1, 11, 0, 0, 8, 0, 0, 9, 0, -1, 16, 0, 0, 10, 0, 2, 11, 0, 2, 12, 0, 2, 13, 0, 0, 14, 0, 0, 15, 0, 0, 17, 0, 6, 1, 2, 4, 8, 40], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, -1, 1, 3, 3, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1], [1, 0, 3, 1, 1, 0, 4, 0, 5, 0, 6, 0, 7, 0, 0, 8, 0, 9, 0, 10]]]]