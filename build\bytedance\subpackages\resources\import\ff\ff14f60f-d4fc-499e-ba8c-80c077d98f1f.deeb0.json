[1, ["ecpdLyjvZBwrvm+cedCcQy", "1ayaeT0/dLgbJVItl0dXFT", "e52TQxfs9FgYg1TfDFagDC", "30DcQwi35MSKNlPeS11GtI"], ["node", "_spriteFrame", "root", "box", "arrow", "desc", "data"], [["cc.Node", ["_name", "_groupIndex", "_obj<PERSON><PERSON>s", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_color", "_anchorPoint", "_trs"], -1, 9, 4, 5, 1, 2, 5, 5, 7], ["cc.Sprite", ["_sizeMode", "_enabled", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_bottom", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -2, 1], ["1d5baTYU5ZFUpwd3ohFqnhn", ["node", "desc", "arrow", "box"], 3, 1, 1, 1, 1]], [[3, 0, 1, 2, 2], [4, 0, 2], [0, 0, 1, 8, 4, 5, 6, 3], [0, 0, 2, 3, 1, 7, 4, 5, 9, 6, 5], [0, 0, 7, 8, 4, 5, 6, 10, 2], [0, 0, 1, 7, 4, 5, 6, 11, 3], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [1, 1, 0, 3, 4, 5, 3], [1, 3, 4, 5, 1], [1, 2, 0, 3, 4, 5, 3], [2, 0, 1, 2, 4, 4], [2, 0, 3, 4, 3], [3, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6], [7, 0, 1, 2, 3, 1]], [[1, "GuideItem"], [2, "GuideItem", 1, [-6, -7], [[14, -5, -4, -3, -2]], [12, -1, 0], [5, 750, 1334]], [4, "box", 1, [-9, -10], [[9, 1, 0, -8, [4], 5]], [0, "5cjUwjidVEfbNsWlZEwK9N", 1, 0], [5, 280, 60], [0, 0.5, 0]], [5, "img_qp02", 1, 2, [[8, -11, [2], 3], [11, 4, -19.755000000000003, -12]], [0, "45MBq4uQdEBaPvvHa3bfGz", 1, 0], [5, 47, 38], [0, -4.5550000000000015, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "mask", 512, 200, 1, 1, [[7, false, 0, -13, [0], 1], [10, 45, 750, 1334, -14]], [0, "49bCmI4bVPtIKi03u5LCss", 1, 0], [4, 4278190080], [5, 750, 1334]], [6, "New Label", 1, 2, [-15], [0, "95YTlxKMpNypBfR2Va3PNa", 1, 0], [5, 240, 39.06], [0, 0, 0.5], [-119.181, 31.369, 0, 0, 0, 0, 1, 1, 1, 1]], [13, false, "<outline color=black width=3>击时,有概率<color=#FFDD42>格挡伤害</color></outline>", 24, 240, 31, 5]], 0, [0, 2, 1, 0, 3, 2, 0, 4, 3, 0, 5, 6, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, -1, 3, 0, -2, 5, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 6, 0, 6, 1, 15], [0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1], [0, 1, 0, 2, 0, 3]]