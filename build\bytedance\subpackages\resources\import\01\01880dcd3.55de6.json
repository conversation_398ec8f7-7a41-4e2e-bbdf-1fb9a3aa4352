[1, ["aevTRizpRDN6Gmg4L3UZFC", "ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "40JVejALND+q8ckpRsrrrB"], ["node", "root", "data", "_parent", "_spriteFrame", "_N$skeletonData", "_defaultClip"], [["cc.Node", ["_name", "_active", "_opacity", "_groupIndex", "_prefab", "_children", "_components", "_contentSize", "_trs", "_parent"], -1, 4, 2, 9, 5, 7, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["9f0c4bxvUVDW5dqX6h4z5Qw", ["isBanRotate", "node"], 2, 1], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "defaultAnimation", "_preCacheMode", "loop", "premultipliedAlpha", "_animationName", "_playTimes", "isPlayerOnLoad", "node", "_materials", "_N$skeletonData"], -5, 1, 3, 6], ["53d1c1OHQFMMb9BROntnkrp", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["922dfHACR5Gb71WaK/MNbo+", ["node", "listenCom"], 3, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "sample", "curveData"], -1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 5, 6, 4, 7, 2], [0, 0, 1, 2, 3, 6, 4, 7, 8, 5], [0, 0, 9, 5, 4, 8, 2], [3, 0, 1, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [5, 0, 1, 2, 3, 2], [1, 1, 2, 1], [6, 0, 1, 2, 3, 2], [7, 0, 1, 1], [8, 0, 1, 2, 3, 5]], [[[[1, "Bullet_11344"], [2, "Bullet_11344", [-5], [[5, true, -2], [6, "default", "play", 0, false, false, "play", 1, true, -3, [2], 3], [7, true, -4, [5], 4]], [8, -1, 0], [5, 1237.496337890625, 872.8482055664062]], [3, "attackPos", false, 0, 6, [[9, 0, -6, [0], 1], [10, -7, 1]], [0, "98/+qymxlE1advjH8T6UKP", 1, 0], [5, 100, 100], [0, 336.29, 0, 0, 0, 0, 1, 7, 6, 1]], [4, "New Node", 1, [2], [0, "4dhs0p74dFoZwA3Zirltfu", 1, 0], [0, 215.691, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, 0, 2, 0, 0, 2, 0, 2, 1, 2, 3, 3, 7], [0, 0, 0, 0, 0, 0], [-1, 4, -1, 5, 6, -1], [1, 2, 3, 4, 0, 0]], [[[11, "fx_katanna01", 1.1666666666666667, 30, {"paths": {"New Node": {"props": {"angle": [{"frame": 0, "value": -65.016}, {"frame": 0.3333333333333333, "value": 62.705999999999996}, {"frame": 0.6666666666666666, "value": -65.016}, {"frame": 0.8666666666666667, "value": 0}]}}, "New Node/attackPos": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.8666666666666667, "value": true}, {"frame": 1.1666666666666667, "value": false}], "position": [{"frame": 0, "value": [0, 325.214, 0]}, {"frame": 0.6666666666666666, "value": [0, 325.214, 0]}, {"frame": 1, "value": [0, 45.566, 0]}, {"frame": 1.0666666666666667, "value": [0, 336.29, 0]}], "scaleX": [{"frame": 0, "value": 1}, {"frame": 0.6666666666666666, "value": 1}, {"frame": 1, "value": 6}, {"frame": 1.0666666666666667, "value": 7}], "scaleY": [{"frame": 0, "value": 6.5}, {"frame": 0.6666666666666666, "value": 6.5}, {"frame": 1, "value": 2}, {"frame": 1.0666666666666667, "value": 6}]}}}, "comps": {"FBoxCollider": {"size": [], "offset": []}}}]], 0, 0, [], [], []]]]