[1, ["ecpdLyjvZBwrvm+cedCcQy", "29FYIk+N1GYaeWH/q1NxQO", "f8npR6F8ZIZoCp3cKXjJQz", "ebJXGx5cNJ+b1BpmgufQFT", "14nW3tmspCkKhk8J07THg8", "f7293wEF9JhIuMEsrLuqng", "c87W2GNFFKAK1npbjTOlIW", "faCRlmMwlE1bRS1xQmqm/5", "27RTpTPjZN9KOAJJnUPPk1", "7a/QZLET9IDreTiBfRn2PD", "01DAwVMS1KH60Q2X4aJmQV", "8dy5FwZKFBcoBWpWjHJr6+", "c3A8lqHgZArb2he1eLfNue", "c6823ZZu5DO5uHXx3BV9h0", "f0BIwQ8D5Ml7nTNQbh1YlS", "4dMZmkcjlJEbhEI0rAtegJ", "8d6WoS4zREH7zTwTEf+kVQ", "0eU8ZlsUlD+IS9zgUWWXSf", "d4MTG2YDhCppa26L5kkPxT", "060SujzZFDa7FrSECWA8CA", "8cSbxth89Hnrgn8Q2l+lOP", "ecDbyWxjBAI5RJ+XSaI8s1", "446BvHYYdOHZDEyZS4HDd/", "bbWlKBSAhCkbx2QrrWJbMu", "caemL4eJZG76bazCXktxyw", "9fZRLbOBNNb6v0Osc8H6iX", "39qsJijFxHcb9Y76qmu/Dv", "b8ZZ86JvdEIbD/fzZDFJ1s", "fckn4D3k5DErYcarrF2kl1", "cb1VFt7lZM5bK3HPiU1WUY", "17saj3+z9IvYCHM92QEano", "c5UQJif2RIab8ZQoztzjvg", "99A8UBER1GIpmEt1V4sqpJ", "1fR7mYEeJIOJ51dzZ82wi2", "088BpBOEhL7JJsqORCo2KC", "7aSL72M71OCKrrZfvV9xtf", "deXSjzd6dCObV+qZtSyZxq", "b9CtqqKmNE64nnbTzPOg0L", "76LavGO79P367CnR+LOYCX", "f2hTCehVtPzZIcN1TPbCCV", "f3p84uzd5EVLXvLuhlyoRY", "cbzcki2OpFaZq59eIwGf5P", "76f40guoBJEo7bCsc9u3/G", "485eGEfEJLOqZGExd6w/kd", "56wtB3qqNKs6hH8sDwaqXO", "e3sBShjv1Kf5fhIfpljds2", "65Ly1x9NZJgY2iouTeuZzu", "19u6nD2T1Bm5m2kMQhfXfd", "5frhkItRtHyK2P3rXZ6vSI", "a28wa3MzdMnZoeuz24Ugar", "87nKKIeM9PHYzFib4is9vj", "c9Q1D8D2ZG/ogt45hnizlM", "1b4xJJnJ5NVIcTy2mpA1ya", "a3xPKaI0VBCa4KthcxOMSE", "a8wQvnCpZD5LXpDU01L3bu", "c5b90iuytLkYWyJTmKCagy", "04pcEmy/ZBAI8X+TNW3AHI", "c0fEWaGdVCgZBBng/ddR3I", "aeCGvPg+NOLJ8faSnAg+4c", "53+pVBObpK6Y8S2CUkahTU", "efV8cKYEpMaItpPk4geml4", "cdZTJosRhC1IxZvwxMbynx", "601bh6lQFFZaXFW8HHrZDC", "c5aTJgX4lOxZjlpNv4s5oo", "14iDxB8fZGZ7mVV1gTgWP3", "d1nrLP0ShMlqc+Ni1Y6yuS", "f7eQcswEpIcoXGq2WDwg5u", "77ibCc89FPO5+3fLXbOjr2", "08o0geABRHY67dZQlvybWV", "fc7Bjg/8dHR7UewOrQGqcB", "73Y8cKfktIQZs973rt3qQE", "e7UCddagpE559kRIcnGyj6", "32/0QVWHlCVKG9qD7+g8Mm", "b5mEdzES1EzaYmIqa1n1Zr", "35tUyib+FHEYFv04roktUs", "5bIZg7ATBHEr1nUKR4hcKM", "84jXinItNAHbXHoCEKLCyi", "11XhEweudJiJOuCIKBVOw4", "98meuyWg1ExqynL+AF/NpT", "00EtROiL9HaIQKOpxY7Q4I", "0cfX66psxEEK+y7AvLeo1C", "c2jxDLZcxFjoIWRGYfQwkM", "36rxqtGxdKfYEnvCqBe4Id", "99/hDavs9I55c+tfQ3r5lk", "deKfV3uA9E2YSQxZzMzF0i", "c82GDWkeFKDL9Xk/UOKCxt", "71gz4dotRCdYXXDDB04Vjo", "af/h7tOI1HIaZOXgd2esc/", "90YmG+HvJMoZGcTzWArMVu", "47HfLgvElBxL6VatMogUGv", "29ppijCI5PSYnb8RLUvqld", "8b1aIhc8RMxoQ0YGf9aXcN", "e3Puhtk5hECqkJHLi5rDPg", "faGUwXo85Im41pTa+5+NAx", "50kaqI9DhC1q5tsoxYU9Yg"], ["node", "_spriteFrame", "_textureSetter", "_N$normalSprite", "_N$disabledSprite", "_N$target", "_N$file", "_parent", "checkMark", "_N$skeletonData", "root", "btnRecommend", "DiffSelect", "idleRed", "lvRed", "rewardRedpoint", "rightnode", "leftnode", "pagerbtn", "pagelbtn", "boxnode", "unlockinfo", "nodeItem", "data", "_N$font", "rewardPre"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint", "_eulerAngles"], -1, 4, 5, 9, 1, 7, 2, 5, 5, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_lineHeight", "_enableWrapText", "_N$cacheMode", "_N$overflow", "_styleFlags", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_top", "_left", "_originalWidth", "_originalHeight", "_right", "alignMode", "_bottom", "node"], -5, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite", "_N$target"], 1, 1, 9, 5, 5, 6, 6, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_N$paddingLeft", "_N$paddingRight", "node", "_layoutSize"], -3, 1, 5], ["cc.Node", ["_name", "_groupIndex", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 0, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_eulerAngles"], 1, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Toggle", ["zoomScale", "_N$transition", "_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 0, 1, 5, 1, 1, 9], ["cc.Prefab", ["_name"], 2], ["737a1A5d/tKd7uuS8bO6S0A", ["node", "nodeArr", "labelArr", "nodeItem", "unlockinfo", "boxnode", "pagelbtn", "pagerbtn", "leftnode", "rightnode", "rewardRedpoint", "lvRed", "idleRed", "DiffSelect", "btnRecommend", "rewardPre"], 3, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["1563dBtNsFCp7Et7MyFQ3Sd", ["SwitchID", "isAutoShow", "node"], 1, 1], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "node", "clickEvents"], 2, 1, 9], ["a0fecYUjEVFFay94nEQdlax", ["showType", "platform", "node"], 1, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Mask", ["_enabled", "node", "_materials"], 2, 1, 3], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["00798YhOSNDMq8AyX+/Wp5o", ["my<PERSON>ey", "node"], 2, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node", "_N$font"], -1, 1, 6], ["828c11dj2FP0K3TQUZsMFn1", ["node"], 3, 1]], [[9, 0, 1, 2, 2], [1, 0, 7, 6, 4, 5, 8, 2], [18, 0, 1, 2, 2], [3, 4, 5, 6, 1], [10, 0, 1, 2, 3, 4], [1, 0, 7, 9, 6, 4, 5, 8, 2], [3, 0, 4, 5, 6, 2], [5, 1, 0, 2, 3, 4, 5, 8, 6, 7, 3], [2, 0, 4, 5, 1, 2, 3, 10, 11, 12, 7], [21, 0, 1, 2], [1, 0, 1, 7, 9, 6, 4, 5, 8, 3], [1, 0, 1, 7, 6, 4, 5, 8, 3], [3, 1, 0, 4, 5, 6, 3], [16, 0, 1, 2, 3], [1, 0, 7, 6, 4, 5, 2], [5, 0, 2, 3, 4, 5, 8, 6, 7, 2], [2, 0, 4, 5, 1, 9, 2, 3, 10, 11, 8], [1, 0, 9, 6, 4, 5, 8, 2], [3, 1, 4, 5, 6, 2], [14, 0, 1, 2, 3], [1, 0, 2, 7, 6, 4, 5, 8, 3], [1, 0, 1, 2, 7, 6, 4, 5, 8, 4], [4, 6, 0, 2, 5, 1, 7, 3, 4, 8, 9], [10, 0, 1, 3, 3], [2, 0, 4, 1, 9, 2, 3, 10, 11, 7], [1, 0, 7, 9, 6, 4, 10, 5, 8, 2], [1, 0, 7, 9, 4, 5, 2], [1, 0, 7, 9, 6, 4, 5, 2], [6, 0, 1, 2, 6, 7, 4], [5, 1, 0, 2, 3, 4, 5, 6, 7, 3], [3, 0, 3, 4, 5, 6, 3], [2, 0, 4, 5, 6, 1, 2, 3, 7, 10, 11, 12, 9], [1, 0, 7, 9, 6, 4, 5, 11, 8, 2], [1, 0, 9, 6, 4, 10, 5, 8, 2], [1, 0, 1, 2, 9, 6, 4, 5, 8, 4], [1, 0, 7, 9, 4, 8, 2], [1, 0, 7, 9, 4, 2], [1, 0, 1, 7, 6, 4, 10, 5, 3], [6, 0, 1, 3, 6, 7, 4], [4, 0, 3, 4, 8, 4], [5, 0, 2, 3, 2], [3, 0, 3, 4, 5, 3], [2, 0, 4, 5, 1, 2, 3, 8, 10, 11, 8], [2, 0, 4, 6, 1, 2, 3, 7, 10, 11, 12, 8], [2, 0, 4, 6, 1, 2, 3, 8, 7, 10, 11, 9], [22, 0, 1, 2, 3, 4, 5, 6, 7, 6], [12, 0, 2], [1, 0, 2, 9, 6, 4, 5, 3], [1, 0, 9, 4, 5, 2], [1, 0, 2, 9, 6, 4, 5, 8, 3], [1, 0, 9, 6, 4, 5, 2], [1, 0, 1, 7, 9, 4, 5, 8, 3], [1, 0, 1, 7, 9, 6, 4, 10, 5, 8, 3], [1, 0, 7, 9, 4, 5, 8, 2], [1, 0, 6, 4, 5, 8, 2], [1, 0, 7, 6, 4, 5, 11, 8, 2], [1, 0, 7, 6, 4, 5, 8, 12, 2], [1, 0, 3, 7, 6, 4, 5, 8, 3], [1, 0, 7, 6, 4, 2], [1, 0, 1, 2, 7, 6, 4, 5, 4], [7, 0, 1, 3, 4, 5, 6, 7, 3], [7, 0, 2, 3, 4, 5, 6, 7, 3], [8, 0, 1, 2, 3, 4, 5, 3], [8, 0, 2, 3, 4, 5, 6, 7, 2], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 1], [9, 1, 2, 1], [6, 0, 1, 6, 7, 3], [6, 0, 1, 4, 5, 2, 6, 7, 6], [4, 0, 1, 8, 3], [4, 0, 2, 1, 8, 4], [4, 0, 2, 8, 3], [4, 0, 5, 8, 3], [4, 0, 8, 2], [5, 2, 3, 1], [5, 1, 0, 2, 3, 3], [3, 2, 4, 5, 2], [3, 2, 1, 0, 4, 5, 4], [3, 4, 5, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 4], [11, 0, 1, 3, 4, 5, 6, 7, 3], [15, 0, 1, 2, 2], [17, 0, 1], [2, 0, 4, 6, 1, 2, 3, 7, 10, 11, 8], [2, 0, 4, 1, 2, 3, 8, 10, 11, 12, 7], [2, 0, 4, 1, 2, 3, 8, 10, 11, 7], [2, 0, 5, 6, 1, 2, 3, 8, 7, 10, 11, 9], [2, 0, 4, 1, 9, 2, 3, 8, 10, 11, 12, 8], [2, 0, 4, 5, 1, 2, 3, 10, 11, 7], [2, 0, 4, 5, 6, 1, 9, 2, 3, 7, 10, 11, 10], [2, 0, 4, 1, 2, 3, 10, 11, 6], [19, 0, 1, 2, 2], [20, 0, 1], [23, 0, 1, 2, 3, 4, 5, 5], [24, 0, 1]], [[[{"name": "btn_gqxzd2", "rect": [0, 0, 200, 58], "offset": [0, 0], "originalSize": [200, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [17]], [[{"name": "btn_main_05", "rect": [0, 13, 120, 110], "offset": [0, -6], "originalSize": [120, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [18]], [[{"name": "btn_main_06", "rect": [0, 1, 120, 123], "offset": [0, -0.5], "originalSize": [120, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [19]], [[{"name": "img_zbtkbq1", "rect": [0, 0, 30, 37], "offset": [0, 0], "originalSize": [30, 37], "capInsets": [7, 0, 7, 0]}], [0], 0, [0], [2], [20]], [[{"name": "icon_setting", "rect": [0, 0, 66, 65], "offset": [0, 0], "originalSize": [66, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [21]], [[{"name": "sp_change_1", "rect": [0, 0, 118, 118], "offset": [0, 0], "originalSize": [118, 118], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [22]], [[{"name": "btn_rkyj", "rect": [6, 1, 87, 105], "offset": [0, -0.5], "originalSize": [99, 106], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [23]], [[{"name": "icon_share", "rect": [13, 18, 84, 73], "offset": [0, 0.5], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [24]], [[{"name": "hall", "rect": [0, 0, 720, 1560], "offset": [0, 0], "originalSize": [720, 1560], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [25]], [[[46, "M20_PrePare_Fight"], [47, "M20_PrePare_Fight", 1, [-18, -19, -20, -21, -22, -23, -24], [[64, -17, [-15, -16], [-14], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2, 284]], [65, -1, 0], [5, 750, 1334]], [32, "LeftBox", 1, [-27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37], [[38, 1, 2, 20, -25, [5, 160, 1210]], [68, 9, 230, -26]], [0, "54Z+THl7tACrOW6M7aTs1+", 1, 0], [5, 160, 1210], [0, 0.5, 1], [-295, 437, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "RightBox", 1, [-40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50], [[38, 1, 2, 20, -38, [5, 160, 460]], [69, 33, 2.987000000000023, 230, -39]], [0, "053gsKzy9IzpfbRtHk7tOz", 1, 0], [5, 160, 460], [0, 0.5, 1], [295, 437, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "contentitem", [-51, -52, -53, -54, -55, -56, -57], [0, "2dCmg4Rj9PJbkbep4lGaSF", 1, 0], [5, 508, 900]], [49, "btn_start", 1, [-60, -61, -62, -63, -64], [[29, 0.9, 3, -58, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "mode_33", 1]], [4, 4293322470], [4, 3363338360], 109, 110], [3, -59, [111], 112]], [0, "57RD3t9rhD/6n/zmolwZux", 1, 0], [5, 209, 209], [0, -417.518, 0, 0, 0, 0, 1, 1, 1, 0]], [50, "FightScroller", [-66, -67, -68, -69, -70, -71], [[39, 45, 240, 700, -65]], [0, "e0XE1cB9RDCaU0xnDk9EHP", 1, 0], [5, 750, 1334]], [33, "toggle1", [-75, -76, -77, -78], [[78, 0.9, 3, false, -74, [4, 4292269782], -73, -72, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onClickDiffToggle", "0", 1]]]], [0, "deWuLkNOxBCpEB4zSkMgO7", 1, 0], [4, 4292269782], [5, 200, 58], [-100, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [33, "toggle2", [-82, -83, -84, -85], [[79, 0.9, 3, -81, [4, 4292269782], -80, -79, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onClickDiffToggle", "1", 1]]]], [0, "662OiwWVlHNId3uVI4jRHA", 1, 0], [4, 4292269782], [5, 200, 58], [100, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "idle", [-89, -90, -91], [[75, false, -86, [75]], [40, 3, -87, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/idleAward/IdleAwardView", 1]]], [19, 15, true, -88]], [0, "35kEOmSPFLsIE0LwF3j1hH", 1, 0], [5, 78, 65], [62, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "btn_quickpass", false, 1, [-94, -95, -96, -97], [[29, 0.9, 3, -92, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "sweeping", 1]], [4, 4293322470], [4, 3363338360], 87, 88], [12, 1, 0, -93, [89], 90]], [0, "5eAdNICmNMoabmviXiqu3J", 1, 0], [5, 162, 114], [-215.5, -417.518, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "btn_quickpass_ad", false, 1, [-100, -101, -102, -103], [[12, 1, 0, -98, [99], 100], [80, "sweeping", -99, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "sweeping", 1]]]], [0, "54GjRZ+UlM9aN3TucwAtRu", 1, 0], [5, 162, 114], [-23.5, -417.518, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_cbl_tt", 2, [-107, -108, -109], [[15, 3, -105, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "PlatformTTSidebarRewardView", 1]], [4, 4293322470], [4, 3363338360], -104, 135, 136], [13, 0, [5, 106], -106]], [0, "ceRl78FAROa7ZwyF3Rf3i6", 1, 0], [5, 100, 100], [0, -50, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_desk_tt", 2, [-113, -114, -115], [[15, 3, -111, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "PlatformTTDeskRewardView", 1]], [4, 4293322470], [4, 3363338360], -110, 142, 143], [13, 0, [5, 106], -112]], [0, "43ilUG0p9EH7B0RSDCwlTO", 1, 0], [5, 100, 100], [0, -170, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_ksset", 2, [-119, -120, -121], [[15, 3, -117, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/platform/PlatformKSSidebarRewardView", 1]], [4, 4293322470], [4, 3363338360], -116, 158, 159], [13, 0, [18], -118]], [0, "44JUMMOjFA5Zr/PlXlXQoD", 1, 0], [5, 100, 100], [0, -410, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "New ToggleContainer", false, 4, [7, 8], [[81, -122], [66, 1, 1, -123, [5, 400, 58]], [6, 0, -124, [28], 29]], [0, "7arRvv3qlBcqqtGOfM4A8o", 1, 0], [5, 400, 58], [0, 322.489, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "left", [-128, -129], [[15, 3, -126, [[4, "737a1A5d/tKd7uuS8bO6S0A", "pageChange", "-1", 1]], [4, 4293322470], [4, 3363338360], -125, 36, 37], [70, 8, 161, -127]], [0, "11Xx2NAb1LnanNkdI3Osf6", 1, 0], [5, 60, 84], [-190, 30.495, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [17, "right", [-133, -134], [[15, 3, -131, [[4, "737a1A5d/tKd7uuS8bO6S0A", "pageChange", "1", 1]], [4, 4293322470], [4, 3363338360], -130, 43, 44], [71, 32, 161, -132]], [0, "d2VVr1e29Dx4dW8aTvpvNt", 1, 0], [5, 60, 84], [190, 30.495, 0, 0, 0, 0, 1, -0.8, 0.8, 1]], [51, "point", false, 6, [-135, -136, -137, -138, -139], [0, "07pXL2U09FFZN07op1pNrt", 1, 0], [5, 750, 1334], [0, -246.124, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnDrawCard", 1, [-142, -143, -144], [[29, 0.9, 3, -140, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/shop/Shop_DrawCards", 1]], [4, 4293322470], [4, 3363338360], 128, 129], [19, 13, true, -141]], [0, "b2ICZcJ5FMuLGa3tDiFoIy", 1, 0], [5, 110, 110], [-284.798, -403.736, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "btn_ShareGift", 2, [-148, -149, -150], [[7, 1.1, 3, -146, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/platform/TT_ShareGiftView", 1]], [4, 4293322470], [4, 3363338360], -145, 150, 151], [13, 0, [5], -147]], [0, "b6sXJfmCNJ7qFLEGDlF4Ul", 1, 0], [4, 4285887978], [5, 100, 100], [0, -290, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_ksShare", 2, [-154, -155], [[15, 3, -152, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "Share", 1]], [4, 4293322470], [4, 3363338360], -151, 180, 181], [13, 0, [18], -153]], [0, "c42IQkl5xLN4wLOgzr7vnf", 1, 0], [5, 100, 100], [0, -790, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "btn_subs_tt", false, 2, [-158, -159, -160], [[15, 3, -157, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "PlatformTTDeskRewardView", 1]], [4, 4293322470], [4, 3363338360], -156, 203, 204]], [0, "4fvEcooQ9A35EvMopVwAzl", 1, 0], [5, 100, 100], [0, -1060, 0, 0, 0, 0, 1, 1, 1, 0]], [52, "btn_adreward", false, 3, [-163, -164, -165, -166], [[7, 1.1, 3, -162, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/ModeBackpackHero/M20_Pop_AdReward", 1]], [4, 4293322470], [4, 3363338360], -161, 225, 226]], [0, "9btZ5aVhlH8JdlwmE4dX/P", 1, 0], [4, 4285887978], [5, 100, 100], [1, -250, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 16, [-169], [[3, -167, [32], 33], [22, 0, 45, 1, 1, 13, 13, 100, 40, -168]], [0, "ecMF43wbRHuYzy3Qa3DXdL", 1, 0], [5, 58, 58], [0, 0, 0, 0, 0, 0, 1, -1, 1, 0]], [5, "Background", 17, [-172], [[3, -170, [39], 40], [22, 0, 45, 1, 1, 13, 13, 100, 40, -171]], [0, "685cVHSbJIsKUC6iLuxIvV", 1, 0], [5, 58, 58], [0, 0, 0, 0, 0, 0, 1, -1, 1, 0]], [10, "reward", false, 6, [-175], [[28, 2, 1, -50, -173, [5, 520, 110]], [12, 1, 0, -174, [50], 51]], [0, "82JIXfRZ5LkZ4GwzZI+KOr", 1, 0], [5, 520, 110], [0, -261.098, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "lv", [-178, -179], [[3, -176, [67], 68], [40, 3, -177, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onOpenBtn", "M20_Prepare_Award", 1]]]], [0, "62FxtDSuhFh4TYNc2s2XWs", 1, 0], [5, 84, 65], [-59, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn", 6, [10, 11, 5], [[28, 1, 1, 30, -180, [5, 209, 200]]], [0, "17Ppize19MMYWSbPIlS7Qd", 1, 0], [5, 209, 200], [0, 62.866, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnMonth", 2, [-184, -185], [[7, 0.9, 3, -182, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/reward/MonthCard/MonthCardView", 1]], [4, 4293322470], [4, 3363338360], -181, 166, 167], [19, 2, true, -183]], [0, "55esxII85JHaQYoGa4Rql4", 1, 0], [5, 110, 110], [0, -535, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnJijin", 2, [-189, -190], [[7, 0.9, 3, -187, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/reward/Fund/FundListView", 1]], [4, 4293322470], [4, 3363338360], -186, 174, 175], [19, 1, true, -188]], [0, "d9FDwrx/5GNpx//2iQGE3j", 1, 0], [5, 110, 110], [0, -665, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "btn_fudai", 2, [-194, -195], [[7, 1.1, 3, -192, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "showExchangeCodeView", 1]], [4, 4293322470], [4, 3363338360], -191, 196, 197], [13, 0, [5], -193]], [0, "c6Zs7EdmlAWp3CGpXBa0sa", 1, 0], [4, 4285887978], [5, 100, 100], [0, -1030, 0, 0, 0, 0, 1, 1, 1, 0.936]], [10, "btn_videogame", false, 3, [-199, -200], [[7, 1.1, 3, -197, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/ModeBackpackHero/M20_Pop_AdSsGame", 1]], [4, 4293322470], [4, 3363338360], -196, 236, 237], [13, 0, [5], -198]], [0, "eciAe4XClJIof7z0rM913A", 1, 0], [5, 100, 100], [1, -355, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btn_minigame", false, 3, [-203, -204, -205], [[7, 1.1, 3, -202, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/setting/MiniGameView", 1]], [4, 4293322470], [4, 3363338360], -201, 243, 244]], [0, "edQ6IJZXhONq7XgsqDxUqj", 1, 0], [5, 100, 100], [1, -270, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btn_challenge", false, 3, [-208, -209, -210], [[7, 1.1, 3, -207, [[23, "737a1A5d/tKd7uuS8bO6S0A", "onClickOpenChanllenge", 1]], [4, 4293322470], [4, 3363338360], -206, 256, 257]], [0, "a7IFaN1VNAg69qhKjBXTIU", 1, 0], [5, 100, 100], [1, -380, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnRecommend", false, 3, [-213, -214], [[7, 0.9, 3, -212, [[23, "737a1A5d/tKd7uuS8bO6S0A", "onClickOpenSuperReward", 1]], [4, 4293322470], [4, 3363338360], -211, 261, 262]], [0, "b90WZWT7JKhZmaEK+Ovyga", 1, 0], [5, 100, 100], [1, -490, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_task", 3, [-218, -219], [[7, 1.1, 3, -216, [[23, "737a1A5d/tKd7uuS8bO6S0A", "onClickOpenTaskView", 1]], [4, 4293322470], [4, 3363338360], -215, 274, 275], [19, 14, true, -217]], [0, "ef4oKabLRIX50jQNoXjJIk", 1, 0], [5, 100, 100], [1, -290, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_seventask", 3, [-223, -224], [[7, 1.1, 3, -221, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/reward/SevenTask/SevenTaskView", 1]], [4, 4293322470], [4, 3363338360], -220, 282, 283], [19, 7, true, -222]], [0, "61rQBnlWhD1rjsAgjU5HMV", 1, 0], [5, 100, 100], [0, -410, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "selectround", 6, [-225, 16, 17], [0, "07pc/S7iRNKZaT9sXaqER7", 1, 0], [5, 750, 600]], [1, "title", 4, [[8, "1.初出茅庐", 45, 46, false, 1, 1, -226, [2], 3], [2, 4, -227, [4, 4280295456]], [73, -228, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "gm_level", 1]]]], [0, "f9ZDCxpg9KNo+vC7HRmzoo", 1, 0], [5, 211.03, 65.96000000000001], [0, 482.71, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "DiffSelect", 4, [-229, -230], [0, "74ZAit9IFEe4rqOYmfSeCO", 1, 0], [0, 419.545, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn", 40, [-233], [[3, -231, [11], 12], [74, 0.9, 3, -232, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/ModeChains/M33_Pop_DiffSelectGeneral", 1]]]], [0, "dfstfQ79lCLay0HkYm3O5e", 1, 0], [5, 118, 118], [78.974, -11.996, 0, 0, 0, 0, 1, 0.5, 0.5, 0]], [53, "box", 26, [-234, -235, -236], [0, "53YnvQT1ZD2LKGUAoLjNIn", 1, 0], [5, 520, 78], [0, 27.074, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "middle", 6, [-238, -239], [[76, false, 1, 0, -237, [76]]], [0, "85PxpMDadG9ZGyYTvmCQut", 1, 0], [5, 520, 110], [0, -179.373, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "iconLayout", 43, [27, 9], [[28, 1, 1, 40, -240, [5, 202, 80]]], [0, "83SM18LfJHyLjY1EuKB7Xd", 1, 0], [5, 202, 80]], [17, "list", [-243], [[12, 1, 0, -241, [118], 119], [67, 1, 1, 20, 20, 20, -242, [5, 120, 99]]], [0, "8bPSa7tvFIU7S1pHKJc65m", 1, 0], [5, 120, 99], [0, 101.547, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 29, [-245], [[3, -244, [162], 163]], [0, "a82dpk5VlG0bIavmAzzwX0", 1, 0], [5, 120, 127], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "Background", 30, [-247], [[3, -246, [170], 171]], [0, "bek+3nZNFD7JacIKXHYfVc", 1, 0], [5, 120, 123], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [25, "btn_signIn", 2, [-250, -251], [[7, 1.1, 3, -249, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/reward/SignIn/SignIn_View", 1]], [4, 4293322470], [4, 3363338360], -248, 188, 189]], [0, "c5RlCzMXdHdqhm3xwH26Wk", 1, 0], [4, 4285887978], [5, 100, 100], [0, -910, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 48, [-253], [[3, -252, [184], 185]], [0, "754Tvq00lGT5XmCFQY44pI", 1, 0], [5, 120, 128], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "Background", 31, [-255], [[18, 1, -254, [192], 193]], [0, "963Xo6D95BapWvrHfz9uK4", 1, 0], [5, 128, 132], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 0]], [5, "btnNewGift", 2, [-258, -259], [[7, 0.9, 3, -257, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/reward/NewGift/NewGiftView", 1]], [4, 4293322470], [4, 3363338360], -256, 211, 212]], [0, "74XPtLNCBGOYbzkZRdSB8H", 1, 0], [5, 110, 110], [0, -1155, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 51, [-261], [[3, -260, [207], 208]], [0, "370pbSnOBMp7brR/L6aOQk", 1, 0], [5, 120, 110], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "Background", [-263], [[3, -262, [215], 216]], [0, "3buesMkshL56TRXwqwFmD/", 1, 0], [5, 73, 79], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 23, [[18, 1, -264, [219], 220], [22, 0, 45, -1.5, -1.5, -4.5, -4.5, 100, 40, -265]], [0, "c5ZLN9EklJoYMO4p7Tsv02", 1, 0], [5, 83, 89], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_feedback", 3, [-269], [[7, 1.1, 3, -267, [[4, "737a1A5d/tKd7uuS8bO6S0A", "openView", "ui/ModeBackpackHero/M20_Pop_FeedBack", 1]], [4, 4293322470], [4, 3363338360], -266, 231, 232], [13, 0, [5], -268]], [0, "68CqTyUJ1JnalFiys46cXu", 1, 0], [5, 100, 100], [1, -170, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 55, [-271], [[3, -270, [229], 230]], [0, "7doTO0dZFC+r07owg5tsfX", 1, 0], [5, 73, 88], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 32, [[18, 1, -272, [233], 234], [22, 0, 45, -0.5, -0.5, -1.5, -1.5, 100, 40, -273]], [0, "76Ziz5j29Dh5iN+sbVe5/n", 1, 0], [5, 81, 83], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "btn_roundbullet", false, 3, [-276, -277], [[7, 1.1, 3, -275, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onOpenWin", "33", 1]], [4, 4293322470], [4, 3363338360], -274, 248, 249]], [0, "aeAmShkIJN3boj7vdvyMdE", 1, 0], [5, 100, 100], [1, -460, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnSuperReward", false, 3, [-280, -281], [[7, 0.9, 3, -279, [[23, "737a1A5d/tKd7uuS8bO6S0A", "onClickOpenRecommend", 1]], [4, 4293322470], [4, 3363338360], -278, 266, 267]], [0, "a5UwkJs7tGiafLBBsJ2Nw/", 1, 0], [5, 100, 100], [1, -610, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Background", 36, [-283], [[3, -282, [270], 271]], [0, "63JKio3LdJfZs9OWkRsBpx", 1, 0], [5, 73, 85], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "Background", 37, [-285], [[3, -284, [278], 279]], [0, "a0BLDQ4V5IUoIakYZplbRZ", 1, 0], [5, 120, 123], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "maskbg", 1, [[72, 45, -286], [6, 0, -287, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [27, "bg", 1, [6], [[39, 45, 750, 1334, -288]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [27, "content", 38, [-290], [[90, false, -289, [30]]], [0, "2bw0CNCUNMqZ3z5C+s7wpO", 1, 0], [5, 490, 900]], [26, "root", 64, [4], [0, "2c+kIl8BNN+IkGCvqqccM2", 1, 0], [5, 500, 900]], [11, "txt", false, 41, [[82, "难度选择", 29, false, false, 1, 1, 1, -291, [10]], [2, 4, -292, [4, 4278190080]]], [0, "fcgBWCJf5Fa7hY/w91FOCT", 1, 0], [5, 124, 58.4], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "unlockinfo", 4, [[83, "已通关", 35, false, 1, 1, 2, -293, [14], 15], [2, 2, -294, [4, 4278190080]]], [0, "b5ECTv73pI7qp9kat2OuRx", 1, 0], [5, 183, 58.4], [0, 343.353, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 7, [[42, "普通", 26, 30, false, 1, 1, 2, -295, [21]], [2, 3, -296, [4, 4278190080]]], [0, "7cdEM8VZtMTYbUGeywIkCj", 1, 0], [5, 120, 43.8], [27.193, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 8, [[42, "精英", 26, 30, false, 1, 1, 2, -297, [27]], [2, 3, -298, [4, 4278190080]]], [0, "49msZbFkpFLZUzbikxUhbx", 1, 0], [5, 120, 43.8], [-25.627, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "redpoint", false, 16, [[6, 0, -299, [34], 35]], [0, "66BKTVumdL5KZLCPOoP3RE", 1, 0], [5, 45, 45], [-40, 50, 0, 0, 0, 0, 1, -1, 1, 1]], [11, "ggtipicon", false, 17, [[6, 0, -300, [41], 42]], [0, "16TVD0QD1EmKd+OGA/WCPe", 1, 0], [5, 45, 45], [-40, 50, 0, 0, 0, 0, 1, -1, 1, -1]], [1, "wave", 42, [[84, "第%d波", 25, false, 1, 1, 2, -301, [47]], [2, 2, -302, [4, 4278190080]]], [0, "6a4+QhOBRKXJacIrnzHrXl", 1, 0], [5, 120, 50.4], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 18, [[24, "关卡进度奖励", 30, false, 1, 1, 1, -303, [60]], [2, 3, -304, [4, 4278190080]]], [0, "d4/OOXFnxJmqNAG0J0VerY", 1, 0], [5, 186, 56.4], [0, -84.328, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 27, [[8, "章节奖励", 24, 24, false, 1, 1, -305, [63], 64], [2, 2, -306, [4, 4278190080]]], [0, "f0ZwawzhVLG4Sjcyk3DsD7", 1, 0], [5, 100, 34.239999999999995], [0, -32.07, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ggtipicon", 27, [[6, 0, -307, [65], 66]], [0, "c0ZYIRfQdLaKoe9o4Po4ZW", 1, 0], [5, 45, 45], [32.137, 30.668, 0, 0, 0, 0, 1, 1, 1, -1]], [1, "New Label", 9, [[8, "游历", 24, 24, false, 1, 1, -308, [71], 72], [2, 2, -309, [4, 4278190080]]], [0, "23kWNIlVlIUInxa7kuoMSj", 1, 0], [5, 52, 34.239999999999995], [0, -32.07, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ggtipicon", 9, [[6, 0, -310, [73], 74]], [0, "62aM2qsjRHA6yM0J+NFr0E", 1, 0], [5, 45, 45], [32.137, 30.668, 0, 0, 0, 0, 1, 1, 1, -1]], [20, "Label", 1, 10, [[43, "扫荡", 36, false, false, 1, 1, 1, -311, [77], 78], [2, 2, -312, [4, 4278190080]]], [0, "69J3qtAFRK47euDfeMU+jn", 1, 0], [5, 76, 54.4], [0, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "energy", 10, [-313, -314], [0, "2dwWXbYUxAFqQCtGp8KTX2", 1, 0]], [20, "Label", 1, 79, [[31, "x5", 24, 30, false, false, 1, 1, 1, -315, [83], 84], [2, 2, -316, [4, 4278190080]]], [0, "bedqbKwERGT7ubsAwvqKPh", 1, 0], [5, 29.34, 41.8], [17.864, -24.356, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "count", 1, 10, [[31, "今日剩余", 24, 30, false, false, 1, 1, 1, -317, [85], 86], [2, 2, -318, [4, 4278190080]]], [0, "70f01YN6VJFpgLBEPgYLGd", 1, 0], [5, 100, 41.8], [0, -89.364, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Label", 1, 11, [[43, "扫荡", 36, false, false, 1, 1, 1, -319, [91], 92], [2, 2, -320, [4, 4278190080]]], [0, "aa7ODzuhNGiqeeyO1iagEt", 1, 0], [5, 76, 54.4], [0, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "icon_lightning", [[3, -321, [95], 96], [91, -322]], [0, "d5txANkN1EFKdkj389cBGP", 1, 0], [5, 50, 52], [0.137, -26, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [20, "count", 1, 11, [[31, "今日剩余", 24, 30, false, false, 1, 1, 1, -323, [97], 98], [2, 2, -324, [4, 4278190080]]], [0, "0eluzswWRDU4S+44pjSbuV", 1, 0], [5, 100, 41.8], [0, -89.364, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Label", false, 1, 5, [[85, "开始游戏", 60, false, false, 1, 1, 2, 1, -325, [101]], [2, 3, -326, [4, 4278221516]]], [0, "62KJGCLXxMgqSo3L0P6kWg", 1, 0], [5, 274, 79.6], [0, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "UseEnergy", 1, 5, [[-327, [2, 2, -328, [4, 4278190080]]], 1, 4], [0, "cbEEHhbmJHEJFrKwplwgCR", 1, 0], [5, 33.480000000000004, 41.8], [18.234, -89.271, 0, 0, 0, 0, 1, 1, 1, 1]], [61, "unlockinfo", false, 6, [[-329, [2, 3, -330, [4, 4278190080]]], 1, 4], [0, "20n2tTa4NE/a4BdOhHULBf", 1, 0], [5, 390, 56.4], [0, -416.587, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "talk", 1, [45, -331], [0, "2eTX6QcT1H05YgPjH90Unm", 1, 0], [1065.367, -470.09, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "item", 45, [-332, -333], [0, "85D+MzjexH6pieEo4Uhuha", 1, 0], [5, 80, 100]], [1, "num", 89, [[86, "400", 24, false, 1, 1, 1, 2, -334, [116], 117], [2, 2, -335, [4, 4278190080]]], [0, "64Qag/tuNKz7+32F7KO6uH", 1, 0], [5, 86.13, 50.4], [13.385, -22.735, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 19, [[8, "召唤", 30, 30, false, 1, 1, -336, [124], 125], [2, 2, -337, [4, 4278190080]]], [0, "9aHYSspcJDEI1wWENSDIy9", 1, 0], [5, 64, 41.8], [-11.455, -34.491, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 19, [[6, 0, -338, [126], 127], [9, 2000, -339]], [0, "bc2J6UcKRHLZ8mEQmbUOuZ", 1, 0], [5, 35, 35], [31.855, 42.272, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "title", false, 12, [[24, "侧边栏", 24, false, 1, 1, 1, -340, [132]], [2, 2, -341, [4, 4278190080]]], [0, "cb/FN8S5NC/7MAu9fAqYPN", 1, 0], [5, 76, 54.4], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 12, [[6, 0, -342, [133], 134], [9, 1008, -343]], [0, "bfhVLOzNxJLLVBEskH6bqc", 1, 0], [5, 35, 35], [37.259, 48.125, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "title", false, 13, [[24, "添加桌面", 24, false, 1, 1, 1, -344, [139]], [2, 2, -345, [4, 4278190080]]], [0, "da4WF+UVdM5JbVvJeZKQca", 1, 0], [5, 100, 54.4], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 13, [[6, 0, -346, [140], 141], [9, 1009, -347]], [0, "55wrGf4MJK2aGLZSt84gPl", 1, 0], [5, 35, 35], [37.259, 48.125, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 20, [[3, -348, [144], 145]], [0, "75k7SLAD1OC7YWMHmjrzfC", 1, 0], [5, 73, 88], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "New Label", 20, [[8, "分享有礼", 28, 30, false, 1, 1, -349, [148], 149], [2, 2, -350, [4, 4278190080]]], [0, "c2aeyt/SpKJ5qHPhjZ6jiH", 1, 0], [5, 116, 41.8], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 14, [[8, "快手福利", 28, 30, false, 1, 1, -351, [154], 155], [2, 2, -352, [4, 4278190080]]], [0, "befVWbTlFFS57ISMiDu0Ok", 1, 0], [5, 116, 41.8], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 46, [[8, "特权卡", 28, 30, false, 1, 1, -353, [160], 161], [2, 2, -354, [4, 4278190080]]], [0, "23Bw9skJJBvat/3qveW/e4", 1, 0], [5, 88, 41.8], [0, -37.277, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 29, [[6, 0, -355, [164], 165], [9, 1024, -356]], [0, "13FaMGA21N/qrkyTVLvDm0", 1, 0], [5, 35, 35], [48.771, 36.635, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 47, [[8, "通行证", 28, 30, false, 1, 1, -357, [168], 169], [2, 2, -358, [4, 4278190080]]], [0, "38s0Vw/BFJz7P8rfyqg9Ye", 1, 0], [5, 88, 41.8], [0, -37.94500000000005, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "redpoint", 30, [[6, 0, -359, [172], 173], [9, 1006, -360]], [0, "96X/XDYDBJnZjvzDf5cpe1", 1, 0], [5, 35, 35], [48, 39.875, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "title", 21, [[8, "分享", 28, 30, false, 1, 1, -361, [178], 179], [2, 2, -362, [4, 4278190080]]], [0, "63iZ0FvhhLS7KoLrc7LE4C", 1, 0], [5, 60, 41.8], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 49, [[8, "签到", 28, 30, false, 1, 1, -363, [182], 183], [2, 2, -364, [4, 4278190080]]], [0, "d1zWCIRIBNCoC5er2c7Huf", 1, 0], [5, 60, 41.8], [0, -39.744, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 48, [[6, 0, -365, [186], 187], [9, 2500, -366]], [0, "1eoLolsTFKRaPA6S04DfVP", 1, 0], [5, 35, 35], [39.49, 41.249, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 50, [[8, "直播福袋", 28, 30, false, 1, 1, -367, [190], 191], [2, 3, -368, [4, 4278190080]]], [0, "9cwP64N+lIw5TCz8HsPBqw", 1, 0], [5, 118, 43.8], [0, -61.566, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [1, "title", 22, [[24, "订阅", 24, false, 1, 1, 1, -369, [200]], [2, 2, -370, [4, 4278190206]]], [0, "9f0/bzVnVKwJm+A29kkBX9", 1, 0], [5, 52, 54.4], [0, -27.537, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 22, [[6, 0, -371, [201], 202], [9, 1009, -372]], [0, "cbLx/K99ZIsIvu2zBxHy4Y", 1, 0], [5, 35, 35], [37.259, 37.777, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 52, [[8, "新手", 28, 30, false, 1, 1, -373, [205], 206], [2, 2, -374, [4, 4278190080]]], [0, "2cA0cnt1tMx5WbwMJ/pFNu", 1, 0], [5, 60, 41.8], [0, -33.492, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "redpoint", 51, [[6, 0, -375, [209], 210], [9, 1025, -376]], [0, "61TxWMXc9IJrgy/M/Y+lYP", 1, 0], [5, 35, 35], [48.771, 45.903, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_setting", 3, [53], [[7, 1.1, 3, -377, [[4, "737a1A5d/tKd7uuS8bO6S0A", "onBtn", "openSetting", 1]], [4, 4293322470], [4, 3363338360], 53, 217, 218]], [0, "573p9g29dNWqgaJUqe6bbG", 1, 0], [5, 100, 100], [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 53, [[8, "设置", 28, 30, false, 1, 1, -378, [213], 214], [2, 2, -379, [4, 4278190080]]], [0, "35IMQgf3ZHsoN2GeQqeGXF", 1, 0], [5, 60, 41.8], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "ggtipicon", false, 23, [[6, 0, -380, [221], 222]], [0, "ae5B0j5oNMf4Sw7bDH/CJI", 1, 0], [5, 45, 45], [49.19, 40.782, 0, 0, 0, 0, 1, 1, 1, -1]], [14, "New Label", 23, [[16, "广告奖励", 24, 26, false, 1, 1, 1, -381, [224]], [2, 3, -382, [4, 4278190080]]], [0, "6etHDAhWlDco/nL4xD0m75", 1, 0], [5, 106, 38.76]], [1, "New Label", 56, [[8, "反馈投诉", 28, 30, false, 1, 1, -383, [227], 228], [2, 2, -384, [4, 4278190080]]], [0, "55T8a+TrpB4ZaB01YfpQ8r", 1, 0], [5, 116, 41.8], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "New Label", 32, [[16, "视频玩法", 24, 26, false, 1, 1, 1, -385, [235]], [2, 3, -386, [4, 4278190080]]], [0, "81B+R3udRCSIzQ+36MQDYh", 1, 0], [5, 104, 54.4]], [1, "Background", 33, [[18, 1, -387, [238], 239]], [0, "f7PTtlhqNEK7rIspOIR/Oz", 1, 0], [5, 110, 71], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "New Label", 33, [[16, "玩法入口", 24, 26, false, 1, 1, 1, -388, [240]], [2, 3, -389, [4, 4278190080]]], [0, "69sEjf8xJBcYYNGvYch9y8", 1, 0], [5, 102, 38.76], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 58, [[18, 1, -390, [245], 246]], [0, "40bqU3rM9O653BZOfXcNBb", 1, 0], [5, 216, 189], [0, 0, 0, 0, 0, 0, 1, -0.5, 0.5, 0]], [1, "New Label", 58, [[87, "末日屠龙", 25, 25, false, 1, 1, -391, [247]], [2, 2, -392, [4, 4278620627]]], [0, "00X+DLxllOW6cxXcMgpVEY", 1, 0], [5, 104, 35.5], [0, -43.667, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 34, [[12, 1, 0, -393, [250], 251]], [0, "e4EbEv7v1FXKDTZZwWUe9n", 1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, -1, 1, 0]], [5, "img_zbtkbq1", 34, [-395], [[12, 1, 0, -394, [253], 254]], [0, "c87nUfEwtJLb9XaAZ4djux", 1, 0], [5, 96, 37], [0, 37.093, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 123, [[16, "主播同款", 18, 22, false, 1, 1, 1, -396, [252]], [2, 2, -397, [4, 4278190080]]], [0, "15efl2lxhD1bfrZ7oJP52V", 1, 0], [5, 76, 31.72], [0, 1.284, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 34, [[16, "蓝眼白龙", 24, 26, false, 1, 1, 1, -398, [255]], [2, 3, -399, [4, 4278190080]]], [0, "69puzfBWdN+LyviLOPqQo8", 1, 0], [5, 102, 38.76], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 35, [[3, -400, [258], 259]], [0, "d1/H/jrIpNEIl5sJ5lOPtd", 1, 0], [5, 102, 94], [0, 7.805, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "New Label", 35, [[16, "屠龙者礼包", 24, 26, false, 1, 1, 1, -401, [260]], [2, 3, -402, [4, 4278190080]]], [0, "42K1ew2spEZ6B4GQEvIKGx", 1, 0], [5, 126, 38.76], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 59, [[18, 1, -403, [263], 264]], [0, "9eo/iUkOBGa45S5PBoWaRA", 1, 0], [5, 96, 99], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "New Label", 59, [[16, "超值奖励", 24, 26, false, 1, 1, 1, -404, [265]], [2, 3, -405, [4, 4278190080]]], [0, "6bTOzFBPROooKWT5+kJLN/", 1, 0], [5, 102, 36.239999999999995]], [1, "New Label", 60, [[8, "任务", 28, 30, false, 1, 1, -406, [268], 269], [2, 2, -407, [4, 4278190080]]], [0, "dbuWdUpnFDRoBIRxJJ0WlS", 1, 0], [5, 60, 41.8], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ggtipicon", 36, [[3, -408, [272], 273], [9, 1020, -409]], [0, "50iSZKC7NE0r675+iOtvK0", 1, 0], [5, 65, 66], [41.384, 27.464, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [1, "New Label", 61, [[8, "七日", 28, 30, false, 1, 1, -410, [276], 277], [2, 2, -411, [4, 4278190080]]], [0, "aaBlOJSThMEIIeKQYAM+GV", 1, 0], [5, 60, 41.8], [0, -39.525, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ggtipicon", 37, [[3, -412, [280], 281], [9, 1021, -413]], [0, "37MKLIiEZLZbAFezaB2BU8", 1, 0], [5, 65, 66], [41.384, 47.573, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [1, "passImg", 4, [[3, -414, [4], 5]], [0, "6ej+XyqRBDn4maFB17+A6E", 1, 0], [5, 558, 566], [0, 128, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "<PERSON><PERSON><PERSON><PERSON>", 4, [[45, "default", "zhang1", 0, false, "zhang1", -415, [6], 7]], [0, "ccfXiXRoFASJ4Wtnic9y2u", 1, 0], [5, 900, 728.000244140625], [0, 128, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lock", 4, [[3, -416, [8], 9]], [0, "95ChmHpuNJDppeUolAgaIC", 1, 0], [5, 34, 41], [0, 34.149, 0, 0, 0, 0, 1, 2, 2, 1]], [55, "New RichText", 40, [[92, false, "<outline color=black width=3>难度:<color=#00ff00>普通</c>", 28, 50, -417, 13]], [0, "29t4EHmp5PFK4GaTXv9fSK", 1, 0], [5, 131.78, 63], [0, 1, 0.5], [27.283, -10.807, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "Background", 7, [[30, 2, false, -418, [16], 17]], [0, "f5vlpHinpBxJcXIqzD+u0A", 1, 0], [5, 200, 58], [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [62, "checkmark", false, 7, [-419], [0, "75Tqf3DkNM8a8eQGJsFMe+", 1, 0], [5, 200, 58]], [41, 2, false, 139, [18]], [1, "icon_ptg", 7, [[3, -420, [19], 20]], [0, "5b8P291tpI36M2VBrEByAn", 1, 0], [5, 36, 36], [-59.057, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Background", 8, [[30, 2, false, -421, [22], 23]], [0, "f2u4B8/fxCJ63tFLFBu9dy", 1, 0], [5, 200, 58]], [63, "checkmark", 8, [-422], [0, "dbyvJvak5DQqt6c0ojRf83", 1, 0], [5, 200, 58], [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [41, 2, false, 143, [24]], [1, "icon_ptg", 8, [[3, -423, [25], 26]], [0, "3dUkMklNRIPZy8K9UX+abl", 1, 0], [5, 54, 41], [65.221, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "Label", false, 24, [[44, "button", 20, false, false, 1, 1, 1, 1, -424, [31]]], [0, "d5EqeXVV5P/YSKwUraBZbG", 1, 0], [4, 4278190080], [5, 100, 40]], [37, "Label", false, 25, [[44, "button", 20, false, false, 1, 1, 1, 1, -425, [38]]], [0, "8958rr04VL36+TJE4AEHNx", 1, 0], [4, 4278190080], [5, 100, 40]], [14, "icon", 42, [[3, -426, [45], 46]], [0, "38erbueu9C1rzdQt+0r/1U", 1, 0], [5, 71, 86]], [11, "redpoint", false, 42, [[3, -427, [48], 49]], [0, "98OYP4iApDfqHwB4UwRVzx", 1, 0], [5, 22, 25], [32, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite", 18, [[3, -428, [52], 53]], [0, "5e4EoEPtFA17gA8VQSW2FR", 1, 0], [5, 52, 14], [-225, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite copy", 18, [[3, -429, [54], 55]], [0, "e21ux7UX9AJJFDV8qQohIn", 1, 0], [5, 52, 14], [-75.729, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite copy", 18, [[3, -430, [56], 57]], [0, "92NnVxMjRH36iq48bkHbzZ", 1, 0], [5, 52, 14], [76.271, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite copy", 18, [[3, -431, [58], 59]], [0, "c99wc6I81Nyo7PDXsYnKH1", 1, 0], [5, 52, 14], [225, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "bg_main_tips", 100, 43, [[12, 1, 0, -432, [61], 62]], [0, "87B3FIOsRK8pirk0Q92GeF", 1, 0], [5, 297, 130], [5, -3.431, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "03", 9, [[3, -433, [69], 70]], [0, "91OCdPoSRM8oQttjL1d9J1", 1, 0], [5, 75, 65], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [21, "hand", false, 1, 10, [[3, -434, [79], 80]], [0, "0dOfTxuVZBPoVHwtHo5J9F", 1, 0], [5, 85, 83], [130.961, -42.86, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_lightning", 79, [[30, 2, false, -435, [81], 82]], [0, "bedfVpm0lJtorAbhD7ZjQ8", 1, 0], [5, 54, 60], [-19.503, -27.313, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [21, "hand", false, 1, 11, [[3, -436, [93], 94]], [0, "8f4lcCMW1DD5HbD82rZIil", 1, 0], [5, 85, 83], [130.961, -42.86, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "video", 11, [83], [0, "62WmGpLpJASYHyCvr/upqQ", 1, 0]], [21, "hand", false, 1, 5, [[3, -437, [102], 103]], [0, "aaRJwMa3ZDTbJkT633qjm/", 1, 0], [5, 85, 83], [130.961, -42.86, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_lightning", 5, [[3, -438, [104], 105]], [0, "5boYT7haFIzI0+1B+w7bKc", 1, 0], [5, 54, 55], [-18.229, -93.119, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [88, "x5", 28, 30, false, false, 1, 1, 1, 1, 86, [106]], [58, "tishi", 5, [[45, "default", "q1", 0, false, "q1", -439, [107], 108]], [0, "2eaFVHjItFmbg0+wdNy4gQ", 1, 0]], [89, "通关第一章后解锁", 48, false, 1, 1, 87, [113]], [14, "icon", 89, [[3, -440, [114], 115]], [0, "fcqEmBu4hNMb9ZhAUr0UlY", 1, 0], [5, 48, 49]], [1, "bg_talk_01", 88, [[3, -441, [120], 121]], [0, "11tHp9SBRB9oUeeDPkYcqf", 1, 0], [5, 22, 13], [0, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Sprite(Splash)", 19, [[12, 1, 0, -442, [122], 123]], [0, "a6zNpSEWFFaZpEWOkc0dh1", 1, 0], [5, 186, 96], [-51.459, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 12, [[3, -443, [130], 131]], [0, "76kb9upspH1pTtroUIhROE", 1, 0], [5, 87, 105], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 13, [[3, -444, [137], 138]], [0, "b3UvZ0Y3xDs44TrqaMNhAW", 1, 0], [5, 99, 105], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "ggtipicon", false, 20, [[6, 0, -445, [146], 147]], [0, "c22KoJilBLnY39Tv09aale", 1, 0], [5, 45, 45], [49.19, 54.782, 0, 0, 0, 0, 1, 1, 1, -1]], [1, "Background", 14, [[3, -446, [152], 153]], [0, "fcrPUURlhAKasrc4S7qyQw", 1, 0], [5, 86, 70], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "redpoint", 14, [[6, 0, -447, [156], 157]], [0, "d28FDYHuJLV4kTMZDYnKvn", 1, 0], [5, 35, 35], [37.259, 48.125, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Background", 21, [[3, -448, [176], 177]], [0, "75lIDoDHxFfrxMqjZb2SVV", 1, 0], [5, 84, 73], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "ggtipicon", false, 31, [[6, 0, -449, [194], 195]], [0, "5e69THKs1KKKxNCvG8E/gR", 1, 0], [5, 45, 45], [49.19, 54.782, 0, 0, 0, 0, 1, 1, 1, -1]], [1, "Background", 22, [[6, 0, -450, [198], 199]], [0, "f7ATDqUDJNr7bD1flDax8y", 1, 0], [5, 98, 83], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "icon_ad_rolebubble", 23, [[77, -451, [223]]], [0, "e9BaXt2iFAyLP4qqHWa+f5", 1, 0], [5, 87, 87], [-61.307, 46.799, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "redpoint", 33, [[6, 0, -452, [241], 242]], [0, "c3KpJqnqdKWoR6x9HcnwV9", 1, 0], [5, 35, 35], [49.771, 36.308, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "GestureDraw", false, 1, 1, [[93, -453]], [0, "198QGmaP5KVJNa75j/09GO", 1, 0], [5, 750, 1334]]], 0, [0, 10, 1, 0, 11, 35, 0, 12, 40, 0, 13, 77, 0, 14, 75, 0, 15, 114, 0, 16, 71, 0, 17, 70, 0, 18, 17, 0, 19, 16, 0, 20, 26, 0, 21, 164, 0, 22, 4, 0, -1, 162, 0, -1, 65, 0, -2, 5, 0, 0, 1, 0, -1, 62, 0, -2, 63, 0, -3, 88, 0, -4, 19, 0, -5, 2, 0, -6, 3, 0, -7, 178, 0, 0, 2, 0, 0, 2, 0, -1, 12, 0, -2, 13, 0, -3, 20, 0, -4, 14, 0, -5, 29, 0, -6, 30, 0, -7, 21, 0, -8, 48, 0, -9, 31, 0, -10, 22, 0, -11, 51, 0, 0, 3, 0, 0, 3, 0, -1, 112, 0, -2, 23, 0, -3, 55, 0, -4, 32, 0, -5, 33, 0, -6, 58, 0, -7, 34, 0, -8, 35, 0, -9, 59, 0, -10, 36, 0, -11, 37, 0, -1, 39, 0, -2, 134, 0, -3, 135, 0, -4, 136, 0, -5, 40, 0, -6, 67, 0, -7, 15, 0, 0, 5, 0, 0, 5, 0, -1, 85, 0, -2, 160, 0, -3, 161, 0, -4, 86, 0, -5, 163, 0, 0, 6, 0, -1, 38, 0, -2, 26, 0, -3, 18, 0, -4, 43, 0, -5, 28, 0, -6, 87, 0, 8, 140, 0, 5, 7, 0, 0, 7, 0, -1, 138, 0, -2, 139, 0, -3, 141, 0, -4, 68, 0, 8, 144, 0, 5, 8, 0, 0, 8, 0, -1, 142, 0, -2, 143, 0, -3, 145, 0, -4, 69, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 155, 0, -2, 76, 0, -3, 77, 0, 0, 10, 0, 0, 10, 0, -1, 78, 0, -2, 156, 0, -3, 79, 0, -4, 81, 0, 0, 11, 0, 0, 11, 0, -1, 82, 0, -2, 158, 0, -3, 159, 0, -4, 84, 0, 5, 12, 0, 0, 12, 0, 0, 12, 0, -1, 168, 0, -2, 93, 0, -3, 94, 0, 5, 13, 0, 0, 13, 0, 0, 13, 0, -1, 169, 0, -2, 95, 0, -3, 96, 0, 5, 14, 0, 0, 14, 0, 0, 14, 0, -1, 171, 0, -2, 99, 0, -3, 172, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 5, 24, 0, 0, 16, 0, 0, 16, 0, -1, 24, 0, -2, 70, 0, 5, 25, 0, 0, 17, 0, 0, 17, 0, -1, 25, 0, -2, 71, 0, -1, 150, 0, -2, 151, 0, -3, 152, 0, -4, 153, 0, -5, 73, 0, 0, 19, 0, 0, 19, 0, -1, 167, 0, -2, 91, 0, -3, 92, 0, 5, 97, 0, 0, 20, 0, 0, 20, 0, -1, 97, 0, -2, 170, 0, -3, 98, 0, 5, 21, 0, 0, 21, 0, 0, 21, 0, -1, 173, 0, -2, 104, 0, 5, 22, 0, 0, 22, 0, -1, 175, 0, -2, 108, 0, -3, 109, 0, 5, 54, 0, 0, 23, 0, -1, 54, 0, -2, 114, 0, -3, 176, 0, -4, 115, 0, 0, 24, 0, 0, 24, 0, -1, 146, 0, 0, 25, 0, 0, 25, 0, -1, 147, 0, 0, 26, 0, 0, 26, 0, -1, 42, 0, 0, 27, 0, 0, 27, 0, -1, 74, 0, -2, 75, 0, 0, 28, 0, 5, 46, 0, 0, 29, 0, 0, 29, 0, -1, 46, 0, -2, 101, 0, 5, 47, 0, 0, 30, 0, 0, 30, 0, -1, 47, 0, -2, 103, 0, 5, 50, 0, 0, 31, 0, 0, 31, 0, -1, 50, 0, -2, 174, 0, 5, 57, 0, 0, 32, 0, 0, 32, 0, -1, 57, 0, -2, 117, 0, 5, 118, 0, 0, 33, 0, -1, 118, 0, -2, 119, 0, -3, 177, 0, 5, 122, 0, 0, 34, 0, -1, 122, 0, -2, 123, 0, -3, 125, 0, 5, 126, 0, 0, 35, 0, -1, 126, 0, -2, 127, 0, 5, 60, 0, 0, 36, 0, 0, 36, 0, -1, 60, 0, -2, 131, 0, 5, 61, 0, 0, 37, 0, 0, 37, 0, -1, 61, 0, -2, 133, 0, -1, 64, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, -1, 41, 0, -2, 137, 0, 0, 41, 0, 0, 41, 0, -1, 66, 0, -1, 148, 0, -2, 72, 0, -3, 149, 0, 0, 43, 0, -1, 154, 0, -2, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, -1, 89, 0, 0, 46, 0, -1, 100, 0, 0, 47, 0, -1, 102, 0, 5, 49, 0, 0, 48, 0, -1, 49, 0, -2, 106, 0, 0, 49, 0, -1, 105, 0, 0, 50, 0, -1, 107, 0, 5, 52, 0, 0, 51, 0, -1, 52, 0, -2, 111, 0, 0, 52, 0, -1, 110, 0, 0, 53, 0, -1, 113, 0, 0, 54, 0, 0, 54, 0, 5, 56, 0, 0, 55, 0, 0, 55, 0, -1, 56, 0, 0, 56, 0, -1, 116, 0, 0, 57, 0, 0, 57, 0, 5, 120, 0, 0, 58, 0, -1, 120, 0, -2, 121, 0, 5, 128, 0, 0, 59, 0, -1, 128, 0, -2, 129, 0, 0, 60, 0, -1, 130, 0, 0, 61, 0, -1, 132, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, -1, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, -1, 157, 0, -2, 80, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, -1, 162, 0, 0, 86, 0, -1, 164, 0, 0, 87, 0, -2, 166, 0, -1, 165, 0, -2, 90, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, 0, 99, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 0, 104, 0, 0, 105, 0, 0, 105, 0, 0, 106, 0, 0, 106, 0, 0, 107, 0, 0, 107, 0, 0, 108, 0, 0, 108, 0, 0, 109, 0, 0, 109, 0, 0, 110, 0, 0, 110, 0, 0, 111, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 115, 0, 0, 116, 0, 0, 116, 0, 0, 117, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 0, 119, 0, 0, 120, 0, 0, 121, 0, 0, 121, 0, 0, 122, 0, 0, 123, 0, -1, 124, 0, 0, 124, 0, 0, 124, 0, 0, 125, 0, 0, 125, 0, 0, 126, 0, 0, 127, 0, 0, 127, 0, 0, 128, 0, 0, 129, 0, 0, 129, 0, 0, 130, 0, 0, 130, 0, 0, 131, 0, 0, 131, 0, 0, 132, 0, 0, 132, 0, 0, 133, 0, 0, 133, 0, 0, 134, 0, 0, 135, 0, 0, 136, 0, 0, 137, 0, 0, 138, 0, -1, 140, 0, 0, 141, 0, 0, 142, 0, -1, 144, 0, 0, 145, 0, 0, 146, 0, 0, 147, 0, 0, 148, 0, 0, 149, 0, 0, 150, 0, 0, 151, 0, 0, 152, 0, 0, 153, 0, 0, 154, 0, 0, 155, 0, 0, 156, 0, 0, 157, 0, 0, 158, 0, 0, 160, 0, 0, 161, 0, 0, 163, 0, 0, 165, 0, 0, 166, 0, 0, 167, 0, 0, 168, 0, 0, 169, 0, 0, 170, 0, 0, 171, 0, 0, 172, 0, 0, 173, 0, 0, 174, 0, 0, 175, 0, 0, 176, 0, 0, 177, 0, 0, 178, 0, 23, 1, 4, 7, 65, 5, 7, 28, 6, 7, 63, 7, 7, 15, 8, 7, 15, 9, 7, 44, 10, 7, 28, 11, 7, 28, 16, 7, 38, 17, 7, 38, 27, 7, 44, 45, 7, 88, 53, 7, 112, 83, 7, 159, 453], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 140, 144, 162, 164], [-1, 1, -1, 6, -1, 1, -1, 9, -1, 1, -1, -1, 1, 24, -1, 6, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, 1, 3, 4, -1, -1, 1, -1, 1, 3, 4, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 6, -1, 1, -1, 1, -1, 1, -1, 6, -1, 1, -1, -1, -1, 6, -1, 1, -1, 1, -1, 6, -1, 6, 3, 4, -1, 1, -1, 6, -1, 1, -1, 1, -1, 6, -1, 1, -1, -1, 1, -1, 1, -1, -1, 9, 3, 4, -1, 1, -1, -1, 1, -1, 6, -1, 1, -1, 1, -1, 1, -1, 6, -1, 1, 3, 4, -1, 1, -1, -1, 1, 3, 4, -1, 1, -1, -1, 1, 3, 4, -1, 1, -1, 1, -1, 6, 3, 4, -1, 1, -1, 6, -1, 1, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, -1, 1, -1, 6, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, -1, 1, -1, -1, 1, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, -1, 6, -1, 1, 3, 4, -1, 1, -1, 1, -1, -1, 3, 4, -1, 6, -1, 1, 3, 4, -1, 1, -1, 3, 4, -1, 1, -1, -1, 1, 3, 4, -1, 1, -1, 3, 4, -1, 1, -1, -1, 1, -1, 3, 4, -1, 1, -1, 3, 4, -1, 1, -1, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, -1, 6, -1, 1, -1, 1, 3, 4, 25, 1, 1, 6, 6], [0, 26, 0, 2, 0, 27, 9, 28, 0, 29, 0, 0, 30, 5, 0, 2, 0, 10, 0, 0, 31, 0, 0, 10, 0, 0, 32, 0, 0, 33, 0, 0, 0, 11, 0, 3, 12, 1, 0, 0, 11, 0, 3, 12, 1, 0, 34, 0, 0, 35, 0, 36, 0, 7, 0, 7, 0, 7, 0, 7, 0, 0, 37, 0, 2, 0, 3, 0, 38, 0, 39, 0, 2, 0, 3, 0, 0, 0, 2, 0, 8, 0, 13, 0, 5, 0, 5, 14, 1, 0, 15, 0, 2, 0, 8, 0, 40, 0, 5, 0, 15, 0, 0, 8, 0, 13, 0, 9, 41, 14, 1, 0, 42, 0, 0, 43, 0, 5, 0, 44, 0, 45, 0, 46, 0, 2, 0, 3, 4, 1, 0, 47, 0, 0, 3, 6, 1, 0, 48, 0, 0, 3, 6, 1, 0, 49, 0, 3, 0, 2, 4, 1, 0, 6, 0, 2, 0, 3, 6, 1, 0, 2, 0, 50, 0, 3, 4, 1, 0, 2, 0, 51, 0, 3, 4, 1, 0, 52, 0, 2, 6, 1, 0, 2, 0, 53, 0, 3, 4, 1, 0, 2, 0, 54, 0, 3, 4, 1, 0, 55, 0, 0, 3, 6, 1, 0, 2, 0, 56, 0, 3, 4, 1, 0, 2, 0, 57, 4, 1, 0, 58, 0, 3, 0, 0, 4, 1, 0, 2, 0, 59, 4, 1, 0, 60, 0, 4, 1, 0, 61, 0, 0, 3, 4, 1, 0, 62, 0, 4, 1, 0, 63, 0, 0, 64, 0, 4, 1, 0, 65, 0, 4, 1, 0, 66, 0, 4, 1, 0, 2, 0, 67, 0, 3, 4, 1, 0, 2, 0, 68, 0, 3, 4, 1, 69, 16, 16, 5, 2]], [[{"name": "btn_main_04", "rect": [0, 0, 73, 88], "offset": [-0.5, 1], "originalSize": [74, 90], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [70]], [[{"name": "btn_tjzzm", "rect": [0, 1, 99, 105], "offset": [0, -0.5], "originalSize": [99, 106], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [71]], [[{"name": "btn_main_zhaohuan", "rect": [0, 0, 105, 96], "offset": [0, 0], "originalSize": [105, 96], "capInsets": [0, 0, 97, 0]}], [0], 0, [0], [2], [72]], [[{"name": "boxicon1_01", "rect": [0, 0, 84, 65], "offset": [0, 0], "originalSize": [84, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [73]], [[{"name": "btn_main_start", "rect": [1, 1, 209, 209], "offset": [0, 0], "originalSize": [211, 211], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [74]], [[{"name": "btn_main_03", "rect": [0, 0, 73, 85], "offset": [-0.5, 0.5], "originalSize": [74, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [75]], [[{"name": "btn_main_vip", "rect": [0, 0, 120, 127], "offset": [0, 0], "originalSize": [120, 127], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [76]], [[{"name": "sp_arrow", "rect": [0, 0, 58, 58], "offset": [0, 0], "originalSize": [58, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [77]], [[{"name": "btn_gqxzd1", "rect": [0, 0, 200, 58], "offset": [0, 0], "originalSize": [200, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [78]], [[{"name": "icon_jyg", "rect": [0, 0, 54, 41], "offset": [0, 0], "originalSize": [54, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [79]], [[{"name": "btn_main_09", "rect": [0, 0, 73, 88], "offset": [0, 0], "originalSize": [73, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [80]], [[{"name": "btn_main_07", "rect": [0, 0, 120, 128], "offset": [0, 0], "originalSize": [120, 128], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [81]], [[{"name": "img_fudai", "rect": [0, 0, 128, 132], "offset": [0, 0], "originalSize": [128, 132], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [82]], [[{"name": "btn_main_01", "rect": [0, 0, 73, 79], "offset": [-0.5, 3.5], "originalSize": [74, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [83]], [[{"name": "icon_ptg", "rect": [0, 0, 36, 36], "offset": [0, 0], "originalSize": [36, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [84]], [[{"name": "bulletdragon07", "rect": [0, 0, 116, 116], "offset": [0, 0], "originalSize": [116, 116], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [85]], [[{"name": "icon_dyyx", "rect": [8, 11, 94, 88], "offset": [0, 0], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [86]], [[{"name": "icon_rkyj", "rect": [12, 20, 86, 70], "offset": [0, 0], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [87]], [[{"name": "btn_main_08", "rect": [0, 1, 120, 123], "offset": [0, -0.5], "originalSize": [120, 124], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [88]], [[{"name": "icon_wfhj", "rect": [0, 19, 110, 71], "offset": [0, 0.5], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [89]], [[{"name": "icon_tlzlb", "rect": [4, 8, 102, 94], "offset": [0, 0], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [90]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [15, 16, 15, 16]}], [0], 0, [0], [2], [91]], [[{"name": "icon_shipinwanfa", "rect": [0, 0, 81, 83], "offset": [0, 0], "originalSize": [81, 83], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [92]], [[{"name": "03", "rect": [0, 0, 75, 65], "offset": [-1.5, 0], "originalSize": [78, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [93]], [[{"name": "icon_thhl", "rect": [7, 5, 96, 99], "offset": [0, 0.5], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [94]]]]