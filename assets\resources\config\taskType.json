{"1": {"id": 1, "define": "logInNum", "name": "登录游戏【】次", "desc": "登录游戏天数达到配置次数。按照玩家每天首次进入游戏，则记录1次。每日0点后，即为第二天。"}, "2": {"id": 2, "define": "buyEnergy", "name": "购买体力N次", "desc": "灵币和广告购买都算购买体力"}, "3": {"id": 3, "define": "useItem", "name": "消耗【】个【】道具", "desc": "玩家使用/消耗某道具达到配置个数。道具id在货币表中"}, "4": {"id": 4, "define": "getItem", "name": "获得【】个【】道具", "desc": "玩家获得某道具达到配置个数。道具id在货币表中"}, "5": {"id": 5, "define": "watchVideo", "name": "观看视频N次", "desc": "游戏内观看任意视频,使用免广告券和购买购买了永久移除广告,也算观看广告"}, "6": {"id": 6, "define": "petUp", "name": "宠物升级【】次", "desc": "玩家进行宠物升级操作达到配置次数。（不区分类型和品级）每一级，记录一次升级次数。"}, "7": {"id": 7, "define": "roleUp", "name": "角色升级【】次", "desc": "玩家进行角色升级操作达到配置次数。（不区分类型和品级）每一级，记录一次升级次数。"}, "8": {"id": 8, "define": "equipUp", "name": "装备强化【】次", "desc": "玩家进行装备强化操作达到配置次数。（不区分类型和品级）每一级，记录一次升级次数。"}, "9": {"id": 9, "define": "skillUp", "name": "法宝升级【】次", "desc": "玩家进行法宝升级操作达到配置次数。（不区分类型和品级）每一级，记录一次升级次数。"}, "10": {"id": 10, "define": "boxOpen", "name": "开启宝箱【】个", "desc": "开启商城任意宝箱法宝书和装备宝箱达到配置次数需配置商城宝箱id，配置为组， 可配置多个"}, "11": {"id": 11, "define": "buyItem", "name": "在商城购买道具【】次", "desc": "在商场每日特惠购买道具达到配置次数此处不区分道具id，只要点击购买即记录次数"}, "12": {"id": 12, "define": "joinLv", "name": "挑战【】(类型)关卡【】次", "desc": "挑战指定类型关卡达到配置次数。表：BagModeLv_type不考虑是否通关的情况。"}, "13": {"id": 13, "define": "winLv", "name": "通关【】(类型)关卡【】次", "desc": "通关指定类型关卡达到配置次数。表：BagModeLv_type必须通关才算完成"}, "14": {"id": 14, "define": "winSpecialLv", "name": "通关【】(指定)关卡【】次", "desc": "通关指定关卡达到配置次数。表：BagModeLv_lvid必须通关才算完成"}, "15": {"id": 15, "define": "skillDraw", "name": "进行法宝召唤【】次", "desc": "进行法宝领悟达到配置次数。按照抽取卡池次数来进行计算，而不是按钮点击次数。"}, "16": {"id": 16, "define": "equipDraw", "name": "进行神器召唤【】次", "desc": "进行神器共鸣达到配置次数。按照抽取卡池次数来进行计算，而不是按钮点击次数。"}, "17": {"id": 17, "define": "petDraw", "name": "进行宠物召唤【】次", "desc": "进行宠物召唤达到配置次数。按照抽取卡池次数来进行计算，而不是按钮点击次数。"}, "18": {"id": 18, "define": "powerUp", "name": "战力提升【】", "desc": "提升战力【Y】，玩家战力提升值达到配置数值，则完成（大于&等于）"}, "19": {"id": 19, "define": "power", "name": "战力达到【】", "desc": "战力达到【Y】，玩家战力达到配置数值，则完成（大于&等于）"}, "20": {"id": 20, "define": "recharge", "name": "充值【】次", "desc": "任一位置进行充值操作一次"}, "21": {"id": 21, "define": "roleDraw", "name": "进行角色招募【】次", "desc": "进行角色招募达到配置次数。按照抽取卡池次数来进行计算，而不是按钮点击次数。"}}