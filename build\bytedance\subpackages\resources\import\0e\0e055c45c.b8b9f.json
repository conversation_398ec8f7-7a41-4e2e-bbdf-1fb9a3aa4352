[1, ["ecpdLyjvZBwrvm+cedCcQy", "a0z3r2zOdOO6AxP8KYzpUv", "3fgImlj0VApqBj7DpRzn2x", "75P6nLYYxATJsKuD72DY8z", "024PcLXhhO5KOA+kkrLK+0", "4eMF4HyClL+KKoOb8lDSPX", "b6s395EyFHUKjrqwAz/90j", "3246HS6R1CNq2H3+dG9pjs", "38Vj51s/FPjaMqIoV0vBxq", "87Ho3K6QZFpa1MAczui5Py", "4bCeQiTetP8ore4SHj0Iji", "a2MjXRFdtLlYQ5ouAFv/+R", "fb+o79saxBSbRVUyvpAuQ5", "fdUR6ZkrBPgbBz9tlhpItZ", "ebG5+xI0pCoacfkga0ysSn", "ffCXtneVZM4Zd1mMmw1u1v", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO", "67Twz0Ck5EJaLgP5HR1c6U", "f0ckMLxftEjaHLeNL2yNBT", "f49Njan9hI0LGxnWkV1Ls/"], ["node", "_spriteFrame", "root", "_textureSetter", "ballNode", "ball", "gameNode", "data", "_parent", "_N$normalSprite", "_N$disabledSprite"], [["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_groupIndex", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color"], -2, 4, 9, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_left", "_top", "_bottom", "node"], -1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Layout", ["_N$layoutType", "_N$spacingY", "_N$verticalDirection", "node"], 0, 1], ["45788/eutZMLZdPOuWWgSON", ["jumpH", "jumpW", "<PERSON><PERSON><PERSON>", "sacleNum", "createNum", "bgMove", "jumpDownTime", "jumpUpTime", "node", "gameNode", "ball", "ballNode", "wallBigList"], -5, 1, 1, 1, 1, 1], ["a6bbbQCGeJFgp5TQPTSx9gB", ["node", "startArr"], 3, 1, 2], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -3, 1, 3]], [[2, 0, 1, 2], [3, 0, 2, 3, 4, 2], [0, 0, 1, 8, 6, 5, 7, 9, 3], [0, 0, 8, 10, 6, 5, 7, 9, 2], [3, 1, 0, 2, 3, 3], [0, 0, 8, 6, 5, 7, 2], [0, 0, 8, 6, 5, 7, 9, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 7], [3, 2, 3, 4, 1], [2, 0, 1, 2, 2], [0, 0, 8, 10, 6, 5, 7, 2], [5, 0, 2], [0, 0, 2, 10, 6, 5, 7, 9, 3], [0, 0, 3, 10, 6, 5, 7, 3], [0, 0, 4, 8, 6, 5, 11, 7, 3], [0, 0, 1, 8, 6, 5, 7, 3], [0, 0, 8, 5, 9, 2], [6, 0, 1, 2, 3, 4], [2, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 9], [8, 0, 1, 1], [9, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3], [4, 0, 1, 2, 3, 4, 5], [4, 0, 4, 2]], [[[{"name": "kuai05", "rect": [0, 0, 187, 172], "offset": [0, 0], "originalSize": [187, 172], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [8]], [[{"name": "kuai02", "rect": [0, 0, 187, 172], "offset": [0, 0], "originalSize": [187, 172], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [9]], [[{"name": "kuai06", "rect": [0, 0, 368, 173], "offset": [0, 0], "originalSize": [368, 173], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [10]], [[[11, "JumpBallScenne"], [12, "WallBigList", 512, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26], [[17, 2, 200, 0, -2]], [9, "28/IKflGhM3b3S8U38B8Xh", -1, 0], [5, 300, 200], [200, 27.5, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "JumpBallScenne", 1, [-32, -33, 1, -34, -35, -36], [[19, 300, 80, 0, -0.3, 30, 540, 0.5, 0.5, -31, -30, -29, -28, 1]], [18, -27, 0], [5, 750, 1334]], [3, "Box", 1, [-42, -43, -44, -45, -46, -47, -48], [[20, -41, [-38, -39, -40]]], [0, "fddxdPKBNPuoF42/y0EIwY", -37], [5, 70, 70], [142.749, 6145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-51, -52, -53], [[4, 1, 0, -50, [26]]], [0, "a7Q9LVKyZPw7hw7hGJoYhE", -49], [5, 70, 70], [-100, 475, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-56, -57, -58], [[4, 1, 0, -55, [42]]], [0, "77ODEI4EFBHb1jAPo3stf5", -54], [5, 70, 70], [-230, 1015, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-61, -62, -63], [[4, 1, 0, -60, [65]]], [0, "705KAin/tE8ZQ2VbmHfdQP", -59], [5, 70, 70], [-150, 1825, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-66, -67, -68], [[4, 1, 0, -65, [81]]], [0, "1afeAr1fdOQb8GicJdXdto", -64], [5, 70, 70], [100, 2365, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-71, -72, -73], [[4, 1, 0, -70, [97]]], [0, "e7I2yboutBy5Rauhkp6CAj", -69], [5, 70, 70], [100, 2905, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-76, -77, -78], [[4, 1, 0, -75, [120]]], [0, "23QkKgbYRI1YOgCs15scIu", -74], [5, 70, 70], [150, 3715, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-81, -82, -83], [[4, 1, 0, -80, [10]]], [0, "a2HmSp3ZdC8YRhINM7dXdR", -79], [5, 70, 70], [-200, -65, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-86, -87, -88], [[4, 1, 0, -85, [17]]], [0, "abeMaWPAJNeIZWJTHdss6O", -84], [5, 70, 70], [-120, 205, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-91, -92, -93], [[4, 1, 0, -90, [33]]], [0, "77yotQaXlOfaNPjHWPRIUX", -89], [5, 70, 70], [-150, 745, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-96, -97, -98], [[4, 1, 0, -95, [49]]], [0, "a4LtYCRZVBAZ4Y9SRj3QQV", -94], [5, 70, 70], [-100, 1285, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-101, -102, -103], [[4, 1, 0, -100, [56]]], [0, "a109cO4spDv6rXBVu1i7ej", -99], [5, 70, 70], [-150, 1555, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-106, -107, -108], [[4, 1, 0, -105, [72]]], [0, "11ZUsyWAtPxKO6liR+77b/", -104], [5, 70, 70], [-200, 2095, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-111, -112, -113], [[4, 1, 0, -110, [88]]], [0, "bbG5sNtdpHBZ/W923o3ya0", -109], [5, 70, 70], [0, 2635, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-116, -117, -118], [[4, 1, 0, -115, [104]]], [0, "3eReDEFQFPb7RXmxNY6irH", -114], [5, 70, 70], [0, 3175, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-121, -122, -123], [[4, 1, 0, -120, [111]]], [0, "728weJcd9J8IiSjlRXm+Ry", -119], [5, 70, 70], [100, 3445, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-126, -127, -128], [[4, 1, 0, -125, [127]]], [0, "55aDiXfZ1FaIhLt7JKpodb", -124], [5, 70, 70], [180, 3985, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-131, -132, -133], [[4, 1, 0, -130, [134]]], [0, "88YEksVLBP/INYhwDX4m+Y", -129], [5, 70, 70], [180, 4255, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-136, -137, -138], [[4, 1, 0, -135, [141]]], [0, "47JMkK8EhM1rnfW8dQJcfm", -134], [5, 70, 70], [180, 4525, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-141, -142, -143], [[4, 1, 0, -140, [148]]], [0, "bcqnRcq6ZMtZntAVc0K3Ca", -139], [5, 70, 70], [180, 4795, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-146, -147, -148], [[4, 1, 0, -145, [155]]], [0, "1d2wZMjIdBuoxxGlxppfWo", -144], [5, 70, 70], [150, 5065, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-151, -152, -153], [[4, 1, 0, -150, [162]]], [0, "eauIYBjCZGLIkr+S+NTMWy", -149], [5, 70, 70], [150, 5335, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-156, -157, -158], [[4, 1, 0, -155, [169]]], [0, "791cwLMjxGSKqCCr0GXckV", -154], [5, 70, 70], [150, 5605, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Wall", 1, [-161, -162, -163], [[4, 1, 0, -160, [176]]], [0, "a6GESA1c1Ombxfb3G+P+px", -159], [5, 70, 70], [150, 5875, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnpause", 2, [-166], [[21, 3, -164, [[22, "45788/eutZMLZdPOuWWgSON", "pauseGame", 2]], [4, 4293322470], [4, 3363338360], 195, 196], [23, 12, 36.24200000000002, -46.89499999999998, 50, -165]], [9, "633f4J6q9O0ZMTxbbhLHh+", 2, 0], [5, 88, 93], [-294.758, -570.5, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "maskbg", 70, 2, [[24, 45, -167], [1, 0, -168, [0], 1]], [9, "7fJDaj8qtAD4iL63ze9MUa", 2, 0], [4, 4278190080], [5, 750, 1334]], [3, "bg_level", 4, [-170], [[1, 0, -169, [19], 20]], [0, "16Rqdai+NA+qSnRDqgpa/k", 4], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_level", 4, [-172], [[1, 0, -171, [22], 23]], [0, "ddk2K6vIhAD5v1v05Scj68", 4], [5, 200, 185]], [10, "bg_level", 5, [-174], [[1, 0, -173, [37], 38]], [0, "ddk2K6vIhAD5v1v05Scj68", 5], [5, 200, 185]], [3, "bg_level", 5, [-176], [[1, 0, -175, [40], 41]], [0, "b3zS5VwrFDu62JAXL/7yyj", 5], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_level", 6, [-178], [[1, 0, -177, [58], 59]], [0, "16Rqdai+NA+qSnRDqgpa/k", 6], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_level", 6, [-180], [[1, 0, -179, [61], 62]], [0, "ddk2K6vIhAD5v1v05Scj68", 6], [5, 200, 185]], [3, "bg_level", 7, [-182], [[1, 0, -181, [74], 75]], [0, "16Rqdai+NA+qSnRDqgpa/k", 7], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_level", 7, [-184], [[1, 0, -183, [77], 78]], [0, "ddk2K6vIhAD5v1v05Scj68", 7], [5, 200, 185]], [10, "bg_level", 8, [-186], [[1, 0, -185, [92], 93]], [0, "ddk2K6vIhAD5v1v05Scj68", 8], [5, 200, 185]], [3, "bg_level", 8, [-188], [[1, 0, -187, [95], 96]], [0, "b3zS5VwrFDu62JAXL/7yyj", 8], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_level", 9, [-190], [[1, 0, -189, [115], 116]], [0, "ddk2K6vIhAD5v1v05Scj68", 9], [5, 200, 185]], [3, "bg_level", 9, [-192], [[1, 0, -191, [118], 119]], [0, "b3zS5VwrFDu62JAXL/7yyj", 9], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "star0", false, 3, [[8, -193, [181], 182]], [0, "dbLPFMfvpFYqBN2nB53A3W", 3], [5, 36, 34], [-62.568, 261.404, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "star0", false, 3, [[8, -194, [185], 186]], [0, "3fG9JTxzBAVJ7P6DwGZr8i", 3], [5, 36, 34], [-1.622, 261.404, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "star0", false, 3, [[8, -195, [189], 190]], [0, "02MbyGoQ1JxpRo+DaYYGs6", 3], [5, 36, 34], [74.148, 261.404, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bg_level", false, 2, [[1, 0, -196, [191], 192]], [9, "76QXWsEdlNmrHCtkBvpy+E", 2, 0], [5, 174, 185]], [5, "bg1", 2, [[8, -197, [2], 3]], [9, "7b9F7VBYNGY4Bu8JRl1voi", 2, 0], [5, 1024, 1700]], [2, "bg_level", false, 10, [[1, 0, -198, [4], 5]], [0, "16Rqdai+NA+qSnRDqgpa/k", 10], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 10, [[1, 0, -199, [6], 7]], [0, "ddk2K6vIhAD5v1v05Scj68", 10], [5, 200, 185]], [2, "bg_level", false, 10, [[1, 0, -200, [8], 9]], [0, "b3zS5VwrFDu62JAXL/7yyj", 10], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 11, [[1, 0, -201, [11], 12]], [0, "16Rqdai+NA+qSnRDqgpa/k", 11], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 11, [[1, 0, -202, [13], 14]], [0, "ddk2K6vIhAD5v1v05Scj68", 11], [5, 200, 185]], [2, "bg_level", false, 11, [[1, 0, -203, [15], 16]], [0, "b3zS5VwrFDu62JAXL/7yyj", 11], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 29, [[7, "+50", 80, 80, 1, 1, 1, -204, [18]]], [0, "0bVTUahStBVJt4KTBFYXUD", 4], [5, 135.7, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 30, [[7, "+5", 80, 80, 1, 1, 1, -205, [21]]], [0, "86MpfI3MdOF54yfAWb8B8z", 4], [5, 91.21, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 4, [[1, 0, -206, [24], 25]], [0, "b3zS5VwrFDu62JAXL/7yyj", 4], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 12, [[1, 0, -207, [27], 28]], [0, "16Rqdai+NA+qSnRDqgpa/k", 12], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 12, [[1, 0, -208, [29], 30]], [0, "ddk2K6vIhAD5v1v05Scj68", 12], [5, 200, 185]], [2, "bg_level", false, 12, [[1, 0, -209, [31], 32]], [0, "b3zS5VwrFDu62JAXL/7yyj", 12], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 5, [[1, 0, -210, [34], 35]], [0, "16Rqdai+NA+qSnRDqgpa/k", 5], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 31, [[7, "-4", 80, 80, 1, 1, 1, -211, [36]]], [0, "347MDyJoBFl6/ca4oHdcyG", 5], [5, 71.13, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 32, [[7, "+1000", 80, 80, 1, 1, 1, -212, [39]]], [0, "6byIB2pgNLoKUqy5b9JTo6", 5], [5, 224.69, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 13, [[1, 0, -213, [43], 44]], [0, "16Rqdai+NA+qSnRDqgpa/k", 13], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 13, [[1, 0, -214, [45], 46]], [0, "ddk2K6vIhAD5v1v05Scj68", 13], [5, 200, 185]], [2, "bg_level", false, 13, [[1, 0, -215, [47], 48]], [0, "b3zS5VwrFDu62JAXL/7yyj", 13], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 14, [[1, 0, -216, [50], 51]], [0, "16Rqdai+NA+qSnRDqgpa/k", 14], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 14, [[1, 0, -217, [52], 53]], [0, "ddk2K6vIhAD5v1v05Scj68", 14], [5, 200, 185]], [2, "bg_level", false, 14, [[1, 0, -218, [54], 55]], [0, "b3zS5VwrFDu62JAXL/7yyj", 14], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 33, [[7, "+55", 80, 80, 1, 1, 1, -219, [57]]], [0, "aaWTVayQpGkq48/rPx5Qy4", 6], [5, 135.7, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 34, [[7, "-1", 80, 80, 1, 1, 1, -220, [60]]], [0, "5bCzm7zhJIjq6avQTrp16X", 6], [5, 71.13, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 6, [[1, 0, -221, [63], 64]], [0, "b3zS5VwrFDu62JAXL/7yyj", 6], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 15, [[1, 0, -222, [66], 67]], [0, "16Rqdai+NA+qSnRDqgpa/k", 15], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 15, [[1, 0, -223, [68], 69]], [0, "ddk2K6vIhAD5v1v05Scj68", 15], [5, 200, 185]], [2, "bg_level", false, 15, [[1, 0, -224, [70], 71]], [0, "b3zS5VwrFDu62JAXL/7yyj", 15], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 35, [[7, "+35", 80, 80, 1, 1, 1, -225, [73]]], [0, "3bQrbBgqBPV4igajPJfKS/", 7], [5, 135.7, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 36, [[7, "/2", 80, 80, 1, 1, 1, -226, [76]]], [0, "974XrJI25JL72cIGiJ+GNw", 7], [5, 66.72, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 7, [[1, 0, -227, [79], 80]], [0, "b3zS5VwrFDu62JAXL/7yyj", 7], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 16, [[1, 0, -228, [82], 83]], [0, "16Rqdai+NA+qSnRDqgpa/k", 16], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 16, [[1, 0, -229, [84], 85]], [0, "ddk2K6vIhAD5v1v05Scj68", 16], [5, 200, 185]], [2, "bg_level", false, 16, [[1, 0, -230, [86], 87]], [0, "b3zS5VwrFDu62JAXL/7yyj", 16], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 8, [[1, 0, -231, [89], 90]], [0, "16Rqdai+NA+qSnRDqgpa/k", 8], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 37, [[7, "+2", 80, 80, 1, 1, 1, -232, [91]]], [0, "48dlUGHFBBfrzkJ+1otQXu", 8], [5, 91.21, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 38, [[7, "+44", 80, 80, 1, 1, 1, -233, [94]]], [0, "63TZUCFtJLzoS1dHe9+uZr", 8], [5, 135.7, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 17, [[1, 0, -234, [98], 99]], [0, "16Rqdai+NA+qSnRDqgpa/k", 17], [5, 174, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 17, [[1, 0, -235, [100], 101]], [0, "ddk2K6vIhAD5v1v05Scj68", 17], [5, 450, 185]], [2, "bg_level", false, 17, [[1, 0, -236, [102], 103]], [0, "b3zS5VwrFDu62JAXL/7yyj", 17], [5, 174, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 18, [[1, 0, -237, [105], 106]], [0, "16Rqdai+NA+qSnRDqgpa/k", 18], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 18, [[1, 0, -238, [107], 108]], [0, "ddk2K6vIhAD5v1v05Scj68", 18], [5, 200, 185]], [2, "bg_level", false, 18, [[1, 0, -239, [109], 110]], [0, "b3zS5VwrFDu62JAXL/7yyj", 18], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 9, [[1, 0, -240, [112], 113]], [0, "16Rqdai+NA+qSnRDqgpa/k", 9], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 39, [[7, "/3", 80, 80, 1, 1, 1, -241, [114]]], [0, "eajxdEQB9LI6/GOLO1HUwR", 9], [5, 66.72, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Label", 40, [[7, "+100", 80, 80, 1, 1, 1, -242, [117]]], [0, "36/kY8L8VEdaifbOjhvayh", 9], [5, 180.2, 100.8], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 19, [[1, 0, -243, [121], 122]], [0, "16Rqdai+NA+qSnRDqgpa/k", 19], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 19, [[1, 0, -244, [123], 124]], [0, "ddk2K6vIhAD5v1v05Scj68", 19], [5, 200, 185]], [2, "bg_level", false, 19, [[1, 0, -245, [125], 126]], [0, "b3zS5VwrFDu62JAXL/7yyj", 19], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 20, [[1, 0, -246, [128], 129]], [0, "16Rqdai+NA+qSnRDqgpa/k", 20], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 20, [[1, 0, -247, [130], 131]], [0, "ddk2K6vIhAD5v1v05Scj68", 20], [5, 200, 185]], [2, "bg_level", false, 20, [[1, 0, -248, [132], 133]], [0, "b3zS5VwrFDu62JAXL/7yyj", 20], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 21, [[1, 0, -249, [135], 136]], [0, "16Rqdai+NA+qSnRDqgpa/k", 21], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 21, [[1, 0, -250, [137], 138]], [0, "ddk2K6vIhAD5v1v05Scj68", 21], [5, 200, 185]], [2, "bg_level", false, 21, [[1, 0, -251, [139], 140]], [0, "b3zS5VwrFDu62JAXL/7yyj", 21], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 22, [[1, 0, -252, [142], 143]], [0, "16Rqdai+NA+qSnRDqgpa/k", 22], [5, 200, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 22, [[1, 0, -253, [144], 145]], [0, "ddk2K6vIhAD5v1v05Scj68", 22], [5, 200, 185]], [2, "bg_level", false, 22, [[1, 0, -254, [146], 147]], [0, "b3zS5VwrFDu62JAXL/7yyj", 22], [5, 200, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 23, [[1, 0, -255, [149], 150]], [0, "16Rqdai+NA+qSnRDqgpa/k", 23], [5, 174, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 23, [[1, 0, -256, [151], 152]], [0, "ddk2K6vIhAD5v1v05Scj68", 23], [5, 300, 185]], [2, "bg_level", false, 23, [[1, 0, -257, [153], 154]], [0, "b3zS5VwrFDu62JAXL/7yyj", 23], [5, 174, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 24, [[1, 0, -258, [156], 157]], [0, "16Rqdai+NA+qSnRDqgpa/k", 24], [5, 174, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 24, [[1, 0, -259, [158], 159]], [0, "ddk2K6vIhAD5v1v05Scj68", 24], [5, 450, 185]], [2, "bg_level", false, 24, [[1, 0, -260, [160], 161]], [0, "b3zS5VwrFDu62JAXL/7yyj", 24], [5, 174, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 25, [[1, 0, -261, [163], 164]], [0, "16Rqdai+NA+qSnRDqgpa/k", 25], [5, 174, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 25, [[1, 0, -262, [165], 166]], [0, "ddk2K6vIhAD5v1v05Scj68", 25], [5, 450, 185]], [2, "bg_level", false, 25, [[1, 0, -263, [167], 168]], [0, "b3zS5VwrFDu62JAXL/7yyj", 25], [5, 174, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_level", false, 26, [[1, 0, -264, [170], 171]], [0, "16Rqdai+NA+qSnRDqgpa/k", 26], [5, 174, 185], [-200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_level", 26, [[1, 0, -265, [172], 173]], [0, "ddk2K6vIhAD5v1v05Scj68", 26], [5, 450, 185]], [2, "bg_level", false, 26, [[1, 0, -266, [174], 175]], [0, "b3zS5VwrFDu62JAXL/7yyj", 26], [5, 174, 185], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "box", 3, [[8, -267, [177], 178]], [0, "c2oGbXyMlLfI8dVd3ZsUyZ", 3], [5, 403, 289]], [6, "star1", 3, [[8, -268, [179], 180]], [0, "12bT9gUQJGsojUeRtjMmzg", 3], [5, 36, 34], [-62.568, 261.404, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "star1", 3, [[8, -269, [183], 184]], [0, "75MZ2Qr0VIOLrHYJIZml7Z", 3], [5, 36, 34], [-1.622, 261.404, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "star1", 3, [[8, -270, [187], 188]], [0, "83NKIlJL5JFZXp+TFi4nA3", 3], [5, 36, 34], [74.148, 261.404, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "BallNode", 2, [9, "be26i83kREDIl88EA405sj", 2, 0], [0, -270, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "tpdg_icon_zanting", 27, [[8, -271, [193], 194]], [9, "3aGEdCwvlHnL/XtJhEDUSv", 2, 0], [5, 94, 99], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]]], 0, [0, 2, 2, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -3, 4, 0, -4, 12, 0, -5, 5, 0, -6, 13, 0, -7, 14, 0, -8, 6, 0, -9, 15, 0, -10, 7, 0, -11, 16, 0, -12, 8, 0, -13, 17, 0, -14, 18, 0, -15, 9, 0, -16, 19, 0, -17, 20, 0, -18, 21, 0, -19, 22, 0, -20, 23, 0, -21, 24, 0, -22, 25, 0, -23, 26, 0, -24, 3, 0, 2, 2, 0, 4, 119, 0, 5, 44, 0, 6, 2, 0, 0, 2, 0, -1, 28, 0, -2, 45, 0, -4, 119, 0, -5, 44, 0, -6, 27, 0, 2, 3, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, 0, 3, 0, -1, 115, 0, -2, 116, 0, -3, 41, 0, -4, 117, 0, -5, 42, 0, -6, 118, 0, -7, 43, 0, 2, 4, 0, 0, 4, 0, -1, 29, 0, -2, 30, 0, -3, 54, 0, 2, 5, 0, 0, 5, 0, -1, 58, 0, -2, 31, 0, -3, 32, 0, 2, 6, 0, 0, 6, 0, -1, 33, 0, -2, 34, 0, -3, 69, 0, 2, 7, 0, 0, 7, 0, -1, 35, 0, -2, 36, 0, -3, 75, 0, 2, 8, 0, 0, 8, 0, -1, 79, 0, -2, 37, 0, -3, 38, 0, 2, 9, 0, 0, 9, 0, -1, 88, 0, -2, 39, 0, -3, 40, 0, 2, 10, 0, 0, 10, 0, -1, 46, 0, -2, 47, 0, -3, 48, 0, 2, 11, 0, 0, 11, 0, -1, 49, 0, -2, 50, 0, -3, 51, 0, 2, 12, 0, 0, 12, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, 2, 13, 0, 0, 13, 0, -1, 61, 0, -2, 62, 0, -3, 63, 0, 2, 14, 0, 0, 14, 0, -1, 64, 0, -2, 65, 0, -3, 66, 0, 2, 15, 0, 0, 15, 0, -1, 70, 0, -2, 71, 0, -3, 72, 0, 2, 16, 0, 0, 16, 0, -1, 76, 0, -2, 77, 0, -3, 78, 0, 2, 17, 0, 0, 17, 0, -1, 82, 0, -2, 83, 0, -3, 84, 0, 2, 18, 0, 0, 18, 0, -1, 85, 0, -2, 86, 0, -3, 87, 0, 2, 19, 0, 0, 19, 0, -1, 91, 0, -2, 92, 0, -3, 93, 0, 2, 20, 0, 0, 20, 0, -1, 94, 0, -2, 95, 0, -3, 96, 0, 2, 21, 0, 0, 21, 0, -1, 97, 0, -2, 98, 0, -3, 99, 0, 2, 22, 0, 0, 22, 0, -1, 100, 0, -2, 101, 0, -3, 102, 0, 2, 23, 0, 0, 23, 0, -1, 103, 0, -2, 104, 0, -3, 105, 0, 2, 24, 0, 0, 24, 0, -1, 106, 0, -2, 107, 0, -3, 108, 0, 2, 25, 0, 0, 25, 0, -1, 109, 0, -2, 110, 0, -3, 111, 0, 2, 26, 0, 0, 26, 0, -1, 112, 0, -2, 113, 0, -3, 114, 0, 0, 27, 0, 0, 27, 0, -1, 120, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, -1, 52, 0, 0, 30, 0, -1, 53, 0, 0, 31, 0, -1, 59, 0, 0, 32, 0, -1, 60, 0, 0, 33, 0, -1, 67, 0, 0, 34, 0, -1, 68, 0, 0, 35, 0, -1, 73, 0, 0, 36, 0, -1, 74, 0, 0, 37, 0, -1, 80, 0, 0, 38, 0, -1, 81, 0, 0, 39, 0, -1, 89, 0, 0, 40, 0, -1, 90, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 120, 0, 7, 2, 1, 8, 2, 271], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 9, 10], [0, 11, 0, 12, 0, 1, 0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 1, 0, 0, 0, 3, 0, 0, 4, 0, 1, 0, 0, 1, 0, 2, 0, 1, 0, 0, 1, 0, 0, 4, 0, 0, 3, 0, 0, 1, 0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 1, 0, 0, 0, 3, 0, 0, 4, 0, 1, 0, 0, 1, 0, 2, 0, 1, 0, 0, 0, 3, 0, 0, 4, 0, 1, 0, 0, 1, 0, 2, 0, 1, 0, 0, 1, 0, 0, 4, 0, 0, 3, 0, 0, 1, 0, 5, 0, 2, 0, 0, 1, 0, 2, 0, 1, 0, 0, 1, 0, 0, 4, 0, 0, 3, 0, 0, 1, 0, 2, 0, 3, 0, 0, 1, 0, 2, 0, 3, 0, 0, 1, 0, 2, 0, 3, 0, 0, 1, 0, 2, 0, 3, 0, 0, 1, 0, 13, 0, 3, 0, 0, 1, 0, 5, 0, 3, 0, 0, 1, 0, 5, 0, 3, 0, 0, 1, 0, 5, 0, 3, 0, 0, 14, 0, 6, 0, 7, 0, 6, 0, 7, 0, 6, 0, 7, 0, 2, 0, 15, 16, 17]], [[{"name": "kuai04", "rect": [0, 0, 187, 172], "offset": [0, 0], "originalSize": [187, 172], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [18]], [[{"name": "bj2", "rect": [0, 0, 1024, 1700], "offset": [0, 0], "originalSize": [1024, 1700], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [19]], [[{"name": "kuai01", "rect": [0, 0, 188, 175], "offset": [0, 0], "originalSize": [188, 175], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [20]]]]