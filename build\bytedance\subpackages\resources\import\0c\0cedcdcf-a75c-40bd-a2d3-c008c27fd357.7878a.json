[1, ["ecpdLyjvZBwrvm+cedCcQy", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "f8npR6F8ZIZoCp3cKXjJQz", "a2MjXRFdtLlYQ5ouAFv/+R", "40OtPssnZP9antMAWsDtDT", "73jGpne/9JUI/171Zur5Pr", "f7293wEF9JhIuMEsrLuqng"], ["node", "_spriteFrame", "_N$normalSprite", "_N$disabledSprite", "_N$file", "_N$font", "root", "desc", "title", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color"], -1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 6, 6, 1, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$cacheMode", "_lineHeight", "_styleFlags", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -3, 1], ["de2592NO81P9LjiJehlzvmZ", ["node", "title", "desc"], 3, 1, 1, 1]], [[2, 0, 1, 2, 2], [0, 0, 7, 5, 4, 6, 9, 2], [1, 1, 0, 3, 4, 5, 3], [9, 0, 1, 2, 3], [10, 0, 1, 2, 2], [5, 0, 2], [0, 0, 1, 8, 5, 4, 6, 3], [0, 0, 2, 7, 5, 4, 10, 6, 3], [0, 0, 7, 8, 4, 2], [0, 0, 8, 5, 4, 6, 9, 2], [0, 0, 3, 7, 5, 4, 6, 9, 3], [0, 0, 7, 8, 5, 4, 6, 9, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [7, 0, 1, 2, 3, 4, 5, 2], [1, 1, 0, 3, 4, 3], [1, 0, 2, 3, 4, 5, 3], [1, 3, 4, 5, 1], [8, 0, 1, 2, 3, 4], [2, 1, 2, 1], [3, 2, 3, 4, 7, 5, 6, 1], [3, 0, 1, 2, 3, 8, 4, 5, 6, 3], [4, 0, 1, 5, 2, 3, 4, 6, 9, 10, 11, 8], [4, 0, 1, 7, 2, 8, 3, 4, 9, 10, 8], [11, 0, 1, 2, 3, 4, 5, 6, 7], [12, 0, 1, 2, 1]], [[5, "Common<PERSON><PERSON>t"], [6, "Common<PERSON><PERSON>t", 1, [-5, -6], [[24, -4, -3, -2]], [18, -1, 0], [5, 750, 1334]], [9, "gg_db_blue2", [-8, -9, -10, -11, -12], [[2, 1, 0, -7, [14], 15]], [0, "131NjnBMlPWbIBn0+JotDb", 1, 0], [5, 555, 410], [0, -2.011, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnClose", false, 2, [[15, 2, false, -13, [3], 4], [19, -15, [[3, "de2592NO81P9LjiJehlzvmZ", "close", 1]], [4, 4294967295], -14, 5, 6]], [0, "c3JFdl569IkJenKkEoXgFH", 1, 0], [5, 40, 40], [235, 260.011, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btnConfirm", 2, [-18], [[20, 0.9, 3, -16, [[3, "de2592NO81P9LjiJehlzvmZ", "close", 1]], [4, 4293322470], [4, 3363338360], 9, 10], [16, -17, [11], 12]], [0, "c74n6/GhpMeJakvr4bw1W4", 1, 0], [5, 206, 80], [0, -128.307, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "bg", 177.98999999999998, 1, [[2, 1, 0, -19, [0], 1], [17, 45, 750, 1334, -20]], [0, "e0XQplFMtOjad63yAtj2DL", 1, 0], [4, 4281542699], [5, 750, 1334]], [1, "Label", 4, [[21, "确  认", 36, false, false, 1, 1, 1, -21, [7], 8], [4, 2, -22, [4, 4278190080]]], [0, "2c3oF0sf5F0riVvsYncXHC", 1, 0], [5, 93.57, 54.4], [-1.297, 5.038, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "title", 2, [[-23, [4, 3, -24, [4, 4278190080]]], 1, 4], [0, "286Ak/b7pD5pi0pvIgNd59", 1, 0], [5, 113.71, 66.47999999999999], [0, 163.757, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "box", 1, [2], [0, "55nslLPmdDc43IXTGR+TR/", 1, 0]], [1, "gg_db", 2, [[14, 1, 0, -25, [2]]], [0, "a6gXaO7aNOHo1VEYHTXT8/", 1, 0], [5, 534, 147], [0, 22.827, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "desc", 2, [-26], [0, "54hBiAHy5FtK6JCW9m0AXp", 1, 0], [4, 4278190080], [5, 350, 39.06], [10.217, 22.827, 0, 0, 0, 0, 1, 1, 1, 1]], [23, false, "提示", 1, 26, 350, 31, 10], [22, "提 示", 48, 48, false, 1, 1, 1, 7, [13]]], 0, [0, 6, 1, 0, 7, 11, 0, 8, 12, 0, 0, 1, 0, -1, 5, 0, -2, 8, 0, 0, 2, 0, -1, 9, 0, -2, 3, 0, -3, 4, 0, -4, 10, 0, -5, 7, 0, 0, 3, 0, 9, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 6, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -1, 12, 0, 0, 7, 0, 0, 9, 0, -1, 11, 0, 10, 1, 2, 11, 8, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 12], [-1, 1, -1, -1, 1, 2, 3, -1, 4, 2, 3, -1, 1, -1, -1, 1, 5, 4], [0, 4, 0, 0, 1, 1, 2, 0, 3, 1, 2, 0, 5, 0, 0, 6, 7, 3]]