[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "a2MjXRFdtLlYQ5ouAFv/+R", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "65hdeSelhOVq5sz7eNtoFd", "97IAXRHnlLq7dNeJPB6SbL", "cb1VFt7lZM5bK3HPiU1WUY", "a0rvROFd5IVr9UoEF7ZIum", "6fAm8iBJdM86X5VfFktFgv", "73jGpne/9JUI/171Zur5Pr", "4613FnL15GjKE6Ci5QeOnx"], ["node", "_spriteFrame", "_N$file", "_parent", "root", "_N$disabledSprite", "_N$font", "templete", "_N$target", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_anchorPoint", "_color"], 0, 9, 4, 5, 2, 1, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_right", "_top", "_bottom", "alignMode", "node"], -5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 2, 4, 5, 7, 2], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.LabelOutline", ["_width", "_enabled", "node", "_color"], 1, 1, 5], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_N$horizontalDirection", "_resize", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node", "_N$font"], -2, 1, 6], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content", "templete"], 3, 1, 5, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["3b6ecCvIIVH/aZnWBqQuLZk", ["UItype", "node", "nodeArr", "labelArr", "imgList"], 2, 1, 2, 2, 2]], [[3, 0, 1, 2, 2], [3, 0, 1, 2], [0, 0, 7, 3, 4, 5, 8, 2], [1, 1, 0, 3, 4, 5, 3], [0, 0, 7, 6, 3, 4, 5, 8, 2], [0, 0, 7, 3, 4, 5, 9, 8, 2], [2, 0, 1, 2, 8, 4], [1, 3, 4, 5, 1], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 8], [6, 0, 2, 3, 2], [8, 0, 2], [0, 0, 1, 6, 3, 4, 5, 3], [0, 0, 2, 7, 3, 4, 10, 5, 3], [0, 0, 7, 6, 3, 4, 5, 2], [0, 0, 6, 3, 4, 5, 2], [0, 0, 7, 6, 3, 4, 5, 9, 8, 2], [0, 0, 6, 3, 4, 5, 9, 2], [0, 0, 6, 3, 4, 5, 8, 2], [0, 0, 7, 3, 4, 10, 5, 8, 2], [9, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 6, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [2, 0, 8, 2], [2, 0, 3, 4, 5, 6, 1, 2, 8, 8], [2, 7, 0, 1, 8, 4], [1, 0, 3, 4, 5, 2], [1, 0, 2, 3, 4, 3], [1, 1, 0, 3, 4, 3], [3, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [6, 1, 0, 2, 3, 3], [10, 0, 1, 2, 3, 4, 5, 6, 2], [11, 0, 1, 2, 3], [7, 0, 1, 2, 5, 6, 4], [7, 3, 0, 4, 5, 6, 4], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 1], [16, 0, 1], [17, 0, 1, 2, 3, 4, 2]], [[10, "Role_BuffLvList"], [11, "Role_BuffLvList", 1, [-7, -8], [[40, 0, -6, [-5], [-4], [-2, -3]]], [28, -1, 0], [5, 750, 1334]], [17, "item", [-11, -12, -13], [[3, 1, 0, -10, [19], 20]], [1, "bdj9JSrOZJEaT6FRRP4W66", -9], [5, 614, 112], [0, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "content", [-16, -17, -18, -19, -20, -21], [[3, 1, 0, -14, [23], 24], [39, -15]], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 680, 914]], [16, "content", [2], [[24, 0, 41, 614, -22], [34, 1, 2, 10, -23, [5, 620, 112]]], [0, "ecKprdHUBMRoHqCj7C6P4V", 1, 0], [5, 620, 112], [0, 0.5, 1]], [4, "lockNode", 2, [-26, -27], [[33, 1, 5, 1, -24, [5, 200, 29.999999999999986]], [23, 37, 372.781, 8.936999999999983, 6.278000000000006, 75.72200000000001, 300, 50, -25]], [1, "8ba5DXv5FPj5Ib+fKVU1e4", 2], [5, 200, 29.999999999999986], [198.063, 34.722, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Background", 3, [[7, -28, [3], 4], [31, 3, -30, [[32, "3b6ecCvIIVH/aZnWBqQuLZk", "close", 1]], [4, 4293322470], [4, 3363338360], -29, 5]], [0, "8eA4UH8WpDFqY1WRuEBzmH", 1, 0], [5, 64, 65], [269.386, 411.808, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "New ScrollView", 3, [-33], [[37, false, 0.75, 0.23, null, null, -31, 4], [38, -32, [0, 0, 20], 4, 22]], [0, "c16wooUcRFu7atWQTNsgll", 1, 0], [5, 620, 597], [0, -118.607, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 7, [4], [[36, 0, -34, [21]], [6, 45, 240, 250, -35]], [0, "f6rJOdci5LpqR2itdXzuYz", 1, 0], [5, 620, 597], [0, 0.5, 1], [0, 298.5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "maskbg", 200, 1, [[22, 45, -36], [25, 0, -37, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [13, "bg", 1, [3], [[6, 45, 750, 1334, -38]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [19, "Label_title", 3, [[-39, [9, 3, -40, [4, 4278190080]]], 1, 4], [0, "faO0xnjS5I05EtB+KJMczm", 1, 0], [5, 198, 66.47999999999999], [0, 412.252, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "frame", 3, [-42], [-41], [0, "94m3lcJuVHaLYK7U8XAFRD", 1, 0], [5, 116, 120], [0, 282.359, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "name", 2, [[8, "等级1", 28, 28, false, 1, 1, 1, -43, [12], 13], [9, 2, -44, [4, 4278190080]]], [1, "faw9K1zJJAW5/1K2cCwiV2", 2], [5, 72.93, 39.28], [0, 0, 0.5], [-295.194, 35.474, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "msg", 5, [[8, "8天21时后结束", 20, 20, false, 1, 1, 1, -45, [16], 17], [30, false, 2, -46, [4, 4278190080]]], [1, "c4nb53XVJK5KUAphA24uAs", 2], [4, 4279571947], [5, 131.66, 25.2], [-4.829999999999998, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "技能详情", 48, 48, false, 1, 1, 1, 11, [2]], [2, "fkbg_02", 3, [[3, 1, 0, -47, [6], 7]], [0, "ba3dgY0OJGsr6W/o7C1onV", 1, 0], [5, 640, 790], [0, -34.559, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_fgx", 3, [[3, 1, 0, -48, [8], 9]], [0, "b3uM7k71pMZ7w2/AfMNXE7", 1, 0], [5, 600, 6], [0, 202.793, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "icon", 12, [-49], [0, "ebWwxcX+BJ8aKjHsCfA9hG", 1, 0], [5, 105, 105], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.8]], [26, 2, false, 18, [10]], [27, 1, 0, 12, [11]], [2, "icon_clock", 5, [[7, -50, [14], 15]], [1, "d6TG55qxBEgoVLuGZJ6Hf5", 2], [5, 34, 41], [83, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "desc", 2, [[35, false, "等级145456", 22, 580, 22, -51, 18]], [1, "0cOSpYttVM8YmVApudj6RO", 2], [5, 580, 27.72], [0, 0, 0.5], [-291.735, -19.048, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, -1, 20, 0, -2, 19, 0, -1, 15, 0, -1, 4, 0, 0, 1, 0, -1, 9, 0, -2, 10, 0, 4, 2, 0, 0, 2, 0, -1, 13, 0, -2, 5, 0, -3, 22, 0, 0, 3, 0, 0, 3, 0, -1, 11, 0, -2, 6, 0, -3, 16, 0, -4, 17, 0, -5, 12, 0, -6, 7, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, -2, 14, 0, 0, 6, 0, 8, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -1, 15, 0, 0, 11, 0, -1, 20, 0, -1, 18, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 16, 0, 0, 17, 0, -1, 19, 0, 0, 21, 0, 0, 22, 0, 9, 1, 2, 3, 4, 3, 3, 10, 4, 3, 8, 51], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 20], [-1, 1, -1, -1, 1, 5, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 2, 6, -1, 1, -1, 7, -1, 1, 2, 1], [0, 2, 0, 0, 3, 4, 0, 5, 0, 6, 0, 0, 0, 1, 0, 7, 0, 1, 1, 0, 8, 0, 9, 0, 10, 1, 11]]