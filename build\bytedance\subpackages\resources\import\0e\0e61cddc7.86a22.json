[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "f8npR6F8ZIZoCp3cKXjJQz", "97a804TTFMVqu/kFna6hsA", "e1t5qlP69IQL6gJxTISi/v", "57qkZVAGJD+ofidLcXAy2R", "c7V1RSuhVOV41GIk7Uph9P", "393Tk7O7VPI72ePId+oK6J", "d2nd02AhZCSYUCtn2htJUh", "cfK92SGtpLCZI/+fJ3kewM", "c7nqCinvlNj7/iFqjIwYYf", "9dG0LepwZCA7qzSYWrBbWq", "52L9wuaDBLfrmTwr7VSVzE", "0afejlz5RNrKdKAPUPPkaj", "90/IIzgktJEre8ZKvWm8bz", "a02zYf3DlGR7r5ATqHKjHF", "ee+rx2ot9HO6Dy8+8B04V4", "6cQRxkJ6NBU48yHfLJlW7p", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO", "601bh6lQFFZaXFW8HHrZDC", "38dhehhD5C3pG/UEulViWN", "31yuUKB8dMEJbt2e/qvNwW", "7a/QZLET9IDreTiBfRn2PD", "68Hhqr/GJKCLEdKqLD+v3H", "1fENyN4wdLFZV0sjd8q8xO", "00QDqyvmxLNrs7pxlhcSFA", "e4wWD0ReJLo6FNORTL/F/3", "9fznqgxwlCFpOx6QAXSpzz", "a55CR97zdMw6j2r02jI1+D", "69dywUJw9Bpqpc53zeIc78"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "_N$file", "_N$barSprite", "activeSkillBar", "roleLifeBar", "BoxBar", "finger", "packLayout", "data", "_N$normalSprite", "_N$disabledSprite", "_N$font"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], -1, 4, 9, 5, 1, 7, 2, 5, 5, 5], "cc.SpriteFrame", ["cc.Widget", ["_alignFlags", "_originalWidth", "_top", "_originalHeight", "_left", "_bottom", "_right", "node"], -4, 1], ["cc.Sprite", ["_type", "_sizeMode", "_fillRange", "_fillType", "_fillStart", "node", "_materials", "_spriteFrame", "_fillCenter"], -2, 1, 3, 6, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_styleFlags", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_groupIndex", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint", "_color"], 0, 2, 4, 5, 7, 1, 2, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_N$verticalDirection", "_N$affectedByScale", "node", "_layoutSize"], -3, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node", "_N$font"], -2, 1, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 12, 9, 4, 5, 5, 7], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc262IT+8VN4JA3vb8qu5Uj", ["UItype", "node", "nodeArr", "packLayout", "finger", "BoxBar", "roleLifeBar", "activeSkillBar"], 2, 1, 2, 1, 1, 1, 1, 1], ["061b8/OBgdEfI0ajbX8IA//", ["<PERSON><PERSON>", "KnapsackName", "node"], 1, 1], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "toValue", "node"], 0, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], -1, 1, 3], ["d732dc7GBNMdKNl/fEraQaw", ["node", "hpLabel", "armorLabel"], 3, 1, 1, 1]], [[6, 0, 1, 2, 2], [6, 0, 1, 2], [3, 5, 6, 7, 1], [0, 0, 7, 5, 4, 6, 8, 2], [17, 0, 1, 2, 2], [0, 0, 2, 7, 5, 4, 6, 8, 3], [3, 0, 1, 5, 6, 7, 3], [16, 0, 1, 2, 3], [0, 0, 1, 7, 5, 4, 6, 8, 3], [2, 0, 4, 6, 2, 5, 1, 3, 7, 8], [3, 0, 1, 2, 5, 6, 7, 4], [0, 0, 9, 5, 4, 6, 10, 8, 2], [0, 0, 7, 9, 5, 4, 6, 2], [0, 0, 1, 7, 9, 4, 8, 3], [0, 0, 7, 9, 5, 4, 6, 8, 2], [0, 0, 1, 7, 5, 4, 6, 10, 8, 3], [0, 0, 1, 7, 5, 4, 6, 3], [0, 0, 3, 1, 7, 5, 4, 6, 10, 8, 12, 4], [5, 0, 7, 3, 4, 5, 9, 6, 2], [12, 0, 1, 2, 3, 4, 5, 6, 3], [3, 5, 6, 1], [3, 0, 1, 5, 6, 3], [15, 0, 1, 2, 2], [7, 2, 3, 1], [4, 0, 1, 5, 2, 3, 4, 8, 9, 7], [4, 0, 1, 2, 6, 3, 4, 8, 9, 7], [10, 0, 2], [0, 0, 1, 9, 5, 4, 6, 3], [0, 0, 2, 7, 9, 5, 4, 8, 3], [0, 0, 1, 7, 4, 6, 8, 3], [0, 0, 9, 5, 4, 6, 2], [0, 0, 7, 9, 5, 4, 6, 10, 8, 2], [0, 0, 2, 1, 7, 5, 4, 6, 8, 4], [0, 0, 7, 9, 4, 8, 2], [0, 0, 1, 7, 5, 4, 11, 6, 10, 8, 3], [0, 0, 1, 7, 5, 4, 11, 6, 3], [0, 0, 1, 5, 4, 6, 8, 3], [0, 0, 7, 5, 4, 6, 2], [0, 0, 7, 5, 4, 6, 10, 8, 2], [5, 0, 1, 8, 3, 4, 5, 6, 3], [5, 0, 2, 7, 3, 4, 10, 5, 6, 3], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [13, 0, 1, 2, 3, 4, 5, 6, 7, 2], [6, 1, 2, 1], [2, 0, 2, 7, 3], [2, 0, 1, 7, 3], [2, 0, 1, 3, 7, 4], [2, 0, 2, 5, 1, 3, 7, 6], [2, 0, 4, 7, 3], [2, 0, 7, 2], [14, 0, 1, 2, 3], [8, 0, 1, 2, 6, 7, 4], [8, 0, 1, 3, 4, 5, 6, 7, 6], [3, 1, 5, 6, 7, 2], [3, 0, 3, 4, 2, 5, 6, 8, 5], [7, 1, 0, 2, 3, 3], [7, 0, 2, 3, 4, 5, 6, 7, 2], [4, 0, 1, 5, 2, 6, 3, 4, 8, 9, 8], [4, 0, 1, 2, 3, 4, 8, 9, 10, 6], [4, 0, 1, 5, 7, 2, 3, 4, 8, 9, 10, 8], [18, 0, 1, 2, 3, 4], [9, 0, 1, 2, 3, 4, 5, 6], [9, 0, 1, 2, 3, 4, 5, 6, 6], [19, 0, 1, 2, 3, 4, 5, 5], [20, 0, 1, 2, 1]], [[[{"name": "jd_zd01", "rect": [0, 0, 701, 38], "offset": [0, 0], "originalSize": [701, 38], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[{"name": "buff_btn", "rect": [0, 0, 144, 144], "offset": [0, 0], "originalSize": [144, 144], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [9]], [[{"name": "img_longtou", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [10]], [[{"name": "jt", "rect": [0, 0, 94, 121], "offset": [0, 0], "originalSize": [94, 121], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [11]], [[{"name": "icon_finger", "rect": [0, 0, 85, 83], "offset": [0, 0], "originalSize": [85, 83], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [12]], [[{"name": "btn_exit", "rect": [0, 0, 62, 62], "offset": [0, 0], "originalSize": [62, 62], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [13]], [[{"name": "seven02", "rect": [0, 0, 136, 136], "offset": [0, 0], "originalSize": [136, 136], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [14]], [[{"name": "img_jngq", "rect": [0, 0, 110, 110], "offset": [0, 0], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [15]], [[{"name": "jd_zd03", "rect": [7, 7, 687, 24], "offset": [0, 0], "originalSize": [701, 38], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [16]], [[[26, "M33_FightUIView"], [27, "M30_FightUIView", 1, [-10, -11], [[42, 0, -9, [-7, -8], -6, -5, -4, -3, -2]], [43, -1, 0], [5, 750, 1334]], [39, "LifeBar", 1, [-14, -15, -16, -17, -18], [-13], [1, "cbdUvTvPNNr4Vo2itvSkv9", -12], [5, 700, 14], [0, 19.237, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "topUI", [-20, -21, -22, -23, -24, -25, -26, -27, -28, -29], [[44, 41, 27, -19]], [0, "8bkbxxodpNcpnBOLn5m5Hf", 1, 0], [5, 750, 100], [0, 0.5, 1], [0, 640, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "BoxBar", false, 3, [-31, -32, -33, -34, -35], [[50, "42", "<PERSON><PERSON><PERSON>", -30]], [0, "e8RYCBKn9F84uS4h0T43jZ", 1, 0], [-43.984, -232.532, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "bottonUI", [2, -37, -38, -39, -40], [[45, 44, 600, -36]], [0, "f3xMePUptLpI+sbMIURS5n", 1, 0], [5, 750, 140], [0, 0.5, 0], [0, -667, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "New Node", 1, 2, [[-42, -43, [29, "New Node", 1, -44, [1, "3eRbRz4bhHvKJyrjiZt1yE", 2], [5, 20, 0], [103.96000000000001, 0, 0, 0, 0, 0, 1, 1, 1, 1]], -45, -46], 1, 1, 4, 1, 1], [[51, 1, 1, 2, -41, [5, 193.92000000000002, 50]]], [1, "fcB0gTNdRJcbgyHCVTpB/q", 2], [5, 193.92000000000002, 50], [0, 0, 0.5], [-352.164, 30.933, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "progressBar", [-51], [[6, 1, 0, -48, [9], 10], [22, 390, -50, -49]], [1, "d0heqGn/REkr96jZ88EG3o", -47], [5, 390, 32]], [12, "progressBar", 4, [-56], [[6, 1, 0, -53, [18], 19], [22, 220, -55, -54]], [1, "d0heqGn/REkr96jZ88EG3o", -52], [5, 228, 32]], [13, "guide", 1, 5, [-57, -58, -59, -60], [0, "d1mCZY58tGCowUkZM5PWE5", 1, 0], [0, 378.802, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "skill", 5, [-63, -64], [[2, -61, [52], 53], [55, 0.9, 3, -62, [[7, "cc262IT+8VN4JA3vb8qu5Uj", "onClickActiveSkill", 1]]]], [0, "6evfM+a4dGlofAZygxek1c", 1, 0], [5, 110, 110], [292.732, 229.857, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "bg", 1, [3, 5], [[46, 45, 750, 1334, -65]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [31, "Layout", 5, [-67], [[52, 1, 2, 10, 0, true, -66, [5, 200, 40.8]]], [0, "2bDcQhesxIL6byEkgrxGis", 1, 0], [5, 200, 40.8], [0, 0.5, 0], [-304.574, 37.845, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "Tips", false, 1, 5, [[57, "点击发射箭矢", 28, 28, false, 1, 1, 1, -68, [41]], [4, 4, -69, [4, 4278190080]]], [0, "6eEh5oyXpMxq487SEokh5i", 1, 0], [5, 176, 43.28], [0, 144.941, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon_finger", 1, 9, [[2, -70, [46], 47], [60, 3, 1, [270, 1], -71]], [0, "7cLKwRJnlN765uACEtFK9k", 1, 0], [5, 85, 83], [-133.819, 31.424, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [14, "btnpause", 3, [-73], [[56, 3, -72, [[7, "cc262IT+8VN4JA3vb8qu5Uj", "pauseGame", 1]], [4, 4293322470], [4, 3363338360], 2, 3]], [0, "dfUReHEtdNp6p7DWejBiVU", 1, 0], [5, 80, 80], [-297.339, -93.348, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "level", 3, [[58, "当前第%d关", 36, false, 1, 1, -74, [6], 7], [4, 3, -75, [4, 4278190080]]], [0, "57+Z5SGi9Ne6qdaUOIXS/w", 1, 0], [5, 197.04, 56.4], [0, -85.727, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "BossLifeBar", 3, [7, -76], [0, "05ATD7tetCFrhHNIYdBmTR", 1, 0], [47.423, -188.069, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "td_bless", false, 3, [[20, -77, [13]], [23, -78, [[7, "cc262IT+8VN4JA3vb8qu5Uj", "onClickDamagePanel", 1]]]], [0, "d8K6IRsIhKiKsaQ4hEEhXE", 1, 0], [5, 112, 95], [-275.797, -284.162, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "buff_btn", false, 3, [[2, -79, [15], 16]], [0, "42z2oGb2VEdKRYbkZp5CFo", 1, 0], [5, 144, 144], [-249.26, -172.862, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [15, "<PERSON><PERSON>", 1, 4, [[24, "20", 25, 26, false, 1, 1, -80, [23]], [4, 4, -81, [4, 4278190080]]], [0, "697O7+BHdD76JepaVrFRLC", 1, 0], [5, 35.66, 40.76], [0, 1, 0.5], [4.036, 2.397, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Limt", 1, 4, [[24, "/100", 25, 26, false, 1, 1, -82, [24]], [4, 4, -83, [4, 4278190080]]], [0, "d5S+nwhmRJh6H7ZLMH6Pi0", 1, 0], [5, 58.21, 40.76], [0, 0, 0.5], [0, 2.397, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "armor", 1, 2, [[9, 45, -3.5, -3.5, -7.5, -7.5, 106, 29, -84], [10, 3, 0, 1, -85, [25], 26]], [1, "66M4NEtSdDG5s7glYP5S9c", 2], [4, 4294964483], [5, 707, 29], [0, 0, 0.5], [-353.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "bg", 1, 2, [[6, 1, 0, -86, [27], 28], [47, 45, -4, -4, 100, 22, -87]], [1, "23rI0XTchItZrciU7yHyDo", 2], [5, 700, 22]], [16, "bgfill", 1, 2, [[9, 45, 5, 5, -0.5, -0.5, 90, 15, -88], [10, 3, 0, 1, -89, [29], 30]], [1, "54/5CMuOVBtZcyESEmAEz4", 2], [5, 690, 15]], [35, "fillSprite", 1, 2, [[9, 45, 5, 5, -0.5, -0.5, 90, 15, -90], [10, 3, 0, 1, -91, [31], 32]], [1, "77SpPIzBZO8q4Fno1peSMZ", 2], [4, 4278255401], [5, 690, 15]], [19, "armor", 1, 6, [[-92, [4, 2, -93, [4, 4278190080]]], 1, 4], [1, "a1VmXAIhdHKr8vn+vod+Dj", 2], [5, 43.96, 54.4], [69.98, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "img_ax", 1, 6, [[2, -94, [36], 37], [48, 8, 115.96000000000001, -95]], [1, "965HJFAg1CgZxJpEyIavUG", 2], [5, 32, 27], [131.96, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "hp", 1, 6, [[-96, [4, 2, -97, [4, 4278190080]]], 1, 4], [1, "66sTwZBFhKIbjqarz5xQVb", 2], [5, 43.96, 54.4], [171.94, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "New Label", 1, [[59, "按住屏幕可左右移动", 24, 24, false, false, 1, 1, -98, [48], 49], [4, 2, -99, [4, 4278190080]]], [0, "e2dNKkEVtEBr2V2XtfNQhr", 1, 0], [5, 220, 34.239999999999995], [0, -57.433, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "maskbg", 1, [[49, 45, -100]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [5, "New RichText", false, 3, [[61, false, "<b><outline color=black width=2>通关主玩法第2关,游戏主界面\n点击玩法合集进入<color=#00ff00>末日屠龙</c>玩法</outline></b>", 1, 23, 30, -101]], [0, "adj8RgtcJITIZVgv9OisPx", 1, 0], [5, 334, 67.8], [0, -73.237, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "tpdg_icon_zanting", 15, [[2, -102, [0], 1]], [0, "41NQUbmGdJHJr6qSEvZucS", 1, 0], [5, 62, 62], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "test", 3, [[23, -103, [[7, "cc262IT+8VN4JA3vb8qu5Uj", "onClickTTTTTTTTTs", 1]]]], [0, "0435LBv8tMsooX44hChu1f", 1, 0], [5, 50, 50], [-227.518, -94.016, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "New Sprite(Splash)", false, 3, [[53, 0, -104, [4], 5]], [0, "36kVUgFmdGt6y/6slcTPvl", 1, 0], [5, 100, 100], [-73.065, -92.346, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [18, "bar", 7, [-105], [1, "38d28QPqAdC1L4aQLAAwhV2", 7], [5, 386, 28], [0, 0, 0.5], [-193, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, 1, 0, 35, [8]], [3, "img_longtou", 17, [[2, -106, [11], 12]], [0, "b5w6L6mRZFgr8nKxoSN6sW", 1, 0], [5, 114, 114], [-237.46, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "diff", 3, [[62, false, "<b><outline color=black width=3>当前难度:<color=#00ff00>普通</c>", 1, 24, 32, -107, 14]], [0, "4dKLrwKglItL6XCQ1AP8h+", 1, 0], [5, 162.67000000000002, 40.32], [0, 0.5, 1], [0, -112.432, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "bar", 8, [-108], [1, "38d28QPqAdC1L4aQLAAwhV2", 8], [5, 220, 28], [0, 0, 0.5], [-110.477, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, 1, 0, 39, [17]], [3, "head", 4, [[63, "bx05", "idle", 0, "idle", -109, [20]]], [0, "b98D4JSTNF3oRMZ6YP/g7Y", 1, 0], [5, 872, 1334], [132.955, -22.436, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [3, "icon_ks", 4, [[2, -110, [21], 22]], [0, "29XmXdZANFtaz92yUdjqna", 1, 0], [5, 60, 51], [-94.132, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon_shield", 1, 6, [[2, -111, [33], 34]], [1, "50JqQMI7JB7oua6t0mEUDF", 2], [5, 46, 47], [23, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "100", 24, false, 1, 1, 1, 26, [35]], [25, "100", 24, false, 1, 1, 1, 28, [38]], [64, 2, 45, 44], [5, "seven01", false, 12, [[2, -112, [39], 40]], [0, "e4sZfeLNBLs4RE0LwJNby3", 1, 0], [5, 136, 136], [0, 20.4, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [17, "jt", 156, 1, 9, [[6, 1, 0, -113, [42], 43]], [0, "5a/+jQljVLyb18/833IIob", 1, 0], [5, 94, 200], [0, 0.5, 1], [0, 52.737, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [17, "jt", 156, 1, 9, [[6, 1, 0, -114, [44], 45]], [0, "d5op6RLpJEBIU7xCWmAag/", 1, 0], [5, 94, 200], [0, 0.5, 1], [0, 52.737, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [13, "tips", 1, 9, [29], [0, "f31qEdN3tI5qq/y0qXfHW3", 1, 0], [0, 39.94, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon", 10, [[20, -115, [50]]], [0, "d7BbtmId1KNqh0UoNpkgdv", 1, 0], [5, 104, 104], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [40, "bar", 167, 10, [-116], [0, "69k+CVzGhIXbTGKu995gUw", 1, 0], [4, 4278190080], [5, 110, 110], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [54, 3, 2, 0.25, 1, 52, [51], [0, 0.5, 0.5]]], 0, [0, 4, 1, 0, 7, 53, 0, 8, 46, 0, 9, 4, 0, 10, 14, 0, 11, 12, 0, -1, 13, 0, -2, 19, 0, 0, 1, 0, -1, 30, 0, -2, 11, 0, 4, 2, 0, -1, 46, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, -4, 25, 0, -5, 6, 0, 0, 3, 0, -1, 31, 0, -2, 15, 0, -3, 33, 0, -4, 34, 0, -5, 16, 0, -6, 17, 0, -7, 18, 0, -8, 38, 0, -9, 19, 0, -10, 4, 0, 0, 4, 0, -1, 8, 0, -2, 41, 0, -3, 42, 0, -4, 20, 0, -5, 21, 0, 0, 5, 0, -2, 12, 0, -3, 13, 0, -4, 9, 0, -5, 10, 0, 0, 6, 0, -1, 43, 0, -2, 26, 0, 3, 6, 0, -4, 27, 0, -5, 28, 0, 4, 7, 0, 0, 7, 0, 6, 36, 0, 0, 7, 0, -1, 35, 0, 4, 8, 0, 0, 8, 0, 6, 40, 0, 0, 8, 0, -1, 39, 0, -1, 48, 0, -2, 49, 0, -3, 14, 0, -4, 50, 0, 0, 10, 0, 0, 10, 0, -1, 51, 0, -2, 52, 0, 0, 11, 0, 0, 12, 0, -1, 47, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 32, 0, 0, 16, 0, 0, 16, 0, -2, 37, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, -1, 44, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 45, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, -1, 36, 0, 0, 37, 0, 0, 38, 0, -1, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 51, 0, -1, 53, 0, 12, 1, 2, 3, 5, 3, 3, 11, 5, 3, 11, 7, 3, 17, 29, 3, 50, 116], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 40, 44, 45, 53], [-1, 1, 13, 14, -1, 1, -1, 5, -1, -1, 1, -1, 1, -1, 15, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 5, -1, -1, -1, 1, 1, 1, 5, 5, 1], [0, 17, 18, 19, 0, 20, 0, 2, 0, 0, 3, 0, 21, 0, 1, 0, 22, 0, 0, 3, 23, 0, 24, 0, 0, 0, 25, 0, 26, 0, 4, 0, 4, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 5, 0, 5, 0, 30, 0, 2, 0, 0, 0, 6, 7, 7, 1, 1, 6]]]]