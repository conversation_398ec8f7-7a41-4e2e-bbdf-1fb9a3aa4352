[1, ["ecpdLyjvZBwrvm+cedCcQy", "94EziLmzlAirmOfZrgDe1Z", "a2MjXRFdtLlYQ5ouAFv/+R", "58L+DSdUdF4pTKifGiWAVB", "68Hhqr/GJKCLEdKqLD+v3H", "f4zEKbzkxD9pCfiqDqEJQu", "c46cSnrYpELobhdnhwO2KF", "29R34cGbFB7YQJIiPE06Fl", "7a/QZLET9IDreTiBfRn2PD"], ["node", "_spriteFrame", "_parent", "root", "asset", "boxSkeleton", "btnList", "skillRoot", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 5, 1, 9, 7, 2, 5, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "_N$cacheMode", "_lineHeight", "_spacingX", "_styleFlags", "node", "_materials"], -8, 1, 3], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_N$paddingLeft", "_N$paddingTop", "node", "_layoutSize"], -3, 1, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc0c96ltstGO44uOXto7ORc", ["node"], 3, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -3, 1], ["64e2fai2llCn5yOIK50fwrS", ["viewScene", "desc", "node", "clickEvents"], 1, 1, 9], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["acb546ropNJz63xOwSaYP94", ["node", "nodeArr", "labelArr", "skillRoot", "richTextArr", "btnList", "boxSkeleton"], 3, 1, 2, 2, 1, 12, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 5, 6, 3, 4, 7, 2], [0, 0, 5, 8, 6, 3, 4, 7, 2], [2, 2, 3, 4, 1], [9, 0, 1, 2, 2], [0, 0, 8, 6, 3, 4, 2], [0, 0, 8, 6, 3, 4, 7, 2], [4, 0, 1, 2, 3, 4, 5, 2], [2, 1, 0, 2, 3, 4, 3], [3, 0, 1, 2, 6, 7, 4], [11, 0, 1, 2, 3], [6, 0, 2], [0, 0, 1, 5, 6, 3, 9, 4, 3], [0, 0, 5, 8, 3, 4, 2], [0, 0, 5, 8, 3, 7, 2], [0, 0, 5, 6, 3, 4, 10, 7, 2], [0, 0, 2, 5, 6, 3, 9, 4, 7, 3], [0, 0, 5, 3, 2], [4, 0, 1, 2, 3, 4, 2], [2, 0, 2, 3, 4, 2], [2, 2, 3, 1], [7, 0, 1, 2, 3, 4], [5, 1, 2, 1], [8, 0, 1, 2, 3, 3], [1, 0, 1, 8, 2, 9, 3, 4, 6, 7, 11, 12, 10], [1, 0, 1, 2, 3, 4, 11, 12, 6], [1, 0, 1, 5, 2, 10, 3, 4, 11, 12, 8], [1, 0, 1, 5, 2, 3, 4, 11, 12, 7], [1, 0, 1, 5, 2, 3, 4, 6, 7, 11, 12, 9], [3, 0, 1, 4, 2, 3, 6, 7, 6], [3, 0, 1, 5, 3, 6, 7, 5], [10, 0, 1, 2, 3, 3], [12, 0, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7], [14, 0, 1, 2, 3, 3], [15, 0, 1, 2, 3, 4, 5, 6, 6], [16, 0, 1, 2, 3, 4, 5, 6, 1]], [[11, "M33_FightBuff2C1View"], [5, "M33_FightBuff2C1View", [-10, -11], [[36, -9, [-7, -8], [-6], -5, [[-4, null], 1, 0], -3, -2]], [22, -1, 0], [5, 750, 1334]], [5, "box", [-13, -14, -15, -16, -17], [[30, 1, 2, -40, 40, -12, [5, 750, 622]]], [0, "cbh74iG2JNWYggDA+9Cz25", 1, 0], [5, 750, 622]], [6, "btn", [-20, -21, -22], [[34, "BagAdGetAllBuff", "是否观看广告获得所有属性", -18, [[10, "acb546ropNJz63xOwSaYP94", "adGetAllSkill", 1]]], [8, 1, 0, -19, [19], 20]], [0, "82OV+8XgRKJrkYTwbOuIlx", 1, 0], [5, 246, 108], [143, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btn", [-25, -26], [[8, 1, 0, -23, [14], 15], [31, 0.9, 3, -24, [[10, "acb546ropNJz63xOwSaYP94", "onClickGiveUp", 1]]]], [0, "707BuQVzNA+rSUxvdnPVVb", 1, 0], [5, 246, 108], [-143, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "title", 2, [-28, -29, -30], [[9, 1, 1, 27.999999999999996, -27, [5, 484, 120]]], [0, "5dMzaKAelOAJOpgYdVL4S+", 1, 0], [5, 484, 120], [0, 251, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Layout", 2, [4, 3], [[9, 1, 1, 40, -31, [5, 532, 260]]], [0, "ddwmWeCCZECaLHLX1UI7xn", 1, 0], [5, 532, 260], [0, -181, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "skillroot", 2, [-33], [[29, 1, 2, 5, 39, 34, -32, [5, 750, 162]]], [0, "46KwqWUtNMdontI7Tm6diE", 1, 0], [5, 750, 162], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 4, [-36], [[26, "下个出现更高品质宝箱\n灵石不保留", 24, false, false, 1, 1, 1, -34, [12]], [4, 3, -35, [4, 4278190080]]], [0, "21IEEODlBEIaNxOhL9XeE0", 1, 0], [5, 246, 96.4], [0, -100.82, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "maskbg", 161, 1, [[19, 0, -37, [0], 1], [21, 45, 750, 1334, -38]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4278190080], [5, 750, 1334]], [13, "bg", 1, [2, -39], [0, "81vg/003JNFJr3x8kM830u", 1, 0], [5, 750, 1334]], [1, "Label", 4, [[27, "放弃", 36, false, false, 1, 1, -40, [13]], [4, 3, -41, [4, 4278190080]]], [0, "bbDjT5aQJIAZ67HlM+sT3w", 1, 0], [5, 78, 56.4], [5.813, 3.428, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 3, [[28, "全都要", 35, false, false, 1, 1, 2, 1, -42, [16]], [4, 3, -43, [4, 4278190080]]], [0, "668sJpPHxOf6EOqNOk8S32", 1, 0], [5, 166, 50.4], [31.847, 2.371, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoicon", 3, [[3, -44, [17], 18], [32, -45]], [0, "1cSWDXKk1BfKRW4AzlM3Bh", 1, 0], [5, 53, 41], [-76.776, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "titleBg", 2, [-46], [0, "49GkTZRoBJaanU0fQqnAVE", 1, 0], [0, 351, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "img_btembx", 14, [[20, -47, [2]]], [0, "97eeEopGNPn4I+eHaf/UbK", 1, 0], [5, 531, 334], [0, 0.5, 0], [0, -156.801, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 5, [[3, -48, [3], 4]], [0, "17VcVYMEtBKoqKo8P+8gFZ", 1, 0], [5, 87, 30], [-198.5, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [7, "title", 5, [-49], [0, "8cXYiN4o5B7qlQz0sxbw/+", 1, 0], [5, 254, 54.18], [-3.552713678800501e-15, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "选择强化属性", 35, 43, false, 1, 1, 1, 2, 1, 17, [5]], [1, "line", 5, [[3, -50, [6], 7]], [0, "1d302R389LPYYOQewVA6+j", 1, 0], [5, 87, 30], [198.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "getinfo", false, 2, [[25, "选择1个宝物", 32, false, 1, 1, -51, [8]]], [0, "2c0uXyodtBBIqa2In1n3N6", 1, 0], [4, 4282298874], [5, 174.5, 50.4], [0, 340.00000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "BuffCardBar", 7, [23, "c14p8WDdJJKZ8b8ieDXPea", true, -52, 9]], [1, "icon_ks", 8, [[3, -53, [10], 11]], [0, "f8PZ+ZgxdBCoLXlkgvWr35", 1, 0], [5, 60, 51], [-87.006, -21.5, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [7, "richtext_remain", 3, [-54], [0, "83CWKPXRNG75hhs/jjfofG", 1, 0], [5, 240, 37.8], [0, 79.285, 0, 0, 0, 0, 1, 1, 1, 1]], [33, false, "剩余次数<color=#00ff00>%d/%d</c>", 1, 25, 240, 30, 23], [18, "kbx", 10, [-55], [0, "38ZkTjkx5MIoGC4VYrEWv7", 1, 0], [5, 872, 1334]], [35, "bx01", "idle", 0, false, "idle", 25, [21]]], 0, [0, 3, 1, 0, 5, 26, 0, 6, 6, 0, -1, 24, 0, 7, 7, 0, -1, 18, 0, -1, 3, 0, -2, 4, 0, 0, 1, 0, -1, 9, 0, -2, 10, 0, 0, 2, 0, -1, 14, 0, -2, 5, 0, -3, 20, 0, -4, 7, 0, -5, 6, 0, 0, 3, 0, 0, 3, 0, -1, 12, 0, -2, 13, 0, -3, 23, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, -2, 11, 0, 0, 5, 0, -1, 16, 0, -2, 17, 0, -3, 19, 0, 0, 6, 0, 0, 7, 0, -1, 21, 0, 0, 8, 0, 0, 8, 0, -1, 22, 0, 0, 9, 0, 0, 9, 0, -2, 25, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 15, 0, 0, 15, 0, 0, 16, 0, -1, 18, 0, 0, 19, 0, 0, 20, 0, 3, 21, 0, 0, 22, 0, -1, 24, 0, -1, 26, 0, 8, 1, 2, 2, 10, 3, 2, 6, 4, 2, 6, 55], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, 1, -1, 4, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1], [0, 2, 0, 0, 1, 0, 0, 1, 0, 3, 0, 4, 0, 0, 0, 5, 0, 0, 6, 0, 7, 8]]