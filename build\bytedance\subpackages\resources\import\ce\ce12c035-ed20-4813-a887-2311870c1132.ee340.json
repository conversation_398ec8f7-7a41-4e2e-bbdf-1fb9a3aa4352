[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "a0rvROFd5IVr9UoEF7ZIum", "a2MjXRFdtLlYQ5ouAFv/+R", "c1f4UDWZJNUJmmvN1IN/rK", "29FYIk+N1GYaeWH/q1NxQO", "c3+ruR7+FEnKfu8yo+WDeT", "b15mUqogdM/7Dcb/ZHR1PL", "4613FnL15GjKE6Ci5QeOnx", "c462hEeYFIrpRk/VTxYhaB", "65hdeSelhOVq5sz7eNtoFd", "4dMZmkcjlJEbhEI0rAtegJ", "7a/QZLET9IDreTiBfRn2PD", "73jGpne/9JUI/171Zur5Pr", "90oitQB7lMkYESDcoOuwj6", "82EupQxdxDPKtDaOG11DxC"], ["node", "_spriteFrame", "_parent", "_N$file", "_N$font", "_N$disabledSprite", "equipitem", "_N$skeletonData", "root", "lockTips", "levelup", "btnUpgradeLabel", "cost", "coin", "btnUpgrade", "equipSpec2Node", "equipSpec2", "equipSpec1Node", "equipSpec1", "equipNode", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_right", "_top", "_left", "_bottom", "node"], -5, 1], ["cc.Label", ["_string", "_N$verticalAlign", "_fontSize", "_isSystemFontUsed", "_enableWrapText", "_N$horizontalAlign", "_N$cacheMode", "_lineHeight", "_styleFlags", "_N$overflow", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "_N$paddingTop", "_N$paddingBottom", "node", "_layoutSize", "_N$cellSize"], -3, 1, 5, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_active", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 0, 1, 2, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$horizontalAlign", "node", "_N$font"], -3, 1, 6], ["cc.Prefab", ["_name"], 2], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -1, 1, 3], ["cc.BlockInputEvents", ["node"], 3, 1], ["676c7mT+BhM5JxSmXY2oShw", ["node", "equipNode", "equipSpec1", "equipSpec1Node", "equipSpec2", "equipSpec2Node", "btnUpgrade", "coin", "cost", "btnUpgradeLabel", "levelup", "lockTips", "equipitem"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6]], [[7, 0, 1, 2, 2], [3, 1, 0, 3, 4, 5, 3], [8, 0, 1, 2, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [0, 0, 7, 6, 4, 5, 8, 2], [3, 3, 4, 5, 1], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 9, 6, 4, 5, 11, 8, 2], [5, 0, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 8, 4], [2, 0, 2, 7, 3, 8, 1, 10, 11, 12, 7], [12, 0, 1, 2, 3], [4, 0, 1, 6, 7, 3], [11, 0, 2], [0, 0, 1, 9, 6, 4, 5, 3], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 9, 6, 4, 5, 2], [0, 0, 7, 9, 4, 5, 8, 2], [0, 0, 6, 4, 5, 11, 8, 2], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 2, 7, 6, 4, 10, 5, 3], [0, 0, 7, 4, 5, 8, 2], [0, 0, 9, 4, 5, 8, 2], [0, 0, 2, 7, 6, 4, 10, 5, 8, 3], [0, 0, 1, 9, 6, 4, 5, 8, 3], [0, 0, 1, 7, 9, 6, 4, 5, 8, 3], [0, 0, 1, 7, 6, 4, 5, 3], [0, 0, 1, 7, 6, 4, 5, 11, 8, 3], [0, 0, 2, 7, 9, 4, 3], [0, 0, 3, 1, 7, 6, 4, 10, 5, 4], [0, 0, 9, 6, 4, 10, 5, 8, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [6, 0, 3, 4, 5, 6, 7, 2], [6, 0, 1, 2, 3, 4, 5, 6, 7, 4], [1, 0, 8, 2], [1, 3, 0, 6, 4, 1, 2, 8, 7], [1, 0, 4, 5, 8, 4], [1, 0, 6, 4, 8, 4], [1, 3, 0, 1, 8, 4], [1, 0, 5, 7, 1, 2, 8, 6], [1, 3, 0, 5, 7, 1, 2, 8, 7], [7, 1, 2, 1], [3, 0, 3, 4, 5, 2], [3, 0, 2, 3, 4, 3], [3, 3, 4, 1], [2, 0, 2, 4, 5, 1, 9, 6, 10, 11, 8], [2, 0, 2, 7, 3, 5, 1, 10, 11, 7], [2, 0, 2, 3, 1, 10, 11, 12, 5], [2, 0, 4, 3, 5, 1, 6, 10, 11, 7], [2, 0, 2, 4, 3, 1, 6, 10, 11, 7], [2, 0, 2, 3, 1, 10, 11, 5], [8, 0, 1, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 2, 3, 4, 5, 2], [10, 0, 1, 5, 2, 3, 4, 6, 7, 7], [10, 0, 1, 2, 3, 4, 6, 7, 6], [4, 0, 1, 3, 2, 6, 7, 8, 5], [4, 0, 1, 4, 5, 2, 6, 7, 6], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 4, 5, 5], [16, 0, 1], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1]], [[14, "M20_Pop_EquipInfo"], [15, "M20_Pop_EquipInfo", 1, [-14, -15], [[63, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2, 40]], [42, -1, 0], [5, 750, 1334]], [17, "content", [-18, -19, -20, -21, -22, -23, -24, -25], [[1, 1, 0, -16, [38], 39], [62, -17]], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [25, "spec2", 1, [-27, -28, -29, -30], [[1, 1, 0, -26, [26], 27]], [0, "143biUispLtYa8MEZrJF35", 1, 0], [5, 620, 150], [0, -90, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "spec1", [-31, -32, -33, -34], [0, "e3sg1rJo9MNqM4TsAaejyJ", 1, 0], [5, 200, 60], [-150, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "frame_lvatr", [3], [[58, 1, 2, 15, 15, 10, -35, [5, 640, 180]], [39, 0, 41, 640, -36]], [0, "1fH4bcTfVGYqS0Vai6xGP1", 1, 0], [5, 640, 180], [0, 0.5, 1], [0, 305, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Background", [-39], [[5, -37, [5], 6], [36, 0, 45, -1.5, -1.5, 100, 40, -38]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 64, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [31, "Background", [-42], [[1, 1, 0, -40, [34], 35], [41, 0, 45, -0.5, -0.5, 100, 40, -41]], [0, "82Fd+gwn1Dr5y89wfiKTX+", 1, 0], [4, 4293322470], [5, 247, 112], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "bg", 1, [-44, 2], [[10, 45, 750, 1334, -43]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [3, "btnclose", 2, [6], [[53, 3, -45, [[12, "676c7mT+BhM5JxSmXY2oShw", "close", 1]], [4, 4293322470], [4, 3363338360], 6, 7], [37, 33, 19.5, 11.625999999999976, -46]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 61, 65], [289.5, 518.874, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "spe1node", 2, [4], [[57, 2, 3, 25, 25, -47, [5, 500, 0], [5, 200, 60]]], [0, "2a2CQUHZNILJtqlOM+yX6Q", 1, 0], [5, 500, 0], [133.758, 452.256, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lvtips", 2, [-50], [[60, false, 0.75, 0.23, null, null, -48, 5], [1, 1, 0, -49, [29], 30]], [0, "f6EM13jStGQJ3TdE6ypGix", 1, 0], [5, 640, 630], [0, -95.875, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "view", 11, [5], [[59, 0, -51, [28]], [40, 45, 10, 10, 640, 670, -52]], [0, "70ax21j9NHULCQpatr26K4", 1, 0], [5, 640, 610]], [8, "lockMsg", [-55], [[48, "2级解锁", 24, false, 1, -53, [24], 25], [2, 3, -54, [4, 4278190080]]], [0, "0bsFfwIxFH+Y0l2SO1d653", 1, 0], [5, 88.51, 56.4], [0, 1, 0.5], [299.665, -47.519, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "upgrade_btn", 2, [7], [[54, 3, -56, [[12, "676c7mT+BhM5JxSmXY2oShw", "upgrade", 1]], [4, 4293322470], [4, 3363338360], 7]], [0, "47zipArrpLUb6HR4SJBDDp", 1, 0], [5, 247, 111], [0, -488.001, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "New Node", 7, [-58, -59], [[13, 1, 2, -57, [5, 300, 116.4]]], [0, "bebdQVpxhFYL78K7TjcUDt", 1, 0], [5, 300, 116.4]], [3, "upgrade_cost", 15, [-61, -62], [[13, 1, 1, -60, [5, 158.8, 62]]], [0, "3570D6ZBBCB5SSn3piTO73", 1, 0], [5, 158.8, 62], [0, -27.199999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "mask", 140, 8, [[43, 0, -63, [0], 1], [10, 45, 100, 100, -64]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [19, "Label_title", [[11, "法宝", 48, 48, false, 1, 1, -65, [2], 3], [2, 3, -66, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 102, 66.47999999999999], [0, 0, 0.5], [-306.788, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "atrname", false, 4, [[47, "目标", 22, 30, false, 1, 1, -67, [13]], [52, 3, -68]], [0, "6be2HOwO1MYbVAx5GGnjKv", 1, 0], [4, 4282071867], [5, 50, 43.8], [-84.85, -22.943, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "bg_icon_01", 1, 3, [-70], [[5, -69, [15], 16]], [0, "0cpTF53pZIbI4WYoJuL1UR", 1, 0], [5, 100, 100], [-227.029, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "name", 3, [[11, "装备", 24, 30, false, 1, 1, -71, [18], 19], [2, 2, -72, [4, 4278190080]]], [0, "64tZKSv79DhqsBktRFuDTM", 1, 0], [5, 52, 41.8], [-227.976, -47.519, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "mask", false, 3, [-73, 13], [0, "29UIEqiOtDjYhuIFQg596u", 1, 0]], [4, "icon_lock_small", 13, [[5, -74, [22], 23], [38, 8, -64.35399999999998, 61.715, -75]], [0, "2faKwcHGxD5Z/nu4w3yrjZ", 1, 0], [5, 57, 43], [-124.36399999999998, -6.21, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Label", 15, [[-76, [2, 2, -77, [4, 4278190080]]], 1, 4], [0, "b75EEL1X9K4JbSHBXhxhUu", 1, 0], [5, 84, 54.4], [0, 31.000000000000004, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "val", 16, [[-78, [2, 3, -79, [4, 4278190080]]], 1, 4], [0, "01Qw154tRGlZMZd3aviIh5", 1, 0], [5, 94.8, 56.4], [31.999999999999993, 3.307, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "lockTips", false, 2, [[-80, [2, 2, -81, [4, 4278190080]]], 1, 4], [0, "27fZZDzvNAFqoYQanwHgX0", 1, 0], [5, 220, 54.4], [0, -490.805, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "maskbg", 1, [[35, 45, -82]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [18, "title_zhua<PERSON><PERSON>", 2, [18], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 679, 86], [0, 522.496, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "Label", false, 6, [[46, "button", 20, false, 1, 1, 1, 1, -83, [4]]], [0, "74MLxONUxHVpe9Vn4MQuhH", 1, 0], [4, 4278190080], [5, 100, 40]], [22, "equip_node", 2, [0, "b07ALh+/hGPJpxngIUdYQu", 1, 0], [5, 180, 260], [-226.257, 343.806, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg", 4, [[1, 1, 0, -84, [8], 9]], [0, "6125VrYMJH06FhYTghFkFe", 1, 0], [5, 200, 50]], [4, "val", 4, [[55, false, "<outline color=black width=3>10<color=#00ff00>+4</c></outline>", 1, 24, 150, 30, -85, 10]], [0, "03XoqMUApIv6UhWXAXLiVZ", 1, 0], [5, 150, 37.8], [19.545, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "icon", 4, [[5, -86, [11], 12]], [0, "d3ZsWNr/BPfrihxjPD1t5h", 1, 0], [5, 46, 58], [-84.85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "icon", 1, 20, [[44, 0, false, -87, [14]]], [0, "32k4L1fahFJJz1ZS3M7u+Q", 1, 0], [5, 64, 64]], [28, "RichText", 1, 3, [[56, false, "<outline color=black width=2>子弹体积<color=#FFDD42>+100%</color>伤害<color=#FFDD42>+30%</color></outline>", 26, 430, 30, -88, 17]], [0, "63Ga3lo7tOd6H1nBwScuay", 1, 0], [5, 430, 37.8], [0, 0, 0.5], [-146.349, 3.543, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "mask", 100, 1, 22, [[1, 1, 0, -89, [20], 21]], [0, "daN9FzWZxJWb3Orue9aIY+", 1, 0], [4, 4278190080], [5, 620, 150]], [49, "升级", false, false, 1, 1, 1, 24, [31]], [33, "coin", 16, [-90], [0, "6b9oNB+F1MqJPJ6F1x8+4l", 1, 0], [5, 64, 59], [-47.400000000000006, 1.5, 0, 0, 0, 0, 1, 0.8, 0.8, 0.7]], [45, 38, [32]], [50, "12222", 32, false, false, 1, 1, 25, [33]], [34, "fx_levelup", false, 80, 2, [-91], [0, "e2/yiCGXZBYbDGZQFQo8Tf", 1, 0], [5, 624.7879638671875, 320.2515563964844], [0, 355.268, 0, 0, 0, 0, 1, 1.2, 0.7, 1]], [61, "default", 0, false, "animation", 41, [36]], [51, "新手礼包获得", 36, false, 1, 26, [37]]], 0, [0, 8, 1, 0, 9, 43, 0, 10, 42, 0, 11, 37, 0, 12, 40, 0, 13, 39, 0, 14, 14, 0, 15, 5, 0, 16, 3, 0, 17, 10, 0, 18, 4, 0, 19, 30, 0, 0, 1, 0, -1, 27, 0, -2, 8, 0, 0, 2, 0, 0, 2, 0, -1, 28, 0, -2, 9, 0, -3, 30, 0, -4, 10, 0, -5, 11, 0, -6, 14, 0, -7, 41, 0, -8, 26, 0, 0, 3, 0, -1, 20, 0, -2, 35, 0, -3, 21, 0, -4, 22, 0, -1, 31, 0, -2, 32, 0, -3, 33, 0, -4, 19, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -1, 29, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, 0, 8, 0, -1, 17, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 23, 0, 0, 14, 0, 0, 15, 0, -1, 24, 0, -2, 16, 0, 0, 16, 0, -1, 38, 0, -2, 25, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -1, 34, 0, 0, 21, 0, 0, 21, 0, -1, 36, 0, 0, 23, 0, 0, 23, 0, -1, 37, 0, 0, 24, 0, -1, 40, 0, 0, 25, 0, -1, 43, 0, 0, 26, 0, 0, 27, 0, 0, 29, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, -1, 39, 0, -1, 42, 0, 20, 1, 2, 2, 8, 3, 2, 5, 4, 2, 10, 5, 2, 12, 6, 2, 9, 7, 2, 14, 13, 2, 22, 18, 2, 28, 91], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 40, 42, 43], [-1, 1, -1, 3, -1, -1, 1, 5, -1, 1, 4, -1, 1, -1, -1, -1, 1, 4, -1, 3, -1, 1, -1, 1, -1, 3, -1, 1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, 1, 6, 3, 3, 7, 3], [0, 4, 0, 1, 0, 0, 5, 6, 0, 7, 2, 0, 8, 0, 0, 0, 9, 2, 0, 2, 0, 3, 0, 10, 0, 1, 0, 3, 0, 0, 11, 0, 0, 0, 0, 12, 13, 0, 0, 14, 15, 1, 2, 16, 1]]