[1, ["ecpdLyjvZBwrvm+cedCcQy", "570VwVpW9CEaNXuavc/qC7", "1fFOBaKA1Ii4S/B7748zch", "d8Uh+j4B9Pq7gdV7E6lgEL", "40n/qScIlL4Kf71vl+Qxf1", "c3A8lqHgZArb2he1eLfNue", "f9n147MxFEQpbs1JXF1rxs", "cb3kWOk8ZPi4eb1pLQKnpl", "39DG3wTrJJCYnXNDS7SKK1", "87EBXzyxJCdIZKC4SFl7Wj", "4aEYWMCPFCHpdfnkOaQ+MN", "4caa2aGdZO5J25xVtwE0AD", "26ges3zJlE6JIiyVf3Cdt6", "a7d0c1XsVI65tBRRYSHBFe", "8eMF6ypgFNH6elA0IJQc2Z", "d6jrVWc8tBlraa/3P01idI", "ffCXtneVZM4Zd1mMmw1u1v", "f323h3t3RI7rnFsKIIigjV", "29FYIk+N1GYaeWH/q1NxQO", "43oScxeppNHJjMcdrN22KX", "98y+xwDxlJZbDcGV61DKoh", "0cXM/wX95DgLmHp7hiKFyD", "2dV7HZEEVJU7fAcR/56+Wt", "53ALzxaUhC0LmKsQvIcaRm"], ["node", "_spriteFrame", "_textureSetter", "_parent", "_N$barSprite", "root", "checkMark", "_N$target", "data", "_N$normalSprite", "_N$disabledSprite"], [["cc.Node", ["_name", "_groupIndex", "_active", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_children", "_parent", "_trs", "_anchorPoint"], -1, 4, 5, 9, 2, 1, 7, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_originalHeight", "node"], -1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["600b21i4KROpoaLB30yj0DX", ["UItype", "node", "nodeArr", "labelArr"], 2, 1, 2, 2], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents"], 2, 1, 5, 1, 1, 9], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.ProgressBar", ["_N$totalLength", "node", "_N$barSprite"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[6, 0, 1, 2, 2], [0, 0, 8, 6, 4, 5, 9, 2], [2, 0, 2, 3, 4, 5, 3], [0, 0, 8, 7, 6, 4, 5, 9, 2], [2, 3, 4, 5, 1], [0, 0, 8, 6, 4, 5, 2], [2, 1, 0, 3, 4, 5, 3], [16, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [17, 0, 1, 2, 2], [0, 0, 8, 7, 6, 4, 5, 10, 9, 2], [0, 0, 7, 6, 4, 5, 9, 2], [0, 0, 2, 8, 7, 4, 5, 9, 3], [10, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [2, 3, 4, 1], [2, 1, 0, 3, 4, 3], [8, 0, 1, 3, 3], [8, 0, 1, 2, 3, 4], [15, 0, 1, 2, 2], [9, 0, 2], [0, 0, 1, 7, 6, 4, 5, 3], [0, 0, 7, 6, 4, 5, 10, 9, 2], [0, 0, 8, 7, 4, 2], [0, 0, 3, 8, 6, 4, 5, 3], [5, 0, 1, 2, 3, 4, 5, 3], [11, 0, 1, 2, 3, 2], [6, 1, 2, 1], [3, 0, 1, 4, 3], [3, 0, 1, 2, 3, 4, 5], [3, 0, 2, 4, 3], [3, 0, 4, 2], [2, 0, 3, 4, 5, 2], [2, 0, 2, 3, 4, 3], [7, 0, 1, 4, 5, 3], [7, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1, 2], [13, 0, 1, 2, 3, 4, 5, 2], [14, 0, 1], [4, 1, 0, 2, 3, 3], [4, 2, 3, 1], [4, 0, 2, 3, 4, 5, 6, 7, 2]], [[[{"name": "bossprogress_02", "rect": [0, 0, 158, 19], "offset": [0, 0], "originalSize": [158, 19], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[{"name": "icon_fd_dz", "rect": [0, 0, 54, 52], "offset": [0, 0], "originalSize": [54, 52], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[{"name": "img_zdtxxz", "rect": [0, 0, 115, 116], "offset": [0, 0], "originalSize": [115, 116], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [8]], [[{"name": "img_zdtxd", "rect": [0, 0, 134, 134], "offset": [0, 0], "originalSize": [134, 134], "capInsets": [0, 58, 0, 23]}], [1], 0, [0], [2], [9]], [[{"name": "img_zdtxdk", "rect": [7, 7, 102, 102], "offset": [0.5, 0], "originalSize": [115, 116], "capInsets": [24.5, 25, 27.5, 34]}], [1], 0, [0], [2], [10]], [[[19, "M30_FightUIView"], [20, "M30_FightUIView", 1, [-16, -17], [[25, 0, -15, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14], [-2, -3]]], [26, -1, 0], [5, 750, 1334]], [21, "topUI", [-19, -20, -21, -22], [[27, 41, -47.60900000000004, -18]], [0, "8bkbxxodpNcpnBOLn5m5Hf", 1, 0], [5, 750, 100], [0, 0.5, 1], [0, 654.609, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_zdtxd", 2, [-26, -27], [[6, 1, 0, -23, [5], 6], [33, 1, 2, -24, [5, 134, 177.36]], [35, 1, -25]], [0, "19q5oknHdIkbtmHaVSnJkL", 1, 0], [5, 134, 177.36], [0, 0.5, 1], [-259.342, -121.66, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "toggle1", [-31, -32, -33], [[36, 3, -30, [4, 4292269782], -29, -28, [[16, "600b21i4KROpoaLB30yj0DX", "onClickToggle", 1]]]], [0, "efjioj7kxImbbM4D7At9rN", 1, 0], [5, 120, 120], [0, 3, 0, 0, 0, 0, 1, 1, 1, 0]], [22, "progress", 2, [-34, -35, -36, -37, -38], [0, "65bj1KS0BDP5/r8t04vGwI", 1, 0]], [11, "other_dis", false, 2, [-39, -40, -41, -42], [0, "1aJ+hbCNhL8KAk+4GZa4Yu", 1, 0], [5, 150, 155], [283.996, -408.742, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "my_dis", false, 2, [-43, -44, -45, -46], [0, "ad3QL/dbxFxrhOJTUCLHAY", 1, 0], [5, 150, 155], [-252.145, -408.742, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg", 1, [2, -48, -49], [[28, 45, 120, 750, 1334, -47]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1214], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New ToggleContainer", 3, [4], [[37, -50], [34, 1, 2, 6, -5, -51, [5, 221, 126]]], [0, "55otNtY8FKDLfRdJWyuJwr", 1, 0], [5, 221, 126], [0, -114.36, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "my_progressbar", 5, [-55], [[6, 1, 0, -52, [10], 11], [18, 170, -54, -53]], [0, "deVs6Y8pxGxo3hK9GNpIKn", 1, 0], [5, 176, 25], [-142.427, -58.923, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "other_progressbar", 5, [-59], [[6, 1, 0, -56, [15], 16], [18, 170, -58, -57]], [0, "82AuelH/dNRrOJ1egt1x4L", 1, 0], [5, 176, 25], [138.036, -58.923, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btn", [-62], [[4, -60, [34], 35], [38, 0.9, 3, -61, [[17, "600b21i4KROpoaLB30yj0DX", "onBtn", "bulletIndex", 1]]]], [0, "ffs5PBNqtM0axSzxPTvIV3", 1, 0], [5, 102, 102], [315.424, 292.188, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "bottonUI", 8, [12, -64], [[29, 44, 600, -63]], [0, "f3xMePUptLpI+sbMIURS5n", 1, 0], [5, 750, 140], [0, 0.5, 0], [0, -607, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_yj", 13, [[31, 0, -65, [36], 37], [39, -66, [[17, "600b21i4KROpoaLB30yj0DX", "onBtn", "Lock", 1]]]], [0, "c3uxKevTJPsrMPv09+9zWu", 1, 0], [5, 102, 102], [313.71, 89.615, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 3, [[7, "切换角色", 26, 36, false, false, 1, 1, 1, -67, [0]], [8, 3, -68, [4, **********]]], [0, "0c0d2UmFNPWrzc2COesZww", 1, 0], [5, 110, 51.36], [0, -25.68, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "my_icon", 5, [[2, 0, false, -69, [12], 13]], [0, "5aGVFOGVNCvrAhFXiBQ3w2", 1, 0], [5, 100, 100], [-275.681, -58.919, 0, 0, 0, 0, 1, 1, 1, 0.845]], [1, "other_icon", 5, [[2, 0, false, -70, [17], 18]], [0, "30cYpkbtxCR7JAJUda+XPZ", 1, 0], [5, 100, 100], [267.659, -58.919, 0, 0, 0, 0, 1, 1, 1, 0.845]], [1, "other_dis_icon", 6, [[2, 0, false, -71, [21], 22]], [0, "fa617HP15M4KTop4fFg0r2", 1, 0], [5, 100, 100], [-14.213, 17.175, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Label", 6, [[-72, [8, 3, -73, [4, **********]]], 1, 4], [0, "d2PcF8e3xCZaxEJ4BKM01u", 1, 0], [5, 62.05, 51.36], [-13.694, -50.483, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "my_dis_icon", 7, [[2, 0, false, -74, [28], 29]], [0, "7fDwa9mr5Neq/LoY20hK9w", 1, 0], [5, 100, 100], [-14.213, 17.175, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Label", 7, [[-75, [8, 3, -76, [4, **********]]], 1, 4], [0, "abzWxFUOxBeIbDrQdVw7ZA", 1, 0], [5, 62.05, 51.36], [-13.694, -50.483, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnpause", 8, [-78], [[40, 3, -77, [[16, "600b21i4KROpoaLB30yj0DX", "pauseGame", 1]], [4, 4293322470], [4, 3363338360], 40, 41]], [0, "27ujCoCO5HjaZztOvUkHN9", 1, 0], [5, 88, 93], [-309.029, -523.905, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "maskbg", 1, [[30, 45, -79]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [23, "Background", 512, 4, [[2, 2, false, -80, [1], 2]], [0, "324YiRcZlGsrFmIxh7RQxB", 1, 0], [5, 115, 116]], [24, "checkmark", 512, 4, [-81], [0, "e2/5shAG1MSI+gWowXG2WL", 1, 0], [5, 115, 116]], [32, 2, false, 25, [3]], [5, "icon", 4, [[14, -82, [4]]], [0, "6dTDwRnWBMw5tjjiGkZh0S", 1, 0], [5, 74, 84]], [1, "icon_fd_dz", 5, [[4, -83, [7], 8]], [0, "9dOP2b8uBMh555QmrdUAtD", 1, 0], [5, 54, 52], [0, -56.255, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "bar", 512, 10, [-84], [0, "68h0JeMO9OoZJapEg5pugy", 1, 0], [5, 170, 19], [0, 0, 0.5], [-85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 1, 0, 29, [9]], [13, "bar", 512, 11, [-85], [0, "f6Md0UIABHFKLolrbebO0n", 1, 0], [5, 170, 19], [0, 1, 0.5], [85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 1, 0, 31, [14]], [1, "img_fd_txjld", 6, [[4, -86, [19], 20]], [0, "c0aVGT1oBGgrsHI7Hc3ySi", 1, 0], [5, 119, 149], [-14.44, -1.575, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "41米", 28, 36, false, false, 1, 1, 1, 19, [23]], [1, "btn_arrow", 6, [[2, 0, false, -87, [24], 25]], [0, "3czjVg1cZAj4tuG/LCfuo8", 1, 0], [5, 29, 45], [60.586, 16.102, 0, 0, 0, 0, 1, -1, 1, -1]], [1, "img_fd_txjld", 7, [[4, -88, [26], 27]], [0, "bepCQxGI5BBbkqm41SYDGT", 1, 0], [5, 119, 149], [-14.44, -1.575, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "41米", 28, 36, false, false, 1, 1, 1, 21, [30]], [1, "btn_arrow", 7, [[2, 0, false, -89, [31], 32]], [0, "26TFkQhBJLD6rpWis36O5s", 1, 0], [5, 29, 45], [-89.768, 16.102, 0, 0, 0, 0, 1, 1, 1, -1]], [5, "icon", 12, [[14, -90, [33]]], [0, "21MWMwWzJHDIFKlnaoDdCY", 1, 0], [5, 168, 62]], [1, "tpdg_icon_zanting", 22, [[4, -91, [38], 39]], [0, "feR9NVX/dDq4pPMMPyUP1o", 1, 0], [5, 94, 99], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]]], 0, [0, 5, 1, 0, -1, 34, 0, -2, 37, 0, -1, 9, 0, -2, 14, 0, -3, 12, 0, -4, 16, 0, -5, 10, 0, -6, 17, 0, -7, 11, 0, -8, 18, 0, -9, 6, 0, -10, 20, 0, -11, 7, 0, 0, 1, 0, -1, 23, 0, -2, 8, 0, 0, 2, 0, -1, 3, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 15, 0, -2, 9, 0, 6, 26, 0, 7, 4, 0, 0, 4, 0, -1, 24, 0, -2, 25, 0, -3, 27, 0, -1, 28, 0, -2, 10, 0, -3, 16, 0, -4, 11, 0, -5, 17, 0, -1, 33, 0, -2, 18, 0, -3, 19, 0, -4, 35, 0, -1, 36, 0, -2, 20, 0, -3, 21, 0, -4, 38, 0, 0, 8, 0, -2, 13, 0, -3, 22, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 4, 30, 0, 0, 10, 0, -1, 29, 0, 0, 11, 0, 4, 32, 0, 0, 11, 0, -1, 31, 0, 0, 12, 0, 0, 12, 0, -1, 39, 0, 0, 13, 0, -2, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, -1, 34, 0, 0, 19, 0, 0, 20, 0, -1, 37, 0, 0, 21, 0, 0, 22, 0, -1, 40, 0, 0, 23, 0, 0, 24, 0, -1, 26, 0, 0, 27, 0, 0, 28, 0, -1, 30, 0, -1, 32, 0, 0, 33, 0, 0, 35, 0, 0, 36, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 8, 1, 2, 3, 8, 4, 3, 9, 12, 3, 13, 91], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 30, 32], [-1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, 9, 10, 1, 1, 1], [0, 0, 1, 0, 0, 0, 11, 0, 12, 0, 0, 2, 0, 13, 0, 0, 2, 0, 14, 0, 3, 0, 4, 0, 0, 5, 0, 3, 0, 4, 0, 0, 5, 0, 0, 1, 0, 15, 0, 16, 17, 18, 19, 20, 21]], [[{"name": "icon_Archi", "rect": [0, 0, 104, 105], "offset": [-0.5, 0], "originalSize": [105, 105], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [22]], [[{"name": "icon_yj", "rect": [0, 0, 79, 76], "offset": [0, 0], "originalSize": [79, 76], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [23]]]]