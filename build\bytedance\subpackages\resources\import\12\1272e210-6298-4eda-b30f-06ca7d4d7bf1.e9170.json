[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "efXBJkjZVIELqSE5w63CwO", "baHW41841PF5Yr61qWY9O/"], ["node", "_spriteFrame", "root", "asset", "ske", "data", "_parent"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Sprite", ["_sizeMode", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "node", "_materials"], -3, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["edfe0zbyDBCGLuLV4CZnMHk", ["node", "nodeArr", "ske"], 3, 1, 2, 1]], [[3, 0, 1, 2, 2], [9, 0, 1, 2, 2], [0, 0, 1, 7, 5, 4, 6, 8, 3], [0, 0, 1, 7, 5, 4, 10, 6, 11, 8, 3], [2, 0, 1, 2, 3, 2], [4, 0, 1, 2, 3, 4, 6, 7, 6], [4, 0, 5, 1, 2, 3, 4, 6, 7, 7], [5, 0, 2], [0, 0, 1, 9, 5, 4, 6, 3], [0, 0, 2, 7, 5, 4, 10, 6, 3], [0, 0, 7, 9, 5, 4, 6, 2], [0, 0, 3, 9, 5, 4, 3], [0, 0, 7, 9, 5, 4, 6, 8, 2], [0, 0, 7, 4, 8, 2], [0, 0, 7, 5, 4, 6, 8, 2], [0, 0, 7, 9, 5, 4, 6, 11, 8, 2], [0, 0, 7, 9, 4, 6, 8, 2], [6, 0, 1, 2, 3, 4, 2], [1, 0, 3, 2], [1, 0, 1, 2, 3, 4], [2, 1, 2, 3, 1], [3, 1, 2, 1], [7, 0, 1, 2, 3, 3], [8, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 3, 3], [11, 0, 1, 2], [12, 0, 1, 2, 1]], [[7, "Role_EquipForgeInfo"], [8, "Role_EquipForgeInfo", 1, [-5, -6], [[26, -4, [-3], -2]], [21, -1, 0], [5, 750, 1334]], [11, "box", false, [-8, -9, -10, -11, -12], [[25, 2, -7]], [0, "b0obUKAb5GbIwgjr/Yxd4i", 1, 0]], [10, "bg", 1, [-14, 2], [[19, 45, 750, 1334, -13]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334]], [9, "maskbg", 200, 1, [[18, 45, -15], [4, 0, -16, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [12, "img_js_btqz5", 2, [-18], [[20, -17, [4], 5]], [0, "e6mKUHq6JEoa8iE7Fp61U5", 1, 0], [5, 472, 103], [0, 359.461, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lv", 1, 5, [[5, "合成成功", false, 1, 1, 1, -19, [3]], [1, 3, -20, [4, 4278190080]]], [0, "7buY09PI1HZpvMjV6GdwQI", 1, 0], [5, 166, 56.4], [0, 10.795, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "name", 1, 2, [[5, "流浪法杖", false, 1, 1, 1, -21, [7]], [1, 3, -22, [4, 4278190080]]], [0, "a21m9Oy4hFl53Ez2c/xbpW", 1, 0], [5, 166, 56.4], [0, -43.372, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "attrLayout", 2, [-24], [[24, 1, 2, -23, [5, 200, 50]]], [0, "cf0yac9MxLm7i3+kzZU8Tt", 1, 0], [5, 200, 50], [0, 0.5, 1], [0, -105.523, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "item", 8, [-25, -26], [0, "d3ayAcaAVDwYqRjbKcsVgg", 1, 0], [5, 0, 50], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "name", 1, 9, [[6, "攻击力", 32, false, 1, 1, 1, -27, [10]], [1, 3, -28, [4, 4278190080]]], [0, "5fJBk2KElMJ7oUiiY4GRkz", 1, 0], [4, 4278828287], [5, 102, 56.4], [0, 0, 0.5], [-197.103, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "val", 1, 9, [[6, "60", 32, false, 1, 1, 1, -29, [11]], [1, 3, -30, [4, 4278190080]]], [0, "d8Qs4RqxdEXbMiB3N4fFRV", 1, 0], [4, 4280677438], [5, 41.86, 56.4], [0, 1, 0.5], [193.625, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "chuizi", 3, [-31], [0, "95ALnr+NpKroiA+WMrhH83", 1, 0], [5, 297, 279.45904541015625]], [23, "default", "animation", 0, false, "animation", 12, [2]], [13, "Role_EquipItem", 2, [22, "24NsWMRx9FqLmyCf1QFTJ5", true, -32, 6], [0, 159.227, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "line", 2, [[4, 0, -33, [8], 9]], [0, "d2LkeebQNAUYWY15DZbjVC", 1, 0], [5, 416, 3], [0, -87.214, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 4, 13, 0, -1, 2, 0, 0, 1, 0, -1, 4, 0, -2, 3, 0, 0, 2, 0, -1, 5, 0, -2, 14, 0, -3, 7, 0, -4, 15, 0, -5, 8, 0, 0, 3, 0, -1, 12, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, -1, 10, 0, -2, 11, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 13, 0, 2, 14, 0, 0, 15, 0, 5, 1, 2, 6, 3, 33], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, -1, 1, 3, -1, -1, 1, -1, -1], [0, 1, 2, 0, 0, 3, 4, 0, 0, 1, 0, 0]]