[1, ["7a/QZLET9IDreTiBfRn2PD", "1a6aecbfb", "ecpdLyjvZBwrvm+cedCcQy", "39GiN1F69OjJbVKoNUAri4"], ["node", "_textureSetter", "root", "data", "_spriteFrame"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_anchorPoint", "_parent", "_trs", "_children"], 1, 9, 4, 5, 5, 1, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["d404ctPv39KjqZKIICW+8gN", ["node"], 3, 1], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["36ac1Nmq0RDA4Cdq1A+7+KF", ["clipImgName", "clipImgRef", "frame_time", "wrapMode", "node"], -1, 1], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_offset", "_size"], 3, 1, 5, 5], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "isPlayerOnLoad", "defaultAnim", "node", "_materials"], -3, 1, 3], ["cc.Widget", ["alignMode", "_alignFlags", "_top", "node"], 0, 1]], [[1, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 7], [3, 0, 2], [0, 0, 1, 8, 2, 3, 4, 5, 3], [0, 0, 6, 2, 3, 4, 7, 2], [0, 0, 1, 6, 2, 3, 4, 5, 7, 3], [4, 0, 1], [5, 0, 1, 2, 3, 4, 5, 4], [6, 0, 1, 2, 3, 4, 5], [7, 0, 1, 2, 1], [1, 1, 2, 1], [9, 0, 1, 2, 3, 4]], [[[{"name": "slz01", "rect": [231, 252, 54, 107], "offset": [0, 0], "originalSize": [54, 107], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [1], [1]], [[[2, "Bullet_73640"], [3, "Bullet", 6, [-6, -7], [[6, -2], [7, 2, 0, false, -3, [2], 3], [8, "img/fight/effect/slz0", "1-6", 21.9, 2, -4], [9, -5, [0, -2, 53.5], [5, 40, 105]]], [10, -1, 0], [5, 54, 107], [0, 0.5, 0]], [4, "New Node", 1, [[1, "default", "purple", 0, "purple", true, "purple", -8, [0]], [11, 2, 1, -120.71079428100587, -9]], [0, "44poFTmshL5JpCOTrJfEnZ", 1, 0], [5, 127.20001983642578, 127.20000457763672], [0, 132.31079084777832, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [5, "img", 6, 1, [[1, "default", "purple", 0, "purple", true, "purple", -10, [1]]], [0, "d1RNI6Fl9KA7yAnhi8JTUO", 1, 0], [5, 127.20001983642578, 127.20000457763672], [0, 0.5, 0.2], [0, -33.459, 0, 0, 0, 0, 1, 1.5, 1.5, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 3, 1, 10], [0, 0, 0, 0], [-1, -1, -1, 4], [0, 0, 2, 3]]]]