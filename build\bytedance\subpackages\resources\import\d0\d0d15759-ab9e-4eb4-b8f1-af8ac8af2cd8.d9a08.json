[1, ["7a/QZLET9IDreTiBfRn2PD", "ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "64pdmxKVNGd5bQ/AjjP2B0"], ["node", "_file", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_parent", "_children", "_color", "_anchorPoint", "_trs"], 1, 9, 4, 5, 1, 2, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "_playTimes", "isPlayerOnLoad", "AnimList", "node", "_materials"], -4, 1, 3], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "startRadius", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -14, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node"], 2, 1], ["b30e3zYGOVAqKE2pcERIUER", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 1, 6, 2, 3, 4, 3], [0, 0, 1, 5, 2, 3, 7, 4, 8, 3], [0, 0, 1, 5, 2, 3, 9, 3], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [1, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 18], [5, 0, 1, 2], [6, 0, 1]], [[1, "Bullet_10000900"], [2, "Bullet_10000900", 6, [-4, -5], [[8, 50, -2], [9, -3]], [6, -1, 0], [5, 100, 100]], [3, "img", 6, 1, [[5, "default", 0, false, "animation", 1, true, ["animation", "animation2"], -6, [0]]], [0, "cdX5ugck5A+Y5xEC5RRm6d", 1, 0], [4, 4288085946], [5, 135.20001220703125, 108], [0, 0.5, 0.2]], [4, "New Particle", 6, 1, [[7, 1, true, 10, 100, 0.8, 1, 360, 360, 5, 5, 1, 1, 30, 30, 0, true, 100, -7, [1], [4, 2751463423], [4, 0], [4, 3607101439], [4, 0], [0, 20, 20], [0, 0, -100], 2, 3]], [0, "306C5oUe5ImqYCi4t+M5WU", 1, 0], [2.33, -32, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 4, 1, 7], [0, 0, 0, 0], [-1, -1, 1, 2], [0, 1, 2, 3]]