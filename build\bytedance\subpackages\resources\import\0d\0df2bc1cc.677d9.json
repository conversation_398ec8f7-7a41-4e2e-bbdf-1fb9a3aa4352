[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "f7293wEF9JhIuMEsrLuqng", "29FYIk+N1GYaeWH/q1NxQO", "e5AxFrgEJJZbqWIxtHFSNA", "e40u3avyZHWYWu7f8ivsQQ", "2eeFHyiVBCl5JLtkr/sTKA", "e7okC76N5PdK86LhdukXa3", "01CvKOcu1PuoWpKVUDcFyw", "0bWa8bT59HdqBP1CErYTXQ", "3eoWYf7shI2adtfCz+nF8N", "a2MjXRFdtLlYQ5ouAFv/+R", "73jGpne/9JUI/171Zur5Pr", "c1f4UDWZJNUJmmvN1IN/rK", "f0BIwQ8D5Ml7nTNQbh1YlS", "f6NcoTBPNJ4qSyD40PNpRP", "45wcsqDkxFWYqkdqLDdU41", "4bnqpPho1LI7RPMxuGPt8w", "8855/VlrRKd6H0MSRnFxAU", "954/yinnBHXJ0UIa9aXiR9", "d3MID0eblN676xIaE3SHtu", "429u7J6VhJuJOFuq9n9nl5", "3al+E83uRFnJYl72Jw3CW9", "4dMZmkcjlJEbhEI0rAtegJ", "40OtPssnZP9antMAWsDtDT", "c6823ZZu5DO5uHXx3BV9h0", "c0VITW/OJML7691KcQocPY", "675ovJ2tVK2aLYfgPUWOMv", "e3ttYJhjNMko3Cg2nIuqnM", "87iKQ2iHhE3I5wlLiIPn2Y", "bc7DN1OnFMKaSUNc74jGAZ", "ddPdxgBW1MzLONcmks1Wvv", "f8dSs5JqpBCrbcJTDIO5sE", "9dQEWfFzNF9JtxfJUOVI3j", "cf73jxyN9Jt47QTJU6ziYh", "f8qDJMtLpGVITcIiNjEmHG", "cdJsyoeQZNZ7yIh4nAP5OJ"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_N$target", "_parent", "_N$disabledSprite", "root", "uidLb", "rectAudio", "rectMusic", "musicNode", "audioNode", "musicPro", "audioPro", "btnShake", "btnMusic", "btnAudio", "_N$barSprite", "data", "_N$normalSprite"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], 0, 4, 5, 9, 1, 2, 7, 5], "cc.SpriteFrame", ["cc.Sprite", ["_type", "_sizeMode", "_enabled", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_N$cacheMode", "_styleFlags", "_lineHeight", "_spacingX", "_enableWrapText", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_children", "_color"], 2, 1, 12, 4, 5, 5, 7, 2, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.Widget", ["_alignFlags", "_right", "_originalWidth", "_originalHeight", "_left", "_top", "node"], -3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite", "_N$normalSprite"], 1, 1, 9, 1, 5, 5, 6, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$spacingX", "node", "_layoutSize"], -1, 1, 5], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Prefab", ["_name"], 2], ["6477eGocRhCf5ZiTSg/zgbY", ["node", "nodeArr", "labelArr", "btnAudio", "btnMusic", "btnShake", "audioPro", "musicPro", "audioNode", "musicNode", "rectMusic", "rectAudio", "uidLb"], 3, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[8, 0, 1, 2, 2], [2, 4, 5, 6, 1], [0, 0, 6, 5, 3, 4, 8, 2], [0, 0, 6, 7, 5, 3, 4, 8, 2], [15, 0, 1, 2, 2], [9, 0, 1, 3, 3], [7, 2, 3, 4, 1], [2, 0, 1, 4, 5, 6, 3], [6, 0, 1, 6, 3], [6, 0, 4, 5, 6, 4], [7, 0, 2, 3, 5, 6, 7, 2], [2, 0, 4, 5, 6, 2], [3, 0, 4, 9, 1, 2, 3, 5, 10, 11, 12, 8], [0, 0, 1, 7, 5, 3, 4, 8, 3], [0, 0, 7, 3, 4, 8, 2], [0, 0, 6, 5, 3, 4, 2], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 2, 6, 5, 3, 4, 3], [0, 0, 6, 3, 4, 8, 2], [4, 0, 1, 7, 2, 3, 4, 5, 2], [5, 0, 2, 3, 4, 5, 7, 6, 2], [5, 0, 2, 3, 4, 5, 6, 2], [9, 0, 1, 2, 3, 4], [2, 4, 5, 1], [2, 0, 1, 4, 5, 3], [11, 0, 2, 3, 2], [3, 0, 4, 1, 6, 2, 3, 10, 11, 12, 7], [12, 0, 2], [0, 0, 7, 5, 3, 4, 2], [0, 0, 7, 5, 3, 4, 8, 2], [0, 0, 6, 7, 3, 4, 2], [0, 0, 1, 6, 7, 5, 3, 4, 8, 3], [0, 0, 2, 6, 5, 3, 9, 4, 3], [0, 0, 6, 7, 3, 4, 8, 2], [0, 0, 6, 7, 5, 3, 4, 2], [0, 0, 1, 6, 7, 5, 3, 4, 3], [0, 0, 2, 6, 5, 3, 4, 8, 3], [4, 0, 1, 2, 3, 8, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 6, 2], [5, 0, 1, 2, 3, 4, 5, 7, 6, 3], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [8, 1, 2, 1], [6, 0, 2, 3, 6, 4], [7, 1, 0, 2, 3, 5, 6, 4, 8, 7, 3], [10, 0, 1, 2, 4, 5, 4], [10, 0, 1, 3, 4, 5, 4], [2, 2, 0, 1, 4, 5, 6, 4], [2, 1, 4, 5, 6, 2], [2, 0, 1, 3, 4, 5, 4], [11, 0, 1, 2, 3, 3], [14, 0, 1], [3, 0, 4, 7, 1, 8, 6, 2, 3, 5, 10, 11, 12, 10], [3, 0, 4, 7, 1, 2, 3, 5, 10, 11, 12, 8], [3, 0, 4, 7, 1, 6, 2, 3, 5, 10, 11, 12, 9], [3, 0, 9, 1, 6, 2, 3, 5, 10, 11, 8], [3, 0, 4, 1, 8, 2, 3, 5, 10, 11, 8]], [[[{"name": "slide_pro", "rect": [1, 0, 295, 16], "offset": [0, 0.5], "originalSize": [297, 17], "capInsets": [9, 0, 10, 0]}], [1], 0, [0], [2], [10]], [[[27, "SettingView"], [28, "SettingView", [-15, -16, -17, -18], [[40, -14, [-13], [-12], -11, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [41, -1, 0], [5, 750, 1334]], [13, "tpdg_ty_img_guan", false, [-22, -23, -24], [[8, 32, 101.5, -19], [6, -21, [[5, "6477eGocRhCf5ZiTSg/zgbY", "onClickMusic", 1]], -20]], [0, "a3GPPIkx9ITrc6IV2vAbln", 1, 0], [5, 147, 71], [100, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "tpdg_ty_img_guan", false, [-28, -29, -30], [[8, 32, 101.5, -25], [6, -27, [[5, "6477eGocRhCf5ZiTSg/zgbY", "onClickAudio", 1]], -26]], [0, "d0zrqLu5VAyr3yipJX/soA", 1, 0], [5, 147, 71], [100, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "tpdg_ty_img_guan", [-34, -35, -36], [[8, 32, 94.505, -31], [6, -33, [[5, "6477eGocRhCf5ZiTSg/zgbY", "onClickShake", 1]], -32]], [0, "dbHXgxqVVLkoX1RFrd++6O", 1, 0], [5, 147, 71], [106.995, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "audio", [-37, -38, -39, -40, 3, -41], [0, "f6vx62sspKZam4g1T8qDy8", 1, 0], [5, 550, 76], [-54.097, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "bg", 1, [-42, -43, -44, -45, -46], [0, "9f8ffN775FfrnxPwsw6MIX", 1, 0], [5, 564, 542]], [14, "music", [-47, 2, -48, -49, -50], [0, "3akJ9MH1tGd6nP/jQE9qbU", 1, 0], [5, 550, 76], [-52.506, 96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "frame", 6, [7, 5, -52], [[44, 1, 2, 20, -51, [5, 503, 268]]], [0, "21Xa+mzUZBRbKGoGLoj6cZ", 1, 0], [5, 503, 268], [0, 34.468, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layout", 6, [-54, -55, -56], [[45, 1, 1, 20, -53, [5, 656, 200]]], [0, "85k2oc0ylPULdsDD0O8/Cq", 1, 0], [5, 656, 200], [0, -185.454, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnhome", 9, [-59], [[10, 3, -57, [[5, "6477eGocRhCf5ZiTSg/zgbY", "onBackHomeClick", 1]], [4, 4293322470], [4, 3363338360], 58], [11, 1, -58, [59], 60]], [0, "860+rlJddHSLV265IQbZmB", 1, 0], [5, 206, 80], [1, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "Replay", 9, [-62, -63], [[10, 3, -60, [[22, "6477eGocRhCf5ZiTSg/zgbY", "onBtn", "Replay", 1]], [4, 4293322470], [4, 3363338360], 66], [11, 1, -61, [67], 68]], [0, "f4zHZyzFhGNYG4PFtnKwxC", 1, 0], [5, 204, 80], [226, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [3, "titlebg", 6, [-65, -66], [[46, false, 1, 0, -64, [10], 11]], [0, "8dhVEn87tD44iUXDttbszW", 1, 0], [5, 520, 82], [0, 237.618, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "misic<PERSON>ro", 7, [-69], [[[7, 1, 0, -67, [21], 22], -68], 4, 1], [0, "06U4PpO79B9KLxi6bOjCB+", 1, 0], [5, 218, 34], [0, 0, 0.5]], [19, "audioPro", 5, [-72], [[[7, 1, 0, -70, [27], 28], -71], 4, 1], [0, "406LZvnm1H1LMLZ04LzVaA", 1, 0], [5, 218, 34], [0, 0, 0.5]], [31, "New ProgressBar", false, 5, [-76], [[7, 1, 0, -73, [37], 38], [49, 300, 0.5, -75, -74]], [0, "b5TXDKB2ZBKYMGghMSS6qW", 1, 0], [5, 300, 15], [0, -49.534, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "IOSRecoveryBuy", 9, [-79], [[10, 3, -77, [[22, "6477eGocRhCf5ZiTSg/zgbY", "onBtn", "IOSRecoveryBuy", 1]], [4, 4293322470], [4, 3363338360], 53], [11, 1, -78, [54], 55]], [0, "fap5wzRf5DjK05eJuo0aGf", 1, 0], [5, 206, 80], [-225, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [37, "lb_uid", 6, [[-80, [6, -82, [[5, "6477eGocRhCf5ZiTSg/zgbY", "onUidClick", 1]], -81]], 1, 4], [0, "4bfdABduhBiZZcIp6quqT4", 1, 0], [4, 4281611316], [5, 415.64, 50.4], [0, 1, 0.5], [248.624, -268.231, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "maskbg", 190, 1, [[47, 0, -83, [0], 1], [42, 45, 750, 1334, -84]], [0, "1anP/8/h9OzYuvGncfIzgz", 1, 0], [4, 4281542699], [5, 750, 1334]], [2, "bg", 6, [[7, 1, 0, -85, [2], 3], [50, -86]], [0, "4d1CQMf2BLu4FGgQmzG5eu", 1, 0], [5, 520, 580], [0, -11.325, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "title", 12, [[51, "设 置", 48, 48, false, 1, 1, 1, 1, 1, -87, [4], 5], [4, 3, -88, [4, 4278190080]]], [0, "eeuvrIpo1PYLsYEnShEDd0", 1, 0], [5, 116.3, 66.47999999999999], [0, 2.417, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnClose", 12, [-91], [[43, 0.9, 3, -90, [[5, "6477eGocRhCf5ZiTSg/zgbY", "close", 1]], [4, 4293322470], [4, 3363338360], -89, 8, 9]], [0, "f2B/ZTGnNPcYziDIotJBRx", 1, 0], [5, 62, 65], [206.736, -0.54, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Background", 21, [[1, -92, [6], 7]], [0, "72qvCbMZxCS4o1Cv7ZJWf6", 1, 0], [5, 64, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "tpdg_ty_icon_yinliang", 7, [[1, -93, [12], 13], [9, 8, 146.501, 176, -94]], [0, "4ccLHb901AwJZBegjV/Vm4", 1, 0], [5, 65, 65], [-95.999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tpdg_ty_icon_yinliang", 5, [[1, -95, [24], 25], [9, 8, 150.5, 176, -96]], [0, "e7cJMUYR9ByYKzDijR/Vdz", 1, 0], [5, 72, 66], [-88.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "shake", 8, [-97, 4], [0, "4764F1129BB5iKhjaMZft6", 1, 0], [5, 550, 76], [-58.871, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tpdg_ty_icon_z<PERSON>ong", 25, [[1, -98, [39], 40], [9, 8, 148.5, 49, -99]], [0, "50qZyzjBZDq4GfSZqhvnVS", 1, 0], [5, 76, 67], [-88.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "tpdg_ty_img_guan", 4, [-101], [[1, -100, [43], 44]], [0, "d3DiwBq8pPzKj5CufzdpsT", 1, 0], [5, 109, 48]], [35, "tpdg_ty_img_kai", false, 4, [-103], [[1, -102, [47], 48]], [0, "85R5Dy0H5Hh7Y0qDqKCP6a", 1, 0], [5, 119, 58]], [2, "New Label", 28, [[26, "开", 28, false, 1, 1, 1, -104, [45], 46], [4, 2, -105, [4, 4278190080]]], [0, "3dPb3njQpNIoJBv2G5afeN", 1, 0], [5, 32, 54.4], [0, 0.997, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 16, [[12, "恢复购买", 36, false, false, 1, 1, 1, -106, [51], 52], [4, 2, -107, [4, 4278190080]]], [0, "feyEHbGYpABb4/jVHU8wCp", 1, 0], [5, 148, 54.4], [0, 4.225, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "txt", 10, [[12, "返回主页", 36, false, false, 1, 1, 1, -108, [56], 57], [4, 2, -109, [4, 4278190080]]], [0, "b1/RWWAvpFW6pfpUDYqCxZ", 1, 0], [5, 148, 54.4], [0, 4.225, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "txt", 11, [[12, "重新开始", 36, false, false, 1, 1, 1, -110, [61], 62], [4, 2, -111, [4, 4278190080]]], [0, "ac83t6J8dCh75WGE9XdQT0", 1, 0], [5, 148, 54.4], [0, 20.161, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "icon_lightning", 11, [-113], [[1, -112, [64], 65]], [0, "40smKzrlZNUpaVA+ej29/D", 1, 0], [5, 54, 55], [-17.384, -20.759, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [38, "Label", 33, [[-114, [4, 2, -115, [4, 4278190080]]], 1, 4], [0, "709l+if/5KEYhs0oHmux3E", 1, 0], [5, 42.91, 54.4], [63.61, 5.518, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tips", 1, [[52, "点击空白区域可关闭", 30, 30, false, 1, 1, 1, -116, [70], 71], [4, 2, -117, [4, 4278190080]]], [0, "64w7xuuWdPrJYyc96OhA8x", 1, 0], [5, 274, 41.8], [0, -335.493, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tips", 1, [[53, "注意:如果断开网络数据将无法同步至服务器\n从而可能导致数据丢失", 30, 30, false, 1, 1, 1, 1, -118, [72], 73], [4, 2, -119, [4, 4278190080]]], [0, "fct2sPsHpNe5vlsAPLDfki", 1, 0], [5, 581.53, 71.8], [0, -576.995, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "tpdg_ty_img_guan", 2, [[1, -120, [14], 15]], [0, "0fwQy5fEdBbJTexnixV4GT", 1, 0], [5, 77, 77]], [16, "tpdg_ty_img_kai", false, 2, [[1, -121, [16], 17]], [0, "cdOZiO9JZFmK/M5gOHad3N", 1, 0], [5, 62, 49]], [17, "tpdg_ty_img_kaiguan", 0, 2, [[1, -122, [18], 19]], [0, "86BDCT10NIrqnqyOdWNT+W", 1, 0], [5, 70, 73]], [20, "bar", 13, [-123], [0, "3bNpluN5lGAYnod5BNKgZq", 1, 0], [5, 212, 34], [0, 0, 0.5], [3, 3.6, 0, 0, 0, 0, 1, 1, 1, 1]], [48, 1, 0, false, 40, [20]], [25, 212, 13, 41], [21, "rectMusic", 7, [-124], [0, "3bk2P772pORJjdzDNxSUzH", 1, 0], [5, 38, 37], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [23, 43, [23]], [18, "musicNode", 7, [0, "6cNZd0+uxNwIl44G0T7Vpf", 1, 0], [5, 218, 34], [112, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "bar", 14, [-125], [0, "91L0gfffFFx6a/wFuQnypv", 1, 0], [5, 212, 34], [0, 0, 0.5], [3, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 1, 0, 46, [26]], [25, 212, 14, 47], [21, "rectAudio", 5, [-126], [0, "90hulrt0VDZYVcn0YYBJON", 1, 0], [5, 38, 37], [163, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [23, 49, [29]], [18, "autioNode", 5, [0, "06S1Ir/PVNCptVm5hhXMvN", 1, 0], [5, 218, 34], [112, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "tpdg_ty_img_guan", 3, [[1, -127, [30], 31]], [0, "2ePMA0WJFHGbjFy6/RRaBt", 1, 0], [5, 77, 77]], [16, "tpdg_ty_img_kai", false, 3, [[1, -128, [32], 33]], [0, "106842SBdA04/QxqP9Ulzo", 1, 0], [5, 62, 49]], [17, "tpdg_ty_img_kaiguan", 0, 3, [[1, -129, [34], 35]], [0, "1e1uDejn9Dg7RLxcHHuGm8", 1, 0], [5, 70, 73]], [39, "bar", 512, 15, [-130], [0, "73d1ub2eNIHaLlbhHORp73", 1, 0], [5, 150, 15], [0, 0, 0.5], [-150, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 1, 0, 55, [36]], [2, "New Label", 27, [[26, "关", 28, false, 1, 1, 1, -131, [41], 42]], [0, "fcjFdk/hJK+byNGp4DLy1a", 1, 0], [5, 28, 50.4], [0, 0.997, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "tpdg_ty_img_kaiguan", 0, 4, [[1, -132, [49], 50]], [0, "098Kbdm+RFlqoRo7TmWzSj", 1, 0], [5, 106, 45], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [54, "-5", false, false, 1, 1, 1, 1, 34, [63]], [55, "12345678901234567890123456789012", 24, false, 1, 2, 1, 1, 17, [69]]], 0, [0, 7, 1, 0, 8, 60, 0, 9, 50, 0, 10, 44, 0, 11, 45, 0, 12, 51, 0, 13, 42, 0, 14, 48, 0, 15, 4, 0, 16, 2, 0, 17, 3, 0, -1, 59, 0, -1, 10, 0, 0, 1, 0, -1, 18, 0, -2, 6, 0, -3, 35, 0, -4, 36, 0, 0, 2, 0, 4, 2, 0, 0, 2, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, 0, 3, 0, 4, 3, 0, 0, 3, 0, -1, 52, 0, -2, 53, 0, -3, 54, 0, 0, 4, 0, 4, 4, 0, 0, 4, 0, -1, 27, 0, -2, 28, 0, -3, 58, 0, -1, 24, 0, -2, 14, 0, -3, 49, 0, -4, 51, 0, -6, 15, 0, -1, 19, 0, -2, 12, 0, -3, 8, 0, -4, 9, 0, -5, 17, 0, -1, 23, 0, -3, 13, 0, -4, 43, 0, -5, 45, 0, 0, 8, 0, -3, 25, 0, 0, 9, 0, -1, 16, 0, -2, 10, 0, -3, 11, 0, 0, 10, 0, 0, 10, 0, -1, 31, 0, 0, 11, 0, 0, 11, 0, -1, 32, 0, -2, 33, 0, 0, 12, 0, -1, 20, 0, -2, 21, 0, 0, 13, 0, -2, 42, 0, -1, 40, 0, 0, 14, 0, -2, 48, 0, -1, 46, 0, 0, 15, 0, 18, 56, 0, 0, 15, 0, -1, 55, 0, 0, 16, 0, 0, 16, 0, -1, 30, 0, -1, 60, 0, 4, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 4, 22, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, -1, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, -1, 57, 0, 0, 28, 0, -1, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, -1, 34, 0, -1, 59, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, -1, 41, 0, -1, 44, 0, -1, 47, 0, -1, 50, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, -1, 56, 0, 0, 57, 0, 0, 58, 0, 19, 1, 2, 5, 7, 3, 5, 5, 4, 5, 25, 5, 5, 8, 7, 5, 8, 132], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 44, 47, 50, 56, 59, 60], [-1, 1, -1, 1, -1, 3, -1, 1, 20, 6, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, -1, 3, 6, -1, 1, -1, 3, 6, -1, 1, -1, 3, -1, -1, 1, 6, -1, 1, -1, -1, 3, -1, 3, 1, 1, 1, 1, 1, 3, 3], [0, 11, 0, 12, 0, 1, 0, 13, 14, 3, 0, 15, 0, 16, 0, 4, 0, 5, 0, 6, 0, 0, 7, 0, 0, 17, 0, 0, 7, 0, 0, 4, 0, 5, 0, 6, 0, 0, 18, 0, 19, 0, 2, 0, 20, 0, 2, 0, 21, 0, 22, 0, 1, 3, 0, 23, 0, 1, 3, 0, 24, 0, 1, 0, 0, 25, 3, 0, 26, 0, 0, 1, 0, 2, 8, 9, 8, 9, 27, 2, 2]], [[{"name": "slide_bar", "rect": [0, 0, 38, 37], "offset": [0, 0.5], "originalSize": [38, 38], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [28]], [[{"name": "tpdg_ty_img_kaiguan", "rect": [0, 0, 70, 73], "offset": [0, 0], "originalSize": [70, 73], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [29]], [[{"name": "img_sz_ylk", "rect": [0, 0, 106, 45], "offset": [0, 0], "originalSize": [106, 45], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [30]], [[{"name": "btn_open", "rect": [0, 0, 119, 58], "offset": [-0.5, 0], "originalSize": [120, 58], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [31]], [[{"name": "img_yinyue", "rect": [0, 0, 65, 65], "offset": [0, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [32]], [[{"name": "tpdg_ty_icon_yinliang", "rect": [0, 0, 72, 66], "offset": [0, 0], "originalSize": [72, 66], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [33]], [[{"name": "default_progressbar", "rect": [0, 0, 30, 15], "offset": [0, 0], "originalSize": [30, 15], "capInsets": [10, 4, 10, 4]}], [1], 0, [0], [2], [34]], [[{"name": "tpdg_ty_icon_z<PERSON>ong", "rect": [0, 0, 76, 67], "offset": [0, 0], "originalSize": [76, 67], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [35]], [[{"name": "btn_close", "rect": [5, 5, 109, 48], "offset": [-0.5, 0], "originalSize": [120, 58], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [36]]]]