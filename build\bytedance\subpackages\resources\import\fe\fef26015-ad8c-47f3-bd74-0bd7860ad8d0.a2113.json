[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "b6no4h/J1IXbpWOrxekU+n", "7a/QZLET9IDreTiBfRn2PD"], ["node", "_file", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint"], 1, 9, 4, 5, 7, 1, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "_stopped", "totalParticles", "emissionRate", "life", "lifeVar", "angle", "angleVar", "startSize", "startSizeVar", "endSize", "endSizeVar", "speed", "speedVar", "tangentialAccel", "rotationIsDir", "startRadius", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -15, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "isPlayerOnLoad", "node", "_materials"], -3, 1, 3], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node"], 2, 1], ["b30e3zYGOVAqKE2pcERIUER", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [2, 0, 2], [0, 0, 1, 7, 2, 3, 4, 5, 3], [0, 0, 1, 6, 2, 3, 5, 3], [0, 0, 1, 6, 2, 3, 4, 8, 3], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 19], [1, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 7, 7], [5, 0, 1, 2], [6, 0, 1]], [[1, "Bullet_10000800"], [2, "Bullet_10000800", 6, [-4, -5], [[8, 70, -2], [9, -3]], [6, -1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [3, "New Particle", 6, 1, [[5, 1, true, false, 50, 999.999985098839, 0.8, 1, 360, 360, 20, 10, 5, 5, 30, 30, 0, true, 100, -6, [0], [4, 2751463423], [4, 0], [4, 3607101439], [4, 0], [0, 20, 20], [0, 0, -100], 1, 2]], [0, "500L136CNFRIwZTgv00wfu", 1, 0], [4.66, -5.394, 0, 0, 0, 0, 1, 2, 2, 2]], [4, "img", 6, 1, [[7, "default", "animation", 0, false, "animation", true, -7, [3]]], [0, "cdX5ugck5A+Y5xEC5RRm6d", 1, 0], [5, 172.261962890625, 164.82627868652344], [0, 0.5, 0.2]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 4, 1, 7], [0, 0, 0, 0], [-1, 1, 2, -1], [0, 1, 2, 3]]