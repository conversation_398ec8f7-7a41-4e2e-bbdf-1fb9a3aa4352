[1, ["ecpdLyjvZBwrvm+cedCcQy", "c46cSnrYpELobhdnhwO2KF", "de9P2St5JLZIV0Yv22GYlt"], ["node", "_spriteFrame", "root", "cardBtn", "isSelectMask", "icon", "tag", "desc", "skillname", "target", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_trs", "_parent", "_children", "_color"], 0, 4, 5, 9, 7, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials"], -4, 1, 3], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.LabelShadow", ["_enabled", "node", "_color", "_offset"], 2, 1, 5, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -3, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["5c4f9jgVJpAr55NnFbZ1r/i", ["node", "skillname", "desc", "tag", "icon", "isSelectMask", "cardBtn", "videoList"], 3, 1, 1, 1, 1, 1, 1, 1, 2], ["cc.<PERSON><PERSON>", ["_enabled", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 7, 5, 3, 4, 6, 2], [1, 1, 0, 3, 4, 3], [6, 0, 1, 2, 3, 4, 5, 2], [0, 0, 1, 7, 5, 3, 4, 6, 3], [7, 0, 1, 2, 2], [1, 3, 4, 1], [1, 3, 4, 5, 1], [4, 0, 2], [0, 0, 8, 5, 3, 4, 6, 2], [0, 0, 1, 7, 8, 5, 3, 4, 6, 3], [0, 0, 1, 7, 8, 3, 9, 4, 6, 3], [0, 0, 2, 7, 5, 3, 9, 4, 3], [5, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 5, 7, 8, 7], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [3, 1, 2, 1], [1, 0, 2, 3, 4, 3], [1, 0, 3, 4, 2], [8, 0, 1, 2, 3, 2], [9, 0, 1, 2, 3, 4, 5, 6, 7], [10, 0, 1, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 1], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3]], [[8, "BuffCardItem"], [9, "BuffCardItem", [-12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22], [[22, -9, -8, -7, -6, -5, -4, -3, [-2]], [23, false, -11, [[24, "5c4f9jgVJpAr55NnFbZ1r/i", "onClick", -10]]]], [16, -1, 0], [5, 217, 400], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "CardBtn", false, 1, [-24], [[6, -23, [1]]], [0, "7eEX6l3NVEALh3Ll4CT8ln", 1, 0], [5, 200, 87], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "name", 1, [[-25, [5, 2, -26, [4, 4278190080]], [19, false, -27, [4, 4278190080], [0, 2, -2]]], 1, 4, 4], [0, "87DSaDvGVLLaamuqimxtMl", 1, 0], [5, 197, 41.8], [0, 192.119, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "videoIcon", 1, [[7, -28, [7], 8], [21, 2, -29]], [0, "8eYqRi7GNPeYhxMw92LIBE", 1, 0], [5, 53, 41], [-91, 194.936, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "isSelect", false, 1, [-30, -31], [0, "90FN7go3lG55xlVD9zvq3d", 1, 0], [4, 4278190080], [5, 229, 309], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "name", 2, [[14, "免费获得", 26, 30, false, 1, 1, -32, [0]], [5, 3, -33, [4, 1476395008]]], [0, "56fV+FstlH6IXzq37l3PYa", 1, 0], [5, 110, 43.8], [0, -10.357, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 1, [[2, 1, 0, -34, [2]]], [0, "8dQi1VzuhPd5s2mn3IFcek", 1, 0], [5, 223, 335], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Buff名称", 26, 30, false, 1, 1, 3, 3, [3]], [1, "frame", 1, [[2, 1, 0, -35, [4]]], [0, "684Z0rXdBIsaGHvawENKQb", 1, 0], [5, 190, 153], [0, -15.972, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "desc", 1, [-36], [0, "83uet4KSxNA4jsMFzxCJkG", 1, 0], [5, 162.9, 39.06], [0, -15.387, 0, 0, 0, 0, 1, 1, 1, 1]], [20, false, "测试", 1, 25, 162.9, 31, 10], [3, "icon", 1, [-37], [0, "92ax78fO1Kkp8sRcxEyiNN", 1, 0], [5, 105, 105], [0, 112.993, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [17, 2, false, 12, [5]], [3, "tag", 1, [-38], [0, "a2vYQG5NxHapIZRSXYHzoR", 1, 0], [5, 26, 34], [-86.368, 148.077, 0, 0, 0, 0, 1, 0.8, 0.8, 0]], [6, 14, [6]], [4, "light", false, 1, [[2, 1, 0, -39, [9]]], [0, "06EOBtjJZHk7rp1mPKgxpp", 1, 0], [5, 223, 335], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "opposite", false, 1, [[2, 1, 0, -40, [10]]], [0, "2bg6SI2GRHOLI9kHO8kzJv", 1, 0], [5, 223, 335], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "isSelect", 179, 5, [[18, 0, -41, [11]]], [0, "dcBMYLZoJHgp9Q8SYvqU6E", 1, 0], [4, 4286414205], [5, 223, 337]], [1, "<PERSON>eon", 5, [[7, -42, [12], 13]], [0, "6a9YxjwIVH3bTufxl/8rza", 1, 0], [5, 50, 43], [86.208, -139.498, 0, 0, 0, 0, 1, 0.8, 0.8, 2]]], 0, [0, 2, 1, 0, -1, 4, 0, 3, 2, 0, 4, 5, 0, 5, 13, 0, 6, 15, 0, 7, 11, 0, 8, 8, 0, 0, 1, 0, 9, 1, 0, 0, 1, 0, -1, 2, 0, -2, 7, 0, -3, 3, 0, -4, 9, 0, -5, 10, 0, -6, 12, 0, -7, 14, 0, -8, 4, 0, -9, 16, 0, -10, 17, 0, -11, 5, 0, 0, 2, 0, -1, 6, 0, -1, 8, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 18, 0, -2, 19, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 9, 0, -1, 11, 0, -1, 13, 0, -1, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 10, 1, 42], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2]]