[1, ["ecpdLyjvZBwrvm+cedCcQy", "2e6Lznd4FLjLlHvglL8iaD", "8eVK90my1K67EEZU4Vji9C", "8bpqI20CFJkp5IwBqgn8wQ"], ["node", "_texture", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.MotionStreak", ["_fadeTime", "_stroke", "_fastMode", "node", "_materials", "_texture", "_color"], 0, 1, 3, 6, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.RigidBody", ["_gravityScale", "enabledContactListener", "node"], 1, 1], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node", "_offset"], 1, 1, 5], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node", "_offset"], 2, 1, 5], ["30ada2xhuJHPZEBfFsjxG3Z", ["node"], 3, 1]], [[1, 0, 1, 2, 2], [0, 0, 1, 6, 3, 4, 5, 7, 3], [4, 0, 2], [0, 0, 1, 8, 3, 4, 5, 3], [0, 0, 2, 6, 3, 4, 7, 3], [0, 0, 6, 3, 4, 9, 5, 7, 2], [2, 0, 1, 2, 3, 4, 6, 5, 4], [2, 0, 1, 2, 3, 4, 5, 4], [1, 0, 1, 2], [1, 1, 2, 1], [3, 0, 1, 3, 4, 5, 3], [3, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3], [6, 0, 1, 2, 3, 3], [7, 0, 1, 2, 2], [8, 0, 1]], [[2, "MPUBGoodsBullet2"], [3, "MPUBGoodsBullet2", 7, [-6, -7, -8, -9], [[12, -10, true, -2], [13, 0, 5, -3, [0, 0, 35]], [14, 5, -4, [0, 0, 35]], [15, -5]], [9, -1, 0], [5, 30, 30]], [4, "fx_tail", false, 1, [[6, 0.2, 10, true, -11, [0], [4, 3707764735], 1]], [8, "02ivEB7BlL8KId85GdWvU8", -10], [0, 0, 0, 0, 0, 0, 1, 1, 0.2, 1]], [1, "img", 6, 1, [[10, 2, false, -12, [2], 3]], [0, "570XkTxHxNYInWnU8pCPUe", 1, 0], [5, 38, 73], [0, 0, 0, 0, 0, 0, 1, 1, 1.5, 1]], [5, "bulle copy", 1, [[11, 1, -13, [4], 5]], [0, "00CDhdMaFAi73Q3VA5whPc", 1, 0], [4, 4278203224], [5, 38, 73], [0, -5.224, 0, 0, 0, 0, 1, 1.5, 2, 3]], [1, "New Sprite", 6, 1, [[7, 0.1, 20, true, -14, [6], 7]], [0, "df30iLPfdG3rryRwuDufb3", 1, 0], [5, 40, 36], [-0.369, 14.374, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, 3, 2, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 4, 1, 14], [0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 2, -1, 2, -1, 1], [0, 1, 0, 2, 0, 3, 0, 1]]