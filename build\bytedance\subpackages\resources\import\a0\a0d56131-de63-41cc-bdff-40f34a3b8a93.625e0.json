[1, ["ecpdLyjvZBwrvm+cedCcQy", "f0PlN5WMBMuokG262MFbDs", "4fO8ZzJohNZYLcRLlUbcVA", "fb5bCOKxJK0Yvt8cjvLTB0", "a2MjXRFdtLlYQ5ouAFv/+R", "5esB9LxCNIpozu/DuPYufl", "29FYIk+N1GYaeWH/q1NxQO", "03W+ZBfDNJwreQxWi3jfXh", "59bVjkP3hAl4UFwALKqTjY", "cfUUhW23NKcY7e1f1CFu3x", "afAeqwhB9AHo1gfdDvnPqU", "c1pcpbJSpPVZnnpm1cMKBM", "29U6anZJpDFYbsZhTvz4iM", "b15mUqogdM/7Dcb/ZHR1PL", "e4wWD0ReJLo6FNORTL/F/3", "e9+7qvGvtJSIULcbyeiiE1", "4eHXODN4pPBJvrFP+OBMO7", "0ewvZNzj9LgLVuL5CxAIV0", "e2+Ytzia5GIK2QczusFM45"], ["node", "_spriteFrame", "root", "_parent", "_N$disabledSprite", "asset", "bar", "attrRich", "_N$target", "data"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_fontSize", "_isSystemFontUsed", "_N$overflow", "_lineHeight", "_enableWrapText", "_N$cacheMode", "_enabled", "node", "_materials"], -8, 1, 3], ["cc.Widget", ["_alignFlags", "_top", "_enabled", "_bottom", "_originalHeight", "node"], -2, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_children"], 2, 1, 12, 4, 5, 7, 5, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Mask", ["_enabled", "node", "_materials"], 2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.RichText", ["_N$string", "_N$fontSize", "_N$lineHeight", "node"], 0, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["f8f7f5kMW5D6a7FvM5PbBMi", ["node", "attrRich", "bar"], 3, 1, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 7, 5, 4, 6, 8, 2], [0, 0, 7, 9, 5, 4, 6, 8, 2], [1, 2, 3, 4, 1], [6, 0, 1, 2, 2], [2, 0, 4, 7, 3, 1, 2, 11, 12, 7], [0, 0, 7, 5, 4, 6, 2], [1, 1, 0, 2, 3, 4, 3], [1, 0, 2, 3, 4, 2], [6, 1, 2, 1], [0, 0, 7, 5, 4, 10, 6, 8, 2], [2, 0, 4, 3, 1, 2, 11, 12, 6], [0, 0, 7, 9, 5, 4, 6, 2], [13, 0, 1, 2, 3, 4, 4], [0, 0, 9, 5, 4, 6, 8, 2], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4], [10, 0, 2], [0, 0, 2, 9, 5, 4, 6, 3], [0, 0, 3, 7, 5, 4, 10, 6, 3], [0, 0, 1, 7, 5, 4, 10, 6, 8, 3], [0, 0, 7, 5, 4, 6, 11, 8, 2], [0, 0, 1, 7, 5, 4, 6, 8, 3], [0, 0, 7, 4, 2], [4, 0, 1, 2, 3, 6, 4, 5, 2], [4, 0, 1, 7, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 3], [3, 0, 5, 2], [3, 2, 0, 1, 5, 4], [3, 0, 1, 3, 4, 5, 5], [1, 1, 2, 3, 4, 2], [1, 2, 4, 1], [1, 1, 0, 2, 3, 3], [1, 0, 2, 3, 2], [5, 1, 2, 1], [12, 0, 1, 2, 3, 3], [2, 0, 5, 3, 1, 2, 6, 11, 12, 7], [2, 0, 8, 5, 1, 2, 6, 9, 11, 12, 8], [2, 10, 0, 4, 7, 1, 2, 11, 12, 7], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 3, 3], [9, 1, 2, 1], [9, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4], [15, 0, 1, 2, 3, 3], [16, 0, 1, 2, 1]], [[17, "PetSigleView"], [18, "PetSigleView", 1, [-5, -6], [[45, -4, -3, -2]], [34, -1, 0], [5, 750, 1334]], [14, "middle", [-9, -10, -11, -12, -13], [[28, false, 41, -69.899, -7], [42, false, -8, [74]]], [0, "17/cT1WT1Dm7S3exQuG7/+", 1, 0], [5, 650, 0], [0, 319.899, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_cw_lieb01", 2, [-15, -16, -17, -18, -19], [[7, 1, 0, -14, [72], 73]], [0, "58ddWFdBZJ/ZxwBx8iGXop", 1, 0], [5, 721, 188], [0, -593.621, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [12, "upNode", 3, [-21, -22, -23, -24, -25], [[13, 1, 1, 100, -20, [5, 685, 50]]], [0, "eel/0h1uVAk5hNPEyaykgE", 1, 0], [5, 685, 50]], [2, "arrNode", 2, [-27, -28, -29, -30], [[13, 1, 1, 30, -26, [5, 650, 200]]], [0, "56vcWSaYZJ7qugFqF6Wh9b", 1, 0], [5, 650, 200], [0, -458.953, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [12, "bg", 1, [-33, 2], [[33, 0, -31, [75]], [29, 5, 417, 417, 1332, -32]], [0, "99b1Qdgu5GApqcw6DScSX1", 1, 0], [5, 650, 500]], [14, "Background", [-37], [[30, 1, -34, [4], 5], [39, 3, -36, [[40, "f8f7f5kMW5D6a7FvM5PbBMi", "close", 1]], [4, 4293322470], [4, 3363338360], -35, 6]], [0, "5cGNk/mrRDCIcCj+lSpduG", 1, 0], [5, 52, 56], [273.919, 363.708, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "img_shuxingd", 5, [-39, -40, -41], [[7, 1, 0, -38, [20], 21]], [0, "62pFPWqPJFPqeEXekUJcsQ", 1, 0], [5, 140, 33], [-255, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_shuxingd", 5, [-43, -44, -45], [[7, 1, 0, -42, [26], 27]], [0, "50vzzsX5lEiJ+i+j8ZnzsO", 1, 0], [5, 140, 33], [-85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_shuxingd", 5, [-47, -48, -49], [[7, 1, 0, -46, [32], 33]], [0, "ca5NVvOAhNGasXzJj+yG1F", 1, 0], [5, 140, 33], [85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "img_shuxingd", 5, [-51, -52, -53], [[7, 1, 0, -50, [38], 39]], [0, "88D7FKPAdOhI08AFBhOA+J", 1, 0], [5, 140, 33], [255, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "title_bg", 6, [-55, 7], [[7, 1, 0, -54, [7], 8]], [0, "f4CIycMT9KGrX/Rottfmc7", 1, 0], [5, 655, 800]], [2, "New Node", 2, [-57, -58], [[41, -56, [13]]], [0, "92b36HVQND8KFeJ8Zx2DdD", 1, 0], [5, 640, 700], [0, -350, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "lbLv", 3, [[[38, false, "等级5：主角攻击+100", 22, 22, 1, 1, -59, [43]], [9, -60, [4, 4279374353]], -61], 4, 4, 1], [0, "a28NFbyH9Iho4FcQL528Gl", 1, 0], [4, 4284208127], [5, 0, 27.72], [0, -67.83, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "New ProgressBar", 3, [-64], [[[7, 1, 0, -62, [45], 46], -63], 4, 1], [0, "52XiQ2SxBKpajqroBT84nY", 1, 0], [5, 634, 26], [0, -12.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_djh", 4, [-66, -67], [[3, -65, [50], 51]], [0, "34LOlVXdVJIpEJFmvw21wQ", 1, 0], [5, 57, 46], [-314, -3.418, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_djh", 4, [-69, -70], [[3, -68, [55], 56]], [0, "a8cUOdg5VI5L5hhrSIsN7f", 1, 0], [5, 57, 46], [-157, -3.418, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_djh", 4, [-72, -73], [[3, -71, [60], 61]], [0, "6fpwIhtsBGBZtBgRIqxizI", 1, 0], [5, 57, 46], [0, -3.418, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_djh", 4, [-75, -76], [[3, -74, [65], 66]], [0, "4fottLnXhGiI5seTjM2sXb", 1, 0], [5, 57, 46], [157, -3.418, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_djh", 4, [-78, -79], [[3, -77, [70], 71]], [0, "4f6lfZJNlCKpfUVyMQpZTg", 1, 0], [5, 57, 46], [314, -3.418, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "maskbg", 200, 1, [[27, 45, -80], [8, 0, -81, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [1, "Label_title", 12, [[36, "宠物", false, 1, 1, 1, 2, -82, [2]], [4, 4, -83, [4, 4278190080]]], [0, "75u1+AdLFAY5OBS+XSulph", 1, 0], [5, 285, 56.4], [0, 357.112, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "icon_xqtb", false, 2, [[31, -84, 14], [15, 3, -85, [[16, "358e0/U1bJMXbjLr35Ojxfq", "onBtn", "info", 1]]]], [0, "34JUxAexhE8rpPBaT9Lw3t", 1, 0], [5, 54, 56], [320.122, -363.286, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "petFight", 2, [-87], [[13, 1, 1, 175, -86, [5, 128, 200]]], [0, "e3+dbmzqBLNqJas40sNb3w", 1, 0], [5, 128, 200], [0, -326.059, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbLv", 8, [[5, "等级", 18, 18, 1, 1, 1, -88, [18]], [9, -89, [4, 4279374353]]], [0, "5fdVwEA65H1oRjfl0/cP+l", 1, 0], [4, 4284208127], [5, 38, 24.68], [0, 24.92, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbLvVal", 8, [[11, "559", 24, 1, 1, 1, -90, [19]], [4, 3, -91, [4, 4279374353]]], [0, "e43kLhPClLRboOL4ZcuNfV", 1, 0], [5, 46.04, 56.4]], [10, "lbLv", 9, [[5, "生命值", 18, 18, 1, 1, 1, -92, [24]], [9, -93, [4, 4279374353]]], [0, "b3MwFpKZhJkbOeQZ+C8MOy", 1, 0], [4, 4284208127], [5, 56, 24.68], [0, 24.92, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbLvVal", 9, [[11, "559", 24, 1, 1, 1, -94, [25]], [4, 3, -95, [4, 4279374353]]], [0, "70Y8jDICpLJJvCBWLEgSN1", 1, 0], [5, 46.04, 56.4]], [10, "lbLv", 10, [[5, "攻击力", 18, 18, 1, 1, 1, -96, [30]], [9, -97, [4, 4279374353]]], [0, "beiJDAPkZAFJqOjVZFcj2z", 1, 0], [4, 4284208127], [5, 56, 24.68], [0, 24.92, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbLvVal", 10, [[11, "559", 24, 1, 1, 1, -98, [31]], [4, 3, -99, [4, 4279374353]]], [0, "12GjnxPylO8qEisMTo87aj", 1, 0], [5, 46.04, 56.4]], [10, "lbLv", 11, [[5, "防御", 18, 18, 1, 1, 1, -100, [36]], [9, -101, [4, 4279374353]]], [0, "86/CGWd9hGb55KT0z3CE1S", 1, 0], [4, 4284208127], [5, 38, 24.68], [0, 24.92, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbLvVal", 11, [[11, "559", 24, 1, 1, 1, -102, [37]], [4, 3, -103, [4, 4279374353]]], [0, "31yhWW/+VKlK4MVSd3bKd7", 1, 0], [5, 46.04, 56.4]], [1, "icon_cw_bz", 3, [[3, -104, [40], 41], [15, 3, -105, [[16, "f8f7f5kMW5D6a7FvM5PbBMi", "onBtn", "UpInfo", 1]]]], [0, "cdn+I2/k5LerXZdg71g5j5", 1, 0], [5, 44, 44], [-181.317, 64.158, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "lbLvVal", 3, [[5, "升级效果", 30, 30, 1, 1, 1, -106, [42]], [4, 3, -107, [4, 4279374353]]], [0, "32jAebdPpC5on0SQzH9P61", 1, 0], [5, 126, 43.8], [-276.704, 65.391, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbLvVal", 16, [[5, "10", 20, 20, 1, 1, 1, -108, [49]], [4, 3, -109, [4, 4279374353]]], [0, "dfFaYjnChLX5X85xgabUpM", 1, 0], [5, 28.25, 31.2], [0, -33.068, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbLvVal", 17, [[5, "10", 20, 20, 1, 1, 1, -110, [54]], [4, 3, -111, [4, 4279374353]]], [0, "440k0qGwNFiqm35uPICH5A", 1, 0], [5, 28.25, 31.2], [0, -33.068, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbLvVal", 18, [[5, "10", 20, 20, 1, 1, 1, -112, [59]], [4, 3, -113, [4, 4279374353]]], [0, "ceTRBzWldGIbI7KODCkHLb", 1, 0], [5, 28.25, 31.2], [0, -33.068, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbLvVal", 19, [[5, "10", 20, 20, 1, 1, 1, -114, [64]], [4, 3, -115, [4, 4279374353]]], [0, "6aXD+OYE1Au5eUD5R18Rf5", 1, 0], [5, 28.25, 31.2], [0, -33.068, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbLvVal", 20, [[5, "10", 20, 20, 1, 1, 1, -116, [69]], [4, 3, -117, [4, 4279374353]]], [0, "6cV8qC4JFMZp3Vs9WzwRtv", 1, 0], [5, 28.25, 31.2], [0, -33.068, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Label", false, 7, [[37, "返回", false, false, 1, 1, 1, 1, -118, [3]]], [0, "f61HEqmPJEc5iPipLkNn75", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_cwbj01", 13, [[8, 0, -119, [9], 10]], [0, "3594N/Yg9JdYfrT9j7XQA+", 1, 0], [5, 1125, 472], [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "img_cw_sydb", 13, [[7, 1, 0, -120, [11], 12]], [0, "4fEurJ6g5KRYRvoTKW8YCH", 1, 0], [5, 1125, 300], [0, 0.5, 1], [0, -63.505, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "PetFingtInfo", 24, [35, "11oxuupvJKQoB7oF4zqAIC", true, -121, 15]], [1, "icon_dj", 8, [[8, 0, -122, [16], 17]], [0, "51qK6DhMNDypbRqTM9AK2l", 1, 0], [5, 50, 50], [-61.406, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_dj", 9, [[8, 0, -123, [22], 23]], [0, "48alZcId5FSpFVJV4pZkhA", 1, 0], [5, 49, 40], [-61.406, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_dj", 10, [[8, 0, -124, [28], 29]], [0, "6cqy23c9ROXKvqjAC9uewR", 1, 0], [5, 40, 40], [-61.406, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icon_dj", 11, [[8, 0, -125, [34], 35]], [0, "36jzG8rE1GSJpxDktKSFQ9", 1, 0], [5, 40, 40], [-61.406, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "", 22, 22, 14], [26, "bar", 512, 15, [-126], [0, "fbALeVsdlI2KN1T9GkBODo", 1, 0], [5, 317, 26], [0, 0, 0.5], [-317, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 1, 0, 49, [44]], [44, 634, 0.5, 15, 50], [6, "icon_dj", 16, [[3, -127, [47], 48]], [0, "b7ipWJEdlBK7vf1N0jYeik", 1, 0], [5, 62, 62]], [1, "icon_dj", 17, [[3, -128, [52], 53]], [0, "75DhDWf3NPyKAT1CPa7LDk", 1, 0], [5, 62, 62], [0, 2.297, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "icon_dj", 18, [[3, -129, [57], 58]], [0, "af5BB8CLRD3qi3cjSd0j4H", 1, 0], [5, 62, 62]], [6, "icon_dj", 19, [[3, -130, [62], 63]], [0, "a3oEzO1BhEIZPVGif4e74V", 1, 0], [5, 62, 62]], [6, "icon_dj", 20, [[3, -131, [67], 68]], [0, "27fdKXrW9K76QISoK2T1C8", 1, 0], [5, 62, 62]]], 0, [0, 2, 1, 0, 6, 51, 0, 7, 48, 0, 0, 1, 0, -1, 21, 0, -2, 6, 0, 0, 2, 0, 0, 2, 0, -1, 13, 0, -2, 23, 0, -3, 24, 0, -4, 5, 0, -5, 3, 0, 0, 3, 0, -1, 33, 0, -2, 34, 0, -3, 14, 0, -4, 15, 0, -5, 4, 0, 0, 4, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, 0, 5, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, 0, 6, 0, 0, 6, 0, -1, 12, 0, 0, 7, 0, 8, 7, 0, 0, 7, 0, -1, 40, 0, 0, 8, 0, -1, 44, 0, -2, 25, 0, -3, 26, 0, 0, 9, 0, -1, 45, 0, -2, 27, 0, -3, 28, 0, 0, 10, 0, -1, 46, 0, -2, 29, 0, -3, 30, 0, 0, 11, 0, -1, 47, 0, -2, 31, 0, -3, 32, 0, 0, 12, 0, -1, 22, 0, 0, 13, 0, -1, 41, 0, -2, 42, 0, 0, 14, 0, 0, 14, 0, -3, 48, 0, 0, 15, 0, -2, 51, 0, -1, 49, 0, 0, 16, 0, -1, 52, 0, -2, 35, 0, 0, 17, 0, -1, 53, 0, -2, 36, 0, 0, 18, 0, -1, 54, 0, -2, 37, 0, 0, 19, 0, -1, 55, 0, -2, 38, 0, 0, 20, 0, -1, 56, 0, -2, 39, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 43, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 2, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, -1, 50, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 9, 1, 2, 3, 6, 7, 3, 12, 131], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50], [-1, 1, -1, -1, -1, 1, 4, -1, 1, -1, 1, -1, 1, -1, 1, 5, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1], [0, 4, 0, 0, 0, 5, 6, 0, 7, 0, 8, 0, 9, 0, 10, 11, 0, 1, 0, 0, 0, 3, 0, 12, 0, 0, 0, 3, 0, 13, 0, 0, 0, 3, 0, 14, 0, 0, 0, 3, 0, 15, 0, 0, 0, 0, 16, 0, 1, 0, 0, 2, 0, 1, 0, 0, 2, 0, 1, 0, 0, 2, 0, 1, 0, 0, 2, 0, 1, 0, 0, 2, 0, 17, 0, 0, 18]]