[1, ["ecpdLyjvZBwrvm+cedCcQy", "52CLU7xQ5GHYbVV4FvmhTi", "4eBbEEaspAhb562ageNdR1", "aco6URPsJEoJAglTZcfWJ1", "a4mGGZpzZD36GGpok/7DY+", "0eW4EHFf9Jd42jlAVGiSjf", "31EmAcfPFOhqbw8EPEOXEL", "2f5nGfqvhCGIyS4jQkoXwR", "23yvNw8PBIR6vT5IQXHGJM", "45vR4rDhtGu6mweFltTqdy", "26DWzlL71BDaoD0PmP3pZd"], ["node", "_spriteFrame", "_textureSetter", "root", "_parent", "data"], [["cc.Node", ["_name", "_groupIndex", "_prefab", "_contentSize", "_parent", "_trs", "_components", "_eulerAngles", "_children", "_anchorPoint"], 1, 4, 5, 1, 7, 9, 5, 12, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_parent", "_children", "_prefab", "_components", "_contentSize"], 2, 1, 2, 4, 9, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2]], [[3, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 3], [0, 0, 1, 4, 6, 2, 3, 9, 5, 7, 3], [0, 0, 4, 6, 2, 3, 2], [0, 0, 4, 6, 2, 3, 5, 7, 2], [5, 0, 2], [0, 0, 1, 8, 2, 3, 3], [0, 0, 4, 2, 5, 2], [0, 0, 4, 6, 2, 3, 5, 2], [2, 0, 1, 2, 3, 2], [2, 0, 1, 2, 4, 3, 5, 2], [3, 1, 2, 1], [4, 2, 3, 4, 1]], [[[[5, "map2"], [6, "map1", 2, [[-2, -3, -4, -5, -6, [7, "pet", -8, [0, "877i+RI+JORL4Ag3QxOIMI", -7, 0], [375.224, 1117.602, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 1, 1, 1, 1, 4], [11, -1, 0], [5, 1153, 2052]], [9, "New Node", 1, [-9, -10, -11, -12, -13, -14], [0, "397yQf5AtNRazjmmHOvbsW", 1, 0]], [10, "wall_middle", 2, [-16], [[1, 1, 0, -15, [4], 5]], [0, "f0juwFfvhM8a/iqO+/mU/1", 1, 0], [5, 535, 350]], [3, "wall_bg", 1, [[1, 2, 0, -17, [0], 1]], [0, "bakdNpLnBKNJhwQmwGSA3B", 1, 0], [5, 3000, 4000]], [3, "base", 3, [[12, -18, [2], 3]], [0, "e7sX870rZEB7xVfSzoNzw+", 1, 0], [5, 167, 166]], [2, "wall copy", 8, 2, [[1, 1, 0, -19, [6], 7]], [0, "71H1iXvbZLpo3EwGr+n2bl", 1, 0], [5, 70, 1130], [0, 0.5, 0], [700, 829.626, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "wall copy", 8, 2, [[1, 1, 0, -20, [8], 9]], [0, "e24LwtIiZFBYYJQNn++mFU", 1, 0], [5, 70, 1130], [0, 0.5, 1], [-700, 454.853, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "wall copy", 8, 2, [[1, 1, 0, -21, [10], 11]], [0, "e5x1XsSJ5JUqkMUnPWTak+", 1, 0], [5, 70, 1130], [0, 0.5, 1], [-700, -428.212, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "wall copy", 8, 2, [[1, 1, 0, -22, [12], 13]], [0, "1ebIqpfj9N/ZuXWzLMUMWb", 1, 0], [5, 70, 1130], [0, 0.5, 0], [700, -686.036, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "wall copy", 8, 2, [[1, 1, 0, -23, [14], 15]], [0, "d2kUUspWlGz4bu51x1z02U", 1, 0], [5, 70, 1130], [0, 0.5, 1], [-700, -949.927, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [8, "wall_top", 1, [[1, 2, 0, -24, [16], 17]], [0, "ddun0UnwRA14bCyiEvZNUl", 1, 0], [5, 1500, 44], [0, 1200, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "wall_left", 1, [[1, 2, 0, -25, [18], 19]], [0, "ef3We0ot9MOoZ53OaN8OJ8", 1, 0], [5, 3000, 44], [-680, 500, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [4, "wall_right", 1, [[1, 2, 0, -26, [20], 21]], [0, "fedo8kkzlPKLI0+e3aKbUH", 1, 0], [5, 3000, 44], [680, 500, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]]], 0, [0, 3, 1, 0, -1, 4, 0, -2, 2, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, 3, 1, 0, 4, 1, 0, -1, 3, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 5, 1, 26], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 3, 0, 4, 0, 5, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2]], [[{"name": "wall4", "rect": [0, 0, 312, 312], "offset": [0, 0], "originalSize": [312, 312], "capInsets": [125, 123, 122, 127]}], [1], 0, [0], [2], [6]], [[{"name": "wall2", "rect": [0, 0, 175, 44], "offset": [0, 0], "originalSize": [175, 44], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[{"name": "wall3", "rect": [0, 0, 761, 70], "offset": [0, 0], "originalSize": [761, 70], "capInsets": [50, 20, 50, 20]}], [1], 0, [0], [2], [8]], [[{"name": "base", "rect": [0, 0, 167, 166], "offset": [0, 0], "originalSize": [167, 166], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [9]], [[{"name": "wall1", "rect": [0, 0, 181, 204], "offset": [0, 0], "originalSize": [181, 204], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [10]]]]