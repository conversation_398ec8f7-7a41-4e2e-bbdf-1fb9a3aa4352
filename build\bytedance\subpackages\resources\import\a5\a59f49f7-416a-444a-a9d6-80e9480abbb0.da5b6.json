[1, ["ecpdLyjvZBwrvm+cedCcQy", "23Tw89umhO5pVi85QDxfi5", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "65OUoZ25pGHqftEDT5VTS4", "29FYIk+N1GYaeWH/q1NxQO", "f6NcoTBPNJ4qSyD40PNpRP", "e9iF9wThhD+JkpWy9IG4Cd", "da/b6oN8FClZAlaX9Cw3wZ"], ["node", "_spriteFrame", "_parent", "root", "templete", "_N$content", "content", "_N$disabledSprite", "_N$target", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "node"], -1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_enableWrapText", "_N$cacheMode", "node", "_materials"], -4, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["enableAutoScale", "node", "content", "templete"], 2, 1, 1, 6], ["cb3eebd/cFNrqu0Se0oKB9l", ["UItype", "node", "nodeArr"], 2, 1, 2]], [[3, 0, 1, 2, 2], [3, 0, 1, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 8, 6, 4, 5, 9, 2], [1, 0, 1, 3, 4, 4], [0, 0, 7, 8, 6, 4, 5, 11, 9, 2], [0, 0, 7, 6, 4, 5, 11, 9, 2], [1, 2, 0, 1, 4, 4], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 3, 2], [5, 0, 2], [0, 0, 1, 8, 6, 4, 5, 3], [0, 0, 2, 7, 6, 4, 10, 5, 3], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 8, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 7, 8, 6, 4, 5, 9, 2], [0, 0, 7, 6, 4, 5, 9, 2], [0, 0, 3, 7, 6, 4, 10, 5, 9, 3], [1, 0, 4, 2], [2, 0, 2, 3, 4, 2], [2, 1, 2, 3, 4, 2], [3, 1, 2, 1], [6, 0, 1], [4, 0, 1, 2, 3, 4, 7, 8, 6], [4, 0, 5, 1, 2, 3, 4, 6, 7, 8, 8], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 2, 3], [13, 0, 1, 2, 2]], [[11, "PassSelectView"], [12, "PassSelectView", 1, [-5, -6], [[30, 0, -4, [-2, -3]]], [23, -1, 0], [5, 750, 1334]], [3, "scrollview", [-13], [[2, 1, 0, -8, [12], 13], [9, false, 0.75, 0.23, null, null, -10, -9], [10, false, -12, -11, 14]], [1, "f12l5R6q5PpK+SZ6DyiGfM", -7], [5, 640, 880], [0, -30.545, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "scrollview", [-20], [[2, 1, 0, -15, [16], 17], [9, false, 0.75, 0.23, null, null, -17, -16], [10, false, -19, -18, 18]], [1, "3cLm84Tl9IOIEwC/YeTswi", -14], [5, 640, 880], [0, -30.545, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "content", [-21, -22, 2, 3], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [3, "Background", [-26], [[22, 1, -23, [6], 7], [28, 3, -25, [[29, "cb3eebd/cFNrqu0Se0oKB9l", "close", 1]], [4, 4293322470], [4, 3363338360], -24, 8]], [0, "8adN9uZ5dNFauTFm5YJOWU", 1, 0], [5, 52, 56], [269.386, -0.668, 0, 0, 0, 0, 1, 1, 1, 0]], [17, "title_zhua<PERSON><PERSON>", 4, [-28, 5], [[2, 1, 0, -27, [9], 10]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 680, 82], [0, 465.372, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "view", 2, [-31], [[8, 0, -29, [11]], [4, 45, 240, 250, -30]], [1, "c1dc9Kk/CRJqaNnBOP4YGDS", 2], [5, 640, 880], [0, 0.5, 1], [0, 440, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "content", 7, [[7, 0, 41, 220, -32]], [1, "036a4WKD5hBcbQJmMrbqoW8", 2], [5, 640, 65.2], [0, 0, 1], [-320, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "view", 3, [-35], [[8, 0, -33, [15]], [4, 45, 240, 250, -34]], [1, "c1dc9Kk/CRJqaNnBOP4YGDS", 3], [5, 640, 880], [0, 0.5, 1], [0, 440, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "content", 9, [[7, 0, 41, 220, -36]], [1, "036a4WKD5hBcbQJmMrbqoW8", 3], [5, 640, 65.2], [0, 0, 1], [-320, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "maskbg", 230, 1, [[20, 45, -37], [21, 0, -38, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [14, "bg", 1, [4], [[4, 45, 750, 1334, -39]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [16, "bg", 4, [[2, 1, 0, -40, [2], 3], [24, -41]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 680, 1000]], [18, "Label_title", 6, [[25, "关卡选择", false, 1, 1, 2, -42, [4]], [27, 4, -43, [4, 4278190080]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 285, 56.4], [0, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Label", false, 5, [[26, "返回", false, false, 1, 1, 1, 1, -44, [5]]], [0, "60hmbYqdZCRaezrdS0zth8", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, -1, 2, 0, -2, 3, 0, 0, 1, 0, -1, 11, 0, -2, 12, 0, 3, 2, 0, 0, 2, 0, 5, 8, 0, 0, 2, 0, 6, 8, 0, 0, 2, 0, -1, 7, 0, 3, 3, 0, 0, 3, 0, 5, 10, 0, 0, 3, 0, 6, 10, 0, 0, 3, 0, -1, 9, 0, -1, 13, 0, -2, 6, 0, 0, 5, 0, 8, 5, 0, 0, 5, 0, -1, 15, 0, 0, 6, 0, -1, 14, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 9, 1, 2, 2, 4, 3, 2, 4, 4, 2, 12, 5, 2, 6, 44], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, -1, 1, 7, -1, 1, -1, -1, 1, 4, -1, -1, 1, 4], [0, 2, 0, 3, 0, 0, 0, 4, 5, 0, 6, 0, 0, 1, 7, 0, 0, 1, 8]]