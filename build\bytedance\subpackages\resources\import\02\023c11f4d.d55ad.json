[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "e2VHprIDlCQre6wlyQ42yl", "f7293wEF9JhIuMEsrLuqng", "cb1VFt7lZM5bK3HPiU1WUY", "eazNjMJRZDXI1JKX9YTFmA", "70aCz0K+hIXpyzPorkF1BM", "a2tZpSYPpN27Shi20RC6+r", "c770npCgpHvrrj8apNO90q", "f1oLpJwq1EEYtLpd1sEGaF", "b2gseIigJABZr/xG0zwT9Y", "29FYIk+N1GYaeWH/q1NxQO", "c0VITW/OJML7691KcQocPY", "ddGPcSaUJOc7TvrBI6xoA6", "dbNkclouNG/oj/cNi+uAJ8", "b9CtqqKmNE64nnbTzPOg0L", "240iR5/alOQZrkRPl+/0cq"], ["node", "_spriteFrame", "_parent", "_N$file", "_N$font", "_textureSetter", "checkMark", "_N$target", "root", "needNode", "scroll", "toggle", "data", "_N$disabledSprite", "_normalMaterial"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_components", "_contentSize", "_children", "_parent", "_trs", "_anchorPoint", "_color"], 0, 4, 9, 5, 2, 1, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_fillRange", "node", "_materials", "_spriteFrame"], -2, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_top", "_originalHeight", "_bottom", "_right", "_left", "alignMode", "node"], -5, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "_N$paddingLeft", "_N$spacingX", "_N$affectedByScale", "node", "_layoutSize"], -5, 1, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_parent", "_children", "_components", "_prefab", "_contentSize"], 1, 1, 2, 2, 4, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Toggle", ["_N$isChecked", "node", "_N$normalColor", "_N$target", "checkMark", "checkEvents", "_normalMaterial"], 2, 1, 5, 1, 1, 9, 6], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$horizontalAlign", "node", "_N$font"], -3, 1, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["6c8a1lUEm1B47KpFvMqrmCs", ["node", "nodeArr", "toggle", "scroll", "needNode"], 3, 1, 2, 1, 1, 1], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "amTime", "node"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["bbcc410J6hMNqmnBqUklFiE", ["path", "node"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.ToggleContainer", ["node"], 3, 1]], [[6, 0, 1, 2, 2], [0, 0, 7, 6, 4, 3, 5, 8, 2], [0, 0, 7, 4, 3, 5, 8, 2], [1, 1, 0, 5, 6, 7, 3], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 8], [18, 0, 1, 2, 2], [1, 5, 6, 7, 1], [0, 0, 7, 6, 4, 3, 5, 2], [0, 0, 2, 7, 4, 3, 10, 5, 3], [0, 0, 6, 4, 3, 5, 8, 2], [0, 0, 7, 6, 4, 3, 5, 9, 8, 2], [0, 0, 7, 6, 3, 2], [0, 0, 7, 4, 3, 10, 5, 2], [0, 0, 7, 4, 3, 5, 2], [0, 0, 7, 4, 3, 5, 9, 8, 2], [12, 0, 1, 2, 3, 4, 5, 6, 2], [1, 1, 0, 2, 5, 6, 7, 4], [1, 1, 0, 2, 5, 6, 4], [2, 0, 2, 4, 1, 3, 8, 6], [3, 0, 1, 8, 9, 3], [3, 0, 1, 2, 8, 9, 4], [8, 0, 1, 3, 3], [8, 0, 1, 2, 3, 4], [10, 0, 1, 2, 3, 4, 6, 7, 6], [11, 0, 2], [0, 0, 1, 6, 4, 3, 5, 3], [0, 0, 6, 4, 3, 5, 9, 8, 2], [0, 0, 6, 4, 3, 5, 9, 2], [0, 0, 6, 4, 3, 5, 2], [5, 0, 2, 3, 4, 5, 6, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [13, 0, 1, 2, 3, 4, 1], [6, 1, 2, 1], [1, 3, 1, 0, 5, 6, 7, 4], [1, 0, 5, 6, 7, 2], [1, 0, 5, 6, 2], [1, 4, 5, 6, 7, 2], [2, 0, 6, 5, 2, 1, 3, 8, 7], [2, 7, 0, 5, 4, 1, 3, 8, 7], [2, 0, 2, 4, 8, 4], [2, 0, 2, 4, 1, 8, 5], [2, 0, 1, 3, 8, 4], [2, 0, 8, 2], [3, 0, 1, 3, 4, 2, 8, 9, 6], [3, 0, 1, 5, 6, 7, 8, 9, 6], [7, 1, 0, 2, 3, 4, 5, 6, 3], [7, 0, 2, 3, 2], [9, 1, 2, 3, 4, 5, 6, 1], [9, 0, 1, 2, 3, 4, 5, 2], [14, 0, 1, 2, 3], [15, 0, 1, 2, 2], [16, 0, 1, 2], [10, 0, 1, 5, 2, 6, 7, 5], [19, 0, 1, 2, 3, 4, 5, 6, 6], [20, 0, 1]], [[[{"name": "sp_base_yellowdetail", "rect": [0, 0, 125, 39], "offset": [-0.5, 0.5], "originalSize": [126, 40], "capInsets": [52.5, 0, 52.5, 0]}], [4], 0, [0], [5], [7]], [[[24, "Role_UpGrade"], [25, "Role_UpGrade", 1, [-9, -10], [[31, -8, [-5, -6, -7], -4, -3, -2]], [32, -1, 0], [5, 750, 1334]], [26, "bottom", [-13, -14, -15], [[3, 1, 0, -11, [47], 48], [37, 5, 325, 325, 474, 100, 800, -12]], [0, "22HLaDIO5NeKI+vrZ8/+Ki", 1, 0], [5, 750, 860], [0, 0.5, 0], [0, -667, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "content", [-18, -19], [[38, 0, 9, 510, 120, 240, 250, -16], [43, 1, 2, 20, 80, 20, -17, [5, 750, 343]]], [0, "09qVo8x8BKl6Ktsi8YKzbx", 1, 0], [5, 750, 343], [0, 0.5, 1]], [15, "New ToggleContainer", 2, [-23, -24], [[-20, [19, 1, 1, -21, [5, 748, 100]], [39, 1, -9.173000000000002, 795.691, -22]], 1, 4, 4], [0, "dcEMho0zxLF7CRSUrdgVUf", 1, 0], [5, 748, 100], [0, 819.173, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "item", [-26, -27, -28], [[3, 1, 0, -25, [15], 16]], [0, "b1qaBB0dNIobC+Kpd2bXly", 1, 0], [5, 720, 138]], [1, "bg_zhuangbei_sz", 2, [-31, -32], [[33, false, 1, 0, -29, [30], 31], [40, 44, 714.12, 7.2379999999999995, 20, -30]], [0, "b4Rg89sXRFBborwCJZljWC", 1, 0], [5, 750, 100], [0, 57.238, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "needCount", [-34, -35], [[44, 1, 1, 10, 10, true, -33, [5, 176.38000000000002, 50]]], [0, "20NcuKXxxLTKxLikzoxB6u", 1, 0], [5, 176.38000000000002, 50], [-9.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btn", [-38], [[45, 0.9, 3, -36, [[21, "6c8a1lUEm1B47KpFvMqrmCs", "onCilckUpGrade", 1]], [4, 4293322470], [4, 3363338360], 25], [3, 1, 0, -37, [26], 27]], [0, "d5GxA2vsNNMaKqPR4aY6eu", 1, 0], [5, 200, 90], [0, -73.388, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "toggle1", 4, [-42, -43], [[47, -41, [4, 4292269782], -40, -39, [[22, "6c8a1lUEm1B47KpFvMqrmCs", "onTap", "1", 1]], 39]], [0, "3eH3ADzgFMCo7P7bWdVz+s", 1, 0], [5, 374, 73], [-187, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "toggle2", 4, [-47, -48], [[48, false, -46, [4, 4292269782], -45, -44, [[22, "6c8a1lUEm1B47KpFvMqrmCs", "onTap", "2", 1]]]], [0, "b9uoKufXVFsa14rHibvczH", 1, 0], [5, 374, 73], [187, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "bg", 1, [2], [[18, 45, 1243.89, -1243.89, 750, 1334, -49], [49, 11, 0.2, -50]], [0, "59kLcAn3BDcJHm7O8cmIJe", 1, 0], [5, 750, 1334], [0, -1243.89, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "ScrollView", 2, [-53], [[-51, [18, 45, 75.5, 105.5, 240, 250, -52]], 1, 4], [0, "15MHpYYEtBf6lG3HK+xoxx", 1, 0], [5, 750, 679], [0, 445, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "view", 12, [3], [[50, 0, -54, [17]], [41, 45, 240, 250, -55]], [0, "a5ytvCvYBNQ7uWRhUt9U0q", 1, 0], [5, 750, 679], [0, 0.5, 1], [0, 339.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lv", 3, [-57], [[20, 1, 2, 20, -56, [5, 750, 85]]], [0, "5bRDXTOXRKaIXNYtK8wnha", 1, 0], [5, 750, 85], [0, -62.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "item", 14, [-59, -60], [[3, 1, 0, -58, [6], 7]], [0, "b1XpHUnbJJaonSXbnW82VQ", 1, 0], [5, 720, 85]], [1, "star", 3, [5], [[20, 1, 2, 20, -61, [5, 750, 138]]], [0, "72fsJ2Z9RNMKnVTxDwcJUz", 1, 0], [5, 750, 138], [0, -194, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "starBar", 5, [-64], [[19, 1, 1, -62, [5, 35, 50]], [51, ["img/Pet/icon_xxhui", "img/Pet/icon_xxh"], -63]], [0, "1b/nxUKThAHYXk8tXFExuH", 1, 0], [5, 35, 50], [0, 0, 0.5], [-335.69, 38.408, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "img_zld2", 6, [7, 8], [[34, 0, -65, [28], 29]], [0, "66/7gJIZZOb641ekoRRB1e", 1, 0], [5, 664, 51], [0, 81.034, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "maskbg", 50, 1, [[42, 45, -66], [35, 0, -67, [0]]], [0, "5eZWKfNM5DUIZUjxNM1w8r", 1, 0], [4, 4278190080], [5, 750, 1334]], [11, "lock", 15, [-68, -69], [0, "caBWOItiFPMYAiruZBFixP", 1, 0]], [11, "lock", 5, [-70, -71], [0, "7cgFbOiztPjZCzTAqeoqf1", 1, 0]], [2, "button_return", 6, [[6, -72, [18], 19], [46, 3, -73, [[21, "6c8a1lUEm1B47KpFvMqrmCs", "close", 1]]]], [0, "16SJN4VRZMh7B/xxexW5CN", 1, 0], [5, 66, 69], [-322.549, 3.946, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "val", 8, [[4, "升星", 36, false, false, 1, 1, 1, -74, [23], 24], [5, 2, -75, [4, 4278190080]]], [0, "7aBV2kNfBEgri4cyjHsKBF", 1, 0], [5, 76, 54.4], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 9, [-77], [[16, 1, 0, false, -76, [34], 35]], [0, "f3YrtWJTFD86kbEyU5hdMF", 1, 0], [5, 300, 65]], [12, "val", 24, [[4, "升级", 36, false, false, 1, 1, 1, -78, [32], 33], [5, 2, -79, [4, 4278190080]]], [0, "40yZQmO69ES5CJJUkauRLg", 1, 0], [4, 4290164406], [5, 76, 54.4]], [29, "checkmark", 9, [-81], [-80], [0, "f73fHExmJCkbHjLvJGU123", 1, 0], [5, 300, 65]], [13, "val", 26, [[4, "升级", 36, false, false, 1, 1, 1, -82, [36], 37], [5, 2, -83, [4, 4278190080]]], [0, "b2YoTcg1BEB5W+mk/qnc1u", 1, 0], [5, 76, 54.4]], [7, "Background", 10, [-85], [[16, 1, 0, false, -84, [42], 43]], [0, "01EwVN7jZDlIQwOJ9BHc5x", 1, 0], [5, 300, 65]], [12, "val", 28, [[4, "升星", 36, false, false, 1, 1, 1, -86, [40], 41], [5, 2, -87, [4, 4278190080]]], [0, "f39OPH5qxEcKzYVJlKvp4X", 1, 0], [4, 4290164406], [5, 76, 54.4]], [30, "checkmark", false, 10, [-89], [-88], [0, "f3wwhXwz1L8oa5S2paeT9F", 1, 0], [5, 300, 65]], [13, "val", 30, [[4, "升星", 36, false, false, 1, 1, 1, -90, [44], 45], [5, 2, -91, [4, 4278190080]]], [0, "37bHq+aN9Bo6qDVQUpDjY0", 1, 0], [5, 76, 54.4]], [14, "desc", 15, [[23, false, "<b><outline color=black width=2>玄武棒,竖直向前翻滚滚</></>", 28, 700, 24, -92, 1]], [0, "560o/JXJNIw7nO5XRgzqwW", 1, 0], [5, 700, 30.240000000000002], [0, 0, 0.5], [-336.605, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lock", 147, 20, [[3, 1, 0, -93, [2], 3]], [0, "d78EEEVCRNX4X6OW9fCJEX", 1, 0], [4, 4278190080], [5, 720, 85]], [2, "icon_lock_small", 20, [[6, -94, [4], 5]], [0, "1eugccLrJPvrai5UhVltIG", 1, 0], [5, 34, 41], [314.441, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "desc", 5, [[23, false, "<b><outline color=black width=2>玄武棒,竖直向前翻滚滚</></>", 28, 700, 24, -95, 8]], [0, "7cEfXjdotB0qsMMuIgeOYl", 1, 0], [5, 700, 30.240000000000002], [0, 0, 0.5], [-336.605, -24.868, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon_xxh", 17, [[36, 5.551115123125783e-17, -96, [9], 10]], [0, "4723CCVY1Kgb/FoLD9WDwg", 1, 0], [5, 35, 35], [17.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lock", 147, 21, [[3, 1, 0, -97, [11], 12]], [0, "1elMvpwzVMjpa6BB2iW/Qt", 1, 0], [4, 4278190080], [5, 720, 138]], [2, "icon_lock_small", 21, [[6, -98, [13], 14]], [0, "fbTrAKhV9IlYAAAt5C7Rdb", 1, 0], [5, 34, 41], [314.441, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [53, false, 0.75, 0.23, null, null, 12, 3], [2, "icon", 7, [[6, -99, [20], 21]], [0, "14c74WG95FCqaJVWszt6wU", 1, 0], [5, 76, 64], [-47.790000000000006, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "val", 7, [[52, false, "<b><outline color=black width=3><color=#00ff00>22</c>/10</outline></>", 1, 32, -100, 22]], [0, "eaT4R9FjNOxJG70l/MJZYf", 1, 0], [5, 95.58000000000001, 50.4], [40.400000000000006, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 1, 0, false, 26, [38]], [17, 1, 0, false, 30, [46]], [54, 4]], 0, [0, 8, 1, 0, 9, 7, 0, 10, 39, 0, 11, 44, 0, -1, 14, 0, -2, 16, 0, -3, 8, 0, 0, 1, 0, -1, 19, 0, -2, 11, 0, 0, 2, 0, 0, 2, 0, -1, 12, 0, -2, 6, 0, -3, 4, 0, 0, 3, 0, 0, 3, 0, -1, 14, 0, -2, 16, 0, -1, 44, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, -2, 10, 0, 0, 5, 0, -1, 35, 0, -2, 17, 0, -3, 21, 0, 0, 6, 0, 0, 6, 0, -1, 22, 0, -2, 18, 0, 0, 7, 0, -1, 40, 0, -2, 41, 0, 0, 8, 0, 0, 8, 0, -1, 23, 0, 6, 42, 0, 7, 9, 0, 0, 9, 0, -1, 24, 0, -2, 26, 0, 6, 43, 0, 7, 10, 0, 0, 10, 0, -1, 28, 0, -2, 30, 0, 0, 11, 0, 0, 11, 0, -1, 39, 0, 0, 12, 0, -1, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, -1, 32, 0, -2, 20, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 36, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, -1, 33, 0, -2, 34, 0, -1, 37, 0, -2, 38, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, -1, 42, 0, -1, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -1, 29, 0, 0, 29, 0, 0, 29, 0, -1, 43, 0, -1, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 40, 0, 0, 41, 0, 12, 1, 2, 2, 11, 3, 2, 13, 5, 2, 16, 7, 2, 18, 8, 2, 18, 100], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 43], [-1, 4, -1, 1, -1, 1, -1, 1, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, 4, -1, 3, 13, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 14, -1, 3, -1, 1, -1, 3, -1, -1, 1, 1, 1], [0, 3, 0, 2, 0, 4, 0, 2, 3, 0, 8, 0, 2, 0, 4, 0, 2, 0, 0, 9, 0, 10, 3, 0, 1, 11, 0, 12, 0, 13, 0, 14, 0, 1, 0, 5, 0, 1, 0, 0, 0, 1, 0, 5, 0, 1, 0, 0, 15, 6, 6]], [[{"name": "sp_base_graydetail", "rect": [0, 0, 125, 39], "offset": [-0.5, 0.5], "originalSize": [126, 40], "capInsets": [46.5, 0, 46.5, 0]}], [4], 0, [0], [5], [16]]]]