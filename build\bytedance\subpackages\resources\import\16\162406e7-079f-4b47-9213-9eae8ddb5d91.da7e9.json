[1, ["ecpdLyjvZBwrvm+cedCcQy", "2e6Lznd4FLjLlHvglL8iaD", "8eVK90my1K67EEZU4Vji9C", "8bpqI20CFJkp5IwBqgn8wQ"], ["node", "_spriteFrame", "root", "_texture", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_trs", "_contentSize", "_parent", "_children", "_eulerAngles", "_color"], 1, 9, 4, 7, 5, 1, 2, 5, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.MotionStreak", ["_fadeTime", "_stroke", "_fastMode", "node", "_materials", "_color", "_texture"], 0, 1, 3, 5, 6], ["cc.RigidBody", ["_gravityScale", "node"], 2, 1], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node", "_offset"], 1, 1, 5], ["8753ebzC8xFWoQEVH4FrhIo", ["node"], 3, 1], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node", "_offset"], 2, 1, 5]], [[1, 0, 1, 2, 2], [3, 0, 2], [0, 0, 1, 7, 2, 3, 5, 4, 8, 3], [0, 0, 6, 2, 3, 4, 2], [0, 0, 1, 6, 2, 3, 5, 4, 3], [0, 0, 6, 2, 3, 9, 5, 4, 2], [4, 0, 1, 2, 3, 4, 5, 6, 4], [1, 0, 1, 2], [1, 1, 2, 1], [2, 0, 1, 3, 4, 5, 3], [2, 2, 3, 4, 5, 2], [2, 3, 4, 1], [5, 0, 1, 2], [6, 0, 1, 2, 3, 3], [7, 0, 1], [8, 0, 1, 2, 2]], [[1, "MPUBGoodsBullet"], [2, "MTPUBGoodsBullet", 7, [-7, -8, -9], [[11, -2, [6]], [12, 1.3, -3], [13, 0.05, 10, -4, [0, 0, 35]], [14, -5], [15, 10, -6, [0, 0, 35]]], [8, -1, 0], [5, 22, 37], [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [3, "fx_tail", 1, [[6, 0.2, 20, true, -11, [0], [4, 3707764735], 1]], [7, "0aOECY0mhF/b6BKs/5lBj0", -10], [0, 10, 0, 0, 0, 0, 1, 1.2, 0.2, 1]], [4, "img", 6, 1, [[9, 2, false, -12, [2], 3]], [0, "9awCMsf5pHXIOMxpa1krrJ", 1, 0], [5, 38, 73], [0, 0, 0, 0, 0, 0, 1, 1, 1.5, 1]], [5, "bulle copy", 1, [[10, 1, -13, [4], 5]], [0, "e4GOYVHnhDG5zEjU1ZnXXq", 1, 0], [4, 4278203224], [5, 38, 73], [0, -5.224, 0, 0, 0, 0, 1, 1.5, 2, 3]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, 2, 2, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, 4, 1, 13], [0, 0, 0, 0, 0, 0, 0], [-1, 3, -1, 1, -1, 1, -1], [0, 1, 0, 2, 0, 3, 0]]