[1, ["ecpdLyjvZBwrvm+cedCcQy", "f7293wEF9JhIuMEsrLuqng", "f9qnuSq3NFwbgvCvXIMiTP", "ddFhwuPitMK6rcBkC3ddyJ", "2b4vWm6xxFHJg6KVm92Xlr", "8855/VlrRKd6H0MSRnFxAU", "cb1VFt7lZM5bK3HPiU1WUY", "52G+GgYkBNNpLxgmPs1ow4", "44z7VELVNODb3zInlzQ8sp"], ["node", "_spriteFrame", "_N$file", "root", "lockinfo", "tag", "puzzleNode", "upgradeNode", "equipLv", "equipFrame", "equipCellIcon", "equipBg", "equipImg", "equipName", "_N$target", "target", "_N$barSprite", "data", "_textureSetter"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_color"], 0, 4, 1, 9, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_N$overflow", "_enableWrapText", "node", "_materials"], -6, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 12, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["17e32vjiNVAD6NNm8o8FLcW", ["node", "equipName", "equipImg", "equipBg", "equipCellIcon", "equipFrame", "equipLv", "upgradeNode", "puzzleNode", "tag", "lockinfo"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$target"], 1, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Widget", ["_alignFlags", "node"], 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[5, 0, 1, 2, 2], [13, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 2], [1, 3, 4, 5, 1], [1, 3, 4, 1], [12, 0, 1, 2], [7, 0, 2], [0, 0, 8, 5, 3, 6, 7, 2], [0, 0, 4, 8, 5, 3, 6, 7, 2], [0, 0, 4, 8, 3, 2], [0, 0, 4, 8, 3, 7, 2], [0, 0, 4, 5, 3, 6, 2], [0, 0, 1, 4, 5, 3, 6, 7, 3], [0, 0, 2, 4, 5, 3, 9, 6, 3], [0, 0, 4, 5, 3, 6, 7, 2], [4, 0, 1, 2, 3, 6, 4, 2], [2, 0, 2, 3, 4, 5, 2], [2, 0, 2, 3, 4, 7, 5, 6, 2], [2, 0, 2, 3, 4, 5, 8, 6, 2], [2, 0, 2, 3, 4, 5, 6, 2], [2, 0, 1, 2, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [9, 0, 1, 2, 3, 4, 3], [10, 0, 1, 2, 3], [5, 1, 2, 1], [11, 0, 1, 2, 3, 3], [1, 1, 0, 3, 4, 5, 3], [1, 0, 3, 4, 5, 2], [1, 1, 3, 4, 2], [1, 0, 2, 3, 4, 3], [1, 1, 0, 3, 4, 3], [3, 0, 1, 2, 3, 4, 5, 9, 10, 7], [3, 0, 1, 2, 3, 6, 4, 5, 9, 10, 8], [3, 0, 1, 2, 8, 3, 4, 5, 7, 9, 10, 9], [3, 0, 1, 2, 3, 6, 4, 5, 7, 9, 10, 9]], [[[[6, "equiplockitem"], [7, "equiplockitem", [-16, -17, -18, -19, -20, -21, -22, -23], [[21, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2], [22, 0.9, 3, -15, [[23, "17e32vjiNVAD6NNm8o8FLcW", "showInfo", -14]], -13]], [24, -1, 0], [5, 165, 221], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "New ProgressBar", 1, [-27, -28, -29], [[25, 132, 0, -25, -24], [26, 1, 0, -26, [8], 9]], [0, "68ZBpPWBlEUKTHIMwG/4sF", 1, 0], [5, 132, 24], [0, -65.116, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [2, "name", 1, [[-30, [5, 16, -31], [1, 2, -32, [4, 4278190080]]], 1, 4, 4], [0, "07oKezl3pGMJdi1I7z0RcL", 1, 0], [5, 44, 35.5], [0, -33.622, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lv", 1, [[-33, [5, 16, -34], [1, 2, -35, [4, 4278190080]]], 1, 4, 4], [0, "f8L1vgu9lG6olepB9EKhmx", 1, 0], [5, 134, 35.5], [0, 87.229, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "icon_lock_small", 1, [-36, -37, -38], [0, "8cbiQeqqJBrZYXSWKdhXhf", 1, 0]], [15, "progress", 2, [[-39, [1, 2, -40, [4, 4278190080]]], 1, 4], [0, "38bpquLW1Mg7OylAw5WynE", 1, 0], [4, 4293062126], [5, 44.13, 29.2]], [10, "state", 2, [-41, -42], [0, "64FiUMlhNBYpo9uWYU98hw", 1, 0], [-54.58, 1.16, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "icon_pintu", 7, [[3, -43, [4], 5]], [0, "9ahSwmj/xJy7Myj05Ai+mJ", 1, 0], [5, 33, 33]], [12, "icon_shengji", false, 7, [[27, 0, -44, [6], 7]], [0, "f9xwFJhVZBF4enRSQ4OYIK", 1, 0], [5, 35, 35], [113.333, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "unlockinfo", 5, [[-45, [1, 2, -46, [4, 4278190080]]], 1, 4], [0, "aeuvJRwtJGILOnSkOl8Gqz", 1, 0], [5, 158.2, 29.2], [0, -63.3, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "bg_goods_02", 1, [-47], [0, "349lLjSodJLZkGfr2jWpCF", 1, 0], [5, 166, 213]], [28, 1, 11, [0]], [17, "New Sprite", 1, [-48], [0, "26GPqRzElEt7OaGOjRJQT/", 1, 0], [4, 4290230199], [5, 80, 80], [0, 37.201, 0, 0, 0, 0, 1, 1, 1, 1]], [29, 0, false, 13, [1]], [18, "bar", 2, [-49], [0, "e8DOYdvHtF2qSi3crVKoBb", 1, 0], [5, 0, 24], [0, 0, 0.5], [-66, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 1, 0, 15, [2]], [31, "0/25", 18, 20, false, 1, 1, 6, [3]], [19, "icon_gezi_1", 1, [-50], [0, "f6W3FKwL5MdrfNPqn+wrDr", 1, 0], [5, 26, 26], [49.056, -34.517, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 18, [10]], [20, "tag", false, 1, [-51], [0, "47fwuX70ZOmJrZGNSn6ljV", 1, 0], [5, 43, 43], [56, 83, 0, 0, 0, 0, 1, 0.55, 0.55, 1]], [4, 20, [11]], [32, "复合", 20, 25, false, 1, 1, 1, 3, [12]], [33, "等级1", 18, 24, false, false, 1, 1, 2, 4, [13]], [13, "New Node", 200, 5, [[3, -52, [14], 15]], [0, "07NjcpTRVH/Z1J8HffbMvl", 1, 0], [4, 4278190080], [5, 166, 213]], [14, "New Node", 5, [[3, -53, [16], 17]], [0, "850MscE7tEy6iQRMoFFw/8", 1, 0], [5, 34, 41], [0, 10.295, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "通关章节XX解锁", 18, 20, false, 1, 1, 1, 3, 10, [18]]], 0, [0, 3, 1, 0, 4, 26, 0, 5, 21, 0, 6, 8, 0, 7, 9, 0, 8, 23, 0, 9, 17, 0, 10, 19, 0, 11, 12, 0, 12, 14, 0, 13, 22, 0, 0, 1, 0, 14, 1, 0, 15, 1, 0, 0, 1, 0, -1, 11, 0, -2, 13, 0, -3, 2, 0, -4, 18, 0, -5, 20, 0, -6, 3, 0, -7, 4, 0, -8, 5, 0, 16, 16, 0, 0, 2, 0, 0, 2, 0, -1, 15, 0, -2, 6, 0, -3, 7, 0, -1, 22, 0, 0, 3, 0, 0, 3, 0, -1, 23, 0, 0, 4, 0, 0, 4, 0, -1, 24, 0, -2, 25, 0, -3, 10, 0, -1, 17, 0, 0, 6, 0, -1, 8, 0, -2, 9, 0, 0, 8, 0, 0, 9, 0, -1, 26, 0, 0, 10, 0, -1, 12, 0, -1, 14, 0, -1, 16, 0, -1, 19, 0, -1, 21, 0, 0, 24, 0, 0, 25, 0, 17, 1, 53], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 16, 17, 22, 23, 26], [-1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 1, 2, 2, 2, 2], [0, 0, 0, 0, 0, 3, 0, 4, 0, 5, 0, 0, 0, 0, 0, 2, 0, 6, 0, 2, 7, 1, 1, 1, 1]], [[{"name": "zb_levelprogress", "rect": [0, 0, 132, 24], "offset": [0, 0], "originalSize": [132, 24], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [18], [8]]]]